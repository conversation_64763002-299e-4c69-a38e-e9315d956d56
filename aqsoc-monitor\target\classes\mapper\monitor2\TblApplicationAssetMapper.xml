<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblApplicationAssetMapper">
    
    <resultMap type="TblApplicationAsset" id="TblApplicationAssetResult">
        <result property="rid"    column="rid"    />
        <result property="aid"    column="AID"    />
        <result property="tid"    column="TID"    />
        <result property="func"    column="func"    />
        <association property="asset"  column="tid" javaType="Assets" resultMap="assetResult" />
        <association property="application"  column="aid" javaType="TblApplication" resultMap="appResult" />
    </resultMap>
    <resultMap id="assetResult" type="Assets">
        <id     property="tid"    column="tid"     />
        <result property="name"  column="zcname"   />
        <result property="type"  column="zctype" />
        <result property="netid"  column="netid" />
        <result property="deptId"  column="dept_id"   />
        <result property="isVirdvc"  column="is_virdvc"   />
        <result property="emp"  column="emp"   />
        <result property="ip"  column="ip"   />
        <result property="mac" column="zcmac"   />
        <result property="location" column="location"   />
        <result property="state"  column="zcstate"   />
    </resultMap>
    <resultMap id="appResult" type="TblApplication">
        <id     property="tid"    column="aid"     />
        <result property="name"  column="appname"   />
        <result property="ver" column="ver"   />
        <result property="deptid"  column="deptid"   />
        <result property="type" column="type"   />
        <result property="grade" column="grade"   />
        <result property="dwid"  column="dwid"   />
        <result property="shelf"  column="shelf"   />
        <result property="pid"  column="pid"   />
    </resultMap>
    <resultMap id="EVServerVo" type="com.ruoyi.monitor2.vo.EVServer">

    </resultMap>

    <sql id="selectTblApplicationAssetVo">
        select rid, aid, tid, func from tbl_application_asset
    </sql>

    <select id="selectTblApplicationAssetList" parameterType="TblApplicationAsset" resultMap="TblApplicationAssetResult">
        select a.rid, a.aid, a.tid, a.func ,b.name as zcname, b.type as zctype, b.netid, b.dept_id, b.is_virdvc,
               b.emp, b.ip, b.mac as zcmac,b.location, b.state as zcstate,
               c.name as appname,c.ver, c.deptid, c.type, c.grade, c.dwid, c.shelf, c.pid
        from tbl_application_asset a
            left join monitor_assets b on a.tid = b.tid
            left join tbl_application c on a.aid = c.tid and c.del_flag='0' and c.shelf ='Y'
        <where>  
            <if test="aid != null  and aid != ''"> and a.aid = #{aid}</if>
            <if test="tid != null  and tid != ''"> and b.name like concat('%', #{tid}, '%')</if>
            <if test="func != null  and func != ''"> and a.func = #{func}</if>
        </where>
        order by a.aid asc
    </select>
    
    <select id="selectTblApplicationAssetByRid" parameterType="Long" resultMap="TblApplicationAssetResult">
        select a.rid, a.aid, a.tid, a.func ,b.name as zcname, b.type as zctype, b.netid, b.dept_id, b.is_virdvc,
               b.emp, b.ip, b.mac as zcmac,b.location, b.state as zcstate,
               c.name as appname,c.ver, c.deptid, c.type, c.grade, c.dwid, c.shelf
        from tbl_application_asset a
                 left join monitor_assets b on a.tid = b.tid
                 left join tbl_application c on a.aid = c.tid and c.del_flag='0' and c.shelf ='Y'
        where a.rid = #{rid}
    </select>
        
    <insert id="insertTblApplicationAsset" parameterType="TblApplicationAsset" useGeneratedKeys="true" keyProperty="rid">
        insert into tbl_application_asset
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aid != null">AID,</if>
            <if test="tid != null">TID,</if>
            <if test="func != null">func,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aid != null">#{aid},</if>
            <if test="tid != null">#{tid},</if>
            <if test="func != null">#{func},</if>
         </trim>
    </insert>

    <update id="updateTblApplicationAsset" parameterType="TblApplicationAsset">
        update tbl_application_asset
        <trim prefix="SET" suffixOverrides=",">
            <if test="aid != null">aid = #{aid},</if>
            <if test="tid != null">tid = #{tid},</if>
            <if test="func != null">func = #{func},</if>
        </trim>
        where rid = #{rid}
    </update>

    <delete id="deleteTblApplicationAssetByRid" parameterType="Long">
        delete from tbl_application_asset where rid = #{rid}
    </delete>

    <delete id="deleteTblApplicationAssetByRids" parameterType="String">
        delete from tbl_application_asset where rid in 
        <foreach item="rid" collection="array" open="(" separator="," close=")">
            #{rid}
        </foreach>
    </delete>



    <select id="selelctEVServerList" parameterType="Long" resultMap="EVServerVo">
        select id,asset_id,server_id from tbl_application_server
        where asset_id = #{assetId}
    </select>

    <insert id="insertEVServer" parameterType="list">
        insert into tbl_application_server
        (id,asset_id,server_id)
        <foreach collection="array" item="evs" separator="),(" close=")" open="values (">
            #{evs.id},
            #{evs.assetId},
            #{evs.serverId}
        </foreach>
    </insert>
</mapper>