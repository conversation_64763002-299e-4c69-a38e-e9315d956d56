<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblVendorMapper">
    
    <resultMap type="TblVendor" id="TblVendorResult">
        <result property="id" column="id"/>
        <result property="vendorCode" column="vendor_code"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="vendorManageName" column="vendor_manage_name"/>
        <result property="vendorPhone" column="vendor_phone"/>
        <result property="vendorEmail" column="vendor_email"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>

    <sql id="selectTblVendorVo">
        select id, vendor_code, vendor_name, vendor_manage_name, vendor_phone, vendor_email, remark, create_by, create_time, update_by, update_time, user_id, dept_id from tbl_vendor as v
    </sql>

    <select id="selectTblVendorList" parameterType="TblVendor" resultMap="TblVendorResult">
        <include refid="selectTblVendorVo"/>
        <where>
            <if test="vendorCode != null  and vendorCode != ''"> and vendor_code = #{vendorCode}</if>
            <if test="vendorName != null  and vendorName != ''"> and vendor_name like concat('%', #{vendorName}, '%')</if>
            <if test="vendorManageName != null  and vendorManageName != ''"> and vendor_manage_name like concat('%', #{vendorManageName}, '%')</if>
            <if test="vendorPhone != null  and vendorPhone != ''"> and vendor_phone = #{vendorPhone}</if>
            <if test="vendorEmail != null  and vendorEmail != ''"> and vendor_email = #{vendorEmail}</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
    </select>

    

    <select id="selectTblVendorListByApplicationId" parameterType="TblVendor" resultMap="TblVendorResult">
        
        select v.id, v.vendor_code, v.vendor_name, v.vendor_manage_name, v.vendor_phone, v.vendor_email, v.remark, v.create_by, v.create_time, v.update_by, v.update_time, v.user_id, v.dept_id 
        from tbl_vendor as v 
        <!--
        on a.vendor = v.id
        -->
        <choose>
        <when test="applicationId != null and applicationId != ''">
            left join tbl_business_application  a
            on 1=1 and  find_in_set(v.id, a.vendors) and a.asset_id = #{applicationId}
        </when>
        <otherwise>
        <if test="applicationCodeJoin != null and applicationCodeJoin != ''">
            left join
            (
                select id  from tbl_vendor where  find_in_set(id,  concat(#{applicationCodeJoin}))
            )  as v1
            on 1=1 and  v1.id = v.id
        
        </if>
        </otherwise>
        </choose>

        <where>
            <if test="vendorCode != null  and vendorCode != ''"> and vendor_code = #{vendorCode}</if>
            <if test="vendorName != null  and vendorName != ''"> and vendor_name like concat('%', #{vendorName}, '%')</if>
            <if test="vendorManageName != null  and vendorManageName != ''"> and vendor_manage_name like concat('%', #{vendorManageName}, '%')</if>
            <if test="vendorPhone != null  and vendorPhone != ''"> and vendor_phone = #{vendorPhone}</if>
            <if test="vendorEmail != null  and vendorEmail != ''"> and vendor_email = #{vendorEmail}</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
    </select>


    <select id="selectTblVendorById" parameterType="Long" resultMap="TblVendorResult">
        <include refid="selectTblVendorVo"/>
        where id = #{id}
    </select>
    <select id="selectTblVendorByIds" parameterType="string" resultMap="TblVendorResult">
        <include refid="selectTblVendorVo"/>
        where id in
        <foreach open="(" close=")" separator="," collection="list" item="id">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblVendor" parameterType="TblVendor" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_vendor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vendorCode != null">vendor_code,</if>
            <if test="vendorName != null">vendor_name,</if>
            <if test="vendorManageName != null">vendor_manage_name,</if>
            <if test="vendorPhone != null">vendor_phone,</if>
            <if test="vendorEmail != null">vendor_email,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vendorCode != null">#{vendorCode},</if>
            <if test="vendorName != null">#{vendorName},</if>
            <if test="vendorManageName != null">#{vendorManageName},</if>
            <if test="vendorPhone != null">#{vendorPhone},</if>
            <if test="vendorEmail != null">#{vendorEmail},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>

    <update id="updateTblVendor" parameterType="TblVendor">
        update tbl_vendor
        <trim prefix="SET" suffixOverrides=",">
            <if test="vendorCode != null">vendor_code = #{vendorCode},</if>
            <if test="vendorName != null">vendor_name = #{vendorName},</if>
            <if test="vendorManageName != null">vendor_manage_name = #{vendorManageName},</if>
            <if test="vendorPhone != null">vendor_phone = #{vendorPhone},</if>
            <if test="vendorEmail != null">vendor_email = #{vendorEmail},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblVendorById" parameterType="Long">
        delete from tbl_vendor where id = #{id}
    </delete>

    <delete id="deleteTblVendorByIds" parameterType="String">
        delete from tbl_vendor where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getVendorListByIds" parameterType="String" resultMap="TblVendorResult">
        SELECT
            id,
            vendor_name
        FROM
            tbl_vendor v
        <where>
            <if test="vendors != null and vendors != ''">
                FIND_IN_SET( v.ID, #{vendors})
            </if>
        </where>
            ORDER BY
                id
    </select>
</mapper>