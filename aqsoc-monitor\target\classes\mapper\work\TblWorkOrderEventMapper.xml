<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.work.mapper.TblWorkOrderEventMapper">

    <resultMap type="TblWorkOrderEvent" id="TblWorkOrderEventResult">
        <result property="id"    column="id"    />
        <result property="workOrderId"    column="work_order_id"    />
        <result property="workOrderTargetId"    column="work_order_target_id"    />
        <result property="type"    column="type"    />
        <result property="eventId"    column="event_id"    />
        <result property="handleState"    column="handle_state"    />
        <result property="checkState"    column="check_state"    />
        <result property="handleDesc" column="handle_desc"    />
        <result property="handleFile" column="handle_file"    />
        <result property="checkDesc" column="check_desc"    />
        <result property="checkFile" column="check_file"    />
    </resultMap>

    <sql id="selectTblWorkOrderEventVo">
        select id, work_order_id, work_order_target_id, type, event_id, handle_state,check_state, handle_desc, handle_file, check_desc, check_file from tbl_work_order_event
    </sql>

    <select id="selectTblWorkOrderEventList" parameterType="TblWorkOrderEvent" resultMap="TblWorkOrderEventResult">
        <include refid="selectTblWorkOrderEventVo"/>
        <where>
            <if test="workOrderId != null "> and work_order_id = #{workOrderId}</if>
            <if test="workOrderIds != null and workOrderIds.size()>0">
                and work_order_id in
                <foreach item="item" collection="workOrderIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="workOrderTargetId != null "> and work_order_target_id = #{workOrderTargetId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="eventId != null "> and event_id = #{eventId}</if>
            <if test="handleState != null "> and handle_state = #{handleState}</if>
            <if test="checkState != null "> and check_state = #{checkState}</if>
        </where>
    </select>

    <select id="selectTblWorkOrderEventById" parameterType="Long" resultMap="TblWorkOrderEventResult">
        <include refid="selectTblWorkOrderEventVo"/>
        where id = #{id}
    </select>

    <select id="selectTblWorkOrderEventByIds" parameterType="Long" resultMap="TblWorkOrderEventResult">
        <include refid="selectTblWorkOrderEventVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblWorkOrderEvent" parameterType="TblWorkOrderEvent" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_work_order_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id,</if>
            <if test="workOrderTargetId != null">work_order_target_id,</if>
            <if test="type != null">type,</if>
            <if test="eventId != null">event_id,</if>
            <if test="handleState != null">handle_state,</if>
            <if test="checkState != null">check_state,</if>
            <if test="handleDesc != null">handle_desc,</if>
            <if test="handleFile != null">handle_file,</if>
            <if test="checkDesc != null">check_desc,</if>
            <if test="checkFile != null">check_file,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">#{workOrderId},</if>
            <if test="workOrderTargetId != null">#{workOrderTargetId},</if>
            <if test="type != null">#{type},</if>
            <if test="eventId != null">#{eventId},</if>
            <if test="handleState != null">#{handleState},</if>
            <if test="checkState != null">#{checkState},</if>
            <if test="handleDesc != null">#{handleDesc},</if>
            <if test="handleFile != null">#{handleFile},</if>
            <if test="checkDesc != null">#{checkDesc},</if>
            <if test="checkFile != null">#{checkFile},</if>
         </trim>
    </insert>
    <insert id="batchInsert">
        insert into tbl_work_order_event (work_order_id, work_order_target_id, type, event_id, handle_state, check_state) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.workOrderId}, #{item.workOrderTargetId}, #{item.type}, #{item.eventId}, #{item.handleState},#{item.checkState})
        </foreach>
    </insert>

    <update id="updateTblWorkOrderEvent" parameterType="TblWorkOrderEvent">
        update tbl_work_order_event
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id = #{workOrderId},</if>
            <if test="workOrderTargetId != null">work_order_target_id = #{workOrderTargetId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="eventId != null">event_id = #{eventId},</if>
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="checkState != null">check_state = #{checkState},</if>
            <if test="handleDesc != null">handle_desc = #{handleDesc},</if>
            <if test="handleFile != null">handle_file = #{handleFile},</if>
            <if test="checkDesc != null">check_desc = #{checkDesc},</if>
            <if test="checkFile != null">check_file = #{checkFile},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchUpdate">
        UPDATE tbl_work_order_event
        SET
        handle_state = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN <if test="item.handleState != null">#{item.handleState}</if> <if test="item.handleState == null">handle_state</if>
        </foreach>
        ELSE handle_state
        END,
        check_state = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN <if test="item.checkState != null">#{item.checkState}</if> <if test="item.checkState == null">check_state</if>
        </foreach>
        ELSE check_state
        END,
        handle_desc = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN <if test="item.handleDesc != null and item.handleDesc != ''">#{item.handleDesc}</if> <if test="item.handleDesc == null or item.handleDesc == ''">handle_desc</if>
        </foreach>
        ELSE handle_desc
        END,
        handle_file = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN <if test="item.handleFile != null and item.handleFile != ''">#{item.handleFile}</if> <if test="item.handleFile == null or item.handleFile == ''">handle_file</if>
        </foreach>
        ELSE handle_file
        END,
        check_desc = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN <if test="item.checkDesc != null and item.checkDesc != ''">#{item.checkDesc}</if> <if test="item.checkDesc == null or item.checkDesc == ''">check_desc</if>
        </foreach>
        ELSE check_desc
        END,
        check_file = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN <if test="item.checkFile != null and item.checkFile != ''">#{item.checkFile}</if> <if test="item.checkFile == null or item.checkFile == ''">check_file</if>
        </foreach>
        ELSE check_file
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <delete id="deleteTblWorkOrderEventById" parameterType="Long">
        delete from tbl_work_order_event where id = #{id}
    </delete>

    <delete id="deleteTblWorkOrderEventByIds" parameterType="String">
        delete from tbl_work_order_event where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTblWorkOrderEventByWorkId">
        delete from tbl_work_order_event where work_order_id = #{workOrderId}
    </delete>
</mapper>
