<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblKnowledgeMapper">
    
    <resultMap type="com.ruoyi.monitor2.domain.TblKnowledge" id="TblKnowledgeResult">
        <result property="kid"    column="kid"    />
        <result property="id"    column="id"    />
        <result property="icp"    column="icp"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="fileIds"    column="file_ids"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="registerTime"    column="register_time"    />
        <result property="exParams"    column="ex_params"    />
        <result property="keyword"    column="keyword"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTblKnowledgeVo">
        select kid, id, icp, title, content,file_ids, create_by, create_time, update_time,register_time,ex_params,keyword,user_id,dept_id,remark from tbl_knowledge
    </sql>

    <select id="selectTblKnowledgeList" parameterType="TblKnowledge" resultMap="TblKnowledgeResult">
        <include refid="selectTblKnowledgeVo"/>
        <where>
            <if test="id != null and id != 0">and id = #{id}</if>
            <if test="icp != null and icp != 0">and icp = #{icp}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="fileIds != null  and fileIds != ''"> and find_in_set(file_ids,#{fileIds})</if>
            <if test="keyword != null  and keyword != ''"> and keyword like concat('%',#{keyword},'%')</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="notKid != null and notKid != 0">and kid != #{notKid}</if>
        </where>
    </select>
    
    <select id="selectTblKnowledgeByKid" parameterType="Long" resultMap="TblKnowledgeResult">
        <include refid="selectTblKnowledgeVo"/>
        where kid = #{kid}
    </select>
    <select id="selectTblKnowledgeByIds" parameterType="TblKnowledgeType" resultMap="TblKnowledgeResult">
        <include refid="selectTblKnowledgeVo"/>
        <where>
            <if test="ids != null">id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="icp != null and icp != ''">and icp = #{icp}</if>
            <if test="title != null  and title != ''"> and title like concat('%',#{title},'%')</if>
            <if test="content != null  and content != ''"> and content like concat('%',#{content},'%')</if>
        </where>
    </select>
        
    <insert id="insertTblKnowledge" parameterType="TblKnowledge">
        insert into tbl_knowledge
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="kid != null">kid,</if>
            <if test="id != null">id,</if>
            <if test="icp != null">icp,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="fileIds != null">file_ids,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="registerTime != null">register_time,</if>
            <if test="exParams != null and exParams != ''">ex_params,</if>
            <if test="keyword != null and keyword != ''">keyword,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="deptId != null and deptId != ''">dept_id,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="kid != null">#{kid},</if>
            <if test="id != null">#{id},</if>
            <if test="icp != null">#{icp},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="fileIds != null">#{fileIds},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="registerTime != null">#{registerTime},</if>
            <if test="exParams != null and exParams != ''">#{exParams},</if>
            <if test="keyword != null and keyword != ''">#{keyword},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="deptId != null and deptId != ''">#{deptId},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTblKnowledge" parameterType="TblKnowledge">
        update tbl_knowledge
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="icp != null">icp = #{icp},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="1==1">file_ids = #{fileIds},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
            <if test="exParams != null and exParams != ''">ex_params = #{exParams},</if>
            <if test="keyword != null and keyword != ''">keyword = #{keyword},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where kid = #{kid}
    </update>

    <delete id="deleteTblKnowledgeByKid" parameterType="Long">
        delete from tbl_knowledge where kid = #{kid}
    </delete>

    <delete id="deleteTblKnowledgeByKids" parameterType="String">
        delete from tbl_knowledge where kid in 
        <foreach item="kid" collection="array" open="(" separator="," close=")">
            #{kid}
        </foreach>
    </delete>
</mapper>