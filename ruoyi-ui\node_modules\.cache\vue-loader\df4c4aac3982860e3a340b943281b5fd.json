{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\hostEvent.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\hostEvent.vue", "mtime": 1756369455972}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7cGFyc2VUaW1lfSBmcm9tICJAL3V0aWxzL3J1b3lpIjsKaW1wb3J0IHtsaXN0RGV2aWNlQ29uZmlnfSBmcm9tICJAL2FwaS9mZnNhZmUvZGV2aWNlQ29uZmlnIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiaG9zdEV2ZW50IiwKICBkaWN0czogWydoYW5kbGVfc3RhdGUnXSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc2hvd0FsbDogZmFsc2UsCiAgICAgIHJhbmdlVGltZTogW10sCiAgICAgIGZvcm06IHt9LAogICAgICB0b3RhbDogMCwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9LAogICAgICBsaXN0OiBbXSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIG11bHRpcGxlU2VsZWN0aW9uOiBbXSwKICAgICAgZGV2aWNlQ29uZmlnTGlzdDogW10sCiAgICAgIHNob3dIYW5kbGVCYXRjaERpYWxvZzogZmFsc2UsCiAgICAgIGZvcm1SdWxlczogewogICAgICAgIGhhbmRsZVN0YXRlOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nlpITnva7nirbmgIEnLCB0cmlnZ2VyOiAnYmx1cid9CiAgICAgICAgXSwKICAgICAgfQogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuaW5pdCgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBpbml0KCkgewogICAgICB0aGlzLnNldERlZmF1bHREYXRlUmFuZ2UoKTsKICAgICAgdGhpcy5nZXREZXZpY2VDb25maWdMaXN0KCk7CiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgZ2V0TGlzdCgpIHsKCiAgICB9LAoKICAgIGdldERldmljZUNvbmZpZ0xpc3QoKXsKICAgICAgbGlzdERldmljZUNvbmZpZyh7cXVlcnlBbGxEYXRhOiB0cnVlfSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZGV2aWNlQ29uZmlnTGlzdCA9IHJlcy5yb3dzOwogICAgICB9KQogICAgfSwKCiAgICAvKiog6K6+572u6buY6K6k5pel5pyf6IyD5Zu0ICovCiAgICBzZXREZWZhdWx0RGF0ZVJhbmdlKCkgewogICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpCiAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKQogICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA3KQoKICAgICAgLy8g5byA5aeL5pe26Ze05Li6IDAwOjAwOjAw77yM57uT5p2f5pe26Ze05Li6IDIzOjU5OjU5CiAgICAgIHN0YXJ0LnNldEhvdXJzKDAsIDAsIDAsIDApCiAgICAgIGVuZC5zZXRIb3VycygyMywgNTksIDU5LCA5OTkpCgogICAgICB0aGlzLnJhbmdlVGltZSA9IFsKICAgICAgICB0aGlzLnBhcnNlVGltZShzdGFydCwgJ3t5fS17bX0te2R9IHtofTp7aX06e3N9JyksCiAgICAgICAgdGhpcy5wYXJzZVRpbWUoZW5kLCAne3l9LXttfS17ZH0ge2h9OntpfTp7c30nKQogICAgICBdCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuYmVnaW5UaW1lID0gdGhpcy5yYW5nZVRpbWVbMF0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmRUaW1lID0gdGhpcy5yYW5nZVRpbWVbMV0KICAgIH0sCgogICAgLy8g5p+l6K+iCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgaWYgKHRoaXMucmFuZ2VUaW1lICE9IG51bGwpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmJlZ2luVGltZSA9IHBhcnNlVGltZSh0aGlzLnJhbmdlVGltZVswXSk7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmRUaW1lID0gcGFyc2VUaW1lKHRoaXMucmFuZ2VUaW1lWzFdKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmJlZ2luVGltZSA9IG51bGw7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmRUaW1lID0gbnVsbDsKICAgICAgfQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLiRlbWl0KCdnZXRMaXN0JywgeyAuLi50aGlzLnF1ZXJ5UGFyYW1zIH0pCiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKCiAgICAvLyDor6bmg4UKICAgIGhhbmRsZURldGFpbChyb3cpIHsKCiAgICB9LAoKICAgIC8vIOWkhOe9rgogICAgaGFuZGxlRGlzcG9zZShyb3cpIHsKCiAgICB9LAoKICAgIC8vIOWvvOWHugogICAgaGFuZGxlRXhwb3J0KCkgewoKICAgIH0sCgogICAgLy8g5om56YeP5Yig6ZmkCiAgICBoYW5kbGVEZWxldGUoKSB7CgogICAgfSwKCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSB2YWwKICAgIH0sCgogICAgLy8g5aSE572u54q25oCBCiAgICBiYXRjaERpc3Bvc2UoKSB7CiAgICAgIHRoaXMuc2hvd0hhbmRsZUJhdGNoRGlhbG9nID0gZmFsc2UKICAgIH0sCgogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsKICAgICAgICBhdHRhY2tJcDogbnVsbCwKICAgICAgICB0YXJnZXRJcDogbnVsbCwKICAgICAgICBhdHRhY2tUeXBlOiBudWxsLAogICAgICAgIHRpbWVSYW5nZTogbnVsbCwKICAgICAgICBoYW5kbGVTdGF0ZTogbnVsbAogICAgICB9CiAgICAgIHRoaXMuc2V0RGVmYXVsdERhdGVSYW5nZSgpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkKICAgIH0sCiAgfQp9Cg=="}, {"version": 3, "sources": ["hostEvent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmLA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "hostEvent.vue", "sourceRoot": "src/views/frailty/event/component", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警时间\">\n                <el-date-picker\n                  v-model=\"rangeTime\"\n                  type=\"datetimerange\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                  :default-time=\"['00:00:00', '23:59:59']\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"综合风险\">\n                <el-select v-model=\"queryParams.attackType\" placeholder=\"请选择综合风险\">\n                  <el-option label=\"高中\" value=\"0\"></el-option>\n                  <el-option label=\"中\" value=\"1\"></el-option>\n                  <el-option label=\"低\" value=\"2\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置状态\">\n                <el-select v-model=\"queryParams.handleState\" placeholder=\"请选择处置状态\">\n                  <el-option label=\"待处理\" value=\"0\"></el-option>\n                  <el-option label=\"处理中\" value=\"1\"></el-option>\n                  <el-option label=\"处理完成\" value=\"2\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\"\n                >查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\"\n                >重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"攻击源IP\">\n                <el-input v-model=\"queryParams.attackIp\" placeholder=\"请输入攻击源IP\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"文件名\">\n                <el-input v-model=\"queryParams.targetIp\" placeholder=\"请输入文件名\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属探针\">\n                <el-select v-model=\"queryParams.deviceConfigId\" filterable clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in deviceConfigList\"\n                    :key=\"item.id\"\n                    :label=\"item.deviceName\"\n                    :value=\"item.id\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">主机事件列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"!multipleSelection.length\"\n                  @click=\"handleDetail\"\n                >批量删除</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <div class=\"tableContainer\">\n          <el-table\n            :data=\"list\"\n            height=\"100%\"\n            v-loading=\"loading\"\n            @selection-change=\"handleSelectionChange\"\n          >\n            <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n            <el-table-column label=\"主机IP\" align=\"left\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span class=\"table-serial-number\">{{ scope.row.attackIp }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"检测事项\" align=\"left\" prop=\"targetIp\"/>\n            <el-table-column label=\"综合风险\" align=\"left\" prop=\"threatenName\"/>\n            <el-table-column label=\"类别\" align=\"left\" prop=\"threatenTime\"/>\n            <el-table-column label=\"处置状态\" align=\"left\" prop=\"threatenLevel\"/>\n            <el-table-column label=\"告警时间\" align=\"left\" prop=\"threatenEndTime\"/>\n            <el-table-column label=\"操作\" width=\"120\">\n              <template slot-scope=\"scope\">\n                <el-button\n                  size=\"mini\"\n                  type=\"text\"\n                  @click=\"handleDetail(scope.row)\"\n                >详情</el-button>\n                <el-button\n                  size=\"mini\"\n                  type=\"text\"\n                  @click=\"handleDispose(scope.row)\"\n                >处置</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleBatchDialog\"\n      width=\"600px\"\n      append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"formRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\n          <el-select v-model=\"form.handleState\" clearable placeholder=\"请选择处置状态\">\n            <el-option v-for=\"dict in dict.type.handle_state\"\n                       :key=\"dict.value\" :label=\"dict.label\"\n                       :value=\"parseInt(dict.value)\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"form.handleDesc\" placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showHandleBatchDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"batchDispose\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {parseTime} from \"@/utils/ruoyi\";\nimport {listDeviceConfig} from \"@/api/ffsafe/deviceConfig\";\n\nexport default {\n  name: \"hostEvent\",\n  dicts: ['handle_state'],\n  data() {\n    return {\n      showAll: false,\n      rangeTime: [],\n      form: {},\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      list: [],\n      loading: false,\n      multipleSelection: [],\n      deviceConfigList: [],\n      showHandleBatchDialog: false,\n      formRules: {\n        handleState: [\n          {required: true, message: '请选择处置状态', trigger: 'blur'}\n        ],\n      }\n    }\n  },\n  created() {\n    this.init()\n  },\n  methods: {\n    init() {\n      this.setDefaultDateRange();\n      this.getDeviceConfigList();\n      this.getList()\n    },\n    getList() {\n\n    },\n\n    getDeviceConfigList(){\n      listDeviceConfig({queryAllData: true}).then(res => {\n        this.deviceConfigList = res.rows;\n      })\n    },\n\n    /** 设置默认日期范围 */\n    setDefaultDateRange() {\n      const end = new Date()\n      const start = new Date()\n      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\n\n      // 开始时间为 00:00:00，结束时间为 23:59:59\n      start.setHours(0, 0, 0, 0)\n      end.setHours(23, 59, 59, 999)\n\n      this.rangeTime = [\n        this.parseTime(start, '{y}-{m}-{d} {h}:{i}:{s}'),\n        this.parseTime(end, '{y}-{m}-{d} {h}:{i}:{s}')\n      ]\n      this.queryParams.beginTime = this.rangeTime[0]\n      this.queryParams.endTime = this.rangeTime[1]\n    },\n\n    // 查询\n    handleQuery() {\n      if (this.rangeTime != null) {\n        this.queryParams.beginTime = parseTime(this.rangeTime[0]);\n        this.queryParams.endTime = parseTime(this.rangeTime[1]);\n      } else {\n        this.queryParams.beginTime = null;\n        this.queryParams.endTime = null;\n      }\n      this.queryParams.pageNum = 1;\n      this.$emit('getList', { ...this.queryParams })\n      this.getList();\n    },\n\n    // 详情\n    handleDetail(row) {\n\n    },\n\n    // 处置\n    handleDispose(row) {\n\n    },\n\n    // 导出\n    handleExport() {\n\n    },\n\n    // 批量删除\n    handleDelete() {\n\n    },\n\n    handleSelectionChange(val) {\n      this.multipleSelection = val\n    },\n\n    // 处置状态\n    batchDispose() {\n      this.showHandleBatchDialog = false\n    },\n\n    resetQuery() {\n      this.queryParams = {\n        attackIp: null,\n        targetIp: null,\n        attackType: null,\n        timeRange: null,\n        handleState: null\n      }\n      this.setDefaultDateRange();\n      this.handleQuery()\n    },\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n\n</style>\n"]}]}