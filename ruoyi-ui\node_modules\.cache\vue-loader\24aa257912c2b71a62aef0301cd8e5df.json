{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\eventList.vue?vue&type=template&id=9f5cded6&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\eventList.vue", "mtime": 1756439379974}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}