<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeIpfilterLogMapper">

    <resultMap type="FfsafeIpfilterLog" id="FfsafeIpfilterLogResult">
        <result property="id"    column="id"    />
        <result property="ip"    column="ip"    />
        <result property="location"    column="location"    />
        <result property="action"    column="action"    />
        <result property="conditions"    column="conditions"    />
        <result property="filterSrc"    column="filter_src"    />
        <result property="createTime"    column="create_time"    />
        <result property="releaseTime"    column="release_time"    />
        <result property="remark"    column="remark"    />
        <result property="outId" column="out_id" />
        <result property="deviceConfigId" column="device_config_id" />
    </resultMap>

    <sql id="selectFfsafeIpfilterLogVo">
        select id, ip, location, action, conditions, filter_src, create_time, release_time, remark,out_id,device_config_id from ffsafe_ipfilter_log
    </sql>

    <select id="selectFfsafeIpfilterLogList" parameterType="FfsafeIpfilterLog" resultMap="FfsafeIpfilterLogResult">
        <include refid="selectFfsafeIpfilterLogVo"/>
        <where>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
            <if test="action != null  and action != ''"> and action = #{action}</if>
            <if test="conditions != null  and conditions != ''"> and conditions = #{conditions}</if>
            <if test="filterSrc != null  and filterSrc != ''"> and filter_src = #{filterSrc}</if>
            <if test="releaseTime != null "> and release_time = #{releaseTime}</if>
            <if test="deviceConfigId != null"> and device_config_id = #{deviceConfigId}</if>
        </where>
    </select>

    <select id="selectFfsafeIpfilterLogById" parameterType="Long" resultMap="FfsafeIpfilterLogResult">
        <include refid="selectFfsafeIpfilterLogVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeIpfilterLogByIds" parameterType="Long" resultMap="FfsafeIpfilterLogResult">
        <include refid="selectFfsafeIpfilterLogVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="countIpNum" resultType="java.lang.Integer">
        select count(distinct ip) from ffsafe_ipfilter_log
    </select>
    <select id="selectLastByIpList" resultType="com.ruoyi.ffsafe.api.domain.FfsafeIpfilterLog" resultMap="FfsafeIpfilterLogResult">
        SELECT
            t1.ip,t1.release_time,t1.action
        FROM
            `ffsafe_ipfilter_log` t1
             JOIN (SELECT max(id) maxId FROM ffsafe_ipfilter_log GROUP BY ip) t2 ON t2.maxId=t1.id
        WHERE
        t1.ip in
        <foreach item="ip" collection="ipList" open="(" separator="," close=")">
            #{ip}
        </foreach>
    </select>

    <insert id="insertFfsafeIpfilterLog" parameterType="FfsafeIpfilterLog" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_ipfilter_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ip != null">ip,</if>
            <if test="location != null">location,</if>
            <if test="action != null">action,</if>
            <if test="conditions != null">conditions,</if>
            <if test="filterSrc != null">filter_src,</if>
            <if test="createTime != null">create_time,</if>
            <if test="releaseTime != null">release_time,</if>
            <if test="remark != null">remark,</if>
            <if test="outId != null">out_id,</if>
            <if test="deviceConfigId != null">device_config_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ip != null">#{ip},</if>
            <if test="location != null">#{location},</if>
            <if test="action != null">#{action},</if>
            <if test="conditions != null">#{conditions},</if>
            <if test="filterSrc != null">#{filterSrc},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="releaseTime != null">#{releaseTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="outId != null">#{outId},</if>
            <if test="deviceConfigId != null">#{deviceConfigId},</if>
        </trim>
    </insert>

    <update id="updateFfsafeIpfilterLog" parameterType="FfsafeIpfilterLog">
        update ffsafe_ipfilter_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="ip != null">ip = #{ip},</if>
            <if test="location != null">location = #{location},</if>
            <if test="action != null">action = #{action},</if>
            <if test="conditions != null">conditions = #{conditions},</if>
            <if test="filterSrc != null">filter_src = #{filterSrc},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="releaseTime != null">release_time = #{releaseTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="outId != null">out_id = #{outId},</if>
            <if test="deviceConfigId != null">device_config_id = #{deviceConfigId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeIpfilterLogById" parameterType="Long">
        delete from ffsafe_ipfilter_log where id = #{id}
    </delete>

    <delete id="deleteFfsafeIpfilterLogByIds" parameterType="String">
        delete from ffsafe_ipfilter_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
