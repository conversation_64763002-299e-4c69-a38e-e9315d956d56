<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblGeneralAssetMapper">
    
    <resultMap type="TblGeneralAsset" id="TblGeneralAssetResult">
        <result property="id"    column="ID"    />
        <result property="name"    column="name"    />
        <result property="grade"    column="grade"    />
        <result property="isTarget"    column="is_target"    />
        <result property="itemTotal"    column="item_total"    />
        <result property="taskId"    column="task_id"    />
        <result property="module"    column="module"    />
        <result property="mdname"    column="mdname"    />
        <result property="path"    column="path"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectTblGeneralAssetVo">
        select ID, name, grade, is_target, item_total, task_id, module, mdname, path, create_time from tbl_general_asset
    </sql>

    <select id="selectTblGeneralAssetList" parameterType="TblGeneralAsset" resultMap="TblGeneralAssetResult">
        <include refid="selectTblGeneralAssetVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="grade != null  and grade != ''"> and grade = #{grade}</if>
            <if test="isTarget != null  and isTarget != ''"> and is_target = #{isTarget}</if>
            <if test="itemTotal != null "> and item_total = #{itemTotal}</if>
            <if test="taskId != null  and taskId != ''"> and task_id = #{taskId}</if>
            <if test="module != null  and module != ''"> and module = #{module}</if>
            <if test="mdname != null  and mdname != ''"> and mdname like concat('%', #{mdname}, '%')</if>
            <if test="path != null  and path != ''"> and path = #{path}</if>
        </where>
    </select>
    
    <select id="selectTblGeneralAssetById" parameterType="Long" resultMap="TblGeneralAssetResult">
        <include refid="selectTblGeneralAssetVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTblGeneralAsset" parameterType="TblGeneralAsset" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_general_asset
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="grade != null">grade,</if>
            <if test="isTarget != null">is_target,</if>
            <if test="itemTotal != null">item_total,</if>
            <if test="taskId != null and taskId != ''">task_id,</if>
            <if test="module != null and module != ''">module,</if>
            <if test="mdname != null">mdname,</if>
            <if test="path != null">path,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="grade != null">#{grade},</if>
            <if test="isTarget != null">#{isTarget},</if>
            <if test="itemTotal != null">#{itemTotal},</if>
            <if test="taskId != null and taskId != ''">#{taskId},</if>
            <if test="module != null and module != ''">#{module},</if>
            <if test="mdname != null">#{mdname},</if>
            <if test="path != null">#{path},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateTblGeneralAsset" parameterType="TblGeneralAsset">
        update tbl_general_asset
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="isTarget != null">is_target = #{isTarget},</if>
            <if test="itemTotal != null">item_total = #{itemTotal},</if>
            <if test="taskId != null and taskId != ''">task_id = #{taskId},</if>
            <if test="module != null and module != ''">module = #{module},</if>
            <if test="mdname != null">mdname = #{mdname},</if>
            <if test="path != null">path = #{path},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTblGeneralAssetById" parameterType="Long">
        delete from tbl_general_asset where ID = #{id}
    </delete>

    <delete id="deleteTblGeneralAssetByIds" parameterType="String">
        delete from tbl_general_asset where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>