<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblNetwareMapper">
    
    <resultMap type="TblNetware" id="TblNetwareResult">
        <result property="netid"    column="netid"    />
        <result property="netname"    column="netname"    />
        <result property="iparea"    column="iparea"    />
        <result property="grade"    column="grade"    />
        <result property="bassets"    column="bassets"    />
        <result property="adnet"    column="adnet"    />
        <result property="manager"    column="manager"    />
        <result property="memo"    column="memo"    />
    </resultMap>

    <sql id="selectTblNetwareVo">
        select netid, netname, iparea, grade, bassets, adnet, manager, memo from tbl_netware
    </sql>

    <select id="selectTblNetwareList" parameterType="TblNetware" resultMap="TblNetwareResult">
        <include refid="selectTblNetwareVo"/>
        <where>  
            <if test="netname != null  and netname != ''"> and netname like concat('%', #{netname}, '%')</if>
            <if test="iparea != null  and iparea != ''"> and iparea = #{iparea}</if>
            <if test="grade != null  and grade != ''"> and grade = #{grade}</if>
            <if test="bassets != null  and bassets != ''"> and bassets = #{bassets}</if>
            <if test="adnet != null  and adnet != ''"> and adnet = #{adnet}</if>
            <if test="manager != null  and manager != ''"> and manager = #{manager}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
        </where>
    </select>
    
    <select id="selectTblNetwareByNetid" parameterType="String" resultMap="TblNetwareResult">
        <include refid="selectTblNetwareVo"/>
        where netid = #{netid}
    </select>
        
    <insert id="insertTblNetware" parameterType="TblNetware">
        insert into tbl_netware
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="netid != null">netid,</if>
            <if test="netname != null and netname != ''">netname,</if>
            <if test="iparea != null">iparea,</if>
            <if test="grade != null">grade,</if>
            <if test="bassets != null">bassets,</if>
            <if test="adnet != null">adnet,</if>
            <if test="manager != null">manager,</if>
            <if test="memo != null">memo,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="netid != null">#{netid},</if>
            <if test="netname != null and netname != ''">#{netname},</if>
            <if test="iparea != null">#{iparea},</if>
            <if test="grade != null">#{grade},</if>
            <if test="bassets != null">#{bassets},</if>
            <if test="adnet != null">#{adnet},</if>
            <if test="manager != null">#{manager},</if>
            <if test="memo != null">#{memo},</if>
         </trim>
    </insert>

    <update id="updateTblNetware" parameterType="TblNetware">
        update tbl_netware
        <trim prefix="SET" suffixOverrides=",">
            <if test="netname != null and netname != ''">netname = #{netname},</if>
            <if test="iparea != null">iparea = #{iparea},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="bassets != null">bassets = #{bassets},</if>
            <if test="adnet != null">adnet = #{adnet},</if>
            <if test="manager != null">manager = #{manager},</if>
            <if test="memo != null">memo = #{memo},</if>
        </trim>
        where netid = #{netid}
    </update>

    <delete id="deleteTblNetwareByNetid" parameterType="String">
        delete from tbl_netware where netid = #{netid}
    </delete>

    <delete id="deleteTblNetwareByNetids" parameterType="String">
        delete from tbl_netware where netid in 
        <foreach item="netid" collection="array" open="(" separator="," close=")">
            #{netid}
        </foreach>
    </delete>
</mapper>