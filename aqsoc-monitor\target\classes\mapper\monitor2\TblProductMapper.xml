<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblProductMapper">

    <resultMap type="com.ruoyi.monitor2.domain.TblProduct" id="TblProductResult">
        <result property="prdid"    column="prdid"    />
        <result property="procName"    column="proc_name"    />
        <result property="manuName"    column="manu_name"    />
        <result property="procType"    column="proc_type"    />
        <result property="ver"    column="ver"    />
        <result property="pubTime"    column="pub_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deployId"    column="deploy_id"    />
        <result property="procNums"    column="proc_nums"    />
        <result property="assetCount" column="asset_count" />
        <result property="serverCount" column="server_count" />
        <result property="applicationCount" column="application_count" />
        <collection property="fingers" ofType="com.ruoyi.monitor2.domain.TblProductFinger">
            <result property="id"    column="id"    />
            <result property="prdid"    column="prdid_finger"    />
            <result property="osName"    column="os_name"    />
            <result property="osVer"    column="os_ver"    />
            <result property="osCpe"    column="os_cpe"    />
        </collection>
    </resultMap>

    <sql id="selectTblProductVo">
        select t1.prdid, t1.proc_name, t1.manu_name, t1.proc_type, t1.ver, t1.pub_time, t1.update_time,t1.proc_nums,
               COUNT(DISTINCT t2.asset_id) asset_count,count(DISTINCT t3.asset_id) server_count,count(DISTINCT t5.asset_id) application_count
        from tbl_product t1
        left join tbl_deploy t2 ON t2.prdid=t1.prdid
        LEFT JOIN tbl_server t3 ON t3.asset_id=t2.asset_id
        LEFT JOIN tbl_application_server t4 ON t4.server_id=t3.asset_id
        LEFT JOIN tbl_business_application t5 ON t5.asset_id=t4.asset_id
        left join sys_dept sd on sd.dept_id=t3.dept_id
    </sql>

    <select id="selectTblProductList" parameterType="com.ruoyi.monitor2.domain.TblProduct" resultMap="TblProductResult">
        <include refid="selectTblProductVo"/>
        <where>
            <if test="prdid != null  and prdid != ''"> and t1.prdid like concat('%', #{prdid}, '%')</if>
            <if test="procName != null  and procName != ''"> and t1.proc_name like concat('%', #{procName}, '%')</if>
            <if test="procNameEqual != null  and procNameEqual != ''"> and t1.proc_name=#{procNameEqual}</if>
            <if test="manuName != null  and manuName != ''"> and t1.manu_name like concat('%', #{manuName}, '%')</if>
            <if test="procType != null  and procType != ''"> and t1.proc_type = #{procType}</if>
            <if test="ver != null  and ver != ''"> and t1.ver = #{ver}</if>
            <if test="pubTime != null "> and t1.pub_time = #{pubTime}</if>
            <if test="procNums != null "> and t1.proc_nums = #{procNums}</if>
            <if test="deptId != null">and (t3.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))</if>
            <if test="applicationId != null">and t5.asset_id=#{applicationId}</if>
        </where>
        GROUP BY t1.prdid
        order by t1.pub_time desc
    </select>

    <select id="selectTblProductByPrdid" parameterType="java.lang.String" resultMap="TblProductResult">
        select tp.prdid, tp.proc_name, tp.manu_name, tp.proc_type, tp.ver, tp.pub_time, tp.update_time, tp.proc_nums, tpf.id, tpf.prdid as prdid_finger,
        tpf.os_name, tpf.os_ver, tpf.os_cpe
        from tbl_product tp
        left join tbl_product_finger tpf on tpf.prdid = tp.prdid
        where tp.prdid = #{prdid}
    </select>

    <select id="selectTblProductByProcName" parameterType="java.lang.String" resultMap="TblProductResult">
        select tp.prdid, tp.proc_name, tp.manu_name, tp.proc_type, tp.ver, tp.pub_time, tp.update_time, tp.proc_nums, tpf.id, tpf.prdid as prdid_finger,
        tpf.os_name, tpf.os_ver, tpf.os_cpe
        from tbl_product tp
        left join tbl_product_finger tpf on tpf.prdid = tp.prdid
        where tp.proc_name = #{procName}
    </select>

    <select id="selectTblProductByAssetId" parameterType="java.lang.String" resultMap="TblProductResult">
        select tp.prdid, tp.proc_name, tp.manu_name, tp.proc_type, tp.ver, tp.pub_time, tp.update_time, tp.proc_nums, tpf.id, tpf.prdid as prdid_finger,
        tpf.os_name, tpf.os_ver, tpf.os_cpe, td.deploy_id
        from tbl_product tp
        inner join tbl_deploy td on td.prdid = tp.prdid
        left join tbl_product_finger tpf on tpf.prdid = tp.prdid
        <where>
            <if test="assetId != null">td.asset_id = #{assetId}</if>
            <if test="softlx != null  and softlx != ''">and td.softlx = #{softlx}</if>
            <if test="port != null"> and td.port = #{port}</if>
        </where>
    </select>
    <select id="selectTblProductByPrdids" parameterType="java.lang.String" resultMap="TblProductResult">
        <include refid="selectTblProductVo"/>
        where t1.prdid in
        <foreach collection="prdids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by t1.prdid
    </select>

    <select id="selectTblProductListByServer" resultType="com.ruoyi.monitor2.domain.TblProduct" resultMap="TblProductResult">
            select t1.prdid, t1.proc_name, t1.manu_name, t1.proc_type, t1.ver, t1.pub_time, t1.update_time,t1.proc_nums,
                   count(DISTINCT t2.asset_id) asset_count,count(DISTINCT t3.asset_id) server_count,count(DISTINCT t5.asset_id) application_count
            from tbl_product t1
            left join tbl_deploy t2 ON t2.prdid=t1.prdid
            LEFT JOIN tbl_server t3 ON t3.asset_id=t2.asset_id
            LEFT JOIN tbl_application_server t4 ON t4.server_id=t3.asset_id
            LEFT JOIN tbl_business_application t5 ON t5.asset_id=t4.asset_id
            left join sys_dept sd on sd.dept_id=t3.dept_id
        <where>
            (t1.proc_type='application' or t1.proc_type='database' or t1.proc_type='middleware')
            <if test="procName != null  and procName != ''"> and t1.proc_name like concat('%', #{procName}, '%')</if>
            <if test="procType != null  and procType != ''"> and t1.proc_type = #{procType}</if>
            <if test="ver != null  and ver != ''"> and t1.ver = #{ver}</if>
            <if test="deptId != null">and (t3.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))</if>
            <if test="applicationId != null">and t5.asset_id=#{applicationId}</if>
        </where>
        group by t1.prdid
        order by t1.pub_time desc
    </select>
    <select id="selectByProcNameList" resultType="com.ruoyi.monitor2.domain.TblProduct">
        select prdid,proc_name,proc_type from tbl_product
        where proc_name in
        <foreach collection="procNameList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND proc_type='system'
    </select>

    <insert id="insertTblProduct" parameterType="com.ruoyi.monitor2.domain.TblProduct">
        insert into tbl_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="prdid != null">prdid,</if>
            <if test="procName != null">proc_name,</if>
            <if test="manuName != null">manu_name,</if>
            <if test="procType != null">proc_type,</if>
            <if test="ver != null">ver,</if>
            <if test="pubTime != null">pub_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="procNums != null">proc_nums,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="prdid != null">#{prdid},</if>
            <if test="procName != null">#{procName},</if>
            <if test="manuName != null">#{manuName},</if>
            <if test="procType != null">#{procType},</if>
            <if test="ver != null">#{ver},</if>
            <if test="pubTime != null">#{pubTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="procNums != null">#{procNums},</if>
         </trim>
    </insert>
    <insert id="batchInsertBaseList">
        insert into tbl_product (prdid,proc_name,proc_type,pub_time) values
        <foreach collection="saveProductList" item="item" index="index" separator=",">
            (#{item.prdid},#{item.procName},#{item.procType},#{item.pubTime})
        </foreach>
    </insert>

    <update id="updateTblProduct" parameterType="com.ruoyi.monitor2.domain.TblProduct">
        update tbl_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="procName != null">proc_name = #{procName},</if>
            <if test="manuName != null">manu_name = #{manuName},</if>
            <if test="procType != null">proc_type = #{procType},</if>
            <if test="ver != null">ver = #{ver},</if>
            <if test="pubTime != null">pub_time = #{pubTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where prdid = #{prdid}
    </update>

    <delete id="deleteTblProductByPrdid" parameterType="java.lang.String">
        delete from tbl_product where prdid = #{prdid}
    </delete>

    <delete id="deleteTblProductByPrdids" parameterType="java.lang.String">
        delete from tbl_product where prdid in
        <foreach item="prdid" collection="array" open="(" separator="," close=")">
            #{prdid}
        </foreach>
    </delete>
</mapper>
