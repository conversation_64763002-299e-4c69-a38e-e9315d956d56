<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeWebmonitorHijackedMapper">

    <resultMap type="FfsafeWebmonitorHijacked" id="FfsafeWebmonitorHijackedResult">
        <result property="id"    column="id"    />
        <result property="domain"    column="domain"    />
        <result property="realIp"    column="real_ip"    />
        <result property="hijackIp"    column="hijack_ip"    />
        <result property="monitorPoint"    column="monitor_point"    />
        <result property="ttl"    column="ttl"    />
        <result property="systemName"    column="system_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="recentlyTime"    column="recently_time"    />
    </resultMap>

    <sql id="selectFfsafeWebmonitorHijackedVo">
        select id, domain, real_ip, hijack_ip, monitor_point, ttl, system_name, start_time, recently_time from ffsafe_webmonitor_hijacked
    </sql>

    <select id="selectFfsafeWebmonitorHijackedList" parameterType="FfsafeWebmonitorHijacked" resultMap="FfsafeWebmonitorHijackedResult">
        <include refid="selectFfsafeWebmonitorHijackedVo"/>
        <where>
            <if test="domain != null  and domain != ''"> and domain = #{domain}</if>
            <if test="realIp != null  and realIp != ''"> and real_ip = #{realIp}</if>
            <if test="hijackIp != null  and hijackIp != ''"> and hijack_ip = #{hijackIp}</if>
            <if test="monitorPoint != null  and monitorPoint != ''"> and monitor_point = #{monitorPoint}</if>
            <if test="ttl != null  and ttl != ''"> and ttl = #{ttl}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="recentlyTime != null "> and recently_time = #{recentlyTime}</if>
        </where>
    </select>

    <select id="selectFfsafeWebmonitorHijackedById" parameterType="Long" resultMap="FfsafeWebmonitorHijackedResult">
        <include refid="selectFfsafeWebmonitorHijackedVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeWebmonitorHijackedByIds" parameterType="Long" resultMap="FfsafeWebmonitorHijackedResult">
        <include refid="selectFfsafeWebmonitorHijackedVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeWebmonitorHijacked" parameterType="FfsafeWebmonitorHijacked" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_webmonitor_hijacked
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="domain != null">domain,</if>
            <if test="realIp != null">real_ip,</if>
            <if test="hijackIp != null">hijack_ip,</if>
            <if test="monitorPoint != null">monitor_point,</if>
            <if test="ttl != null">ttl,</if>
            <if test="systemName != null">system_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="recentlyTime != null">recently_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="domain != null">#{domain},</if>
            <if test="realIp != null">#{realIp},</if>
            <if test="hijackIp != null">#{hijackIp},</if>
            <if test="monitorPoint != null">#{monitorPoint},</if>
            <if test="ttl != null">#{ttl},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="recentlyTime != null">#{recentlyTime},</if>
        </trim>
    </insert>

    <update id="updateFfsafeWebmonitorHijacked" parameterType="FfsafeWebmonitorHijacked">
        update ffsafe_webmonitor_hijacked
        <trim prefix="SET" suffixOverrides=",">
            <if test="domain != null">domain = #{domain},</if>
            <if test="realIp != null">real_ip = #{realIp},</if>
            <if test="hijackIp != null">hijack_ip = #{hijackIp},</if>
            <if test="monitorPoint != null">monitor_point = #{monitorPoint},</if>
            <if test="ttl != null">ttl = #{ttl},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="recentlyTime != null">recently_time = #{recentlyTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeWebmonitorHijackedById" parameterType="Long">
        delete from ffsafe_webmonitor_hijacked where id = #{id}
    </delete>

    <delete id="deleteFfsafeWebmonitorHijackedByIds" parameterType="String">
        delete from ffsafe_webmonitor_hijacked where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>