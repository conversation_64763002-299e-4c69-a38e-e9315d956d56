package com.ruoyi.threaten.service.impl;

import cn.anmte.aqsoc.threaten.origin.mapper.ThreatenAlarmOriginMapper;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.*;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.ffsafe.component.FFSafeRequestComponent;
import com.ruoyi.monitor2.domain.MonitorBssVulnDeal;
import com.ruoyi.monitor2.domain.MonitorBssWebvulnDeal;
import com.ruoyi.monitor2.enums.TimeRange;
import com.ruoyi.monitor2.util.TimeRangePair;
import com.ruoyi.monitor2.util.TimeRangeUtil;
import com.ruoyi.monitor2.vo.VulnGradeStatisticsVO;
import com.ruoyi.rabbitmq.domain.SyncMessage;
import com.ruoyi.rabbitmq.enums.DataTypeEnum;
import com.ruoyi.rabbitmq.enums.OperationTypeEnum;
import com.ruoyi.rabbitmq.service.IHandleDataSyncSender;
import com.ruoyi.safe.domain.*;
import com.ruoyi.safe.domain.dto.OverviewParams;
import com.ruoyi.safe.mapper.TblAssetOverviewMapper;
import com.ruoyi.safe.mapper.TblServerMapper;
import com.ruoyi.safe.service.ITblBusinessApplicationService;
import com.ruoyi.safe.service.ITblDeductionDetailService;
import com.ruoyi.safe.service.ITblNetworkIpMacService;
import com.ruoyi.safe.service.ITblThreatDeductionStandardService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.threaten.domain.FfsafeHoneypotAttackdetail;
import com.ruoyi.threaten.domain.TblThreatenAlarm;
import com.ruoyi.threaten.domain.TblThreatenInfo;
import com.ruoyi.threaten.domain.TblThreatenTypeSeg;
import com.ruoyi.threaten.mapper.TblThreatenAlarmMapper;
import com.ruoyi.threaten.mapper.TblThreatenInfoMapper;
import com.ruoyi.threaten.service.ITblThreatenAlarmService;
import com.ruoyi.threaten.service.IAttackDirectionService;
import com.ruoyi.threaten.util.GapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getUserId;


/**
 * 威胁情报Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-16
 */
@Service
@Slf4j
public class TblThreatenAlarmServiceImpl implements ITblThreatenAlarmService {
    @Autowired
    private TblThreatenAlarmMapper tblThreatenAlarmMapper;
    @Autowired
    private ITblNetworkIpMacService tblNetworkIpMacService;
    @Autowired
    private IAttackDirectionService attackDirectionService;
    @Autowired
    private TblThreatenInfoMapper tblThreatenInfoMapper;
    @Autowired
    private ITblBusinessApplicationService tblBusinessApplicationService;
    @Autowired
    private Snowflake snowflake;
    @Resource
    private FFSafeRequestComponent ffSafeRequestComponent;

    @Resource
    private TblServerMapper tblServerMapper;
    @Resource
    private ThreatenAlarmOriginMapper threatenAlarmOriginMapper;

    @Resource
    private TblAssetOverviewMapper tblAssetOverviewMapper;
    @Resource
    private IHandleDataSyncSender handleDataSyncSender;
    @Autowired
    private ITblDeductionDetailService tblDeductionDetailService;
    @Autowired
    private ITblThreatDeductionStandardService tblThreatDeductionStandardService;
    @Resource
    private ITblDeviceConfigService deviceConfigService;


    /**
     * 查询威胁情报
     *
     * @param id 威胁情报主键
     * @return 威胁情报
     */
    @Override
    public TblThreatenAlarm selectTblThreatenAlarmById(Long id)
    {
        TblThreatenAlarm threatenAlarm = tblThreatenAlarmMapper.selectTblThreatenAlarmById(id);
        //受害者ip
        String destIp = threatenAlarm.getDestIp();
        if (ObjectUtils.isNotEmpty(destIp)){
            //查询ip和ip对应的网络区域
            TblNetworkIpMac networkIpMac = new TblNetworkIpMac();
            networkIpMac.setIpv4(destIp);
            List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(networkIpMac);
            if (ObjectUtils.isNotEmpty(tblNetworkIpMacs) && tblNetworkIpMacs.size() > 0){
                //赋值告警区域  改为assetId
                threatenAlarm.setDomainAlarm(tblNetworkIpMacs.get(0).getDomainFullName());
            }
        }
        // 关联资产
        if (!Objects.isNull(threatenAlarm.getAssetId())) {
            TblAssetOverview tblAssetOverview = tblAssetOverviewMapper.selectTblAssetOverviewByAssetId(threatenAlarm.getAssetId());
            if (Objects.nonNull(tblAssetOverview)) {
                threatenAlarm.setAssetName(tblAssetOverview.getAssetName());
                threatenAlarm.setAssetType(tblAssetOverview.getAssetType());
            }
        }
        return threatenAlarm;
    }

    /**
     * 批量查询威胁情报
     *
     * @param ids 威胁情报主键集合
     * @return 威胁情报集合
     */
    @Override
    public List<TblThreatenAlarm> selectTblThreatenAlarmByIds(Long[] ids)
    {
        return tblThreatenAlarmMapper.selectTblThreatenAlarmByIds(ids);
    }

    /**
     * 查询威胁情报列表
     *
     * @param tblThreatenAlarm 威胁情报
     * @return 威胁情报
     */
    @Override
    public List<TblThreatenAlarm> selectTblThreatenAlarmList(TblThreatenAlarm tblThreatenAlarm)
    {
        return tblThreatenAlarmMapper.selectTblThreatenAlarmList(tblThreatenAlarm);
    }

    /**
     * 新增威胁情报
     *
     * @param tblThreatenAlarm 威胁情报
     * @return 结果
     */
    @Override
    public int insertTblThreatenAlarm(TblThreatenAlarm tblThreatenAlarm)
    {
        tblThreatenAlarm.setDataId(snowflake.nextIdStr());
        TblThreatenAlarm query=new TblThreatenAlarm();
        query.setThreatenType(tblThreatenAlarm.getThreatenType());
        query.setThreatenName(tblThreatenAlarm.getThreatenName());
        query.setDestIp(tblThreatenAlarm.getDestIp());
        query.setSrcIp(tblThreatenAlarm.getSrcIp());
        query.setDestPort(tblThreatenAlarm.getDestPort());
        query.setDataSource(tblThreatenAlarm.getDataSource());
        List<TblThreatenAlarm> alarmList=tblThreatenAlarmMapper.selectTblThreatenAlarmStateList(query);
        if (CollectionUtils.isNotEmpty(alarmList)) {
            TblThreatenAlarm item=alarmList.get(0);
            if (item.getFlowState()==null){
                item.setFlowState(TblThreatenAlarm.NO_WORK);
            }
            if (!item.getFlowState().equals(TblThreatenAlarm.IS_FINISH_WORK)) {
                tblThreatenAlarm.setId(item.getId());
                Integer alarmNum = item.getAlarmNum() == null ? (2) : (item.getAlarmNum() + 1);
                tblThreatenAlarm.setAlarmNum(alarmNum);
                tblThreatenAlarm.setUpdateTime(DateUtils.getNowDate());
                /* 更新情报库 */
                TblThreatenInfo tblThreatenInfo=new TblThreatenInfo();
                tblThreatenInfo.setAttackIp(tblThreatenAlarm.getSrcIp());
                List<TblThreatenInfo> tblThreatenInfoList= tblThreatenInfoMapper.selectTblThreatenInfoList(tblThreatenInfo);
                if (CollectionUtils.isEmpty(tblThreatenInfoList)){
                    tblThreatenInfo.setAttackPort(String.valueOf(tblThreatenAlarm.getSrcPort()));
                    tblThreatenInfoMapper.insertTblThreatenInfo(tblThreatenInfo);
                } else {
                    tblThreatenInfoList.get(0).setAttackPort(String.valueOf(tblThreatenAlarm.getSrcPort()));
                    tblThreatenInfoMapper.updateTblThreatenInfo(tblThreatenInfoList.get(0));
                }
                // 设置攻击方向
                tblThreatenAlarm.setAttackDirection(attackDirectionService.determineAttackDirection(tblThreatenAlarm.getSrcIp(), tblThreatenAlarm.getDestIp()));
                return tblThreatenAlarmMapper.updateTblThreatenAlarm(tblThreatenAlarm);
            }
        }
            TblThreatenInfo tblThreatenInfo=new TblThreatenInfo();
            tblThreatenInfo.setAttackIp(tblThreatenAlarm.getSrcIp());
            List<TblThreatenInfo> tblThreatenInfoList= tblThreatenInfoMapper.selectTblThreatenInfoList(tblThreatenInfo);
            if (CollectionUtils.isEmpty(tblThreatenInfoList)){
                tblThreatenAlarm.setHitIntelligence(0L);
                tblThreatenInfo.setAttackPort(String.valueOf(tblThreatenAlarm.getSrcPort()));
                tblThreatenInfoMapper.insertTblThreatenInfo(tblThreatenInfo);
            } else {
                tblThreatenAlarm.setHitIntelligence(1L);
                tblThreatenInfoList.get(0).setAttackPort(String.valueOf(tblThreatenAlarm.getSrcPort()));
                tblThreatenInfoMapper.updateTblThreatenInfo(tblThreatenInfoList.get(0));
            }
            tblThreatenAlarm.setCreateTime(DateUtils.getNowDate());
            tblThreatenAlarm.setUpdateTime(DateUtils.getNowDate());
            tblThreatenAlarm.setCreateBy(SecurityUtils.getUsername());
            tblThreatenAlarm.setDataSource(2);
            tblThreatenAlarm.setAlarmNum(1);
            // 设置攻击方向
            tblThreatenAlarm.setAttackDirection(attackDirectionService.determineAttackDirection(tblThreatenAlarm.getSrcIp(), tblThreatenAlarm.getDestIp()));
        int i = tblThreatenAlarmMapper.insertTblThreatenAlarm(tblThreatenAlarm);
        if (i>0 && ((tblThreatenAlarm.getAlarmLevel() != null && tblThreatenAlarm.getAlarmLevel() > 2) || "其他/蜜罐诱捕".equals(tblThreatenAlarm.getThreatenType()))){
            sendDataSyncMessageAsync(tblThreatenAlarm, OperationTypeEnum.INSERT);
        }
        //将该威胁情报纳入至扣分详情规则详情中
        TblDeductionDetail tblDeductionDetail = new TblDeductionDetail();
        tblDeductionDetail.setDeductionDate(tblThreatenAlarm.getCreateTime());
        String attackSeg = tblThreatenAlarmMapper.selectThreatenTypeByThreatenType(tblThreatenAlarm.getThreatenType());
        tblDeductionDetail.setDeductionType(attackSeg);
        tblDeductionDetail.setDeductionLevel(tblThreatenAlarm.getAlarmLevel().toString());
        tblDeductionDetail.setRiskType("外部威胁");
        tblDeductionDetail.setReferenceId(tblThreatenAlarm.getId().toString());
        List<TblThreatDeductionStandard> tblThreatDeductionStandards = tblThreatDeductionStandardService.selectTblThreatDeductionStandardList(new TblThreatDeductionStandard());
        tblDeductionDetailService.scoringRuleProcessing(tblDeductionDetail,tblThreatDeductionStandards);
        tblDeductionDetailService.insertTblDeductionDetail(tblDeductionDetail);
        return i;
    }

    /**
     * 导入威胁情报列表
     *
     * @param tblThreatenAlarmList 威胁情报列表
     * @return 结果
     */
    @Override
    public int insertTblThreatenAlarmList(List<TblThreatenAlarm> tblThreatenAlarmList) {

        int data = 0;
        for(TblThreatenAlarm item:tblThreatenAlarmList){
            TblThreatenAlarm query=new TblThreatenAlarm();
            query.setThreatenType(item.getThreatenType());
            query.setThreatenName(item.getThreatenName());
            query.setDestIp(item.getDestIp());
            query.setSrcIp(item.getSrcIp());
            query.setDestPort(item.getDestPort());
            query.setDataSource(item.getDataSource());
            List<TblThreatenAlarm> alarmList=tblThreatenAlarmMapper.selectTblThreatenAlarmStateList(query);
            if (CollectionUtils.isNotEmpty(alarmList)) {
                    TblThreatenAlarm item1=alarmList.get(0);
                    if (item1.getFlowState()==null){
                        item1.setFlowState(TblThreatenAlarm.NO_WORK);
                    }
                    if (!item1.getFlowState().equals(TblThreatenAlarm.IS_FINISH_WORK)) {
                        item.setId(item1.getId());
                        Integer alarmNum = item1.getAlarmNum() == null ? (2) : (item1.getAlarmNum() + 1);
                        item.setAlarmNum(alarmNum);
                        item.setUpdateTime(DateUtils.getNowDate());
                        /* 更新情报库 */
                        TblThreatenInfo tblThreatenInfo=new TblThreatenInfo();
                        tblThreatenInfo.setAttackIp(item.getSrcIp());
                        List<TblThreatenInfo> tblThreatenInfoList= tblThreatenInfoMapper.selectTblThreatenInfoList(tblThreatenInfo);
                        if (CollectionUtils.isEmpty(tblThreatenInfoList)){
                            tblThreatenInfo.setAttackPort(String.valueOf(item.getSrcPort()));
                            tblThreatenInfoMapper.insertTblThreatenInfo(tblThreatenInfo);
                        } else {
                            tblThreatenInfoList.get(0).setAttackPort(String.valueOf(item.getSrcPort()));
                            tblThreatenInfoMapper.updateTblThreatenInfo(tblThreatenInfoList.get(0));
                        }
                        // 设置攻击方向
                        item.setAttackDirection(attackDirectionService.determineAttackDirection(item.getSrcIp(), item.getDestIp()));
                        data = tblThreatenAlarmMapper.updateTblThreatenAlarm(item);
                        continue;
                    }
            }
                TblThreatenInfo tblThreatenInfo=new TblThreatenInfo();
                tblThreatenInfo.setAttackIp(item.getSrcIp());
                List<TblThreatenInfo> tblThreatenInfoList= tblThreatenInfoMapper.selectTblThreatenInfoList(tblThreatenInfo);
                if (CollectionUtils.isEmpty(tblThreatenInfoList)){
                    item.setHitIntelligence(0L);
                    tblThreatenInfo.setAttackPort(String.valueOf(item.getSrcPort()));
                    tblThreatenInfoMapper.insertTblThreatenInfo(tblThreatenInfo);
                } else {
                    item.setHitIntelligence(1L);
                    tblThreatenInfo.setId(tblThreatenInfoList.get(0).getId());
                    tblThreatenInfo.setAttackPort(String.valueOf(item.getSrcPort()));
                    tblThreatenInfoMapper.updateTblThreatenInfo(tblThreatenInfo);
                }
                item.setCreateTime(DateUtils.getNowDate());
                item.setCreateBy(SecurityUtils.getUsername());
                item.setUpdateTime(DateUtils.getNowDate());
                item.setDataSource(2);
                item.setAlarmNum(1);
                // 设置攻击方向
                item.setAttackDirection(attackDirectionService.determineAttackDirection(item.getSrcIp(), item.getDestIp()));
                data = tblThreatenAlarmMapper.insertTblThreatenAlarm(item);
                if (data>0 && ((item.getAlarmLevel() != null && item.getAlarmLevel() > 2) || "其他/蜜罐诱捕".equals(item.getThreatenType()))){
                    sendDataSyncMessageAsync(item, OperationTypeEnum.INSERT);
                }
        }
        return data;
    }

    /**
     * 修改威胁情报
     *
     * @param tblThreatenAlarm 威胁情报
     * @return 结果
     */
    @Override
    public int updateTblThreatenAlarm(TblThreatenAlarm tblThreatenAlarm)
    {
        /* 判断攻击IP是否被修改（查不到代表是修改为新的一条ip，查到代表未修改或者修改为已经存在的ip，存在和未修改都不进行情报状态更新） */
        TblThreatenAlarm alarm=new TblThreatenAlarm();
        alarm.setThreatenName(tblThreatenAlarm.getThreatenName());
        alarm.setThreatenType(tblThreatenAlarm.getThreatenType());
        alarm.setSrcIp(tblThreatenAlarm.getSrcIp());
        alarm.setDestIp(tblThreatenAlarm.getDestIp());
        alarm.setDestPort(tblThreatenAlarm.getDestPort());
        alarm.setDataSource(tblThreatenAlarm.getDataSource());
        if(StrUtil.isBlank(tblThreatenAlarm.getHandleState())){
            List<TblThreatenAlarm> tblThreatenAlarmList=tblThreatenAlarmMapper.selectTblThreatenAlarmStateList(alarm);
            TblThreatenInfo tblThreatenInfo=new TblThreatenInfo();
            tblThreatenInfo.setAttackIp(tblThreatenAlarm.getSrcIp());
            List<TblThreatenInfo> tblThreatenInfoList= tblThreatenInfoMapper.selectTblThreatenInfoList(tblThreatenInfo);
            if (tblThreatenInfoList==null||tblThreatenInfoList.size()==0){
                if (tblThreatenAlarmList==null||tblThreatenAlarmList.size()==0) {
                    tblThreatenAlarm.setHitIntelligence(0L);
                }
                tblThreatenInfo.setAttackPort(String.valueOf(tblThreatenAlarm.getSrcPort()));
                int i = tblThreatenInfoMapper.insertTblThreatenInfo(tblThreatenInfo);
            } else {
                tblThreatenInfoList.get(0).setAttackPort(String.valueOf(tblThreatenAlarm.getSrcPort()));
                tblThreatenInfoMapper.updateTblThreatenInfo(tblThreatenInfoList.get(0));
                if (tblThreatenAlarmList==null||tblThreatenAlarmList.size()==0) {
                    tblThreatenAlarm.setHitIntelligence(1L);
                }
            }
        }
        tblThreatenAlarm.setUpdateTime(DateUtils.getNowDate());
        // 设置攻击方向
        tblThreatenAlarm.setAttackDirection(attackDirectionService.determineAttackDirection(tblThreatenAlarm.getSrcIp(), tblThreatenAlarm.getDestIp()));
        int i = tblThreatenAlarmMapper.updateTblThreatenAlarm(tblThreatenAlarm);
        if(i > 0){
            CopyOptions copyOptions = new CopyOptions();
            copyOptions.setOverride(false);
            TblThreatenAlarm threatenAlarm = selectTblThreatenAlarmById(tblThreatenAlarm.getId());
            String handleState = tblThreatenAlarm.getHandleState();
            if (StrUtil.isNotBlank(handleState)) {
                // 处置
                TblThreatenAlarm tblThreatenAlarmTemp = new TblThreatenAlarm();
                tblThreatenAlarmTemp.setId(tblThreatenAlarm.getId());
                tblThreatenAlarmTemp.setAlarmLevel(threatenAlarm.getAlarmLevel());
                tblThreatenAlarmTemp.setHandleState(tblThreatenAlarm.getHandleState());
                tblThreatenAlarmTemp.setHandleDesc(tblThreatenAlarm.getHandleDesc());
                tblThreatenAlarmTemp.setDisposer(getUserId().toString());
                tblThreatenAlarmTemp.setUpdateTime(new Date());
                tblThreatenAlarm = tblThreatenAlarmTemp;
            } else {
                BeanUtil.copyProperties(threatenAlarm, tblThreatenAlarm, copyOptions);
            }
            if((tblThreatenAlarm.getAlarmLevel() != null && tblThreatenAlarm.getAlarmLevel() > 2) || "其他/蜜罐诱捕".equals(tblThreatenAlarm.getThreatenType())){
                sendDataSyncMessageAsync(tblThreatenAlarm, OperationTypeEnum.INSERT);
            }
        }
        return i;
    }

    /**
     * 删除威胁情报信息
     *
     * @param id 威胁情报主键
     * @return 结果
     */
    @Override
    public int deleteTblThreatenAlarmById(Long id)
    {
        int i = tblThreatenAlarmMapper.deleteTblThreatenAlarmById(id);
        if(i > 0){
            TblThreatenAlarm delData = new TblThreatenAlarm();
            delData.setId(id);
            sendDataSyncMessageAsync(delData, OperationTypeEnum.DELETE);
        }
        return i;
    }

    /**
     * 批量删除威胁情报
     *
     * @param ids 需要删除的威胁情报主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTblThreatenAlarmByIds(Long[] ids)
    {
        int i = tblThreatenAlarmMapper.deleteTblThreatenAlarmByIds(ids);
        //关联删除threaten_alarm_origin表中的数据
        threatenAlarmOriginMapper.deleteThreatenAlarmOriginByAlarmIds(ids);
        if(i > 0){
            for (Long id : ids) {
                TblThreatenAlarm delData = new TblThreatenAlarm();
                delData.setId(id);
                sendDataSyncMessageAsync(delData, OperationTypeEnum.DELETE);
            }
        }
        return i;
    }

    @Override
    public List<HashMap> getThreatenTypeTop10(TblThreatenAlarm tblThreatenAlarm) {
        tblThreatenAlarm.setTop10(true);
        return tblThreatenAlarmMapper.getThreatenType(tblThreatenAlarm);
    }

    @Override
    public List<HashMap> getThreatenNameTop10(TblThreatenAlarm tblThreatenAlarm) {
        tblThreatenAlarm.setTop10(true);
        return tblThreatenAlarmMapper.getThreatenName(tblThreatenAlarm);
    }

    @Override
    public List<HashMap> getThreatenType(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getThreatenType(tblThreatenAlarm);
    }

    @Override
    public List<VulnGradeStatisticsVO> getThreatenGrade(TblThreatenAlarm tblThreatenAlarm) {
        if (StrUtil.isBlank(tblThreatenAlarm.getTimeRange())) {
            throw new ServiceException("时间范围不能为空");
        }
        // 处理时间范围
        setTimeRange(tblThreatenAlarm);

        // 获取统计数据
        List<HashMap> rawData = tblThreatenAlarmMapper.getThreatenGrade(tblThreatenAlarm);

        // 计算百分比
        calculatePercent(rawData);

        // 转换为VO对象
        return convertToVulnGradeStatisticsVO(rawData);
    }

    private void setTimeRange(TblThreatenAlarm tblThreatenAlarm) {
        // 设置时间范围(必填)
        TimeRange timeRange = Optional.ofNullable(TimeRange.fromCode(tblThreatenAlarm.getTimeRange()))
                .orElseThrow(() -> new ServiceException("无效的时间范围"));
        TimeRangePair timeRangePair = TimeRangeUtil.getTimeRange(
                timeRange.getAmount(),
                timeRange.getUnit()
        );
        tblThreatenAlarm.setStartTime(timeRangePair.getStartDate());
        tblThreatenAlarm.setEndTime(timeRangePair.getEndDate());
    }

    private void calculatePercent(List<HashMap> list) {
        Integer totalNum = list.stream()
                .mapToInt(e -> Math.toIntExact((Long) e.get("value")))
                .sum();

        list.forEach(e -> {
            Long value = (Long) e.get("value");
            double percent = totalNum == 0 ? 0.0 :
                    (value.doubleValue() / totalNum.doubleValue()) * 100;
            e.put("percent", Double.parseDouble(String.format("%.2f", percent)));
        });
    }

    private List<VulnGradeStatisticsVO> convertToVulnGradeStatisticsVO(List<HashMap> rawData) {
        return rawData.stream()
                .map(data -> VulnGradeStatisticsVO.builder()
                        .name((String) data.get("name"))
                        .value((Long) data.get("value"))
                        .percent((Double) data.get("percent"))
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<TblThreatenAlarm> getAttackIpListTop10(TblThreatenAlarm tblThreatenAlarm) {
        tblThreatenAlarm.setTop10(true);
        return tblThreatenAlarmMapper.getAttackIpList(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> getDestIpListTop10(TblThreatenAlarm tblThreatenAlarm) {
        tblThreatenAlarm.setTop10(true);
        return tblThreatenAlarmMapper.getDestIpList(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> getAlertInfoList(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getAlertInfoList(tblThreatenAlarm);
    }

    @Autowired
    private ISysDictDataService dictDataService;

    @Override
    public Map<String, List> getThreatenAnalyze(TblThreatenAlarm tblThreatenAlarm) {
        Map<String, List> result = new HashMap<>();
        SysDictData dictData=new SysDictData();
        dictData.setDictType("threaten_type");
        List<SysDictData> labelList = dictDataService.selectDictDataList(dictData);
        // List<String> dictList = labelList.stream().map(e -> e.getDictLabel()).collect(Collectors.toList());
        List<EchartsSeriesDict> dictList = labelList.stream().map(e -> GapUtil.convertClass(e.getDictLabel())).collect(Collectors.toList());

        int diffDays = DateUtils.differentDaysByMillisecond(tblThreatenAlarm.getStartTime(), tblThreatenAlarm.getEndTime());
        if (diffDays == 0) {
            // 获取数据
            List<Map<String, Object>> dataList = tblThreatenAlarmMapper.getThreatenDayList(tblThreatenAlarm);
            String sDate = DateUtils.format(tblThreatenAlarm.getStartTime(), DateUtils.YYYY_MM_DD);
            String eDate = DateUtils.format(tblThreatenAlarm.getEndTime(), DateUtils.YYYY_MM_DD);
            result = DateUtils.sliceUpDateRangeByDictUtil(sDate, eDate, dictList, dataList);
        } else if (diffDays <= 31) {
            // 获取数据
            List<Map<String, Object>> dataList = tblThreatenAlarmMapper.getThreatenDayList(tblThreatenAlarm);
            String sDate = DateUtils.format(tblThreatenAlarm.getStartTime(), DateUtils.YYYY_MM_DD);
            String eDate = DateUtils.format(tblThreatenAlarm.getEndTime(), DateUtils.YYYY_MM_DD);
            result = DateUtils.sliceUpDateRangeByDictUtil(sDate, eDate, dictList, dataList);
        } else if (diffDays <= 350) {
            // 获取数据
            List<Map<String, Object>> dataList = tblThreatenAlarmMapper.getThreatenMonthList(tblThreatenAlarm);
            String sDate = DateUtils.format(tblThreatenAlarm.getStartTime(), DateUtils.YYYY_MM);
            String eDate = DateUtils.format(tblThreatenAlarm.getEndTime(), DateUtils.YYYY_MM);
            result = DateUtils.sliceUpDateRangeByDictUtil(sDate, eDate, dictList, dataList);
        } else {
            // 获取数据
            List<Map<String, Object>> dataList = tblThreatenAlarmMapper.getThreatenYearList(tblThreatenAlarm);
            String sDate = DateUtils.format(tblThreatenAlarm.getStartTime(), DateUtils.YYYY);
            String eDate = DateUtils.format(tblThreatenAlarm.getEndTime(), DateUtils.YYYY);
            result = DateUtils.sliceUpDateRangeByDictUtil(sDate, eDate, dictList, dataList);
        }
        return result;
    }

    @Override
    public List<HashMap> getThreatenTypeWithPercentage(TblThreatenAlarm tblThreatenAlarm) {
        return calculatePercentage(tblThreatenAlarmMapper.getThreatenType(tblThreatenAlarm));
    }

    @Override
    public int getAttackNum(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getAttackNum(tblThreatenAlarm);
    }

    @Override
    public int getHighRiskHostNum(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getHighRiskHostNum(tblThreatenAlarm);
    }

    @Override
    public int getRiskHostNum(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getRiskHostNum(tblThreatenAlarm);
    }

    @Override
    public int getThreatenAlarmNum(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getThreatenAlarmNum(tblThreatenAlarm);
    }

    @Override
    public Map<Integer, Object> getThreatenLevelNum(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getThreatenLevelNum(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> selectAttackIpList(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectAttackIpList(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> selectVictimIpList(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectVictimIpList(tblThreatenAlarm);
    }

    @Override
    public TblThreatenTypeSeg getAttackSegSummary(TblThreatenAlarm tblThreatenAlarm) {
        TblThreatenTypeSeg tblThreatenTypeSeg = new TblThreatenTypeSeg();

        List<TblThreatenAlarm> tblThreatenAlarmList = tblThreatenAlarmMapper.getAttackSegSummary(tblThreatenAlarm);
        if (tblThreatenAlarmList != null) {
            for (int i = 0; i < tblThreatenAlarmList.size(); i++) {
                TblThreatenAlarm temp = tblThreatenAlarmList.get(i);
                if (StringUtils.isNotBlank(temp.getAttackSeg())) {
                    int count = temp.getAlarmNum();
                    tblThreatenTypeSeg.dataLineRoll(temp.getAttackSeg(), count);
                }
            }
        }
        return tblThreatenTypeSeg;
    }

    @Override
    public TblThreatenTypeSeg getAssetAttackSegSummary(TblThreatenAlarm tblThreatenAlarm) {
        TblThreatenTypeSeg tblThreatenTypeSeg = new TblThreatenTypeSeg();

        List<TblThreatenAlarm> tblThreatenAlarmList = tblThreatenAlarmMapper.getAssetAttackSegSummary(tblThreatenAlarm);
        if (tblThreatenAlarmList != null) {
            for (int i = 0; i < tblThreatenAlarmList.size(); i++) {
                TblThreatenAlarm temp = tblThreatenAlarmList.get(i);
                if (StringUtils.isNotBlank(temp.getAttackSeg())) {
                    int count = temp.getAlarmNum();
                    tblThreatenTypeSeg.dataLineRoll(temp.getAttackSeg(), count);
                }
            }
        }
        return tblThreatenTypeSeg;
    }

    @Override
    public TblThreatenTypeSeg selectIpAttackSegNum(TblThreatenAlarm tblThreatenAlarm) {
        TblThreatenTypeSeg tblThreatenTypeSeg = new TblThreatenTypeSeg();
        List<TblThreatenAlarm> tblThreatenAlarmList = tblThreatenAlarmMapper.selectIpAttackSegNum(tblThreatenAlarm);
        if (tblThreatenAlarmList != null) {
            for (int i = 0; i < tblThreatenAlarmList.size(); i++) {
                TblThreatenAlarm temp = tblThreatenAlarmList.get(i);
                if (StringUtils.isNotBlank(temp.getAttackSeg())) {
                    int count = temp.getAlarmNum();
                    tblThreatenTypeSeg.dataLineRoll(temp.getAttackSeg(), count);
                }
            }
        }
        return tblThreatenTypeSeg;
    }

    @Override
    public List<TblThreatenAlarm> selectAttackIpOverviewList(TblThreatenAlarm tblThreatenAlarm) {
        List<TblThreatenAlarm> tblThreatenAlarms = tblThreatenAlarmMapper.selectAttackIpOverviewList(tblThreatenAlarm);
        if(CollUtil.isNotEmpty(tblThreatenAlarms)){
            //分阶段统计
            tblThreatenAlarms.forEach(alarm -> {
                TblThreatenAlarm query = new TblThreatenAlarm();
                BeanUtil.copyProperties(tblThreatenAlarm,query);
                query.setAttackIp(alarm.getAttackIp());
                TblThreatenTypeSeg threatenTypeSeg = getEventSegTypeList(query);
                alarm.setTypeSeg(threatenTypeSeg);
            });
        }
        return tblThreatenAlarms;
    }

    @Override
    public List<TblThreatenAlarm> selectVictimIpOverviewList(TblThreatenAlarm tblThreatenAlarm) {
        List<TblThreatenAlarm> tblThreatenAlarms = tblThreatenAlarmMapper.selectVictimIpOverviewList(tblThreatenAlarm);
        if(CollUtil.isNotEmpty(tblThreatenAlarms)){
            //分阶段统计
            tblThreatenAlarms.forEach(alarm -> {
                TblThreatenAlarm query = new TblThreatenAlarm();
                BeanUtil.copyProperties(tblThreatenAlarm,query);
                query.setVictimIp(alarm.getVictimIp());
                TblThreatenTypeSeg threatenTypeSeg = getEventSegTypeList(query);
                alarm.setTypeSeg(threatenTypeSeg);
            });
        }
        return tblThreatenAlarms;
    }

    @Override
    public List<TblThreatenAlarm> selectAssetIpOverviewList(TblThreatenAlarm tblThreatenAlarm) {
        List<TblThreatenAlarm> tblThreatenAlarms = tblThreatenAlarmMapper.selectAssetIpOverviewList(tblThreatenAlarm);
        if(CollUtil.isNotEmpty(tblThreatenAlarms)){
            //分阶段统计 数据对不上是因为威胁事件的攻击IP、目的IP可能属于多条，分IP统计时就会出现重复统计的情况,除非不用OR，而是确定用目标IP还是源IP
            tblThreatenAlarms.forEach(alarm -> {
                TblThreatenAlarm query = new TblThreatenAlarm();
                query.setIpOr(alarm.getVictimIp());
                query.setDeptId(tblThreatenAlarm.getDeptId());
                query.setStartTime(tblThreatenAlarm.getStartTime());
                query.setEndTime(tblThreatenAlarm.getEndTime());
                TblThreatenTypeSeg threatenTypeSeg = selectIpAttackSegNum(query);
                alarm.setTypeSeg(threatenTypeSeg);
            });
        }
        return tblThreatenAlarms;
    }

    @Override
    public List<TblThreatenAlarm> selectAttackIpReportList(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectAttackIpReportList(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> selectVictimIpReportList(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectVictimIpReportList(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> selectOutConnectReportList(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectOutConnectReportList(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> selectAttackIpVictimStatReport(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectAttackIpVictimStatReport(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> selectVictimIpAttackStatReport(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectVictimIpAttackStatReport(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> selectTimeAxisList(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectTimeAxisList(tblThreatenAlarm);
    }

    @Override
    public TblThreatenTypeSeg getEventSegTypeList(TblThreatenAlarm tblThreatenAlarm) {
        TblThreatenTypeSeg seg = new TblThreatenTypeSeg();
        List<TblThreatenAlarm> segTypeList = tblThreatenAlarmMapper.getEventSegTypeList(tblThreatenAlarm);
        if (CollectionUtils.isNotEmpty(segTypeList)) {
            segTypeList.forEach(e -> {
                if (StringUtils.isNotBlank(e.getAttackSeg())) {
                    seg.dataLineRoll(e.getAttackSeg(), e.getAlarmNum());
                }
            });
        }
        return seg;
    }

    @Override
    public List<TblThreatenAlarm> getGroupByName(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getGroupByName(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> getGroupByLevel(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getGroupByLevel(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> getGroupByAttack(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getGroupByAttack(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> getGroupByVictim(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getGroupByVictim(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> exportAttackStage(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.exportAttackStage(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> exportSufferStage(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.exportSufferStage(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> selectThreatenByIpList(List<String> ipList) {
        return tblThreatenAlarmMapper.selectThreatenByIpList(ipList);
    }

    @Override
    public List<TblThreatenAlarm> getThreatenAlarmNumByAssetId(Long assetId) {
        //List<JSONObject> assetIpList = tblBusinessApplicationService.selectAssetIpByAssetId(assetId);
        List<JSONObject> assetIpList = tblBusinessApplicationService.selectAssetServerByAssetId(assetId);
        if (CollUtil.isNotEmpty(assetIpList)){
            List<String> ipList = assetIpList.stream().map(item1 -> item1.getString("ipv4")).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(ipList)){
                //根据ipList查询从威胁情报中获取到威胁信息
                return this.selectThreatenByIpList(ipList);
            }
        }
        return null;
    }

    // 计算百分比并添加到每个元素中
    private List<HashMap> calculatePercentage(List<HashMap> allThreatenTypes) {
        if (CollUtil.isEmpty(allThreatenTypes)) {
            return Collections.emptyList();
        }
        // 计算VALUE总和，处理null或无法转换为Number的情况
        BigDecimal totalValue = allThreatenTypes.stream()
                .map(map -> {
                    Object value = map.get("value");
                    if (value == null) {
                        map.put("value", 0L);
                        return BigDecimal.ZERO;
                    }
                    if (value instanceof Number) {
                        return BigDecimal.valueOf(((Number) value).doubleValue());
                    }
                    map.put("value", 0L);
                    return BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 检查totalValue是否为零，避免除以零异常
        if (totalValue.compareTo(BigDecimal.ZERO) == 0) {
            return Collections.emptyList();
        }
        BigDecimal sumPercentage = BigDecimal.ZERO;
        int size = allThreatenTypes.size();
        for (int i = 0; i < size; i++) {
            HashMap<String, Object> map = allThreatenTypes.get(i);
            BigDecimal value = BigDecimal.valueOf(((Number) map.get("value")).doubleValue());
            BigDecimal percentage = value.multiply(BigDecimal.valueOf(100)).divide(totalValue, 4, RoundingMode.HALF_EVEN);
            if (i < size - 1) {
                sumPercentage = sumPercentage.add(percentage);
                map.put("percentage", percentage.setScale(2, RoundingMode.HALF_EVEN).toString() + "%");
            } else {
                // 调整最后一个元素的百分比，以确保总和为100%
                BigDecimal finalPercentage = BigDecimal.valueOf(100).subtract(sumPercentage);
                map.put("percentage", finalPercentage.setScale(2, RoundingMode.HALF_EVEN).toString() + "%");
            }
        }
        return allThreatenTypes;
    }

    @Override
    public List<FfsafeHoneypotAttackdetail> getFfsafeHoneypotAttackdetailList(TblThreatenAlarm tblThreatenAlarm) {
        FfsafeHoneypotAttackdetail query = new FfsafeHoneypotAttackdetail();
        query.setEventName(tblThreatenAlarm.getThreatenName());
        query.setSrcIp(tblThreatenAlarm.getSrcIp());
        query.setDestIp(tblThreatenAlarm.getDestIp());
        query.setDestPort(tblThreatenAlarm.getDestPort());
        return tblThreatenAlarmMapper.selectFfsafeHoneypotAttackdetailList(query);
    }

    @Override
    public JSONObject getThreatTrend(ThreatTrendForm threatTrendForm) {
        JSONObject result = new JSONObject();
        Integer type = threatTrendForm.getType();
        Integer offset = threatTrendForm.getOffset();
        Date startTime = null;
        Date endTime = null;
        if(1 == type){
            //按周
            Date now = DateUtil.date();
            DateTime start = DateUtil.offsetWeek(now, offset);
            DateTime end = null;
            if(threatTrendForm.isContainSelf()){
                end = DateUtil.endOfWeek(now,true);
            }else {
                end = DateUtil.endOfWeek(DateUtil.offsetWeek(now, -1),true);
            }
            startTime = DateUtil.beginOfWeek(start);
            endTime = DateUtil.endOfWeek(end);
            threatTrendForm.setStartDate(startTime);
            threatTrendForm.setEndDate(endTime);
            List<JSONObject> threatenAlarmCounts = tblThreatenAlarmMapper.getThreatTrend(threatTrendForm);
            List<JSONObject> vulnCounts = tblThreatenAlarmMapper.getVulnTrend(threatTrendForm);
            DateTime endOfWeek = DateUtil.endOfWeek(now, true);
            long index = DateUtil.betweenWeek(startTime, endOfWeek, true);
            long endIndex = 0;
            if(threatTrendForm.isContainSelf()){
                endIndex = -1;
            }
            List<Integer> threatenAlarmData = new ArrayList<>();
            List<Integer> vulnData = new ArrayList<>();
            List<String> nameData = new ArrayList<>();
            for (long i = index-1; i >= endIndex; i--) {
                String currentName = "";
                if(threatTrendForm.isContainSelf() && i == 0){
                    currentName = "本周";
                }else if(threatTrendForm.isContainSelf() && i == 1){
                    currentName = "上周";
                }else if (!threatTrendForm.isContainSelf() && i == 0){
                    currentName = "上周";
                }else {
                    currentName = StrUtil.format("上{}周",Convert.numberToChinese((i+1),false));
                }
                nameData.add(currentName);
                DateTime thisDate = DateUtil.offset(endTime, DateField.WEEK_OF_YEAR, Long.valueOf(-i).intValue());
                String currentWeek = StrUtil.format("{}{}",thisDate.year(),thisDate.weekOfYear());
                int sum1 = threatenAlarmCounts.stream().filter(item -> currentWeek.equals(item.getString("dimension"))).mapToInt(item -> item.getIntValue("count")).sum();
                int sum2 = vulnCounts.stream().filter(item -> currentWeek.equals(item.getString("dimension"))).mapToInt(item -> item.getIntValue("count")).sum();
                threatenAlarmData.add(sum1);
                vulnData.add(sum2);
            }

            result.put("threatenAlarmData",threatenAlarmData);
            result.put("vulnData",vulnData);
            result.put("nameData",nameData);
        }
        return result;
    }

	@Override
    public List<JSONObject> groupAlarmLevelStatistics(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.groupAlarmLevelStatistics(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> getAttackIpListTop5(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getAttackIpListTop5(tblThreatenAlarm);
    }

    @Override
    public List<TblThreatenAlarm> getDestIpListTop5(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getDestIpListTop5(tblThreatenAlarm);
    }

    @Override
    public void batchUpdateHandleState(List<TblThreatenAlarm> handleList) {
        tblThreatenAlarmMapper.batchUpdateHandleState(handleList);
    }

    @Override
    public List<TblThreatenAlarm> selectNotSyncList() {
        DateTime nowDate = DateUtil.date();
        DateTime begin = DateUtil.beginOfDay(DateUtil.offsetDay(nowDate, -7)); // 获取一周前的日期
        return tblThreatenAlarmMapper.selectNotSyncList(begin);
    }

    @Override
    public DataTypeEnum getDataType() {
        return DataTypeEnum.BASE_THREATEN_ALARM;
    }

    @Override
    public boolean handle(SyncMessage<Object> message) {
        if(OperationTypeEnum.ADD_BLOCKING.equals(message.getOperationType())){
            JSONObject params = BeanUtil.toBean(message.getData(), JSONObject.class);
            addBlockIp(params);
            return true;
        }
        TblThreatenAlarm threatenAlarm = BeanUtil.toBean(message.getData(), TblThreatenAlarm.class);
        // 服务中台处置
        if (Objects.equals(OperationTypeEnum.HANDLE, message.getOperationType())) {
            threatenAlarm.setDisposer("999999");
            tblThreatenAlarmMapper.updateTblThreatenAlarm(threatenAlarm);
        } else {
            TblThreatenAlarm alarmInDB = selectTblThreatenAlarmById(threatenAlarm.getId());
            if (alarmInDB != null) {
                TblThreatenAlarm update = new TblThreatenAlarm();
                update.setId(threatenAlarm.getId());
                String syncStatus = "1"; //已同步
                update.setSynchronizationStatus(syncStatus);
                tblThreatenAlarmMapper.updateTblThreatenAlarm(update);
            }
        }
        return true;
    }

    @Override
    public AjaxResult addBlockIp(JSONObject params) {
        TblDeviceConfig queryDeviceConfig = new TblDeviceConfig();
        queryDeviceConfig.setStatus(1);
        List<TblDeviceConfig> configList = deviceConfigService.selectTblDeviceConfigList(queryDeviceConfig);
        if(CollUtil.isEmpty(configList)){
            return AjaxResult.error("未找到任何配置设备，请确认已经配置设备并且是启用状态");
        }
        try {
            for (TblDeviceConfig config : configList) {
                FFSafeRequestComponent.deviceConfigThreadLocal.set(config);
                String resStr = ffSafeRequestComponent.sendPostRequest(FFSafeRequestComponent.ADD_BLOCK_IP_URL, params, null);
                if(StrUtil.isNotBlank(resStr) && !"ok".equals(JSON.parseObject(resStr).getString("message"))){
                    return AjaxResult.error(JSON.parseObject(resStr).getString("message"));
                }
            }
        } finally {
            FFSafeRequestComponent.deviceConfigThreadLocal.remove();
        }
        return AjaxResult.success();
    }

    @Override
    public int countThreatenAlarmNum(OverviewParams params) {
        return tblThreatenAlarmMapper.countThreatenAlarmNum(params);
    }


    @Override
    public int selectTblThreatenAlarmListDeputy(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectTblThreatenAlarmListDeputy(tblThreatenAlarm);
    }

    @Override
    public int countAttackIpOverview(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.countAttackIpOverview(tblThreatenAlarm);
    }

    @Override
    public List<JSONObject> selectTop10Alarm(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectTop10Alarm(tblThreatenAlarm);
    }

    @Override
    public List<JSONObject> selectAlarmTypeNum(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectAlarmTypeNum(tblThreatenAlarm);
    }

    @Override
    public List<JSONObject> selectAlarmLevelStatistics(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectAlarmLevelStatistics(tblThreatenAlarm);
    }

    @Override
    public List<JSONObject> selectRecentlyBlockedList(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectRecentlyBlockedList(tblThreatenAlarm);
    }

    @Override
    public List<JSONObject> selectAttackIpOverviewTop10(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.selectAttackIpOverviewTop10(tblThreatenAlarm);
    }

    @Override
    public List<JSONObject> getRankingOfAlarmSystems(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getRankingOfAlarmSystems(tblThreatenAlarm);
    }

    @Override
    public JSONObject getHostOverview(TblThreatenAlarm tblThreatenAlarm) {
        return tblThreatenAlarmMapper.getHostOverview(tblThreatenAlarm);
    }


    @Async
    protected void sendDataSyncMessageAsync(TblThreatenAlarm threatenAlarm, OperationTypeEnum operationType) {
        try {
            SyncMessage<TblThreatenAlarm> message = new SyncMessage<>();
            message.setDataType(DataTypeEnum.BASE_THREATEN_ALARM);
            message.setOperationType(operationType);
            message.setData(threatenAlarm);
            message.setTimestamp(System.currentTimeMillis());
            handleDataSyncSender.sendDataSync(message);
        } catch (Exception e) {
            // 异步发送失败记录日志，不影响主流程
            log.error("发送数据同步消息失败", e);
        }
    }

    @Override
    public List<TblThreatenAlarm> selectListByGreaterThanId(Long threatenAlarmId) {
        return tblThreatenAlarmMapper.selectListByGreaterThanId(threatenAlarmId);
    }
}
