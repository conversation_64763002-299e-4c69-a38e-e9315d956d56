<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblThreatDeductionStandardMapper">

    <resultMap type="TblThreatDeductionStandard" id="TblThreatDeductionStandardResult">
        <result property="id"    column="id"    />
        <result property="contentName"    column="content_name"    />
        <result property="type"    column="type"    />
        <result property="totalScore"    column="total_score"    />
        <result property="itemTotal"    column="item_total"    />
        <result property="deductionPerEvent"    column="deduction_per_event"    />
        <result property="critical"    column="critical"    />
        <result property="high"    column="high"    />
        <result property="mediumHazard"    column="medium_hazard"    />
        <result property="low"    column="low"    />
        <result property="createdTime"    column="created_time"    />
        <result property="modifiedTime"    column="modified_time"    />
    </resultMap>

    <sql id="selectTblThreatDeductionStandardVo">
        select id, content_name, type, total_score, item_total, deduction_per_event, critical, high, medium_hazard, low, created_time, modified_time from tbl_threat_deduction_standard
    </sql>

    <select id="selectTblThreatDeductionStandardList" parameterType="TblThreatDeductionStandard" resultMap="TblThreatDeductionStandardResult">
        <include refid="selectTblThreatDeductionStandardVo"/>
        <where>
            <if test="contentName != null  and contentName != ''"> and content_name like concat('%', #{contentName}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="totalScore != null "> and total_score = #{totalScore}</if>
            <if test="itemTotal != null "> and item_total = #{itemTotal}</if>
            <if test="deductionPerEvent != null "> and deduction_per_event = #{deductionPerEvent}</if>
            <if test="critical != null  and critical != ''"> and critical = #{critical}</if>
            <if test="high != null  and high != ''"> and high = #{high}</if>
            <if test="mediumHazard != null  and mediumHazard != ''"> and medium_hazard = #{mediumHazard}</if>
            <if test="low != null  and low != ''"> and low = #{low}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="modifiedTime != null "> and modified_time = #{modifiedTime}</if>
        </where>
    </select>

    <select id="selectTblThreatDeductionStandardById" parameterType="Long" resultMap="TblThreatDeductionStandardResult">
        <include refid="selectTblThreatDeductionStandardVo"/>
        where id = #{id}
    </select>

    <select id="selectTblThreatDeductionStandardByIds" parameterType="Long" resultMap="TblThreatDeductionStandardResult">
        <include refid="selectTblThreatDeductionStandardVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblThreatDeductionStandard" parameterType="TblThreatDeductionStandard" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_threat_deduction_standard
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contentName != null">content_name,</if>
            <if test="type != null">type,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="itemTotal != null">item_total,</if>
            <if test="deductionPerEvent != null">deduction_per_event,</if>
            <if test="critical != null">critical,</if>
            <if test="high != null">high,</if>
            <if test="mediumHazard != null">medium_hazard,</if>
            <if test="low != null">low,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="modifiedTime != null">modified_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contentName != null">#{contentName},</if>
            <if test="type != null">#{type},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="itemTotal != null">#{itemTotal},</if>
            <if test="deductionPerEvent != null">#{deductionPerEvent},</if>
            <if test="critical != null">#{critical},</if>
            <if test="high != null">#{high},</if>
            <if test="mediumHazard != null">#{mediumHazard},</if>
            <if test="low != null">#{low},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="modifiedTime != null">#{modifiedTime},</if>
         </trim>
    </insert>

    <update id="updateTblThreatDeductionStandard" parameterType="TblThreatDeductionStandard">
        update tbl_threat_deduction_standard
        <trim prefix="SET" suffixOverrides=",">
            <if test="contentName != null">content_name = #{contentName},</if>
            <if test="type != null">type = #{type},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="itemTotal != null">item_total = #{itemTotal},</if>
            <if test="deductionPerEvent != null">deduction_per_event = #{deductionPerEvent},</if>
            <if test="critical != null">critical = #{critical},</if>
            <if test="high != null">high = #{high},</if>
            <if test="mediumHazard != null">medium_hazard = #{mediumHazard},</if>
            <if test="low != null">low = #{low},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="modifiedTime != null">modified_time = #{modifiedTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblThreatDeductionStandardById" parameterType="Long">
        delete from tbl_threat_deduction_standard where id = #{id}
    </delete>

    <delete id="deleteTblThreatDeductionStandardByIds" parameterType="String">
        delete from tbl_threat_deduction_standard where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
