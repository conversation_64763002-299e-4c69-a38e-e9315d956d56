<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblApplicationMapper">

    <resultMap type="TblApplication" id="TblApplicationResult">
        <result property="tid"    column="tid"    />
        <result property="name"    column="name"    />
        <result property="ver"    column="ver"    />
        <result property="type"    column="type"    />
        <result property="func"     column="func" />
        <result property="grade"    column="grade"/>
        <result property="url"    column="url"    />
        <result property="port"    column="port"    />
        <result property="mainfile" column="mainfile"/>
        <result property="pid"    column="pid"    />
        <result property="deptid"    column="deptid"    />
        <result property="manager"    column="manager"    />
        <result property="tele"    column="tele"    />
        <result property="email"    column="email"    />
        <result property="language"    column="language"    />
        <result property="dwid"    column="dwid"    />
        <result property="contactor"    column="contactor"    />
        <result property="phone"    column="phone"    />
        <result property="memo"    column="memo"    />
        <result property="shelf" column="shelf"/>
        <result property="state"    column="state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblApplicationVo">
        select tid, name, ver, type, func, grade, url, port, mainfile, pid, deptid, manager, tele, email, language, dwid, contactor, phone, memo, shelf, state, create_by, create_time, update_time from tbl_application
    </sql>

    <select id="selectTblApplicationList" parameterType="TblApplication" resultMap="TblApplicationResult">
        select a.tid, a.name, a.ver, a.type, a.func, a.grade, a.url, port, a.mainfile, a.pid, a.deptid, a.manager, a.tele, a.email, a.language, a.dwid,
               a.contactor, a.phone, a.memo, a.shelf, a.state, a.create_by, a.create_time, a.update_time
        from tbl_application a left join sys_dept b on a.deptid=b.dept_id
        <where>
            pid ='0'
            <if test="tid != null  and tid != ''"> and a.tid = #{tid}</if>
            <if test="name != null  and name != ''"> and a.name like concat('%', #{name}, '%')</if>
            <if test="ver != null  and ver != ''"> and a.ver = #{ver}</if>
            <if test="type != null  and type != ''"> and a.type = #{type}</if>
            <if test="url != null  and url != ''"> and a.url = #{url}</if>
            <if test="port != null  and port != ''"> and a.port = #{port}</if>
            <if test="pid != null  and pid != ''"> and a.pid = #{pid}</if>
            <if test="deptid != null  and deptid != ''"> and (a.deptid= #{deptid} or find_in_set(#{deptid}, b.ancestors))</if>
            <if test="manager != null  and manager != ''"> and a.manager = #{manager}</if>
            <if test="tele != null  and tele != ''"> and a.tele = #{tele}</if>
            <if test="email != null  and email != ''"> and a.email = #{email}</if>
            <if test="language != null  and language != ''"> and a.language = #{language}</if>
            <if test="dwid != null  and dwid != ''"> and a.dwid = #{dwid}</if>
            <if test="contactor != null  and contactor != ''"> and a.contactor = #{contactor}</if>
            <if test="phone != null  and phone != ''"> and a.phone = #{phone}</if>
            <if test="shelf != null  and shelf != ''"> and a.shelf = #{shelf}</if>
            <if test="state != null  and state != ''"> and a.state = #{state}</if>
        </where>
    </select>

    <select id="selectSubApplicationList" parameterType="String" resultMap="TblApplicationResult">
        select a.tid, a.name, a.ver, a.type, a.func, a.grade, a.url, port, a.mainfile, a.pid, a.deptid, a.manager, a.tele, a.email, a.language, a.dwid,
        a.contactor, a.phone, a.memo, a.shelf, a.state, a.create_by, a.create_time, a.update_time
        from tbl_application a
        where pid in
        <foreach item="tid" collection="array" open="(" separator="," close=")">
            #{tid}
        </foreach>
    </select>

    <select id="selectApplicationByAssets" parameterType="String" resultType="java.util.Map">
        select b.rid, a.tid, a.name, a.ver, a.type, a.func, a.grade, a.url, a.port, a.mainfile, a.pid, a.deptid, a.manager,
               a.tele, a.email, a.language, a.dwid,a.contactor,a.phone ,a.memo ,a.shelf ,a.state
        from tbl_application_asset b
         left join tbl_application a ON a.tid = b.aid
         left join monitor_assets c ON c.tid = b.tid
        where b.TID = #{tid}
    </select>

    <select id="selectTblApplicationByTid" parameterType="String" resultMap="TblApplicationResult">
        <include refid="selectTblApplicationVo"/>
        where tid = #{tid}
    </select>

    <select id="selectTblApplicationByTidWithChild" parameterType="String" resultMap="TblApplicationResult">
        <include refid="selectTblApplicationVo"/>
        where tid = #{tid} or pid= #{tid}
    </select>
    <select id="selectTblApplicationModuleList" resultMap="TblApplicationResult">
        <include refid="selectTblApplicationVo"/>
        where pid!='0'
    </select>

    <select id="selectMaxTid" parameterType="String" resultType="java.lang.String">
        select tid from tbl_application where tid like concat("%", #{prix}, "%")
        order by tid desc limit 1
    </select>


    <insert id="insertTblApplication" parameterType="TblApplication">
        insert into tbl_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tid != null">tid,</if>
            <if test="name != null">name,</if>
            <if test="ver != null">ver,</if>
            <if test="type != null">type,</if>
            <if test="func != null">func,</if>
            <if test="grade != null">grade,</if>
            <if test="url != null">url,</if>
            <if test="port != null">port,</if>
            <if test="mainfile != null">mainfile,</if>
            <if test="pid != null">pid,</if>
            <if test="deptid != null">deptid,</if>
            <if test="manager != null">manager,</if>
            <if test="tele != null">tele,</if>
            <if test="email != null">email,</if>
            <if test="language != null">language,</if>
            <if test="dwid != null">dwid,</if>
            <if test="contactor != null">contactor,</if>
            <if test="phone != null">phone,</if>
            <if test="memo != null">memo,</if>
            <if test="shelf != null">shelf,</if>
            <if test="state != null">state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tid != null">#{tid},</if>
            <if test="name != null">#{name},</if>
            <if test="ver != null">#{ver},</if>
            <if test="type != null">#{type},</if>
            <if test="func != null">#{func},</if>
            <if test="grade != null">#{grade},</if>
            <if test="url != null">#{url},</if>
            <if test="port != null">#{port},</if>
            <if test="mainfile != null">#{mainfile},</if>
            <if test="pid != null">#{pid},</if>
            <if test="deptid != null">#{deptid},</if>
            <if test="manager != null">#{manager},</if>
            <if test="tele != null">#{tele},</if>
            <if test="email != null">#{email},</if>
            <if test="language != null">#{language},</if>
            <if test="dwid != null">#{dwid},</if>
            <if test="contactor != null">#{contactor},</if>
            <if test="phone != null">#{phone},</if>
            <if test="memo != null">#{memo},</if>
            <if test="shelf != null">#{shelf},</if>
            <if test="state != null">#{state},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTblApplication" parameterType="TblApplication">
        update tbl_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="ver != null">ver = #{ver},</if>
            <if test="type != null">type = #{type},</if>
            <if test="func != null">func = #{func},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="url != null">url = #{url},</if>
            <if test="port != null">port = #{port},</if>
            <if test="mainfile !=null">mainfile =#{mainfile},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="deptid != null">deptid = #{deptid},</if>
            <if test="manager != null">manager = #{manager},</if>
            <if test="tele != null">tele = #{tele},</if>
            <if test="email != null">email = #{email},</if>
            <if test="language != null">language = #{language},</if>
            <if test="dwid != null">dwid = #{dwid},</if>
            <if test="contactor != null">contactor = #{contactor},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="shelf != null">shelf = #{shelf},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where tid = #{tid}
    </update>

    <delete id="deleteTblApplicationByTid" parameterType="String">
        delete from tbl_application where tid = #{tid}
    </delete>

    <delete id="deleteTblApplicationByTids" parameterType="String">
        delete from tbl_application where tid in
        <foreach item="tid" collection="array" open="(" separator="," close=")">
            #{tid}
        </foreach>
    </delete>
</mapper>
