<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenWebShellMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenWebShellListVO">
        select a.* from threaten_web_shell a
        <where>
            <if test="query.alarmId!=null">
                and a.id in(select origin_id from threaten_alarm_origin where alarm_id=#{query.alarmId} and threaten_type=#{query.threatenType.name})
            </if>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.businessGroupId!=null">
                and a.business_group_id = #{query.businessGroupId}
            </if>
            <if test="query.businessGroupName!=null and query.businessGroupName!=''">
                and a.business_group_name like concat('%', #{query.businessGroupName}, '%')
            </if>
            <if test="query.domains!=null and query.domains!=''">
                and a.domains like concat('%', #{query.domains}, '%')
            </if>
            <if test="query.eventCreateTime!=null">
                and a.event_create_time = #{query.eventCreateTime}
            </if>
            <if test="query.eventDescription!=null and query.eventDescription!=''">
                and a.event_description like concat('%', #{query.eventDescription}, '%')
            </if>
            <if test="query.eventLevel!=null and query.eventLevel!=''">
                and a.event_level like concat('%', #{query.eventLevel}, '%')
            </if>
            <if test="query.eventName!=null and query.eventName!=''">
                and a.event_name like concat('%', #{query.eventName}, '%')
            </if>
            <if test="query.eventSolution!=null and query.eventSolution!=''">
                and a.event_solution like concat('%', #{query.eventSolution}, '%')
            </if>
            <if test="query.eventState!=null and query.eventState!=''">
                and a.event_state like concat('%', #{query.eventState}, '%')
            </if>
            <if test="query.eventType!=null and query.eventType!=''">
                and a.event_type like concat('%', #{query.eventType}, '%')
            </if>
            <if test="query.eventUpdateTime!=null">
                and a.event_update_time = #{query.eventUpdateTime}
            </if>
            <if test="query.fileAccessTime!=null">
                and a.file_access_time = #{query.fileAccessTime}
            </if>
            <if test="query.fileCreateTime!=null">
                and a.file_create_time = #{query.fileCreateTime}
            </if>
            <if test="query.fileGroup!=null and query.fileGroup!=''">
                and a.file_group like concat('%', #{query.fileGroup}, '%')
            </if>
            <if test="query.fileMd5!=null and query.fileMd5!=''">
                and a.file_md5 like concat('%', #{query.fileMd5}, '%')
            </if>
            <if test="query.fileModifiedTime!=null">
                and a.file_modified_time = #{query.fileModifiedTime}
            </if>
            <if test="query.fileOwner!=null and query.fileOwner!=''">
                and a.file_owner like concat('%', #{query.fileOwner}, '%')
            </if>
            <if test="query.filePath!=null and query.filePath!=''">
                and a.file_path like concat('%', #{query.filePath}, '%')
            </if>
            <if test="query.filePermission!=null and query.filePermission!=''">
                and a.file_permission like concat('%', #{query.filePermission}, '%')
            </if>
            <if test="query.fileSize!=null">
                and a.file_size = #{query.fileSize}
            </if>
            <if test="query.fileType!=null and query.fileType!=''">
                and a.file_type like concat('%', #{query.fileType}, '%')
            </if>
            <if test="query.hostId!=null">
                and a.host_id = #{query.hostId}
            </if>
            <if test="query.hostIp!=null and query.hostIp!=''">
                and a.host_ip like concat('%', #{query.hostIp}, '%')
            </if>
            <if test="query.hostName!=null and query.hostName!=''">
                and a.host_name like concat('%', #{query.hostName}, '%')
            </if>
            <if test="query.startTimeline!=null">
                and a.timeline >= DATE_FORMAT(#{query.startTimeline}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="query.endTimeline!=null">
                and a.timeline &lt; DATE_FORMAT(#{query.endTimeline}, '%Y-%m-%d %H:%i:%s')
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenWebShellListVO">
        select a.* from threaten_web_shell a
        where a.id=#{id}
    </select>
</mapper>
