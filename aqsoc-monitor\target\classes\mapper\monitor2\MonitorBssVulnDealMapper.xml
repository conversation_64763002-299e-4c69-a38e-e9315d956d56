<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.MonitorBssVulnDealMapper">


    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssVulnDeal" id="MonitorBssVulnDealBaseResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="category"    column="category"    />
        <result property="severity"    column="severity"    />
        <result property="hostIp"    column="host_ip"    />
        <result property="assetId"    column="asset_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="hostPort"    column="host_port"    />
        <result property="protocol"    column="protocol"    />
        <result property="scanNum"    column="scan_num"    />
        <result property="dataSource"   column="data_source" />
        <result property="fileUrl"   column="file_url" />
        <result property="dealStatus"    column="deal_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="handleType"    column="handle_type"    />
        <result property="handleState"    column="handle_state"    />
        <result property="handleDesc"    column="handle_desc"    />
        <result property="flowState"    column="flow_state"    />
        <result property="workId" column="workId" />
        <result property="prodId" column="prod_id" />
        <result property="assetName" column="asset_name" />
        <result property="assetType" column="asset_type" />
        <result property="assetTypeDesc" column="asset_type_desc" />
        <result property="deptName" column="dept_name" />
        <result property="workOrderId" column="work_order_id" />
        <result property="disposer" column="disposer" />
        <result property="synchronizationStatus" column="synchronization_status" />
    </resultMap>
    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssVulnDeal" id="MonitorBssVulnDealResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="category"    column="category"    />
        <result property="severity"    column="severity"    />
        <result property="hostIp"    column="host_ip"    />
        <result property="assetId"    column="asset_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="hostPort"    column="host_port"    />
        <result property="protocol"    column="protocol"    />
        <result property="scanNum"    column="scan_num"    />
        <result property="dataSource"   column="data_source" />
        <result property="fileUrl"   column="file_url" />
        <result property="dealStatus"    column="deal_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="handleType"    column="handle_type"    />
        <result property="handleState"    column="handle_state"    />
        <result property="handleDesc"    column="handle_desc"    />
        <result property="flowState"    column="flow_state"    />
        <result property="workId" column="workId" />
        <result property="prodId" column="prod_id" />
        <result property="assetName" column="asset_name" />
        <result property="assetType" column="asset_type" />
        <result property="assetTypeDesc" column="asset_type_desc" />
        <result property="deptName" column="dept_name" />
        <result property="workOrderId" column="work_order_id" />
        <result property="disposer" column="disposer" />
        <result property="synchronizationStatus" column="synchronization_status" />
        <association property="bssVulnInfo" javaType="MonitorBssVulnInfo">
            <result property="title"    column="vi_title"    />
            <result property="poc"    column="vi_poc"    />
            <result property="exp"    column="vi_exp"    />
            <result property="exposures"    column="vi_exposures"    />
            <result property="summary"    column="vi_summary"    />
            <result property="impact"    column="vi_impact"    />
            <result property="solution"    column="vi_solution"    />
            <result property="definiteness"    column="vi_definiteness"    />
            <result property="category"    column="vi_category"    />
            <result property="severity"    column="vi_severity"    />
            <result property="cvss"    column="vi_cvss"    />
            <result property="publishedDateTime"    column="vi_published_date_time"    />
            <result property="detail"    column="vi_detail"    />
        </association>
        <collection property="businessApplications" column="query_asset_id" select="com.ruoyi.safe.mapper.TblBusinessApplicationMapper.selectListByServerId" />
    </resultMap>

    <sql id="selectMonitorBssVulnDealVo">
        select vd.*,(select Max(vs.id) FROM monitor_bss_vuln_result vs WHERE vs.title=vd.title) as vulnId from monitor_bss_vuln_deal vd
    </sql>

    <select id="getMonitorBssVulnDealList" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnDeal" resultMap="MonitorBssVulnDealResult">
        SELECT vd.* FROM monitor_bss_vuln_deal vd LEFT JOIN tbl_work_order wo ON FIND_IN_SET(vd.id, wo.event_ids)
        <where>
            (work_type = '1' or work_type is null) and (flow_state != '4' or flow_state is null)
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="hostIp != null  and hostIp != ''"> and host_ip = #{hostIp}</if>
            <if test="hostPort != null "> and host_port = #{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="dataSource != null "> and data_source = #{dataSource}</if>
        </where>
    </select>

    <select id="findMonitorBssVulnDealList" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnDeal" resultMap="MonitorBssVulnDealResult">
        select id, title, category, severity, host_ip, host_port, handle_state,  data_source, scan_num FROM monitor_bss_vuln_deal
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="hostIp != null  and hostIp != ''"> and host_ip = #{hostIp}</if>
            <if test="hostPort != null "> and host_port = #{hostPort}</if>
            <if test="dataSource != null "> and data_source = #{dataSource}</if>
            and handle_state != 1
        </where>
    </select>

    <select id="selectListCount" resultType="java.lang.Integer">
        select count(*) from (
        SELECT vd.id
        FROM monitor_bss_vuln_deal vd
        <where>
            <if test="(domainId != null  and domainId != '') or (deptId != null) or (applicationId != null)">
                and ( 1=2
                <if test="ipv4List != null and ipv4List.size() > 0">
                    OR vd.host_ip in
                    <foreach collection="ipv4List" item="ipItem" open="(" separator="," close=")">
                        #{ipItem}
                    </foreach>
                </if>
                )
            </if>
            <if test="ids != null and ids.size() > 0">
                and vd.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and vd.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and vd.host_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="title != null  and title != ''">and title like concat('%',#{title},'%')</if>
            <if test="category != null  and category != ''">and category = #{category}</if>
            <if test="severity != null "> and severity = #{severity}</if>
            <if test="hostIp != null  and hostIp != ''">and host_ip=#{hostIp}</if>
            <if test="hostPort != 0 and hostPort != null ">and host_port=#{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="scanNum != null "> and scan_num = #{scanNum}</if>
            <if test="handleStatus == 99 "> and handle_status is null</if>
            <if test="handleStatus != null and handleStatus != 99"> and handle_status = #{handleStatus}</if>
            <if test="handleState != null  and handleState != ''"> and vd.handle_state = #{handleState}</if>
            <if test="handleType != null and handleType != '' "> and handle_type = #{handleType}</if>
            <if test="id != null">and vd.ID = #{id}</if>
            <if test="dealStatus != null "> and deal_status = #{dealStatus}</if>
            <if test="disposers != null and disposers.size() > 0">
                and vd.disposer in
                <foreach collection="disposers" item="disposer" open="(" close=")" separator=",">
                    #{disposer}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''"> and vd.synchronization_status = #{synchronizationStatus}</if>
            <if test="dataSource != null">and vd.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(vd.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="startTime != null">
                and vd.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and vd.create_time &lt;= #{endTime}
            </if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and vd.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderId" open="(" close=")" separator=",">
                    #{workOrderId}
                </foreach>
            </if>
        </where>

                             ) temp
    </select>

    <select id="selectMonitorBssVulnDealList" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnDeal" resultMap="MonitorBssVulnDealResult">
        SELECT vd.id,vd.`title`, vd.`category`, vd.`severity`, vd.`host_ip`, vd.`host_port`, vd.`protocol`, vd.`scan_num`, vd.`data_source`, vd.`file_url`, vd.`deal_status`, vd.`create_by`, vd.`create_time`, vd.`update_by`, vd.`update_time`,vd.disposer, vd.handle_state,vd.handle_desc,vd.work_order_id,vd.synchronization_status
        FROM monitor_bss_vuln_deal vd
        <where>
            vd.severity != 0
            <if test="ipv4List != null and ipv4List.size() > 0">
                AND vd.host_ip in
                <foreach collection="ipv4List" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="ids != null and ids.size() > 0">
                and vd.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and vd.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and vd.host_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="title != null  and title != ''">and title like concat('%',#{title},'%')</if>
            <if test="category != null  and category != ''">and category = #{category}</if>
            <if test="severity != null "> and severity = #{severity}</if>
            <if test="hostIp != null  and hostIp != ''">and host_ip=#{hostIp}</if>
            <if test="hostPort != 0 and hostPort != null ">and host_port=#{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="scanNum != null "> and scan_num = #{scanNum}</if>
            <if test="handleStatus == 99 "> and handle_status is null</if>
            <if test="handleStatus != null and handleStatus != 99"> and handle_status = #{handleStatus}</if>
            <if test="handleState != null  and handleState != ''"> and vd.handle_state = #{handleState}</if>
            <if test="handleType != null and handleType != '' "> and handle_type = #{handleType}</if>
            <if test="id != null">and vd.ID = #{id}</if>
            <if test="id != null">and vd.id = #{id}</if>
            <if test="dealStatus != null "> and deal_status = #{dealStatus}</if>
            <if test="disposers != null and disposers.size() > 0">
                and vd.disposer in
                <foreach collection="disposers" item="disposer" open="(" close=")" separator=",">
                    #{disposer}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''"> and vd.synchronization_status = #{synchronizationStatus}</if>
            <if test="dataSource != null">and vd.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(vd.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="startTime != null">
                and vd.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and vd.create_time &lt;= #{endTime}
            </if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and vd.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderId" open="(" close=")" separator=",">
                    #{workOrderId}
                </foreach>
            </if>
        </where>
        <if test="ids != null and ids.size() > 0">
            order by FIELD(
                     vd.id,
                     <foreach collection="ids" item="idItem" separator=",">
                         #{idItem}
                     </foreach>
            )
        </if>
        <if test="ids == null or ids.size() &lt; 1">
            order by vd.update_time desc
        </if>
    </select>

    <select id="getServerIpGapLinkList" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnDeal" resultMap="MonitorBssVulnDealResult">
        SELECT vd.*,wo.flow_state,wo.id AS workId,wo.prod_id, wh.handle_state
        FROM monitor_bss_vuln_deal vd
            LEFT JOIN tbl_work_order wo ON FIND_IN_SET(vd.id,wo.event_ids) AND wo.work_type = '1'
            left join tbl_work_history wh on wh.work_id = wo.id AND wh.id = (SELECT twh.id FROM tbl_work_history twh WHERE twh.work_id=wo.id ORDER BY twh.create_time DESC LIMIT 1)
            LEFT JOIN tbl_network_ip_mac nim ON vd.host_ip = nim.ipv4
        <where>
            <if test="assetId != null and assetId != ''">
                nim.asset_id = #{assetId}
            </if>
        </where>
        order by update_time desc
    </select>


    <select id="getMonitorBssVulnDealDetail" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnDeal" resultMap="MonitorBssVulnDealResult">
        select vd.id, vd.title, vd.host_ip, vd.host_port, vd.protocol, vd.data_source, vd.create_time, vd.file_url,vd.`category`,vd.`severity`,vd.handle_state,vd.handle_desc,vd.`update_time`,vd.`scan_num`, ao.asset_name as asset_name, ao.asset_type as asset_type, ao.asset_type_desc as asset_type_desc, vi.title as vi_title, vi.poc as vi_poc, vi.category as vi_category, vi.severity as vi_severity, vi.exposures as vi_exposures, vi.cvss as vi_cvss, vi.published_date_time as vi_published_date_time, vi.exp as vi_exp, vi.impact as vi_impact, vi.summary as vi_summary, vi.detail as vi_detail, vi.solution as vi_solution
        from monitor_bss_vuln_deal vd left join
        tbl_network_ip_mac im on vd.host_ip = im.ipv4 left join
        tbl_asset_overview ao on im.asset_id = ao.asset_id left join
        monitor_bss_vuln_info vi on vd.title = vi.title
        <where>
            <if test="id != null  and id != ''"> and vd.id = #{id}</if>
            <if test="title != null  and title != ''"> and vd.title = #{title}</if>
            <if test="hostIp != null  and hostIp != ''"> and vd.host_ip = #{hostIp}</if>
            <if test="hostPort != 0 and hostPort != null "> and vd.host_port = #{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and vd.protocol = #{protocol}</if>
            <if test="dataSource != null "> and vd.data_source = #{dataSource}</if>
        </where>
    </select>

    <select id="selectMonitorBssVulnDealById" parameterType="java.lang.Long" resultMap="MonitorBssVulnDealResult">
        <include refid="selectMonitorBssVulnDealVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorBssVulnDealByIds" parameterType="java.lang.Long" resultMap="MonitorBssVulnDealResult">
        <include refid="selectMonitorBssVulnDealVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMonitorBssVulnDeal" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnDeal" useGeneratedKeys="true" keyProperty="id">
        insert into monitor_bss_vuln_deal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="category != null">category,</if>
            <if test="severity != null">severity,</if>
            <if test="hostIp != null and hostIp != ''">host_ip,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="hostPort != 0 and hostPort != null ">host_port,</if>
            <if test="protocol != null and protocol != ''">protocol,</if>
            <if test="scanNum != null">scan_num,</if>
            <if test="dataSource != null">data_source,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="dealStatus != null">deal_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="disposer != null">disposer,</if>
            <if test="synchronizationStatus != null">synchronization_status,</if>
            <if test="dataId != null">data_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="category != null">#{category},</if>
            <if test="severity != null">#{severity},</if>
            <if test="hostIp != null and hostIp != ''">#{hostIp},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="hostPort != 0 and hostPort != null ">#{hostPort},</if>
            <if test="protocol != null and protocol != ''">#{protocol},</if>
            <if test="scanNum != null">#{scanNum},</if>
            <if test="dataSource != null">#{dataSource},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="dealStatus != null">#{dealStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="disposer != null">#{disposer},</if>
            <if test="synchronizationStatus != null">#{synchronizationStatus},</if>
            <if test="dataId != null">#{dataId},</if>
        </trim>
    </insert>

    <update id="updateMonitorBssVulnDeal" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnDeal">
        update monitor_bss_vuln_deal
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="category != null">category = #{category},</if>
            <if test="severity != null">severity = #{severity},</if>
            <if test="hostIp != null and hostIp != ''">host_ip = #{hostIp},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="hostPort != 0 and hostPort != null ">host_port = #{hostPort},</if>
            <if test="protocol != null and protocol != ''">protocol = #{protocol},</if>
            <if test="scanNum != null">scan_num = #{scanNum},</if>
            <if test="dataSource != null">data_source = #{dataSource},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="dealStatus != null">deal_status = #{dealStatus},</if>
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="handleDesc != null">handle_desc = #{handleDesc},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="disposer != null">disposer = #{disposer},</if>
            <if test="synchronizationStatus != null">synchronization_status = #{synchronizationStatus},</if>
            <if test="handleTime != null"> handle_time = #{handleTime}</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateBatchMonitorBssVulnDeal" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnDeal">
        update monitor_bss_vuln_deal
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="category != null">category = #{category},</if>
            <if test="severity != null">severity = #{severity},</if>
            <if test="hostIp != null and hostIp != ''">host_ip = #{hostIp},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="hostPort != 0 and hostPort != null ">host_port = #{hostPort},</if>
            <if test="protocol != null and protocol != ''">protocol = #{protocol},</if>
            <if test="scanNum != null">scan_num = #{scanNum},</if>
            <if test="dataSource != null">data_source = #{dataSource},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="dealStatus != null">deal_status = #{dealStatus},</if>
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="handleDesc != null">handle_desc = #{handleDesc},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="disposer != null">disposer = #{disposer},</if>
            <if test="synchronizationStatus != null">synchronization_status = #{synchronizationStatus},</if>
        </trim>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdateHandleState">
        UPDATE monitor_bss_vuln_deal SET
        handle_state = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.handleState != null">#{item.handleState}</if> <if test="item.handleState == null">handle_state</if>
        </foreach>
        ELSE handle_state
        END,
        work_order_id = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.workOrderId != null">#{item.workOrderId}</if> <if test="item.workOrderId == null">work_order_id</if>
        </foreach>
        ELSE work_order_id
        END,
        disposer = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.disposer != null">#{item.disposer}</if> <if test="item.disposer == null">disposer</if>
        </foreach>
        ELSE disposer
        END,
        handle_time = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.handleTime != null">#{item.handleTime}</if> <if test="item.handleTime == null">handle_time</if>
        </foreach>
        ELSE handle_time
        END,
        handle_desc = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.handleDesc != null">#{item.handleDesc}</if> <if test="item.handleDesc == null">handle_desc</if>
        </foreach>
        ELSE handle_desc
        END
        where id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <delete id="deleteMonitorBssVulnDealById" parameterType="java.lang.Long">
        delete from monitor_bss_vuln_deal where id = #{id}
    </delete>

    <delete id="deleteMonitorBssVulnDealByIds" parameterType="java.lang.String">
        delete from monitor_bss_vuln_deal where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="count" resultType="java.lang.Integer">
        select count(1)
        from monitor_bss_vuln_deal
        <where>
            <if test="title != null  and title != ''">and title like concat('%',#{title},'%')</if>
            <if test="category != null  and category != ''">and category like concat('%',#{category},'%')</if>
            <if test="severity != null "> and severity = #{severity}</if>
            <if test="hostIp != null  and hostIp != ''">and host_ip=#{hostIp}</if>
            <if test="hostPort != 0 and hostPort != null ">and host_port=#{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="handleState != null  and handleState != ''"> and handle_state = #{handleState}</if>
            <if test="scanNum != null "> and scan_num = #{scanNum}</if>
            <if test="handleType != null and handleType != '' "> and handle_type = #{handleType}</if>
            <if test="id != null">and vd.ID = #{id}</if>
            <if test="startTime != null and endTime != null">
                and vd.create_time between #{startTime} and #{endTime}
            </if>
            <!--<if test="deptId != null "> and (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))</if>-->
        </where>
    </select>
    <select id="getLoopholeType" resultType="java.util.HashMap">
        SELECT
        CASE WHEN sdd.dict_label IS NULL THEN category
        ELSE sdd.dict_label
        END AS NAME,
        count( category ) AS VALUE
        FROM monitor_bss_vuln_deal bvd
        LEFT JOIN sys_dict_data sdd ON sdd.dict_type = 'loophole_category'
        AND bvd.category = sdd.dict_value
        <where>
            <if test="title != null  and title != ''">and title like concat('%',#{title},'%')</if>
            <if test="category != null  and category != ''">and category like concat('%',#{category},'%')</if>
            <if test="severity != null "> and severity = #{severity}</if>
            <if test="hostIp != null  and hostIp != ''">and host_ip=#{hostIp}</if>
            <if test="hostPort != 0 and hostPort != null ">and host_port=#{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="scanNum != null "> and scan_num = #{scanNum}</if>
            <if test="handleType != null and handleType != '' "> and handle_type = #{handleType}</if>
            <if test="id != null">and bvd.ID = #{id}</if>
        </where>
        GROUP BY category
        order by count(category) desc
        <if test="top10">
            limit 10
        </if>
    </select>


    <!--
    <select id="getALLDealGrade" resultType="java.util.HashMap">
        select
            CASE severity WHEN '0' THEN '未知'
                          WHEN '1' THEN '低危'
                          WHEN '2' THEN '中危'
                          WHEN '3' THEN '高危'
                          WHEN '4' THEN '严重'
                END AS `name`,
            count(severity) as value
        FROM
            ( SELECT severity,create_time FROM monitor_bss_vuln_deal UNION ALL SELECT severity,create_time FROM monitor_bss_webvuln_deal ) t WHERE severity in ('0','1', '2', '3', '4')
        group by severity
        order by severity desc
    </select>
    -->

    <select id="getALLDealGrade" resultType="java.util.HashMap">
        SELECT
        CASE t1.severity
            WHEN '0' THEN '未知'
            WHEN '1' THEN '低危'
            WHEN '2' THEN '中危'
            WHEN '3' THEN '高危'
            WHEN '4' THEN '严重'
            END AS `name`,
        IFNULL(t2.value, 0) as value
        FROM (
        SELECT '0' as severity UNION ALL
        SELECT '1' UNION ALL
        SELECT '2' UNION ALL
        SELECT '3' UNION ALL
        SELECT '4'
        ) t1
        LEFT JOIN (
        SELECT severity, count(severity) as value
        FROM (
        SELECT severity,create_time FROM monitor_bss_vuln_deal
        UNION ALL
        SELECT severity,create_time FROM monitor_bss_webvuln_deal
        ) t
        WHERE t.severity in ('0','1','2','3','4')
        <if test="startTime != null and endTime != null">
            AND t.create_time  BETWEEN #{startTime} AND #{endTime}
        </if>
        GROUP BY t.severity
        ) t2 ON t1.severity = t2.severity
        ORDER BY t1.severity DESC
    </select>

    <select id="getAllLoopholeType" resultType="java.util.HashMap">
        SELECT
            CASE WHEN sdd.dict_label IS NULL THEN
                     category
                 ELSE sdd.dict_label
                END AS NAME,
            count( category ) AS VALUE
        FROM
            ( SELECT category FROM monitor_bss_vuln_deal UNION ALL SELECT category FROM monitor_bss_webvuln_deal ) t
            LEFT JOIN sys_dict_data sdd ON sdd.dict_type = 'loophole_category' AND t.category = sdd.dict_value
        GROUP BY category
    </select>

    <select id="getIpWebInfoList" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnDeal" resultType="com.ruoyi.monitor2.domain.MonitorBssVulnDeal">
        SELECT
            title, category, severity, update_time as updateTime
        FROM
            ( SELECT title, category, severity, update_time FROM monitor_bss_vuln_deal
              UNION ALL
              SELECT title, category, severity, update_time FROM monitor_bss_webvuln_deal ) m
        <where>
            <if test="startTime != null and endTime != null">
                update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="showSeverities != null and showSeverities.size() != 0">
                AND m.severity IN
                <foreach collection="showSeverities"
                         item="severity" index="index" separator="," open="(" close=")">
                    #{severity}
                </foreach>
            </if>
        </where>
        <if test="params.orderBy != null">
            ORDER BY ${params.orderBy}
        </if>
        <if test="params.sort != null">
            ${params.sort}
        </if>
    </select>

    <select id="getIpWebDayList" resultType="java.util.Map">
        SELECT
            DATE_FORMAT( updateTime, '%Y-%m-%d' ) AS dateStr,
            CASE
                severity
                WHEN '0' THEN
                    '未知'
                WHEN '1' THEN
                    '低危'
                WHEN '2' THEN
                    '中危'
                WHEN '3' THEN
                    '高危'
                WHEN '4' THEN
                    '严重'
                END AS level,
            count( 0 ) AS countNum
        FROM
            ( SELECT severity, update_time AS updateTime FROM monitor_bss_vuln_deal UNION ALL SELECT severity, update_time AS updateTime FROM monitor_bss_webvuln_deal ) m
        <where>
            <if test="startTime != null and endTime != null">
                updateTime BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
            DATE_FORMAT( updateTime, '%Y-%m-%d' ), severity
    </select>

    <select id="getIpWebMonthList" resultType="java.util.Map">
        SELECT
            DATE_FORMAT( updateTime, '%Y-%m' ) AS dateStr,
            CASE
                severity
                WHEN '0' THEN
                '未知'
                WHEN '1' THEN
                '低危'
                WHEN '2' THEN
                '中危'
                WHEN '3' THEN
                '高危'
                WHEN '4' THEN
                '严重'
            END AS level,
            count( 0 ) AS countNum
        FROM
        ( SELECT severity, update_time AS updateTime FROM monitor_bss_vuln_deal UNION ALL SELECT severity, update_time AS updateTime FROM monitor_bss_webvuln_deal ) m
        <where>
            <if test="startTime != null and endTime != null">
                updateTime BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
        DATE_FORMAT( updateTime, '%Y-%m' ), severity
    </select>

    <select id="getIpWebYearList" resultType="java.util.Map">
        SELECT
            DATE_FORMAT( updateTime, '%Y' ) AS dateStr,
            CASE
                severity
                WHEN '0' THEN
                '未知'
                WHEN '1' THEN
                '低危'
                WHEN '2' THEN
                '中危'
                WHEN '3' THEN
                '高危'
                WHEN '4' THEN
                '严重'
            END AS level,
        count( 0 ) AS countNum
        FROM
        ( SELECT severity, update_time AS updateTime FROM monitor_bss_vuln_deal UNION ALL SELECT severity, update_time AS updateTime FROM monitor_bss_webvuln_deal ) m
        <where>
            <if test="startTime != null and endTime != null">
                updateTime BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
        DATE_FORMAT( updateTime, '%Y' ), severity
    </select>

    <select id="getLeakTypeCount" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            SUM(IF(severity = 1,1,0)) `none`,
            SUM(IF(severity = 2,1,0)) `low`,
            SUM(IF(severity = 3,1,0)) `medium`,
            SUM(IF(severity = 4,1,0)) `high`
        FROM
            monitor_bss_vuln_deal
        WHERE
            host_ip = #{ip}
    </select>

    <select id="getLeakTypeCountByIps" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        SUM(
        IF
        ( severity = 1, 1, 0 )) `none`,
        SUM(
        IF
        ( severity = 2, 1, 0 )) `low`,
        SUM(
        IF
        ( severity = 3, 1, 0 )) `medium`,
        SUM(
        IF
        ( severity = 4, 1, 0 )) `high` ,
        host_ip ip
        FROM
        monitor_bss_vuln_deal
        WHERE
        host_ip IN
        <foreach collection="ipList" item="ip" index="index" separator="," open="(" close=")">
            #{ip}
        </foreach>
        GROUP BY
        host_ip
    </select>

    <select id="getRickLevelStat" resultType="java.util.HashMap">
        select d.severity, count(vd.id) as num from (
            select 0 as severity union all
            select 1 union all
            select 2 union all
            select 3 union all
            select 4
        ) d left join monitor_bss_vuln_deal vd on d.severity = vd.severity
        <where>
            <if test="startTime != null and endTime != null">
               and vd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="ipv4List != null and ipv4List.size() > 0">
                AND vd.host_ip in
                <foreach collection="ipv4List" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
        </where>
        group by d.severity
        order by d.severity desc
    </select>

    <select id="getHandleStateStat" resultType="java.util.HashMap">
        select d.handle_state, count(vd.id) as num from (
            select 0 as handle_state union all
            select 1 union all
            select 2 union all
            select 3
        ) d left join monitor_bss_vuln_deal vd on d.handle_state = vd.handle_state
        where
            vd.severity != 0
        group by d.handle_state
        order by d.handle_state desc
    </select>

    <select id="getHostVulnTypeList" resultType="java.util.HashMap">
        select distinct category from monitor_bss_vuln_deal
        group by category
    </select>
    <select id="selectNoSyncList" resultType="com.ruoyi.monitor2.domain.MonitorBssVulnDeal" resultMap="MonitorBssVulnDealBaseResult">
        select * from monitor_bss_vuln_deal where (synchronization_status = '0' OR synchronization_status is null)
        and severity > 1
    </select>

    <select id="countVulnDealNum" resultType="java.lang.Integer">
        SELECT
        count( t1.id )
        FROM
        monitor_bss_vuln_deal t1
        <if test="deptId != 100">
            right JOIN (
            SELECT
            im.asset_id,
            im.ipv4,
            GROUP_CONCAT( sd.dept_name ) dept_name,
            sd.dept_id,
            t2.asset_name,
            t2.asset_type_desc,
            t2.asset_class_desc
            FROM
            tbl_network_ip_mac im
            LEFT JOIN tbl_asset_overview t2 ON t2.asset_id = im.asset_id
            LEFT JOIN sys_dept sd ON sd.dept_id = t2.dept_id
            WHERE
            (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
            GROUP BY
            im.asset_id
            ) t2 ON t1.host_ip = t2.ipv4
        </if>
        <where>
            <if test="startTime != null and endTime != null">
                and t1.update_time between #{startTime} and #{endTime}
            </if>
        </where>
    </select>

    <select id="getInnerRiskHeaderData" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            COUNT(1) hostRiskNum,
            sum(IF(vd.handle_state = 1,1,0)) disposedOfNum
        FROM
            monitor_bss_vuln_deal vd
        <where>
            vd.severity NOT IN ('0')
            <if test="startTime != null and endTime != null">
                and vd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="(domainId != null  and domainId != '') or (deptId != null) or (applicationId != null)">
                and ( 1=2
                <if test="ipv4List != null and ipv4List.size() > 0">
                    OR vd.host_ip in
                    <foreach collection="ipv4List" item="ipItem" open="(" separator="," close=")">
                        #{ipItem}
                    </foreach>
                </if>
                )
            </if>
            <if test="ids != null and ids.size() > 0">
                and vd.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and vd.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and vd.host_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="title != null  and title != ''">and title like concat('%',#{title},'%')</if>
            <if test="category != null  and category != ''">and category = #{category}</if>
            <if test="severity != null "> and severity = #{severity}</if>
            <if test="hostIp != null  and hostIp != ''">and host_ip=#{hostIp}</if>
            <if test="hostPort != 0 and hostPort != null ">and host_port=#{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="scanNum != null "> and scan_num = #{scanNum}</if>
            <if test="handleStatus == 99 "> and handle_status is null</if>
            <if test="handleStatus != null and handleStatus != 99"> and handle_status = #{handleStatus}</if>
            <if test="handleState != null  and handleState != ''"> and vd.handle_state = #{handleState}</if>
            <if test="handleType != null and handleType != '' "> and handle_type = #{handleType}</if>
            <if test="id != null">and vd.ID = #{id}</if>
            <if test="dealStatus != null "> and deal_status = #{dealStatus}</if>
            <if test="disposers != null and disposers.size() > 0">
                and vd.disposer in
                <foreach collection="disposers" item="disposer" open="(" close=")" separator=",">
                    #{disposer}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''"> and vd.synchronization_status = #{synchronizationStatus}</if>
            <if test="dataSource != null">and vd.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(vd.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and vd.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderId" open="(" close=")" separator=",">
                    #{workOrderId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="webVulnerabilityDataAggregation" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            COUNT(DISTINCT wvd.id) webVulnerabilitiesNum,
            SUM(IF(wvd.handle_state = 1,1,0)) webDisposalNum
        FROM
            monitor_bss_webvuln_deal wvd
                LEFT JOIN tbl_work_order wo ON FIND_IN_SET( wvd.id, wo.event_ids )
                AND wo.work_type = '2'
                LEFT JOIN tbl_business_application tba ON wvd.web_url = tba.url
                LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = tba.domain_id
                LEFT JOIN sys_dept sd ON sd.dept_id = tba.dept_id
        <where>
            wvd.severity NOT IN ( 0 )
            <if test="startTime != null and endTime != null">
                AND wvd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null and deptId != 100">
                AND (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
            </if>
        </where>
    </select>

    <select id="getweakIPPasswordsDataAggregation" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            COUNT(DISTINCT wpd.id) weakIPPasswordsNum,
            SUM(IF(wpd.handle_state = 1,1,0)) weakIPDisposalNum
        FROM
            monitor_bss_wp_deal wpd
                LEFT JOIN tbl_network_ip_mac nim ON nim.ipv4 = wpd.host_ip
                LEFT JOIN tbl_asset_overview tao ON tao.asset_id = nim.asset_id
                LEFT JOIN sys_dept sd ON sd.dept_id = tao.dept_id
        <where>
            <if test="deptId != null and deptId != 100"> and (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))</if>
            <if test="startTime != null and endTime != null">
                and wpd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY
            wpd.update_time DESC
    </select>

    <select id="getTheTypeOfVulnerabilityOfTheHost" resultType="com.alibaba.fastjson2.JSONObject">
        select CASE
        WHEN d.severity =1 THEN '低危'
        WHEN d.severity =2 THEN '中危'
        WHEN d.severity =3 THEN '高危'
        WHEN d.severity =4 THEN '严重'
        ELSE '未知'
        END AS severity, count(vd.id) as num from (
        select 1 as severity union all
        select 2 union all
        select 3 union all
        select 4
        ) d left join monitor_bss_vuln_deal vd on d.severity = vd.severity
        <where>
            <if test="startTime != null and endTime != null">
                and vd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="(domainId != null  and domainId != '') or (deptId != null) or (applicationId != null)">
                and ( 1=2
                <if test="ipv4List != null and ipv4List.size() > 0">
                    OR vd.host_ip in
                    <foreach collection="ipv4List" item="ipItem" open="(" separator="," close=")">
                        #{ipItem}
                    </foreach>
                </if>
                )
            </if>
            <if test="ids != null and ids.size() > 0">
                and vd.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and vd.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and vd.host_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="title != null  and title != ''">and title like concat('%',#{title},'%')</if>
            <if test="category != null  and category != ''">and category = #{category}</if>
            <if test="severity != null "> and severity = #{severity}</if>
            <if test="hostIp != null  and hostIp != ''">and host_ip=#{hostIp}</if>
            <if test="hostPort != 0 and hostPort != null ">and host_port=#{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="scanNum != null "> and scan_num = #{scanNum}</if>
            <if test="handleStatus == 99 "> and handle_status is null</if>
            <if test="handleStatus != null and handleStatus != 99"> and handle_status = #{handleStatus}</if>
            <if test="handleState != null  and handleState != ''"> and vd.handle_state = #{handleState}</if>
            <if test="handleType != null and handleType != '' "> and handle_type = #{handleType}</if>
            <if test="id != null">and vd.ID = #{id}</if>
            <if test="dealStatus != null "> and deal_status = #{dealStatus}</if>
            <if test="disposers != null and disposers.size() > 0">
                and vd.disposer in
                <foreach collection="disposers" item="disposer" open="(" close=")" separator=",">
                    #{disposer}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''"> and vd.synchronization_status = #{synchronizationStatus}</if>
            <if test="dataSource != null">and vd.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(vd.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and vd.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderId" open="(" close=")" separator=",">
                    #{workOrderId}
                </foreach>
            </if>
        </where>
        <if test="ids != null and ids.size() > 0">
            order by FIELD(
            vd.id,
            <foreach collection="ids" item="idItem" separator=",">
                #{idItem}
            </foreach>
            )
        </if>
        group by d.severity
        order by d.severity desc
    </select>

    <select id="getHostVulnerabilityDataAggregation" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            COUNT(1) total,
            SUM(IF(vd.handle_state != 1,1,0)) toBeDisposedOfNum,
            SUM(IF(vd.handle_state = 1,1,0)) disposalNum,
            SUM(IF(vd.handle_state = 1,TIMESTAMPDIFF(HOUR, vd.create_time, vd.handle_time),0)) AS totalHours
        FROM
            monitor_bss_vuln_deal vd
        <where>
            <if test="(domainId != null  and domainId != '') or (deptId != null) or (applicationId != null)">
                and ( 1=2
                <if test="ipv4List != null and ipv4List.size() > 0">
                    OR vd.host_ip in
                    <foreach collection="ipv4List" item="ipItem" open="(" separator="," close=")">
                        #{ipItem}
                    </foreach>
                </if>
                )
            </if>
            <if test="ids != null and ids.size() > 0">
                and vd.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and vd.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and vd.host_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="startTime != null and endTime != null">
                and vd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="title != null  and title != ''">and title like concat('%',#{title},'%')</if>
            <if test="category != null  and category != ''">and category = #{category}</if>
            <if test="severity != null "> and severity = #{severity}</if>
            <if test="hostIp != null  and hostIp != ''">and host_ip=#{hostIp}</if>
            <if test="hostPort != 0 and hostPort != null ">and host_port=#{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="scanNum != null "> and scan_num = #{scanNum}</if>
            <if test="handleStatus == 99 "> and handle_status is null</if>
            <if test="handleStatus != null and handleStatus != 99"> and handle_status = #{handleStatus}</if>
            <if test="handleState != null  and handleState != ''"> and vd.handle_state = #{handleState}</if>
            <if test="handleType != null and handleType != '' "> and handle_type = #{handleType}</if>
            <if test="id != null">and vd.ID = #{id}</if>
            <if test="dealStatus != null "> and deal_status = #{dealStatus}</if>
            <if test="disposers != null and disposers.size() > 0">
                and vd.disposer in
                <foreach collection="disposers" item="disposer" open="(" close=")" separator=",">
                    #{disposer}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''"> and vd.synchronization_status = #{synchronizationStatus}</if>
            <if test="dataSource != null">and vd.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(vd.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and vd.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderId" open="(" close=")" separator=",">
                    #{workOrderId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getWebVulnerabilityDataAggregation" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            COUNT(DISTINCT wvd.id) total,
            SUM(IF(wvd.handle_state != 1,1,0)) toBeDisposedOfNum,
            SUM(IF(wvd.handle_state = 1,1,0)) disposalNum,
            SUM(IF(wvd.handle_state = 1,TIMESTAMPDIFF(HOUR, wvd.create_time, wvd.handle_time),0)) AS totalHours
        FROM
            monitor_bss_webvuln_deal wvd
                LEFT JOIN tbl_work_order wo ON FIND_IN_SET( wvd.id, wo.event_ids )
                AND wo.work_type = '2'
                LEFT JOIN tbl_business_application tba ON wvd.web_url = tba.url
                LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = tba.domain_id
                LEFT JOIN sys_dept sd ON sd.dept_id = tba.dept_id
        <where>
            wvd.severity not in (0)
            <if test="deptId != null and deptId != 100">and (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))</if>
            <if test="startTime != null and endTime != null">
                and wvd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>

    <select id="getWeakIpPasswordsDataAggregation" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            COUNT(DISTINCT wpd.id) total,
            SUM(IF(wpd.handle_state != 1,1,0)) toBeDisposedOfNum,
            SUM(IF(wpd.handle_state = 1,1,0)) disposalNum,
            SUM(IF(wpd.handle_state = 1,TIMESTAMPDIFF(HOUR, wpd.create_time, wpd.handle_time),0)) AS totalHours
        FROM
            monitor_bss_wp_deal wpd
                LEFT JOIN tbl_network_ip_mac nim ON nim.ipv4 = wpd.host_ip
                LEFT JOIN tbl_asset_overview tao ON tao.asset_id = nim.asset_id
                LEFT JOIN sys_dept sd ON sd.dept_id = tao.dept_id
        <where>
            <if test="deptId != null and deptId != 100"> and (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))</if>
            <if test="startTime != null and endTime != null">
                and wpd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>

    <select id="getInnerRiskTypeData" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        vd.category,
        COUNT(distinct vd.id) num
        FROM
        monitor_bss_vuln_deal vd
        <where>
            <if test="(domainId != null  and domainId != '') or (deptId != null) or (applicationId != null)">
                and ( 1=2
                <if test="ipv4List != null and ipv4List.size() > 0">
                    OR vd.host_ip in
                    <foreach collection="ipv4List" item="ipItem" open="(" separator="," close=")">
                        #{ipItem}
                    </foreach>
                </if>
                )
            </if>
            <if test="ids != null and ids.size() > 0">
                and vd.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and vd.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and vd.host_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="title != null  and title != ''">and title like concat('%',#{title},'%')</if>
            <if test="category != null  and category != ''">and category = #{category}</if>
            <if test="severity != null "> and severity = #{severity}</if>
            <if test="hostIp != null  and hostIp != ''">and host_ip=#{hostIp}</if>
            <if test="hostPort != 0 and hostPort != null ">and host_port=#{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="scanNum != null "> and scan_num = #{scanNum}</if>
            <if test="handleStatus == 99 "> and handle_status is null</if>
            <if test="handleStatus != null and handleStatus != 99"> and handle_status = #{handleStatus}</if>
            <if test="handleState != null  and handleState != ''"> and vd.handle_state = #{handleState}</if>
            <if test="handleType != null and handleType != '' "> and handle_type = #{handleType}</if>
            <if test="id != null">and vd.ID = #{id}</if>
            <if test="dealStatus != null "> and deal_status = #{dealStatus}</if>
            <if test="disposers != null and disposers.size() > 0">
                and vd.disposer in
                <foreach collection="disposers" item="disposer" open="(" close=")" separator=",">
                    #{disposer}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''"> and vd.synchronization_status = #{synchronizationStatus}</if>
            <if test="dataSource != null">and vd.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(vd.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and vd.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderId" open="(" close=")" separator=",">
                    #{workOrderId}
                </foreach>
            </if>
            <if test="startTime != null and endTime != null">
                and vd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
        vd.category
        ORDER BY
        COUNT(distinct vd.id) DESC
        limit 5
    </select>

    <select id="getInnerRiskRankingData" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        vd.title,
        COUNT(distinct vd.id) num
        FROM
        monitor_bss_vuln_deal vd
        <where>
            <if test="(domainId != null  and domainId != '') or (deptId != null) or (applicationId != null)">
                and ( 1=2
                <if test="ipv4List != null and ipv4List.size() > 0">
                    OR vd.host_ip in
                    <foreach collection="ipv4List" item="ipItem" open="(" separator="," close=")">
                        #{ipItem}
                    </foreach>
                </if>
                )
            </if>
            <if test="ids != null and ids.size() > 0">
                and vd.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and vd.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and vd.host_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="title != null  and title != ''">and title like concat('%',#{title},'%')</if>
            <if test="category != null  and category != ''">and category = #{category}</if>
            <if test="severity != null "> and severity = #{severity}</if>
            <if test="hostIp != null  and hostIp != ''">and host_ip=#{hostIp}</if>
            <if test="hostPort != 0 and hostPort != null ">and host_port=#{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="scanNum != null "> and scan_num = #{scanNum}</if>
            <if test="handleStatus == 99 "> and handle_status is null</if>
            <if test="handleStatus != null and handleStatus != 99"> and handle_status = #{handleStatus}</if>
            <if test="handleState != null  and handleState != ''"> and vd.handle_state = #{handleState}</if>
            <if test="handleType != null and handleType != '' "> and handle_type = #{handleType}</if>
            <if test="id != null">and vd.ID = #{id}</if>
            <if test="dealStatus != null "> and deal_status = #{dealStatus}</if>
            <if test="disposers != null and disposers.size() > 0">
                and vd.disposer in
                <foreach collection="disposers" item="disposer" open="(" close=")" separator=",">
                    #{disposer}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''"> and vd.synchronization_status = #{synchronizationStatus}</if>
            <if test="dataSource != null">and vd.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(vd.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and vd.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderId" open="(" close=")" separator=",">
                    #{workOrderId}
                </foreach>
            </if>
            <if test="startTime != null and endTime != null">
                and vd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
        vd.title
        ORDER BY
        COUNT(distinct vd.id) DESC
        LIMIT 5
    </select>

    <select id="getRiskSystemRanking" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        t1.asset_name AS assetName,
        t4.webVulnerabilitiesNum,
        t2.ipLiakNum,
        t3.ipWeakPasswordsNum,
        COALESCE ( t4.webVulnerabilitiesNum, 0 ) + COALESCE ( t2.ipLiakNum, 0 ) + COALESCE ( t3.ipWeakPasswordsNum, 0 ) AS total_risk
        FROM
        (-- 查询业务系统web漏洞
        SELECT
        a.asset_id,
        a.asset_name,
        a.url
        FROM
        tbl_business_application a
        LEFT JOIN sys_user m ON a.manager = m.user_id
        LEFT JOIN sys_user b ON a.user_id = b.user_id
        LEFT JOIN sys_dept c ON a.dept_id = c.dept_id
        LEFT JOIN tbl_vendor d ON a.vendor = d.id
        LEFT JOIN tbl_asset_overview e ON a.asset_id = e.asset_id
        <where>
            <if test="deptId != null and deptId != 100">
                and (c.dept_id=#{deptId} OR find_in_set(#{deptId},c.ancestors))
            </if>
        </where>
        GROUP BY
        a.asset_id
        ) t1
        LEFT JOIN(
        SELECT
        a1.asset_id,
        COUNT( a2.id ) AS webVulnerabilitiesNum
        FROM
        tbl_business_application a1 LEFT JOIN monitor_bss_webvuln_deal a2 ON a2.asset_id = a1.asset_id
        <where>
            <if test="startTime != null and endTime != null">
                and a2.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
        a1.asset_id
        ) t4 ON t4.asset_id = t1.asset_id
        LEFT JOIN (
        -- 查询ip漏洞
        SELECT
        tdf.asset_id,
        COUNT( mbv.id ) AS ipLiakNum
        FROM
        (
        SELECT
        tas.asset_id,
        tas.server_id,
        GROUP_CONCAT( nim.ipv4 SEPARATOR ', ' ) AS ipv4_list
        FROM
        tbl_application_server tas
        LEFT JOIN tbl_network_ip_mac nim ON nim.asset_id = tas.server_id
        WHERE
        tas.asset_id IS NOT NULL
        GROUP BY
        tas.asset_id
        ) tdf
        LEFT JOIN monitor_bss_vuln_deal mbv ON FIND_IN_SET( mbv.host_ip, tdf.ipv4_list )
        <where>
            <if test="startTime != null and endTime != null">
                and mbv.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
        tdf.asset_id
        ) t2 ON t1.asset_id = t2.asset_id
        LEFT JOIN(
        -- ip弱口令
        SELECT
        tdf.asset_id,
        COUNT( bwd.id ) AS ipWeakPasswordsNum
        FROM
        (
        SELECT
        tas.asset_id,
        tas.server_id,
        GROUP_CONCAT( nim.ipv4 SEPARATOR ', ' ) AS ipv4_list
        FROM
        tbl_application_server tas
        LEFT JOIN tbl_network_ip_mac nim ON nim.asset_id = tas.server_id
        WHERE
        tas.asset_id IS NOT NULL
        GROUP BY
        tas.asset_id
        ) tdf
        LEFT JOIN monitor_bss_wp_deal bwd ON FIND_IN_SET( bwd.host_ip, tdf.ipv4_list )
        <where>
            <if test="startTime != null and endTime != null">
                and bwd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
        tdf.asset_id
        ) t3 ON t1.asset_id = t3.asset_id
        ORDER BY
        total_risk DESC
        LIMIT 5
    </select>

    <select id="webVulnerabilityTypeStatistics" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            wvd.category,
            COUNT(DISTINCT wvd.id) num
        FROM
            monitor_bss_webvuln_deal wvd
                LEFT JOIN tbl_work_order wo ON FIND_IN_SET( wvd.id, wo.event_ids )
                AND wo.work_type = '2'
                LEFT JOIN tbl_business_application tba ON wvd.web_url = tba.url
                LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = tba.domain_id
                LEFT JOIN sys_dept sd ON sd.dept_id = tba.dept_id
        <where>
            wvd.severity NOT IN ( 0 )
            <if test="deptId != null and deptId != 100">
                and (sd.dept_id=#{deptId} OR find_in_set(#{deptId},sd.ancestors))
            </if>
            <if test="startTime != null and endTime != null">
                and wvd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
            wvd.category
        ORDER BY
            COUNT(DISTINCT wvd.id) DESC
    </select>

    <select id="webVulnerabilityRanking" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            wvd.title,
            COUNT(DISTINCT wvd.id) num
        FROM
            monitor_bss_webvuln_deal wvd
                LEFT JOIN tbl_work_order wo ON FIND_IN_SET( wvd.id, wo.event_ids )
                AND wo.work_type = '2'
                LEFT JOIN tbl_business_application tba ON wvd.web_url = tba.url
                LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = tba.domain_id
                LEFT JOIN sys_dept sd ON sd.dept_id = tba.dept_id
        <where>
            wvd.severity NOT IN ( 0 )
            <if test="deptId != null and deptId != 100">
                and (sd.dept_id=#{deptId} OR find_in_set(#{deptId},sd.ancestors))
            </if>
            <if test="startTime != null and endTime != null">
                and wvd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
            wvd.title
        ORDER BY
            COUNT(DISTINCT wvd.id) DESC
        LIMIT 5
    </select>

    <select id="findMonitorBssVulnDealAtLastId" resultType="java.lang.String">
        SELECT
            id
        FROM
            monitor_bss_vuln_deal
        ORDER BY
            id DESC
            LIMIT 1
    </select>
</mapper>
