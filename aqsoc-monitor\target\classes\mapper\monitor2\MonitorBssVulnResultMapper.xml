<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.MonitorBssVulnResultMapper">

    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssVulnResult" id="MonitorBssVulnResultResult">
        <result property="id"    column="id"    />
        <result property="planId"    column="plan_id"    />
        <result property="xprocessId"    column="xprocess_id"    />
        <result property="title"    column="title"    />
        <result property="poc"    column="poc"    />
        <result property="exp"    column="exp"    />
        <result property="summary"    column="summary"    />
        <result property="impact"    column="impact"    />
        <result property="detail"    column="detail"    />
        <result property="solution"    column="solution"    />
        <result property="definiteness"    column="definiteness"    />
        <result property="category"    column="category"    />
        <result property="exposures"    column="exposures"    />
        <result property="cvss"    column="cvss"    />
        <result property="publishedDateTime"    column="published_date_time"    />
        <result property="targetType"    column="target_type"    />
        <result property="hostIp"    column="host_ip"    />
        <result property="hostPort"    column="host_port"    />
        <result property="protocol"    column="protocol"    />
        <result property="webUrl"    column="web_url"    />
        <result property="severity"    column="severity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMonitorBssVulnResultVo">
        select id, plan_id, xprocess_id, title, poc, exp, summary, impact, detail, solution, definiteness, category, exposures, cvss, published_date_time, target_type, host_ip, host_port, protocol, web_url, severity, create_by, create_time, update_by, update_time from monitor_bss_vuln_result
    </sql>

    <select id="selectMonitorBssVulnResultList" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnResult" resultMap="MonitorBssVulnResultResult">
        <include refid="selectMonitorBssVulnResultVo"/>
        <where>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="xprocessId != null "> and xprocess_id = #{xprocessId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="poc != null  and poc != ''"> and poc = #{poc}</if>
            <if test="exp != null  and exp != ''"> and exp = #{exp}</if>
            <if test="summary != null  and summary != ''"> and summary = #{summary}</if>
            <if test="impact != null  and impact != ''"> and impact = #{impact}</if>
            <if test="detail != null  and detail != ''"> and detail = #{detail}</if>
            <if test="solution != null  and solution != ''"> and solution = #{solution}</if>
            <if test="definiteness != null and definiteness != -1"> and definiteness = #{definiteness}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="exposures != null  and exposures != ''"> and exposures = #{exposures}</if>
            <if test="cvss != null  and cvss != ''"> and cvss = #{cvss}</if>
            <if test="publishedDateTime != null "> and published_date_time = #{publishedDateTime}</if>
            <if test="targetType != 0 "> and target_type = #{targetType}</if>
            <if test="hostIp != null  and hostIp != ''"> and host_ip = #{hostIp}</if>
            <if test="hostPort != 0 "> and host_port = #{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="webUrl != null  and webUrl != ''"> and web_url = #{webUrl}</if>
            <if test="severity != -1 "> and severity = #{severity}</if>
        </where>
    </select>

    <select id="selectMonitorBssVulnResultById" parameterType="java.lang.Long" resultMap="MonitorBssVulnResultResult">
        <include refid="selectMonitorBssVulnResultVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorBssVulnResultByIds" parameterType="java.lang.Long" resultMap="MonitorBssVulnResultResult">
        <include refid="selectMonitorBssVulnResultVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMonitorBssVulnResult" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnResult" useGeneratedKeys="true" keyProperty="id">
        insert into monitor_bss_vuln_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="xprocessId != null">xprocess_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="poc != null">poc,</if>
            <if test="exp != null">exp,</if>
            <if test="summary != null">summary,</if>
            <if test="impact != null">impact,</if>
            <if test="detail != null">detail,</if>
            <if test="solution != null">solution,</if>
            <if test="definiteness != null">definiteness,</if>
            <if test="category != null and category != ''">category,</if>
            <if test="exposures != null and exposures != ''">exposures,</if>
            <if test="cvss != null">cvss,</if>
            <if test="publishedDateTime != null">published_date_time,</if>
            <if test="targetType != null">target_type,</if>
            <if test="hostIp != null">host_ip,</if>
            <if test="hostPort != null">host_port,</if>
            <if test="protocol != null">protocol,</if>
            <if test="webUrl != null">web_url,</if>
            <if test="severity != -1">severity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},</if>
            <if test="xprocessId != null">#{xprocessId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="poc != null">#{poc},</if>
            <if test="exp != null">#{exp},</if>
            <if test="summary != null">#{summary},</if>
            <if test="impact != null">#{impact},</if>
            <if test="detail != null">#{detail},</if>
            <if test="solution != null">#{solution},</if>
            <if test="definiteness != null">#{definiteness},</if>
            <if test="category != null and category != ''">#{category},</if>
            <if test="exposures != null and exposures != ''">#{exposures},</if>
            <if test="cvss != null">#{cvss},</if>
            <if test="publishedDateTime != null">#{publishedDateTime},</if>
            <if test="targetType != null">#{targetType},</if>
            <if test="hostIp != null">#{hostIp},</if>
            <if test="hostPort != null">#{hostPort},</if>
            <if test="protocol != null">#{protocol},</if>
            <if test="webUrl != null">#{webUrl},</if>
            <if test="severity != -1">#{severity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertMonitorBssVulnResultList" parameterType="list" useGeneratedKeys="true" keyProperty="id">
        insert into monitor_bss_vuln_result values
        <foreach collection="list" item="item" separator=",">
            (#{item.planId}, #{item.xprocessId}, #{item.title}, #{item.poc}, #{item.exp}, #{item.summary}, #{item.impact}, #{item.detail}, #{item.solution}, #{item.definiteness}, #{item.category}, #{item.exposures}, #{item.cvss}, #{item.publishedDateTime}, #{item.targetType}, #{item.hostIp}, #{item.hostPort}, #{item.protocol}, #{item.webUrl}, #{item.severity}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <update id="updateMonitorBssVulnResult" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnResult">
        update monitor_bss_vuln_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="xprocessId != null">xprocess_id = #{xprocessId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="poc != null">poc = #{poc},</if>
            <if test="exp != null">exp = #{exp},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="impact != null">impact = #{impact},</if>
            <if test="detail != null">detail = #{detail},</if>
            <if test="solution != null">solution = #{solution},</if>
            <if test="definiteness != null">definiteness = #{definiteness},</if>
            <if test="category != null and category != ''">category = #{category},</if>
            <if test="exposures != null and exposures != ''">exposures = #{exposures},</if>
            <if test="cvss != null">cvss = #{cvss},</if>
            <if test="publishedDateTime != null">published_date_time = #{publishedDateTime},</if>
            <if test="targetType != null">target_type = #{targetType},</if>
            <if test="hostIp != null">host_ip = #{hostIp},</if>
            <if test="hostPort != null">host_port = #{hostPort},</if>
            <if test="protocol != null">protocol = #{protocol},</if>
            <if test="webUrl != null">web_url = #{webUrl},</if>
            <if test="severity != -1">severity = #{severity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorBssVulnResultById" parameterType="java.lang.Long">
        delete from monitor_bss_vuln_result where id = #{id}
    </delete>

    <delete id="deleteMonitorBssVulnResultByIds" parameterType="java.lang.String">
        delete from monitor_bss_vuln_result where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="findImpactAssetList" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnResult"
            resultType="com.ruoyi.monitor2.domain.MonitorBssVulnResult">
        SELECT
            tnim.asset_id as assetId,
            mbvr.host_ip as hostIp,
            mbvr.host_port as hostPort,
            mbvr.protocol,
            tao.asset_name as assetName,
            asset_type as assetType,
            asset_type_desc as assetTypeDesc,
            asset_class as assetClass,
            asset_class_desc as assetClassDesc,
            mbvr.create_time as createTime
        FROM
            monitor_bss_vuln_result mbvr
                LEFT JOIN tbl_network_ip_mac tnim ON mbvr.host_ip = tnim.ipv4
                LEFT JOIN tbl_asset_overview tao ON tnim.asset_id = tao.asset_id
        <where>
            <if test="id != null "> and mbvr.id = #{id}</if>
            <if test="title != null and title != ''">
                and mbvr.title = #{title}
            </if>
            <if test="xprocessId != null">
                and mbvr.xprocess_id = #{xprocessId}
            </if>
            <if test="hostIp != null  and hostIp != ''"> and mbvr.host_ip = #{hostIp}</if>
            <if test="hostPort != 0 "> and mbvr.host_port = #{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and mbvr.protocol = #{protocol}</if>
        </where>
        <if test="title != null and title != ''">
            GROUP BY mbvr.host_ip, mbvr.host_port, mbvr.protocol
        </if>
    </select>

    <select id="findAnalyseType" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnResult"
            resultType="com.ruoyi.monitor2.domain.MonitorBssVulnResult">
        SELECT
            protocol as name,
            count( protocol ) as value
        FROM
            monitor_bss_vuln_result
        <where>
            <if test="planId != null">
                plan_id = #{planId}
            </if>
            <if test="xprocessId != null">
                AND xprocess_id = #{xprocessId}
            </if>
        </where>
        GROUP BY protocol
    </select>

    <select id="selectGapTypeAnalyse" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnResult"
            resultType="com.ruoyi.monitor2.domain.MonitorBssVulnResult">
        SELECT
            category as name,
            count( category ) as value
        FROM
            monitor_bss_vuln_result
        <where>
            <if test="planId != null">
                plan_id = #{planId}
            </if>
            <if test="xprocessId != null">
                AND xprocess_id = #{xprocessId}
            </if>
        </where>
        GROUP BY category
    </select>

    <select id="selectGapNumStatistics" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnResult"
            resultType="com.ruoyi.monitor2.domain.MonitorBssVulnResult">
        SELECT (CASE WHEN severity = '1' THEN '低危'
                     WHEN severity = '2' THEN '中危'
                     WHEN severity = '3' THEN '高危'
                     WHEN severity = '4' THEN '严重'
                     ELSE '未知' END) AS name,
               count( severity ) as value
        FROM
            monitor_bss_vuln_result
        <where>
            <if test="planId != null">
                plan_id = #{planId}
            </if>
            <if test="xprocessId != null">
                AND xprocess_id = #{xprocessId}
            </if>
        </where>
        GROUP BY severity
    </select>

    <select id="selectJobGapList" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnResult"
            resultType="com.ruoyi.monitor2.domain.MonitorBssVulnResult">
        SELECT
            title,
            severity,
            category,
            target_type as targetType,
            web_url as webUrl,
            id
        FROM
            monitor_bss_vuln_result
        <where>
            <if test="planId != null">
                plan_id = #{planId}
            </if>
            <if test="xprocessId != null">
                AND xprocess_id = #{xprocessId}
            </if>
        </where>
    </select>


    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssVulnResult" id="VulnTargetStat">
        <!--
        <result property="planId"    column="plan_id"    />
        <result property="xprocessId"    column="xprocess_id"    />
        -->
        <result property="title"    column="title"    />
        <result property="category"    column="category"    />
        <result property="severity"    column="severity"    />
        <result property="targetNum"    column="target_num"    />
    </resultMap>


    <select id="selectTargetNumList" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnResult" resultMap="VulnTargetStat">
        SELECT
            title,
            category,
            severity,
            web_url AS webUrl
        FROM
            monitor_bss_vuln_result
        WHERE
            plan_id = #{planId} and xprocess_id = #{xprocessId}
    </select>
    <select id="selectGapIntelList" resultMap="MonitorBssVulnResultResult">
        SELECT
            id,plan_id,xprocess_id,title,severity,category
        from
            monitor_bss_vuln_result
        <where>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="xprocessId != null "> and xprocess_id = #{xprocessId}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="severity != -1 "> and severity = #{severity}</if>
            <if test="category != null  and category != ''"> and category like concat('%',#{category},'%')</if>
        </where>
        group by title
    </select>

    <select id="findWebGapAsset" parameterType="com.ruoyi.monitor2.domain.MonitorBssVulnResult"
            resultType="com.ruoyi.monitor2.domain.MonitorBssVulnResult">
        SELECT
            tba.asset_id as assetId,
            tba.asset_name as assetName,
            tao.asset_type as assetType,
            tao.asset_type_desc as assetTypeDesc,
            tao.asset_class as assetClass,
            tao.asset_class_desc as assetClassDesc,
            mbvr.create_time as createTime
        FROM monitor_bss_vuln_result mbvr
        LEFT JOIN tbl_business_application tba ON mbvr.web_url = tba.url
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = tba.asset_id
        <where>
            <if test="id != null">
                mbvr.id = #{id}
            </if>
        </where>
    </select>
</mapper>