<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenLossHostMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenLossHostListVO">
        select a.* from threaten_loss_host a left join threaten_alarm_origin b on a.id = b.origin_id and (b.threaten_type = 'threaten_loss_host' or b.threaten_type = '失陷主机')
        <where>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.baseId!=null">
                and a.base_id = #{query.baseId}
            </if>
            <if test="query.attackNum!=null">
                and a.attack_num = #{query.attackNum}
            </if>
            <if test="query.attackDirection!=null">
                and a.attack_direction = #{query.attackDirection}
            </if>
            <if test="query.attackStage!=null">
                and a.attack_stage = #{query.attackStage}
            </if>
            <if test="query.attackResult!=null">
                and a.attack_result = #{query.attackResult}
            </if>
            <if test="query.lossType!=null and query.lossType!=''">
                and a.loss_type like concat('%', #{query.lossType}, '%')
            </if>
            <if test="query.linkDomain!=null and query.linkDomain!=''">
                and a.link_domain like concat('%', #{query.linkDomain}, '%')
            </if>
            <if test="query.linkIp!=null and query.linkIp!=''">
                and a.link_ip like concat('%', #{query.linkIp}, '%')
            </if>
            <if test="query.attackTime!=null">
                and a.attack_time = #{query.attackTime}
            </if>
            <if test="query.alarmId!=null">
                and b.alarm_id = #{query.alarmId}
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenLossHostListVO">
        select a.* from threaten_loss_host a 
        where a.id=#{id}
    </select>
</mapper>
