<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.MonitorExploreJobMapper">

    <resultMap type="com.ruoyi.safe.domain.MonitorExploreJob" id="MonitorExploreJobResult">
        <result property="id"    column="id"    />
        <result property="jobId"    column="job_id"    />
        <result property="totalScan"    column="total_scan"    />
        <result property="alive"    column="alive"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMonitorExploreJobVo">
        select id, job_id, total_scan, alive, status, create_by, create_time, update_by, update_time, remark from monitor_explore_job
    </sql>

    <select id="selectMonitorExploreJobList" parameterType="com.ruoyi.safe.domain.MonitorExploreJob" resultMap="MonitorExploreJobResult">
        <include refid="selectMonitorExploreJobVo"/>
        <where>
            <if test="jobId != null "> and job_id = #{jobId}</if>
            <if test="totalScan != null "> and total_scan = #{totalScan}</if>
            <if test="alive != null "> and alive = #{alive}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectMonitorExploreJobById" parameterType="java.lang.Long" resultMap="MonitorExploreJobResult">
        <include refid="selectMonitorExploreJobVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorExploreJobByIds" parameterType="java.lang.Long" resultMap="MonitorExploreJobResult">
        <include refid="selectMonitorExploreJobVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMonitorExploreJob" useGeneratedKeys="true" keyProperty="id" parameterType="com.ruoyi.safe.domain.MonitorExploreJob">
        insert into monitor_explore_job
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="jobId != null">job_id,</if>
            <if test="totalScan != null">total_scan,</if>
            <if test="alive != null">alive,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="jobId != null">#{jobId},</if>
            <if test="totalScan != null">#{totalScan},</if>
            <if test="alive != null">#{alive},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMonitorExploreJob" parameterType="com.ruoyi.safe.domain.MonitorExploreJob">
        update monitor_explore_job
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobId != null">job_id = #{jobId},</if>
            <if test="totalScan != null">total_scan = #{totalScan},</if>
            <if test="alive != null">alive = #{alive},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateMonitorExploreJobByJobId" parameterType="com.ruoyi.safe.domain.MonitorExploreJob">
        update monitor_explore_job
        <trim prefix="SET" suffixOverrides=",">
            <if test="totalScan != null">total_scan = #{totalScan},</if>
            <if test="alive != null">alive = #{alive},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where job_id = #{jobId} and status = 1
    </update>

    <delete id="deleteMonitorExploreJobById" parameterType="java.lang.Long">
        delete from monitor_explore_job where id = #{id}
    </delete>

    <delete id="deleteMonitorExploreJobByIds" parameterType="java.lang.String">
        delete from monitor_explore_job where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
