<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblEmpDocMapper">
    
    <resultMap type="TblEmpDoc" id="TblEmpDocResult">
        <result property="id"    column="id"    />
        <result property="eid"    column="eid"    />
        <result property="empName"    column="emp_name"    />
        <result property="dwid"    column="dwid"    />
        <result property="dwName"    column="dw_name"    />
        <result property="title"    column="title"    />
        <result property="tm"    column="tm"    />
        <result property="type"    column="type"    />
        <result property="attach"    column="attach"    />
        <result property="content"    column="content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblEmpDocVo">
        select a.id, a.eid, a.title,a.tm, a.type, a.attach, a.content, a.create_by, a.create_time,
               a.update_by, a.update_time,b.name as emp_name,b.dwid,c.name as dw_name
        from tbl_emp_doc a
        left join tbl_company_emp b on a.eid=b.eid
        left join tbl_company c on b.dwid=c.dwid
    </sql>

    <select id="selectTblEmpDocList" parameterType="TblEmpDoc" resultMap="TblEmpDocResult">
        <include refid="selectTblEmpDocVo"/>
        <where>
            <if test="dwid != null "> and c.dwid = #{dwid}</if>
            <if test="eid != null "> and a.eid = #{eid}</if>
            <if test="empName != null "> and b.name like  concat('%',#{empName},'%')</if>
            <if test="title != null  and title != ''"> and a.title = #{title}</if>
            <if test="type != null  and type != ''"> and a.type = #{type}</if>
        </where>
    </select>
    
    <select id="selectTblEmpDocById" parameterType="Long" resultMap="TblEmpDocResult">
        <include refid="selectTblEmpDocVo"/>
        where a.id = #{id}
    </select>
        
    <insert id="insertTblEmpDoc" parameterType="TblEmpDoc">
        insert into tbl_emp_doc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="eid != null">eid,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="tm != null">tm,</if>
            <if test="type != null">type,</if>
            <if test="attach != null">attach,</if>
            <if test="content != null">content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="eid != null">#{eid},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="tm != null">#{tm},</if>
            <if test="type != null">#{type},</if>
            <if test="attach != null">#{attach},</if>
            <if test="content != null">#{content},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblEmpDoc" parameterType="TblEmpDoc">
        update tbl_emp_doc
        <trim prefix="SET" suffixOverrides=",">
            <if test="eid != null">eid = #{eid},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="tm != null">tm = #{tm},</if>
            <if test="type != null">type = #{type},</if>
            <if test="attach != null">attach = #{attach},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblEmpDocById" parameterType="Long">
        delete from tbl_emp_doc where id = #{id}
    </delete>

    <delete id="deleteTblEmpDocByIds" parameterType="String">
        delete from tbl_emp_doc where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>