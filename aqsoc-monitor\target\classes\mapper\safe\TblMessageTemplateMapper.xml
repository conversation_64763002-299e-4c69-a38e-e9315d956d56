<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblMessageTemplateMapper">

    <resultMap type="TblMessageTemplate" id="TblMessageTemplateResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="type"    column="type"    />
        <result property="templateContent"    column="template_content"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deptName"    column="dept_name"    />
    </resultMap>

    <sql id="selectTblMessageTemplateVo">
        select tmt.id, tmt.dept_id, tmt.type, tmt.template_content, tmt.is_enabled, tmt.create_by, tmt.create_time, tmt.update_by, tmt.update_time ,sd.dept_name
        from tbl_message_template tmt
        left join sys_dept sd on tmt.dept_id = sd.dept_id
    </sql>

    <select id="selectTblMessageTemplateList" parameterType="TblMessageTemplate" resultMap="TblMessageTemplateResult">
        <include refid="selectTblMessageTemplateVo"/>
        <where>
            <if test="deptId != null "> and tmt.dept_id = #{deptId}</if>
            <if test="type != null  and type != ''"> and tmt.type = #{type}</if>
            <if test="templateContent != null  and templateContent != ''"> and tmt.template_content like concat('%',#{templateContent},'%')</if>
            <if test="isEnabled != null  and isEnabled != ''"> and tmt.is_enabled = #{isEnabled}</if>
        </where>
    </select>

    <select id="selectTblMessageTemplateById" parameterType="Long" resultMap="TblMessageTemplateResult">
        <include refid="selectTblMessageTemplateVo"/>
        where tmt.id = #{id}
    </select>

    <select id="selectTblMessageTemplateByIds" parameterType="Long" resultMap="TblMessageTemplateResult">
        <include refid="selectTblMessageTemplateVo"/>
        where tmt.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblMessageTemplate" parameterType="TblMessageTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_message_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="type != null">type,</if>
            <if test="templateContent != null">template_content,</if>
            <if test="isEnabled != null">is_enabled,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="type != null">#{type},</if>
            <if test="templateContent != null">#{templateContent},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblMessageTemplate" parameterType="TblMessageTemplate">
        update tbl_message_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="templateContent != null">template_content = #{templateContent},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblMessageTemplateById" parameterType="Long">
        delete from tbl_message_template where id = #{id}
    </delete>

    <delete id="deleteTblMessageTemplateByIds" parameterType="String">
        delete from tbl_message_template where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
