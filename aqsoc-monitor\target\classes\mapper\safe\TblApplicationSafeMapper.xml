<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblApplicationSafeMapper">

    <resultMap type="TblApplicationSafe" id="TblApplicationSafeResult">
        <result property="assetId"    column="asset_id"    />
        <result property="safeId"    column="safe_id"    />
        <result property="ips"    column="ips"    />
        <result property="id"    column="id"    />
    </resultMap>

    <sql id="selectTblApplicationSafeVo">
        select asset_id, safe_id, ips,id from tbl_application_safe
    </sql>

    <select id="selectTblApplicationSafeList" parameterType="TblApplicationSafe" resultMap="TblApplicationSafeResult">
        <include refid="selectTblApplicationSafeVo"/>
        <where>
            <if test="ips != null  and ips != ''"> and ips = #{ips}</if>
            <if test="assetId != null  and assetId != ''"> and asset_id = #{assetId}</if>
            <if test="safeId != null  and safeId != ''"> and safe_id = #{safeId}</if>
            <if test="id != null  and id != ''"> and id = #{id}</if>
        </where>
    </select>

    <select id="selectTblApplicationSafeById" parameterType="Long" resultMap="TblApplicationSafeResult">
        <include refid="selectTblApplicationSafeVo"/>
        where id = #{id}
    </select>

    <select id="selectTblApplicationSafeByIds" parameterType="Long" resultMap="TblApplicationSafeResult">
        <include refid="selectTblApplicationSafeVo"/>
        where id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </select>

    <insert id="insertTblApplicationSafe" parameterType="TblApplicationSafe">
        insert into tbl_application_safe
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="safeId != null">safe_id,</if>
            <if test="ips != null">ips,</if>
            <if test="id != null">id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="safeId != null">#{safeId},</if>
            <if test="ips != null">#{ips},</if>
            <if test="id != null">#{id},</if>
         </trim>
    </insert>

    <update id="updateTblApplicationSafe" parameterType="TblApplicationSafe">
        update tbl_application_safe
        <trim prefix="SET" suffixOverrides=",">
            <if test="safeId != null">safe_id = #{safeId},</if>
            <if test="ips != null">ips = #{ips},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblApplicationSafeByAssetId" parameterType="Long">
        delete from tbl_application_safe where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblApplicationSafeByAssetIds" parameterType="String">
        delete from tbl_application_safe where id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
    <delete id="deleteTblApplicationSafe" parameterType="TblApplicationSafe">
        delete from tbl_application_safe
        <where>
            <if test="ips != null  and ips != ''"> and ips = #{ips}</if>
            <if test="assetId != null  and assetId != ''"> and asset_id = #{assetId}</if>
            <if test="safeId != null  and safeId != ''"> and safe_id = #{safeId}</if>
            <if test="id != null  and id != ''"> and id = #{id}</if>
        </where>
    </delete>

    <select id="selectCountNum" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(0) from tbl_application_safe
        <where>
            <if test="assetId != null  and assetId != ''"> asset_id = #{assetId}</if>
        </where>
    </select>
</mapper>
