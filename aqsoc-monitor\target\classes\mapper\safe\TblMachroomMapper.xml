<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblMachroomMapper">

    <resultMap type="TblMachroom" id="TblMachroomResult">
        <result property="assetId" column="asset_id"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="location" column="location"/>
        <result property="degreeImportance" column="degree_importance"/>
        <result property="manger" column="manger"/>
        <result property="phone" column="phone"/>
        <result property="vendor" column="vendor"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="assetType" column="asset_type"/>
        <result property="assetTypeDesc" column="asset_type_desc"/>
        <result property="assetClass" column="asset_class"/>
        <result property="assetClassDesc" column="asset_class_desc"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="orgnId" column="orgn_id"/>
    </resultMap>

    <sql id="selectTblMachroomVo">
        select a.asset_id,
               a.asset_code,
               a.asset_name,
               a.location,
               a.degree_importance,
               a.manger,
               a.phone,
               a.vendor,
               d.vendor_name,
               a.asset_type,
               a.asset_type_desc,
               a.asset_class,
               a.asset_class_desc,
               a.remark,
               a.create_by,
               a.create_time,
               a.update_by,
               a.update_time,
               a.user_id,
               a.dept_id
        from tbl_machroom a
                 left join tbl_vendor d on a.vendor = d.id
                 left join tbl_asset_overview o on a.asset_id = o.asset_id
                left join tbl_location l on o.location_id = l.location_id
    </sql>

    <select id="selectTblMachroomList" parameterType="TblMachroom" resultMap="TblMachroomResult">
        <include refid="selectTblMachroomVo"/>
        <where>
            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>
            <if test="assetCode != null  and assetCode != ''"> and a.asset_code like concat('%', #{assetCode}, '%')</if>
            <if test="assetName != null  and assetName != ''"> and a.asset_name like concat('%', #{assetName}, '%')</if>
            <if test="degreeImportance != null  and degreeImportance != ''"> and a.degree_importance = #{degreeImportance}</if>
            <if test="assetType != null  and assetType != ''"> and a.asset_type = #{assetType}</if>
            <if test="assetClass != null  and assetClass != ''"> and a.asset_class = #{assetClass}</if>
            <if test="location != null and location != ''">and o.location_id = #{location}</if>
            <if test="deptId != null and deptId != 0">
                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
    </select>

    <select id="selectTblMachroomByAssetId" parameterType="Long" resultMap="TblMachroomResult">
        <include refid="selectTblMachroomVo"/>
        where a.asset_id = #{assetId}
    </select>

    <select id="selectTblMachroomByAssetIds" parameterType="Long" resultMap="TblMachroomResult">
        <include refid="selectTblMachroomVo"/>
        where a.asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </select>

    <insert id="insertTblMachroom" parameterType="TblMachroom">
        insert into tbl_machroom
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="assetCode != null">asset_code,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="location != null and location != ''">location,</if>
            <if test="degreeImportance != null">degree_importance,</if>
            <if test="manger != null">manger,</if>
            <if test="phone != null">phone,</if>
            <if test="vendor != null">vendor,</if>
            <if test="assetType != null">asset_type,</if>
            <if test="assetTypeDesc != null">asset_type_desc,</if>
            <if test="assetClass != null">asset_class,</if>
            <if test="assetClassDesc != null">asset_class_desc,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="orgnId != null">orgn_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="assetCode != null">#{assetCode},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="location != null and location != ''">#{location},</if>
            <if test="degreeImportance != null">#{degreeImportance},</if>
            <if test="manger != null">#{manger},</if>
            <if test="phone != null">#{phone},</if>
            <if test="vendor != null">#{vendor},</if>
            <if test="assetType != null">#{assetType},</if>
            <if test="assetTypeDesc != null">#{assetTypeDesc},</if>
            <if test="assetClass != null">#{assetClass},</if>
            <if test="assetClassDesc != null">#{assetClassDesc},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="orgnId != null">#{orgnId},</if>
         </trim>
    </insert>

    <update id="updateTblMachroom" parameterType="TblMachroom">
        update tbl_machroom
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="location != null and location != ''">location = #{location},</if>
            <if test="degreeImportance != null">degree_importance = #{degreeImportance},</if>
            <if test="manger != null">manger = #{manger},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="vendor != null">vendor = #{vendor},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="assetTypeDesc != null">asset_type_desc = #{assetTypeDesc},</if>
            <if test="assetClass != null">asset_class = #{assetClass},</if>
            <if test="assetClassDesc != null">asset_class_desc = #{assetClassDesc},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="orgnId != null">orgn_id = #{orgnId},</if>
        </trim>
        where asset_id = #{assetId}
    </update>

    <delete id="deleteTblMachroomByAssetId" parameterType="Long">
        delete from tbl_machroom where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblMachroomByAssetIds" parameterType="String">
        delete from tbl_machroom where asset_id in 
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
</mapper>