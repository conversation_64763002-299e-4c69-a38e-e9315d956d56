<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.asset.mapper.TblAssetFieldsItemMapper">

    <resultMap type="TblAssetFieldsItem" id="TblAssetFieldsItemResult">
        <result property="id"    column="id"    />
        <result property="refId"    column="ref_id"    />
        <result property="fieldName"    column="field_name"    />
        <result property="fieldKey"    column="field_key"    />
        <result property="required"    column="required"    />
        <result property="isQuery"    column="is_query"    />
        <result property="isShow"    column="is_show"    />
        <result property="assetType" column="asset_type" />
        <result property="sort" column="sort" />
    </resultMap>

    <sql id="selectTblAssetFieldsItemVo">
        select id, ref_id, field_name, field_key, required, is_query, is_show,asset_type,sort from tbl_asset_fields_item
    </sql>

    <select id="selectTblAssetFieldsItemList" parameterType="TblAssetFieldsItem" resultMap="TblAssetFieldsItemResult">
        <include refid="selectTblAssetFieldsItemVo"/>
        <where>
            <if test="refId != null "> and ref_id = #{refId}</if>
            <if test="fieldName != null  and fieldName != ''"> and field_name like concat('%', #{fieldName}, '%')</if>
            <if test="fieldKey != null  and fieldKey != ''"> and field_key = #{fieldKey}</if>
            <if test="required != null "> and required = #{required}</if>
            <if test="isQuery != null "> and is_query = #{isQuery}</if>
            <if test="isShow != null "> and is_show = #{isShow}</if>
            <if test="assetType != null"> and asset_type = #{assetType}</if>
        </where>
        order by sort
    </select>

    <select id="selectTblAssetFieldsItemById" parameterType="Long" resultMap="TblAssetFieldsItemResult">
        <include refid="selectTblAssetFieldsItemVo"/>
        where id = #{id}
    </select>

    <select id="selectTblAssetFieldsItemByIds" parameterType="Long" resultMap="TblAssetFieldsItemResult">
        <include refid="selectTblAssetFieldsItemVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectTblAssetFieldsItemListByRefId"
            resultType="cn.anmte.aqsoc.asset.domain.TblAssetFieldsItem" resultMap="TblAssetFieldsItemResult">
        <include refid="selectTblAssetFieldsItemVo"/>
        where ref_id = #{refId}
        order by sort
    </select>
    <select id="selectItemList" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t1.id,
            t1.ref_id,
            t1.field_name,
            t1.field_key,
            t1.required,
            t1.is_query,
            t1.is_show,
            t1.asset_type,
            t1.sort
        FROM
            tbl_asset_fields_item t1
            JOIN tbl_asset_fields t2 ON t2.id=t1.ref_id
        <where>
            <if test="assetType != null">
                AND t2.asset_type = #{assetType}
            </if>
        </where>
    </select>

    <insert id="insertTblAssetFieldsItem" parameterType="TblAssetFieldsItem" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_asset_fields_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="refId != null">ref_id,</if>
            <if test="fieldName != null">field_name,</if>
            <if test="fieldKey != null">field_key,</if>
            <if test="required != null">required,</if>
            <if test="isQuery != null">is_query,</if>
            <if test="isShow != null">is_show,</if>
            <if test="assetType != null">asset_type,</if>
            <if test="sort != null">sort,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="refId != null">#{refId},</if>
            <if test="fieldName != null">#{fieldName},</if>
            <if test="fieldKey != null">#{fieldKey},</if>
            <if test="required != null">#{required},</if>
            <if test="isQuery != null">#{isQuery},</if>
            <if test="isShow != null">#{isShow},</if>
            <if test="assetType != null">#{assetType},</if>
            <if test="sort != null">#{sort},</if>
         </trim>
    </insert>
    <insert id="batchInsert">
        insert into tbl_asset_fields_item (ref_id, field_name, field_key, required, is_query, is_show, asset_type,sort) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.refId}, #{item.fieldName}, #{item.fieldKey}, #{item.required}, #{item.isQuery}, #{item.isShow},#{item.assetType},#{item.sort})
        </foreach>
    </insert>

    <update id="updateTblAssetFieldsItem" parameterType="TblAssetFieldsItem">
        update tbl_asset_fields_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="refId != null">ref_id = #{refId},</if>
            <if test="fieldName != null">field_name = #{fieldName},</if>
            <if test="fieldKey != null">field_key = #{fieldKey},</if>
            <if test="required != null">required = #{required},</if>
            <if test="isQuery != null">is_query = #{isQuery},</if>
            <if test="isShow != null">is_show = #{isShow},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="sort != null">sort = #{sort},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchUpdate">
        update tbl_asset_fields_item set
        field_name = CASE id
        <foreach item="item" index="index" collection="list">
            WHEN #{item.id} THEN #{item.fieldName}
        </foreach>
        ELSE field_name
        END,
        required = CASE id
        <foreach item="item" index="index" collection="list">
            WHEN #{item.id} THEN #{item.required}
        </foreach>
        ELSE required
        END,
        is_query = CASE id
        <foreach item="item" index="index" collection="list">
            WHEN #{item.id} THEN #{item.isQuery}
        </foreach>
        ELSE is_query
        END,
        is_show = CASE id
        <foreach item="item" index="index" collection="list">
            WHEN #{item.id} THEN #{item.isShow}
        </foreach>
        ELSE is_show
        END,
        sort = CASE id
        <foreach item="item" index="index" collection="list">
            WHEN #{item.id} THEN #{item.sort}
        </foreach>
        ELSE sort
        END
    </update>

    <delete id="deleteTblAssetFieldsItemById" parameterType="Long">
        delete from tbl_asset_fields_item where id = #{id}
    </delete>

    <delete id="deleteTblAssetFieldsItemByIds" parameterType="String">
        delete from tbl_asset_fields_item where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
