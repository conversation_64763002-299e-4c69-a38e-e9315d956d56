<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblNetworkMacMapper">
    
    <resultMap type="TblNetworkMac" id="TblNetworkMacResult">
        <result property="macId"    column="mac_id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="assetName"    column="asset_name"    />
        <result property="mac"    column="mac"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isWireless"    column="is_wireless"    />
    </resultMap>

    <sql id="selectTblNetworkMacVo">
        select mac_id, asset_id, asset_name, mac, remark, create_by, create_time, update_by, update_time, is_wireless from tbl_network_mac
    </sql>

    <select id="selectTblNetworkMacList" parameterType="TblNetworkMac" resultMap="TblNetworkMacResult">
        <include refid="selectTblNetworkMacVo"/>
        <where>  
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="mac != null  and mac != ''"> and mac = #{mac}</if>
            <if test="isWireless != null "> and is_wireless = #{isWireless}</if>
        </where>
    </select>
    
    <select id="selectTblNetworkMacByMacId" parameterType="Long" resultMap="TblNetworkMacResult">
        <include refid="selectTblNetworkMacVo"/>
        where mac_id = #{macId}
    </select>
        
    <insert id="insertTblNetworkMac" parameterType="TblNetworkMac">
        insert into tbl_network_mac
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="macId != null">mac_id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="assetName != null">asset_name,</if>
            <if test="mac != null">mac,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isWireless != null">is_wireless,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="macId != null">#{macId},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="assetName != null">#{assetName},</if>
            <if test="mac != null">#{mac},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isWireless != null">#{isWireless},</if>
         </trim>
    </insert>

    <update id="updateTblNetworkMac" parameterType="TblNetworkMac">
        update tbl_network_mac
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="assetName != null">asset_name = #{assetName},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isWireless != null">is_wireless = #{isWireless},</if>
        </trim>
        where mac_id = #{macId}
    </update>

    <delete id="deleteTblNetworkMacByMacId" parameterType="Long">
        delete from tbl_network_mac where mac_id = #{macId}
    </delete>

    <delete id="deleteTblNetworkMacByMacIds" parameterType="String">
        delete from tbl_network_mac where mac_id in 
        <foreach item="macId" collection="array" open="(" separator="," close=")">
            #{macId}
        </foreach>
    </delete>
</mapper>