<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.asset.mapper.TblAssetFieldsMapper">

    <resultMap type="TblAssetFields" id="TblAssetFieldsResult">
        <result property="id"    column="id"    />
        <result property="formRef"    column="form_ref"    />
        <result property="formName"    column="form_name"    />
        <result property="assetType"    column="asset_type"    />
        <collection property="fieldsItems" column="id" select="cn.anmte.aqsoc.asset.mapper.TblAssetFieldsItemMapper.selectTblAssetFieldsItemListByRefId" />
    </resultMap>

    <sql id="selectTblAssetFieldsVo">
        select id, form_ref, form_name, asset_type from tbl_asset_fields
    </sql>

    <select id="selectTblAssetFieldsList" parameterType="TblAssetFields" resultMap="TblAssetFieldsResult">
        <include refid="selectTblAssetFieldsVo"/>
        <where>
            <if test="formRef != null  and formRef != ''"> and form_ref = #{formRef}</if>
            <if test="formName != null  and formName != ''"> and form_name like concat('%', #{formName}, '%')</if>
            <if test="assetType != null "> and asset_type = #{assetType}</if>
        </where>
    </select>

    <select id="selectTblAssetFieldsById" parameterType="Long" resultMap="TblAssetFieldsResult">
        <include refid="selectTblAssetFieldsVo"/>
        where id = #{id}
    </select>

    <select id="selectTblAssetFieldsByIds" parameterType="Long" resultMap="TblAssetFieldsResult">
        <include refid="selectTblAssetFieldsVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblAssetFields" parameterType="TblAssetFields" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_asset_fields
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formRef != null">form_ref,</if>
            <if test="formName != null">form_name,</if>
            <if test="assetType != null">asset_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="formRef != null">#{formRef},</if>
            <if test="formName != null">#{formName},</if>
            <if test="assetType != null">#{assetType},</if>
         </trim>
    </insert>

    <insert id="batchInsert">
        insert into tbl_asset_fields (form_ref, form_name, asset_type) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.formRef}, #{item.formName}, #{item.assetType})
        </foreach>
    </insert>

    <update id="updateTblAssetFields" parameterType="TblAssetFields">
        update tbl_asset_fields
        <trim prefix="SET" suffixOverrides=",">
            <if test="formRef != null">form_ref = #{formRef},</if>
            <if test="formName != null">form_name = #{formName},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchUpdate">
        update tbl_asset_fields SET
        form_name = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.formName != null and item.formName != ''">#{item.formName}</if> <if test="item.formName == null or item.formName == ''">form_name</if>
        </foreach>
        ELSE form_name
        END
    </update>

    <delete id="deleteTblAssetFieldsById" parameterType="Long">
        delete from tbl_asset_fields where id = #{id}
    </delete>

    <delete id="deleteTblAssetFieldsByIds" parameterType="String">
        delete from tbl_asset_fields where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
