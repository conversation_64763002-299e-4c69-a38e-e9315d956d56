<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblResidentPersonnelMapper">

    <resultMap type="TblResidentPersonnel" id="TblResidentPersonnelResult">
        <result property="fId"    column="F_Id"    />
        <result property="fOrgId"    column="F_org_id"    />
        <result property="fUscc"    column="F_uscc"    />
        <result property="fName"    column="F_name"    />
        <result property="fSex"    column="F_sex"    />
        <result property="fNation"    column="F_nation"    />
        <result property="fIdentityNumber"    column="F_identity_number"    />
        <result property="fMobilePhone"    column="F_mobile_phone"    />
        <result property="fEmail"    column="F_email"    />
        <result property="fServiceYears"    column="F_service_years"    />
        <result property="fPoliticalOutlook"    column="F_political_outlook"    />
        <result property="fPoliticalOutlookOther"    column="F_political_outlook_other"    />
        <result property="fHighestDegree"    column="F_highest_degree"    />
        <result property="fDuties"    column="F_duties"    />
        <result property="fPosition"    column="F_position"    />
        <result property="fConfidentialSoftware"    column="F_confidential_software"    />
        <result property="fAntivirusSoftware"    column="F_antivirus_software"    />
        <result property="fDualUseOne"    column="F_dual_use_one"    />
        <result property="fKeyPosition"    column="F_key_position"    />
        <result property="fBackgroundReview"    column="F_background_review"    />
        <result property="fPhotos"    column="F_photos"    />
        <result property="fBeStationed"    column="F_be_stationed"    />
        <result property="fTempleteId"    column="F_templete_id"    />
        <result property="fWorkContent"    column="F_work_content"    />
        <result property="fWorkContentOther"    column="F_work_content_other"    />
        <result property="fStationStart"    column="F_station_start"    />
        <result property="fStationEnd"    column="F_station_end"    />
        <result property="fGateCardId"    column="F_gate_card_id"    />
        <result property="fBasicInfo"    column="F_basic_info"    />
        <result property="fConfidentialityAgreement"    column="F_confidentiality_agreement"    />
        <result property="fIdCardCopy"    column="F_id_card_copy"    />
        <result property="fTenantid"    column="f_tenantid"    />
        <result property="fFlowid"    column="f_flowid"    />
    </resultMap>

    <resultMap id="TblResidentPersonnelTblPersonnelOverseasExperienceResult" type="TblResidentPersonnel" extends="TblResidentPersonnelResult">
        <collection property="tblPersonnelOverseasExperienceList" notNullColumn="sub_F_Id" javaType="java.util.List" resultMap="TblPersonnelOverseasExperienceResult" />
    </resultMap>

    <resultMap id="TblResidentPersonnelVoResult" type="com.ruoyi.monitor2.vo.TblResidentPersonnelVo" extends="TblResidentPersonnelTblPersonnelOverseasExperienceResult">
        <result property="templateName" column="template_name" />
        <result property="orgName" column="org_name" />
        <result property="orgNickName" column="org_nick_name" />
    </resultMap>

    <resultMap type="TblPersonnelOverseasExperience" id="TblPersonnelOverseasExperienceResult">
        <result property="fId"    column="sub_F_Id"    />
        <result property="fPersonnelId"    column="sub_F_personnel_id"    />
        <result property="fOverseasTime"    column="sub_F_overseas_time"    />
        <result property="fReturnTime"    column="sub_F_return_time"    />
        <result property="fOverseasReason"    column="sub_F_overseas_reason"    />
        <result property="fDestinationCity"    column="sub_F_destination_city"    />
        <result property="fTenantid"    column="sub_f_tenantid"    />
    </resultMap>

      <resultMap id="TblResidentPersonnelTblPersonnelCollaborationExperienceResult" type="TblResidentPersonnel" extends="TblResidentPersonnelResult">
        <collection property="tblPersonnelCollaborationExperienceList" notNullColumn="sub_F_Id" javaType="java.util.List" resultMap="TblPersonnelCollaborationExperienceResult" />
    </resultMap>

    <resultMap type="TblPersonnelCollaborationExperience" id="TblPersonnelCollaborationExperienceResult">
        <result property="fId"    column="sub_F_Id"    />
        <result property="fPersonnelId"    column="sub_F_personnel_id"    />
        <result property="fStartTime"    column="sub_F_start_time"    />
        <result property="fEndTime"    column="sub_F_end_time"    />
        <result property="fCooperatingAgency"    column="sub_F_cooperating_agency"    />
        <result property="fCooperatingDescription"    column="sub_F_cooperating_description"    />
        <result property="fWorkUnit"    column="sub_F_work_unit"    />
        <result property="fTenantid"    column="sub_f_tenantid"    />
    </resultMap>
    <sql id="selectTblResidentPersonnelVo">
        select F_Id, F_org_id, F_uscc, F_name, F_sex, F_nation, F_identity_number, F_mobile_phone, F_email, F_service_years, F_political_outlook, F_political_outlook_other, F_highest_degree, F_duties, F_position, F_confidential_software, F_antivirus_software, F_key_position, F_background_review, F_photos, F_be_stationed, F_templete_id, F_work_content, F_work_content_other, F_station_start, F_station_end, F_gate_card_id, F_basic_info, F_confidentiality_agreement, F_id_card_copy, f_tenantid, f_flowid from tbl_resident_personnel
    </sql>

    <select id="selectTblResidentPersonnelList" parameterType="TblResidentPersonnel" resultMap="TblResidentPersonnelResult">
        <include refid="selectTblResidentPersonnelVo"/>
        <where>
            <if test="fOrgId != null  and fOrgId != ''"> and F_org_id = #{fOrgId}</if>
            <if test="fUscc != null  and fUscc != ''"> and F_uscc = #{fUscc}</if>
            <if test="fName != null  and fName != ''"> and F_name like concat('%', #{fName}, '%')</if>
            <if test="fSex != null "> and F_sex = #{fSex}</if>
            <if test="fNation != null  and fNation != ''"> and F_nation = #{fNation}</if>
            <if test="fIdentityNumber != null  and fIdentityNumber != ''"> and F_identity_number = #{fIdentityNumber}</if>
            <if test="fMobilePhone != null  and fMobilePhone != ''"> and F_mobile_phone = #{fMobilePhone}</if>
            <if test="fEmail != null  and fEmail != ''"> and F_email = #{fEmail}</if>
            <if test="fServiceYears != null  and fServiceYears != ''"> and F_service_years = #{fServiceYears}</if>
            <if test="fPoliticalOutlook != null "> and F_political_outlook = #{fPoliticalOutlook}</if>
            <if test="fPoliticalOutlookOther != null  and fPoliticalOutlookOther != ''"> and F_political_outlook_other = #{fPoliticalOutlookOther}</if>
            <if test="fHighestDegree != null "> and F_highest_degree = #{fHighestDegree}</if>
            <if test="fDuties != null "> and F_duties = #{fDuties}</if>
            <if test="fPosition != null "> and F_position = #{fPosition}</if>
            <if test="fConfidentialSoftware != null "> and F_confidential_software = #{fConfidentialSoftware}</if>
            <if test="fAntivirusSoftware != null "> and F_antivirus_software = #{fAntivirusSoftware}</if>
            <if test="fDualUseOne != null "> and F_dual_use_one = #{fDualUseOne}</if>
            <if test="fKeyPosition != null "> and F_key_position = #{fKeyPosition}</if>
            <if test="fBackgroundReview != null "> and F_background_review = #{fBackgroundReview}</if>
            <if test="fPhotos != null  and fPhotos != ''"> and F_photos = #{fPhotos}</if>
            <if test="fBeStationed != null "> and F_be_stationed = #{fBeStationed}</if>
            <if test="fTempleteId != null "> and F_templete_id = #{fTempleteId}</if>
            <if test="fWorkContent != null  and fWorkContent != ''"> and F_work_content = #{fWorkContent}</if>
            <if test="fWorkContentOther != null  and fWorkContentOther != ''"> and F_work_content_other = #{fWorkContentOther}</if>
            <if test="fStationStart != null "> and F_station_start = #{fStationStart}</if>
            <if test="fStationEnd != null "> and F_station_end = #{fStationEnd}</if>
            <if test="fGateCardId != null  and fGateCardId != ''"> and F_gate_card_id = #{fGateCardId}</if>
            <if test="fBasicInfo != null  and fBasicInfo != ''"> and F_basic_info = #{fBasicInfo}</if>
            <if test="fConfidentialityAgreement != null  and fConfidentialityAgreement != ''"> and F_confidentiality_agreement = #{fConfidentialityAgreement}</if>
            <if test="fIdCardCopy != null  and fIdCardCopy != ''"> and F_id_card_copy = #{fIdCardCopy}</if>
            <if test="fTenantid != null  and fTenantid != ''"> and f_tenantid = #{fTenantid}</if>
            <if test="fFlowid != null  and fFlowid != ''"> and f_flowid = #{fFlowid}</if>
        </where>
    </select>

    <select id="selectTblResidentPersonnelByFId" parameterType="String" resultMap="TblResidentPersonnelTblPersonnelOverseasExperienceResult">
        select a.F_Id, a.F_org_id, a.F_uscc, a.F_name, a.F_sex, a.F_nation, a.F_identity_number, a.F_mobile_phone, a.F_email, a.F_service_years, a.F_political_outlook, a.F_political_outlook_other, a.F_highest_degree, a.F_duties, a.F_position, a.F_confidential_software,a.F_antivirus_software,a.F_dual_use_one, a.F_key_position, a.F_background_review, a.F_photos, a.F_be_stationed, a.F_templete_id, a.F_work_content, a.F_work_content_other, a.F_station_start, a.F_station_end, a.F_gate_card_id, a.F_basic_info, a.F_confidentiality_agreement, a.F_id_card_copy, a.f_tenantid, a.f_flowid,
 b.F_Id as sub_F_Id, b.F_personnel_id as sub_F_personnel_id, b.F_overseas_time as sub_F_overseas_time, b.F_return_time as sub_F_return_time, b.F_overseas_reason as sub_F_overseas_reason, b.F_destination_city as sub_F_destination_city, b.f_tenantid as sub_f_tenantid
        from tbl_resident_personnel a
        left join tbl_personnel_overseas_experience b on b.F_personnel_id = a.F_Id
        where a.F_Id = #{fId}
    </select>

    <select id="selectTblResidentPersonnelByFIds" parameterType="String" resultMap="TblResidentPersonnelVoResult">
        SELECT
            a.F_Id,
            a.F_org_id,
            a.F_uscc,
            a.F_name,
            a.F_sex,
            a.F_nation,
            a.F_identity_number,
            a.F_mobile_phone,
            a.F_email,
            a.F_service_years,
            a.F_political_outlook,
            a.F_political_outlook_other,
            a.F_highest_degree,
            a.F_duties,
            a.F_confidential_software,
            a.F_antivirus_software,
            a.F_dual_use_one,
            a.F_position,
            a.F_key_position,
            a.F_background_review,
            a.F_photos,
            a.F_be_stationed,
            a.F_templete_id,
            a.F_work_content,
            a.F_work_content_other,
            a.F_station_start,
            a.F_station_end,
            a.F_gate_card_id,
            a.F_basic_info,
            a.F_confidentiality_agreement,
            a.F_id_card_copy,
            a.f_tenantid,
            a.f_flowid,
            b.F_Id AS sub_F_Id,
            b.F_personnel_id AS sub_F_personnel_id,
            b.F_overseas_time AS sub_F_overseas_time,
            b.F_return_time AS sub_F_return_time,
            b.F_overseas_reason AS sub_F_overseas_reason,
            b.F_destination_city AS sub_F_destination_city,
            b.f_tenantid AS sub_f_tenantid,
            c.`name` template_name,
            d.F_org_full_name org_name,
            d.F_org_nick_name org_nick_name
        FROM
            tbl_resident_personnel a
            LEFT JOIN tbl_personnel_overseas_experience b ON b.F_personnel_id = a.F_Id
            LEFT JOIN tbl_seat_templete c ON c.id = a.F_templete_id
            LEFT JOIN tbl_partner_organization d ON d.F_Id = a.F_org_id
        where a.F_Id in
        <foreach item="fId" collection="array" open="(" separator="," close=")">
            #{fId}
        </foreach>
    </select>

    <insert id="insertTblResidentPersonnel" parameterType="TblResidentPersonnel">
        insert into tbl_resident_personnel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fId != null">F_Id,</if>
            <if test="fOrgId != null">F_org_id,</if>
            <if test="fUscc != null">F_uscc,</if>
            <if test="fName != null">F_name,</if>
            <if test="fSex != null">F_sex,</if>
            <if test="fNation != null">F_nation,</if>
            <if test="fIdentityNumber != null">F_identity_number,</if>
            <if test="fMobilePhone != null">F_mobile_phone,</if>
            <if test="fEmail != null">F_email,</if>
            <if test="fServiceYears != null">F_service_years,</if>
            <if test="fPoliticalOutlook != null">F_political_outlook,</if>
            <if test="fPoliticalOutlookOther != null">F_political_outlook_other,</if>
            <if test="fHighestDegree != null">F_highest_degree,</if>
            <if test="fDuties != null">F_duties,</if>
            <if test="fPosition != null">F_position,</if>
            <if test="fConfidentialSoftware != null">F_confidential_software,</if>
            <if test="fAntivirusSoftware != null">F_antivirus_software,</if>
            <if test="fDualUseOne != null">F_dual_use_one,</if>
            <if test="fKeyPosition != null">F_key_position,</if>
            <if test="fBackgroundReview != null">F_background_review,</if>
            <if test="fPhotos != null">F_photos,</if>
            <if test="fBeStationed != null">F_be_stationed,</if>
            <if test="fTempleteId != null">F_templete_id,</if>
            <if test="fWorkContent != null">F_work_content,</if>
            <if test="fWorkContentOther != null">F_work_content_other,</if>
            <if test="fStationStart != null">F_station_start,</if>
            <if test="fStationEnd != null">F_station_end,</if>
            <if test="fGateCardId != null">F_gate_card_id,</if>
            <if test="fBasicInfo != null">F_basic_info,</if>
            <if test="fConfidentialityAgreement != null">F_confidentiality_agreement,</if>
            <if test="fIdCardCopy != null">F_id_card_copy,</if>
            <if test="fTenantid != null">f_tenantid,</if>
            <if test="fFlowid != null">f_flowid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fId != null">#{fId},</if>
            <if test="fOrgId != null">#{fOrgId},</if>
            <if test="fUscc != null">#{fUscc},</if>
            <if test="fName != null">#{fName},</if>
            <if test="fSex != null">#{fSex},</if>
            <if test="fNation != null">#{fNation},</if>
            <if test="fIdentityNumber != null">#{fIdentityNumber},</if>
            <if test="fMobilePhone != null">#{fMobilePhone},</if>
            <if test="fEmail != null">#{fEmail},</if>
            <if test="fServiceYears != null">#{fServiceYears},</if>
            <if test="fPoliticalOutlook != null">#{fPoliticalOutlook},</if>
            <if test="fPoliticalOutlookOther != null">#{fPoliticalOutlookOther},</if>
            <if test="fHighestDegree != null">#{fHighestDegree},</if>
            <if test="fDuties != null">#{fDuties},</if>
            <if test="fPosition != null">#{fPosition},</if>
            <if test="fConfidentialSoftware != null">#{fConfidentialSoftware},</if>
            <if test="fAntivirusSoftware != null">#{fAntivirusSoftware},</if>
            <if test="fDualUseOne != null">#{fDualUseOne},</if>
            <if test="fKeyPosition != null">#{fKeyPosition},</if>
            <if test="fBackgroundReview != null">#{fBackgroundReview},</if>
            <if test="fPhotos != null">#{fPhotos},</if>
            <if test="fBeStationed != null">#{fBeStationed},</if>
            <if test="fTempleteId != null">#{fTempleteId},</if>
            <if test="fWorkContent != null">#{fWorkContent},</if>
            <if test="fWorkContentOther != null">#{fWorkContentOther},</if>
            <if test="fStationStart != null">#{fStationStart},</if>
            <if test="fStationEnd != null">#{fStationEnd},</if>
            <if test="fGateCardId != null">#{fGateCardId},</if>
            <if test="fBasicInfo != null">#{fBasicInfo},</if>
            <if test="fConfidentialityAgreement != null">#{fConfidentialityAgreement},</if>
            <if test="fIdCardCopy != null">#{fIdCardCopy},</if>
            <if test="fTenantid != null">#{fTenantid},</if>
            <if test="fFlowid != null">#{fFlowid},</if>
         </trim>
    </insert>

    <update id="updateTblResidentPersonnel" parameterType="TblResidentPersonnel">
        update tbl_resident_personnel
        <trim prefix="SET" suffixOverrides=",">
            <if test="fOrgId != null">F_org_id = #{fOrgId},</if>
            <if test="fUscc != null">F_uscc = #{fUscc},</if>
            <if test="fName != null">F_name = #{fName},</if>
            <if test="fSex != null">F_sex = #{fSex},</if>
            <if test="fNation != null">F_nation = #{fNation},</if>
            <if test="fIdentityNumber != null">F_identity_number = #{fIdentityNumber},</if>
            <if test="fMobilePhone != null">F_mobile_phone = #{fMobilePhone},</if>
            <if test="fEmail != null">F_email = #{fEmail},</if>
            <if test="fServiceYears != null">F_service_years = #{fServiceYears},</if>
            <if test="fPoliticalOutlook != null">F_political_outlook = #{fPoliticalOutlook},</if>
            <if test="fPoliticalOutlookOther != null">F_political_outlook_other = #{fPoliticalOutlookOther},</if>
            <if test="fHighestDegree != null">F_highest_degree = #{fHighestDegree},</if>
            <if test="fDuties != null">F_duties = #{fDuties},</if>
            <if test="fPosition != null">F_position = #{fPosition},</if>
            <if test="fConfidentialSoftware != null">F_confidential_software = #{fConfidentialSoftware},</if>
            <if test="fAntivirusSoftware != null">F_antivirus_software = #{fAntivirusSoftware},</if>
            <if test="fDualUseOne != null">F_dual_use_one = #{fDualUseOne},</if>
            <if test="fKeyPosition != null">F_key_position = #{fKeyPosition},</if>
            <if test="fBackgroundReview != null">F_background_review = #{fBackgroundReview},</if>
            <if test="fPhotos != null">F_photos = #{fPhotos},</if>
            <if test="fBeStationed != null">F_be_stationed = #{fBeStationed},</if>
            <if test="fTempleteId != null">F_templete_id = #{fTempleteId},</if>
            <if test="fWorkContent != null">F_work_content = #{fWorkContent},</if>
            <if test="fWorkContentOther != null">F_work_content_other = #{fWorkContentOther},</if>
            <if test="fStationStart != null">F_station_start = #{fStationStart},</if>
            <if test="fStationEnd != null">F_station_end = #{fStationEnd},</if>
            <if test="fGateCardId != null">F_gate_card_id = #{fGateCardId},</if>
            <if test="fBasicInfo != null">F_basic_info = #{fBasicInfo},</if>
            <if test="fConfidentialityAgreement != null">F_confidentiality_agreement = #{fConfidentialityAgreement},</if>
            <if test="fIdCardCopy != null">F_id_card_copy = #{fIdCardCopy},</if>
            <if test="fTenantid != null">f_tenantid = #{fTenantid},</if>
            <if test="fFlowid != null">f_flowid = #{fFlowid},</if>
        </trim>
        where F_Id = #{fId}
    </update>

    <delete id="deleteTblResidentPersonnelByFId" parameterType="String">
        delete from tbl_resident_personnel where F_Id = #{fId}
    </delete>

    <delete id="deleteTblResidentPersonnelByFIds" parameterType="String">
        delete from tbl_resident_personnel where F_Id in
        <foreach item="fId" collection="array" open="(" separator="," close=")">
            #{fId}
        </foreach>
    </delete>

    <delete id="deleteTblPersonnelOverseasExperienceByFPersonnelIds" parameterType="String">
        delete from tbl_personnel_overseas_experience where F_personnel_id in
        <foreach item="fPersonnelId" collection="array" open="(" separator="," close=")">
            #{fPersonnelId}
        </foreach>
    </delete>

    <delete id="deleteTblPersonnelOverseasExperienceByFPersonnelId" parameterType="String">
        delete from tbl_personnel_overseas_experience where F_personnel_id = #{fPersonnelId}
    </delete>

    <insert id="batchTblPersonnelOverseasExperience">
        insert into tbl_personnel_overseas_experience( F_Id, F_personnel_id, F_overseas_time, F_return_time, F_overseas_reason, F_destination_city, f_tenantid) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.fId}, #{item.fPersonnelId}, #{item.fOverseasTime}, #{item.fReturnTime}, #{item.fOverseasReason}, #{item.fDestinationCity}, #{item.fTenantid})
        </foreach>
    </insert>




    <delete id="deleteTblPersonnelCollaborationExperienceByFPersonnelIds" parameterType="String">
        delete from tbl_personnel_collaboration_experience where F_personnel_id in
        <foreach item="fPersonnelId" collection="array" open="(" separator="," close=")">
            #{fPersonnelId}
        </foreach>
    </delete>

    <delete id="deleteTblPersonnelCollaborationExperienceByFPersonnelId" parameterType="String">
        delete from tbl_personnel_collaboration_experience where F_personnel_id = #{fPersonnelId}
    </delete>

    <insert id="batchTblPersonnelCollaborationExperience">
        insert into tbl_personnel_collaboration_experience( F_Id, F_personnel_id, F_start_time, F_end_time, F_cooperating_agency, F_cooperating_description, F_work_unit, f_tenantid) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.fId}, #{item.fPersonnelId}, #{item.fStartTime}, #{item.fEndTime}, #{item.fCooperatingAgency}, #{item.fCooperatingDescription}, #{item.fWorkUnit}, #{item.fTenantid})
        </foreach>
    </insert>

</mapper>
