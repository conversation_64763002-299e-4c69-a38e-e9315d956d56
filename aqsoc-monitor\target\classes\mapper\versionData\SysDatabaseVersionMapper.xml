<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.version.mapper.SysDatabaseVersionMapper">

    <resultMap type="SysDatabaseVersion" id="SysDatabaseVersionResult">
        <result property="itemVersion"    column="item_version"    />
        <result property="dbVersion"    column="db_version"    />
        <result property="timeline"    column="timeline"    />
        <result property="desction"    column="desction"    />
        <result property="createTime"    column="create_time"    />
        <result property="creator"    column="creator"    />
    </resultMap>

    <sql id="selectSysDatabaseVersionVo">
        select item_version, db_version, timeline, desction, create_time, creator from sys_database_version
    </sql>

    <select id="selectSysDatabaseVersionList" parameterType="SysDatabaseVersion" resultMap="SysDatabaseVersionResult">
        <include refid="selectSysDatabaseVersionVo"/>
        <where>
            <if test="itemVersion != null  and itemVersion != ''"> and item_version = #{itemVersion}</if>
            <if test="dbVersion != null  and dbVersion != ''"> and db_version = #{dbVersion}</if>
            <if test="timeline != null "> and timeline = #{timeline}</if>
            <if test="desction != null  and desction != ''"> and desction = #{desction}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
        </where>
        ORDER BY timeline DESC
    </select>

    <select id="selectSysDatabaseVersionByItemVersion" parameterType="String" resultMap="SysDatabaseVersionResult">
        <include refid="selectSysDatabaseVersionVo"/>
        where item_version = #{itemVersion}
    </select>

    <select id="selectSysDatabaseVersionByItemVersions" parameterType="String" resultMap="SysDatabaseVersionResult">
        <include refid="selectSysDatabaseVersionVo"/>
        where item_version in
        <foreach item="itemVersion" collection="array" open="(" separator="," close=")">
            #{itemVersion}
        </foreach>
    </select>

    <insert id="insertSysDatabaseVersion" parameterType="SysDatabaseVersion">
        insert into sys_database_version
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemVersion != null">item_version,</if>
            <if test="dbVersion != null">db_version,</if>
            <if test="timeline != null">timeline,</if>
            <if test="desction != null">desction,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemVersion != null">#{itemVersion},</if>
            <if test="dbVersion != null">#{dbVersion},</if>
            <if test="timeline != null">#{timeline},</if>
            <if test="desction != null">#{desction},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
         </trim>
    </insert>

    <update id="updateSysDatabaseVersion" parameterType="SysDatabaseVersion">
        update sys_database_version
        <trim prefix="SET" suffixOverrides=",">
            <if test="dbVersion != null">db_version = #{dbVersion},</if>
            <if test="timeline != null">timeline = #{timeline},</if>
            <if test="desction != null">desction = #{desction},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creator != null">creator = #{creator},</if>
        </trim>
        where item_version = #{itemVersion}
    </update>

    <delete id="deleteSysDatabaseVersionByItemVersion" parameterType="String">
        delete from sys_database_version where item_version = #{itemVersion}
    </delete>

    <delete id="deleteSysDatabaseVersionByItemVersions" parameterType="String">
        delete from sys_database_version where item_version in
        <foreach item="itemVersion" collection="array" open="(" separator="," close=")">
            #{itemVersion}
        </foreach>
    </delete>
</mapper>
