<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.threaten.mapper.TblThreatenInfoMapper">

    <resultMap type="TblThreatenInfo" id="TblThreatenInfoResult">
        <result property="id"    column="id"    />
        <result property="attackIp"    column="attack_ip"    />
        <result property="threatenLevel"    column="threaten_level"    />
        <result property="attackPort"    column="attack_port"    />
        <result property="procotol"    column="procotol"    />
        <result property="analysis"    column="analysis"    />
    </resultMap>

    <sql id="selectTblThreatenInfoVo">
        select id, attack_ip, threaten_level, attack_port, procotol, analysis from tbl_threaten_info
    </sql>

    <select id="selectTblThreatenInfoList" parameterType="TblThreatenInfo" resultMap="TblThreatenInfoResult">
        <include refid="selectTblThreatenInfoVo"/>
        <where>
            <if test="attackIp != null  and attackIp != ''"> and attack_ip = #{attackIp}</if>
            <if test="threatenLevel != null "> and threaten_level = #{threatenLevel}</if>
            <if test="attackPort != null  and attackPort != ''"> and attack_port like concat('%', #{attackPort},'%')</if>
            <if test="procotol != null  and procotol != ''"> and procotol like concat('%', #{procotol},'%')</if>
            <if test="analysis != null  and analysis != ''"> and analysis = #{analysis}</if>
        </where>
        order by id desc
    </select>

    <select id="selectTblThreatenInfoById" parameterType="Integer" resultMap="TblThreatenInfoResult">
        <include refid="selectTblThreatenInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectTblThreatenInfoByIds" parameterType="Integer" resultMap="TblThreatenInfoResult">
        <include refid="selectTblThreatenInfoVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblThreatenInfo" parameterType="TblThreatenInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_threaten_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="attackIp != null">attack_ip,</if>
            <if test="threatenLevel != null">threaten_level,</if>
            <if test="attackPort != null">attack_port,</if>
            <if test="procotol != null">procotol,</if>
            <if test="analysis != null">analysis,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="attackIp != null">#{attackIp},</if>
            <if test="threatenLevel != null">#{threatenLevel},</if>
            <if test="attackPort != null">#{attackPort},</if>
            <if test="procotol != null">#{procotol},</if>
            <if test="analysis != null">#{analysis},</if>
         </trim>
    </insert>

    <update id="updateTblThreatenInfo" parameterType="TblThreatenInfo">
        update tbl_threaten_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="attackIp != null">attack_ip = #{attackIp},</if>
            <if test="1==1">threaten_level = #{threatenLevel},</if>
            <if test="attackPort != null">attack_port = #{attackPort},</if>
            <if test="procotol != null">procotol = #{procotol},</if>
            <if test="analysis != null">analysis = #{analysis},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblThreatenInfoById" parameterType="Integer">
        delete from tbl_threaten_info where id = #{id}
    </delete>

    <delete id="deleteTblThreatenInfoByIds" parameterType="String">
        delete from tbl_threaten_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <insert id="insertTblThreatenInfoList" parameterType="list" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_threaten_info (attack_ip,threaten_level,attack_port,procotol,analysis) values
        <foreach collection="list" item="item" separator=",">
            (#{item.attackIp}, #{item.threatenLevel},#{item.attackPort},#{item.procotol},#{item.analysis} )
        </foreach>
    </insert>
</mapper>
