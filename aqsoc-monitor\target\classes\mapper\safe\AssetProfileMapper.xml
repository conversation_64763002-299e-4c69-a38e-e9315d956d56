<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.AssetProfileMapper">

    <select id="getBaseStatistics" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT t1.num applicationCount,t2.num serverCount,t3.num networkCount,t4.num safeCount,t5.num boundaryCount,t6.num terminalCount FROM
            (SELECT
                 COUNT( 1 ) num
             FROM
                 tbl_business_application t1
                     LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
             <where>
                 <if test="deptId != null">
                     AND (t1.dept_id = #{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
                 </if>
             </where>
            ) t1,
            (SELECT
                 COUNT( 1 ) num
             FROM
                 tbl_server t1
                     LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
            <where>
                <if test="deptId != null">
                    AND (t1.dept_id = #{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
                </if>
            </where>
            ) t2,
            (SELECT
                 COUNT( 1 ) num
             FROM
                 tbl_network_devices t1
                 LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
            <where>
                <if test="deptId != null">
                    AND (t1.dept_id = #{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
                </if>
            </where>
            ) t3,
            (SELECT
                 COUNT( 1 ) num
             FROM
                 tbl_safety t1
                 LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
             <where>
                 <if test="deptId != null">
                     AND (t1.dept_id = #{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
                 </if>
             </where>
             ) t4,
            (SELECT
                 COUNT( 1 ) num
             FROM
                 tbl_zone_boundary t1
                     LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
            <where>
                <if test="deptId != null">
                    AND (t1.dept_id = #{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
                </if>
            </where>
            ) t5,
            (SELECT
            COUNT( 1 ) num
            FROM
            tbl_terminal t1
            LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
            <where>
                <if test="deptId != null">
                    AND (t1.dept_id = #{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
                </if>
            </where>
            ) t6
    </select>
    <select id="getNetworkDomainCount" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
          t1.domain_name domainName,t1.domain_id domainId,COUNT(DISTINCT t2.asset_id) applicationCount,COUNT(DISTINCT t7.id) ipVulnCount,COUNT(DISTINCT t8.id) webVulnCount
        FROM
          tbl_network_domain t1
          LEFT JOIN sys_dept sd ON sd.dept_id=t1.dept_id
          LEFT JOIN tbl_network_ip_mac t6 ON t6.domain_id=t1.domain_id
          LEFT JOIN tbl_application_server t4 ON t4.server_id=t6.asset_id
          LEFT JOIN tbl_business_application t2 ON (t2.domain_id=t1.domain_id OR t2.asset_id=t4.asset_id)
          LEFT JOIN monitor_bss_vuln_deal t7 ON t7.host_ip=t6.ipv4 and t7.handle_state=0
          LEFT JOIN monitor_bss_webvuln_deal t8 ON t8.web_url=t2.url and t8.handle_state=0
        <where>
            t1.is_last_level = 1
            <if test="deptId != null">
                AND (t1.dept_id = #{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
            </if>
            <if test="applicationId != null">
                AND t2.asset_id=#{applicationId}
            </if>
        </where>
        GROUP BY
            t1.domain_id
    </select>
    <select id="getAssetOverview" resultType="com.alibaba.fastjson2.JSONObject">
        select * from
        (SELECT COUNT(DISTINCT t1.id) threatenCount FROM tbl_threaten_alarm t1
        <where>
            t1.handle_state=0
            <if test="ipv4List != null and ipv4List.size()>0">
                AND (
                t1.src_ip in
                <foreach collection="ipv4List" item="ipv4" separator="," open="(" close=")">
                    #{ipv4}
                </foreach>
                 OR
                t1.dest_ip in
                <foreach collection="ipv4List" item="ipv4" separator="," open="(" close=")">
                    #{ipv4}
                </foreach>
                )
            </if>
        </where> ) t2,
        (SELECT COUNT(DISTINCT t1.domain_id) networkDomainCount FROM tbl_network_domain t1
        LEFT JOIN sys_dept sd ON sd.dept_id=t1.dept_id
        LEFT JOIN tbl_network_ip_mac t2 ON t2.domain_id=t1.domain_id
        LEFT JOIN tbl_application_server t3 ON t3.server_id=t2.asset_id
        LEFT JOIN tbl_business_application tba ON (tba.asset_id=t2.asset_id OR tba.asset_id=t3.asset_id)
        <where> t1.is_last_level=1 <if test="deptId != null">AND (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors)) </if> <if test="applicationId != null">AND tba.asset_id=#{applicationId}</if> </where> ) t3,
        (SELECT COUNT(DISTINCT t1.prdid) systemCount FROM tbl_product t1 LEFT JOIN tbl_deploy t2 ON t2.prdid=t1.prdid LEFT JOIN tbl_server t3 ON t3.asset_id=t2.asset_id LEFT JOIN sys_dept sd ON sd.dept_id=t3.dept_id
        LEFT JOIN tbl_application_server t4 ON t4.server_id=t3.asset_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id=t4.asset_id
        WHERE t1.proc_type='system' <if test="deptId != null">AND (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors)) </if> <if test="applicationId != null">AND tba.asset_id=#{applicationId}</if> ) t4,
        (SELECT COUNT(DISTINCT t1.prdid) middlewareCount FROM tbl_product t1 LEFT JOIN tbl_deploy t2 ON t2.prdid=t1.prdid LEFT JOIN tbl_server t3 ON t3.asset_id=t2.asset_id LEFT JOIN sys_dept sd ON sd.dept_id=t3.dept_id
        LEFT JOIN tbl_application_server t4 ON t4.server_id=t3.asset_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id=t4.asset_id
        WHERE t1.proc_type='middleware' <if test="deptId != null">AND (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors)) </if> <if test="applicationId != null">AND tba.asset_id=#{applicationId}</if> ) t9,
        (select count(temp.id) portCount from (SELECT t1.id FROM tbl_application_server t1
        LEFT JOIN tbl_server t3 ON t3.asset_id = t1.server_id
        LEFT JOIN sys_dept sd ON sd.dept_id = t3.dept_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id = t1.asset_id
        <where>t1.`port` IS NOT NULL <if test="deptId != null">AND (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors)) </if> <if test="applicationId != null">AND tba.asset_id=#{applicationId}</if> </where> GROUP BY t1.`port`) temp) t5,
        (SELECT COUNT(DISTINCT t1.id) firewallNatCount FROM tbl_firewall_nat t1 LEFT JOIN tbl_safety t2 ON t2.asset_id=t1.asset_id LEFT JOIN sys_dept sd ON sd.dept_id=t2.dept_id
        <where><if test="deptId != null">AND (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors)) </if> <if test="applicationId != null">and find_in_set(#{applicationId},t1.business_application_id)</if> </where> ) t6,
        (SELECT COUNT(1) linkCount FROM tbl_application_link t1 LEFT JOIN tbl_business_application t2 ON t2.asset_id=t1.asset_id LEFT JOIN sys_dept sd ON sd.dept_id=t2.dept_id
        <where><if test="deptId != null">AND (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors)) </if> <if test="applicationId != null">AND t2.asset_id=#{applicationId}</if> </where> ) t7
        <!-- (SELECT COUNT(DISTINCT t1.id) workOrderCount FROM tbl_work_order t1 LEFT JOIN sys_dept sd ON sd.dept_id=t1.handle_dept
        <where><if test="deptId != null">AND (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors)) </if> <if test="applicationId != null">AND t1.application_id=#{applicationId}</if> </where> ) t8 -->
        <if test="applicationId != null">
            ,(SELECT COUNT(DISTINCT t1.asset_id) serverCount FROM tbl_server t1 LEFT JOIN tbl_application_server t2 ON t2.server_id=t1.asset_id LEFT JOIN tbl_business_application tba ON tba.asset_id=t2.asset_id where tba.asset_id=#{applicationId}) t10
        </if>
    </select>
    <select id="getThreatenAlarmCount" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            COUNT(1) threatenAlarmCount,src_ip,dest_ip
        FROM
            tbl_threaten_alarm
        WHERE
            handle_state=0
            <if test="ipv4List != null and ipv4List.size()>0">
                AND (
                src_ip in
                <foreach collection="ipv4List" item="ipv4" separator="," open="(" close=")">
                    #{ipv4}
                </foreach>
                 OR
                dest_ip in
                <foreach collection="ipv4List" item="ipv4" separator="," open="(" close=")">
                    #{ipv4}
                </foreach>
                )
            </if>
        GROUP BY
            src_ip,dest_ip
    </select>
</mapper>
