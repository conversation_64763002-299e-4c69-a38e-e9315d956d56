<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.MonitorBssWpDealMapper">

    <resultMap type="MonitorBssWpDeal" id="MonitorBssWpDealResult">
        <result property="id"    column="id"    />
        <result property="hostIp"    column="host_ip"    />
        <result property="assetId"    column="asset_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="hostPort"    column="host_port"    />
        <result property="serviceType"    column="service_type"    />
        <result property="username"    column="username"    />
        <result property="weakPassword"    column="weak_password"    />
        <result property="num"    column="num"    />
        <result property="handleState"    column="handle_state"    />
        <result property="handleDesc"    column="handle_desc"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deptName"    column="dept_name"    />
        <result property="disposer"    column="disposer"    />
        <result property="synchronizationStatus"    column="synchronization_status"    />
        <collection property="businessApplications" column="query_asset_id" select="com.ruoyi.safe.mapper.TblBusinessApplicationMapper.selectListByServerId" />
    </resultMap>

    <sql id="selectMonitorBssWpDealVo">
        select id, host_ip, asset_id, dept_id, host_port, service_type, username, weak_password, num, handle_state, handle_desc, create_time, update_time from monitor_bss_wp_deal
    </sql>

    <select id="selectMonitorBssWpDealList" parameterType="MonitorBssWpDeal" resultMap="MonitorBssWpDealResult">
        SELECT wpd.id, wpd.disposer, wpd.synchronization_status, wpd.host_ip, wpd.asset_id, wpd.host_port, wpd.service_type, wpd.username, wpd.weak_password, wpd.num, wpd.handle_state, wpd.handle_desc, wpd.create_time, wpd.update_time,GROUP_CONCAT(sd.dept_name) dept_name,wpd.dept_id,GROUP_CONCAT(nim.asset_id) query_asset_id  from monitor_bss_wp_deal wpd
        LEFT JOIN tbl_network_ip_mac nim on nim.ipv4=wpd.host_ip
        LEFT JOIN tbl_asset_overview tao on tao.asset_id=nim.asset_id
        LEFT JOIN sys_dept sd on sd.dept_id=tao.dept_id
        <where>
            <if test="disposers != null and disposers.size() > 0">
                wpd.disposer in
                <foreach item="disposer" collection="disposers" open="(" separator="," close=")">
                    #{disposer}
                </foreach>
            </if>
            <if test="synchronizationStatus != null  and synchronizationStatus != ''"> and wpd.synchronization_status = #{synchronizationStatus}</if>
            <if test="hostIp != null  and hostIp != ''"> and wpd.host_ip = #{hostIp}</if>
            <if test="assetId != null "> and wpd.asset_id = #{assetId}</if>
            <if test="deptId != null "> and (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))</if>
            <if test="hostPort != null "> and wpd.host_port = #{hostPort}</if>
            <if test="serviceType != null  and serviceType != ''"> and wpd.service_type = #{serviceType}</if>
            <if test="username != null  and username != ''"> and wpd.username like concat('%', #{username}, '%')</if>
            <if test="weakPassword != null  and weakPassword != ''"> and wpd.weak_password = #{weakPassword}</if>
            <if test="num != null "> and wpd.num = #{num}</if>
            <if test="handleState != null "> and wpd.handle_state = #{handleState}</if>
            <if test="handleDesc != null  and handleDesc != ''"> and wpd.handle_desc = #{handleDesc}</if>
            <if test="ids != null">
                and wpd.id in
                <foreach item="idItem" collection="ids" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="notIds != null and notIds.size()>0">
                and wpd.id not in
                <foreach item="idItem" collection="notIds" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="startTime != null">
                and wpd.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and wpd.create_time &lt;= #{endTime}
            </if>
            <if test="params.dataScope != null and params.dataScope != ''"> ${params.dataScope}</if>
        </where>
        group by wpd.id
        order by wpd.update_time desc
    </select>

    <select id="findMonitorBssWpDealList" parameterType="MonitorBssWpDeal" resultMap="MonitorBssWpDealResult">
        <include refid="selectMonitorBssWpDealVo"/>
        <where>
            <if test="hostIp != null  and hostIp != ''"> and host_ip = #{hostIp}</if>
            <if test="hostPort != null "> and host_port = #{hostPort}</if>
            <if test="serviceType != null  and serviceType != ''"> and service_type = #{serviceType}</if>
            <if test="username != null  and username != ''"> and username like concat('%', #{username}, '%')</if>
            <if test="weakPassword != null  and weakPassword != ''"> and weak_password = #{weakPassword}</if>
            and handle_state != 1
        </where>
    </select>

    <select id="selectMonitorBssWpDealById" parameterType="Long" resultMap="MonitorBssWpDealResult">
        <include refid="selectMonitorBssWpDealVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorBssWpDealByIds" parameterType="Long" resultMap="MonitorBssWpDealResult">
        <include refid="selectMonitorBssWpDealVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMonitorBssWpDeal" parameterType="MonitorBssWpDeal" useGeneratedKeys="true" keyProperty="id">
        insert into monitor_bss_wp_deal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hostIp != null">host_ip,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="hostPort != null">host_port,</if>
            <if test="serviceType != null">service_type,</if>
            <if test="username != null">username,</if>
            <if test="weakPassword != null">weak_password,</if>
            <if test="num != null">num,</if>
            <if test="handleState != null">handle_state,</if>
            <if test="handleDesc != null">handle_desc,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="disposer != null">disposer,</if>
            <if test="synchronizationStatus != null">synchronization_status,</if>
            <if test="dataId != null">data_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hostIp != null">#{hostIp},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="hostPort != null">#{hostPort},</if>
            <if test="serviceType != null">#{serviceType},</if>
            <if test="username != null">#{username},</if>
            <if test="weakPassword != null">#{weakPassword},</if>
            <if test="num != null">#{num},</if>
            <if test="handleState != null">#{handleState},</if>
            <if test="handleDesc != null">#{handleDesc},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="disposer != null">#{disposer},</if>
            <if test="synchronizationStatus != null">#{synchronizationStatus},</if>
            <if test="dataId != null">#{dataId},</if>
        </trim>
    </insert>

    <update id="updateMonitorBssWpDeal" parameterType="MonitorBssWpDeal">
        update monitor_bss_wp_deal
        <trim prefix="SET" suffixOverrides=",">
            <if test="hostIp != null">host_ip = #{hostIp},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="hostPort != null">host_port = #{hostPort},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="username != null">username = #{username},</if>
            <if test="weakPassword != null">weak_password = #{weakPassword},</if>
            <if test="num != null">num = #{num},</if>
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="handleDesc != null">handle_desc = #{handleDesc},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="disposer != null">disposer = #{disposer},</if>
            <if test="synchronizationStatus != null">synchronization_status = #{synchronizationStatus},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchUpdateHandleState">
        UPDATE monitor_bss_wp_deal SET
        handle_state = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.handleState != null">#{item.handleState}</if> <if test="item.handleState == null">handle_state</if>
        </foreach>
        ELSE handle_state
        END,
        disposer = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.disposer != null">#{item.disposer}</if> <if test="item.disposer == null">disposer</if>
        </foreach>
        ELSE disposer
        END,
        handle_desc = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.handleDesc != null">#{item.handleDesc}</if> <if test="item.handleDesc == null">handle_desc</if>
        </foreach>
        ELSE handle_desc
        END
        where id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <delete id="deleteMonitorBssWpDealById" parameterType="Long">
        delete from monitor_bss_wp_deal where id = #{id}
    </delete>

    <delete id="deleteMonitorBssWpDealByIds" parameterType="String">
        delete from monitor_bss_wp_deal where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateBatchMonitorBssWpDeal" parameterType="MonitorBssWpDeal">
        update monitor_bss_wp_deal
        <trim prefix="SET" suffixOverrides=",">
            <if test="hostIp != null">host_ip = #{hostIp},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="hostPort != null">host_port = #{hostPort},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="username != null">username = #{username},</if>
            <if test="weakPassword != null">weak_password = #{weakPassword},</if>
            <if test="num != null">num = #{num},</if>
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="handleDesc != null">handle_desc = #{handleDesc},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="getHandleStateStat" resultType="java.util.HashMap">
        select d.handle_state, count(wd.id) as num from (
            select 0 as handle_state union all
            select 1 union all
            select 2 union all
            select 3
        ) d left join monitor_bss_wp_deal wd on d.handle_state = wd.handle_state
            LEFT JOIN (
        SELECT
        *
        FROM
        tbl_network_ip_mac
        GROUP BY
        ipv4
        ) nim on nim.ipv4=wd.host_ip
            LEFT JOIN tbl_asset_overview tao on tao.asset_id=nim.asset_id
            LEFT JOIN sys_dept sd on sd.dept_id=tao.dept_id
        <where>
            <if test="params.dataScope != null and params.dataScope != ''"> ${params.dataScope}</if>
        </where>
        group by d.handle_state
        order by d.handle_state desc
    </select>
    <select id="selectNoSyncList" resultType="com.ruoyi.monitor2.domain.MonitorBssWpDeal">
        select * from monitor_bss_wp_deal where synchronization_status = 0 OR synchronization_status is null
    </select>

    <select id="countMonitorBssWpNum" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT wpd.id)
        FROM
            monitor_bss_wp_deal wpd
                LEFT JOIN tbl_network_ip_mac nim ON nim.ipv4 = wpd.host_ip
                LEFT JOIN tbl_asset_overview tao on tao.asset_id=nim.asset_id
                LEFT JOIN sys_dept sd on sd.dept_id=tao.dept_id
        <where>
            <if test="deptId != 100 and deptId != null">
                (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
            </if>
            <if test="startTime != null and endTime != null">
                and wpd.update_time between #{startTime} and #{endTime}
            </if>
        </where>
    </select>
</mapper>

