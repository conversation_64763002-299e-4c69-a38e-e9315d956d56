-- =====================================================
-- 威胁告警攻击方向字段添加升级脚本
-- 创建时间：2025-01-28
-- 说明：在tbl_threaten_alarm表中添加attack_direction字段及相关数据字典
-- =====================================================

-- 1. 添加攻击方向字段到威胁告警表
ALTER TABLE tbl_threaten_alarm ADD COLUMN attack_direction VARCHAR(10) DEFAULT '4' COMMENT '攻击方向：1-内对内，2-内对外，3-外对内，4-未知';

-- 2. 插入攻击方向字典类型
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES
('攻击方向', 'attack_direction', '0', 'admin', NOW(), '威胁告警攻击方向字典');

-- 3. 插入攻击方向字典数据
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '内对内', '1', 'attack_direction', '', 'primary', 'N', '0', 'admin', NOW(), '内网到内网的攻击'),
(2, '内对外', '2', 'attack_direction', '', 'success', 'N', '0', 'admin', NOW(), '内网到外网的攻击'),
(3, '外对内', '3', 'attack_direction', '', 'warning', 'N', '0', 'admin', NOW(), '外网到内网的攻击'),
(4, '未知', '4', 'attack_direction', '', 'info', 'Y', '0', 'admin', NOW(), '无法判断攻击方向');

-- 4. 验证脚本执行结果
-- 检查字段是否添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_NAME = 'tbl_threaten_alarm' AND COLUMN_NAME = 'attack_direction';

-- 检查数据字典是否创建成功
-- SELECT * FROM sys_dict_type WHERE dict_type = 'attack_direction';
-- SELECT * FROM sys_dict_data WHERE dict_type = 'attack_direction' ORDER BY dict_sort;
