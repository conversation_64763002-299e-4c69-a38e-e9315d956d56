<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rule.mapper.TblRuleManagementMapper">
    
    <resultMap type="TblRuleManagement" id="TblRuleManagementResult">
        <result property="id"    column="id"    />
        <result property="tid"    column="tid"    />
        <result property="typeName"    column="type_name"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="registerTime"    column="register_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="cilentId"    column="cilent_id"    />
    </resultMap>

    <sql id="selectTblRuleManagementVo">
        select id, a.tid, b.name as type_name,title,content, register_time, a.create_time, a.create_by, a.update_time, a.update_by, a.remark, a.user_id, a.dept_id, a.cilent_id from tbl_rule_management a
        left join tbl_rule_management_tree b on a.tid = b.tid
    </sql>

    <select id="selectTblRuleManagementList" parameterType="TblRuleManagement" resultMap="TblRuleManagementResult">
        <include refid="selectTblRuleManagementVo"/>
        <where>  
            <if test="tid != null "> and a.tid = #{tid}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="registerTime != null "> and register_time = #{registerTime}</if>
        </where>
    </select>
    <select id="selectTblRuleManagementByTids" parameterType="list" resultMap="TblRuleManagementResult">
        <include refid="selectTblRuleManagementVo"/>
        <where>
            <if test="tids!=null " >
                a.tid in
                <foreach collection="tids" item="tid" index="index" open="(" close=")" separator=",">
                    #{tid}
                </foreach>
            </if>
            <if test="tid != null "> and a.tid = #{tid}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="registerTime != null "> and register_time = #{registerTime}</if>
        </where>
    </select>
    <select id="selectTblRuleManagementById" parameterType="Long" resultMap="TblRuleManagementResult">
        <include refid="selectTblRuleManagementVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblRuleManagement" parameterType="TblRuleManagement" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_rule_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tid != null">tid,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="registerTime != null">register_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="cilentId != null">cilent_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tid != null">#{tid},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="registerTime != null">#{registerTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="cilentId != null">#{cilentId},</if>
         </trim>
    </insert>

    <update id="updateTblRuleManagement" parameterType="TblRuleManagement">
        update tbl_rule_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="tid != null">tid = #{tid},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="cilentId != null">cilent_id = #{cilentId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblRuleManagementById" parameterType="Long">
        delete from tbl_rule_management where id = #{id}
    </delete>

    <delete id="deleteTblRuleManagementByIds" parameterType="String">
        delete from tbl_rule_management where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>