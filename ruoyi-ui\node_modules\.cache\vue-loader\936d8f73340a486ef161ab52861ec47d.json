{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\userSelect.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\userSelect.vue", "mtime": 1756369456052}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7YWRkVXNlciwgZGVsVXNlciwgZ2V0VXNlciwgZ2V0VXNlcnMsIGxpc3RVc2VyLCB1cGRhdGVVc2VyfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7CmltcG9ydCBEZXB0U2VsZWN0IGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9zZWxlY3QvZGVwdFNlbGVjdCc7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlVzZXJTZWxlY3QiLAogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZScsICdzeXNfdXNlcl9zZXgnXSwKICBjb21wb25lbnRzOiB7RGVwdFNlbGVjdH0sCiAgcHJvcHM6IHsKICAgIHVzZXJkYXRhOiB7dHlwZTogU3RyaW5nLCBkZWZhdWx0OiBudWxsLH0sCiAgICAvLyDlgLwKICAgIHZhbHVlOiB7cmVxdWlyZWQ6IHRydWV9LAogICAgLy8g5YiG6ZqU56ymCiAgICBzZXBhcmF0b3I6IHt0eXBlOiBTdHJpbmcsIHJlcXVpcmVkOiBmYWxzZSwgZGVmYXVsdDogJywnfSwKICAgIC8vIOaYr+WQpuWkmumAiQogICAgbXVsdGlwbGU6IHt0eXBlOiBCb29sZWFuLCByZXF1aXJlZDogZmFsc2UsIGRlZmF1bHQ6IGZhbHNlfSwKICAgIC8vIOaYr+WQpuWPr+S7pea4heepuumAiemhuQogICAgY2xlYXJhYmxlOiB7dHlwZTogQm9vbGVhbiwgcmVxdWlyZWQ6IGZhbHNlLCBkZWZhdWx0OiB0cnVlfSwKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDkuLvplK7lrZfmrrXlkI0KICAgICAgaWROYW1lOiAndXNlcklkJywKICAgICAgLy8g6YCJ6aG55qCH562+5a2X5q615ZCNCiAgICAgIGxhYmVsTmFtZTogJ25pY2tOYW1lJywKICAgICAgLy8g6YCJ6aG55YC85a2X5q615ZCNCiAgICAgIHZhbHVlTmFtZTogJ3VzZXJJZCcsCiAgICAgIC8vIOaooeWdl+WQjQogICAgICBtb2R1bGVOYW1lOiAnc3lzdGVtJywKICAgICAgLy8g5Lia5Yqh5ZCNCiAgICAgIGJ1c2luZXNzTmFtZTogJ3VzZXInLAogICAgICAvLyDlip/og73lkI0KICAgICAgZnVuY3Rpb25OYW1lOiAn5Lq65ZGYJywKICAgICAgLy8g6YCJ5Lit6aG5CiAgICAgIHNlbGVjdGVkOiBudWxsLAogICAgICAvLyDmiYDmnInpgInkuK3pobnnmoRpZOWSjG5hbWUKICAgICAgb3B0aW9uczogW10sCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOaJgOaciemAieS4remhueeahGlkCiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtYW55OiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDooajmoLzmlbDmja4KICAgICAgbGlzdDogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgdXNlck5hbWU6IG51bGwsCiAgICAgICAgbmlja05hbWU6IG51bGwsCiAgICAgICAgcGhvbmVudW1iZXI6IG51bGwsCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgdXNlck5hbWU6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui0puWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgICB7bWluOiAyLCBtYXg6IDIwLCBtZXNzYWdlOiAn6LSm5Y+36ZW/5bqm5b+F6aG75LuL5LqOIDIg5ZKMIDIwIOS5i+mXtCcsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgXSwKICAgICAgICBkZXB0SWQ6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumDqOmXqOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgICBuaWNrTmFtZTogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5aeT5ZCN5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICBdLAogICAgICAgIHBhc3N3b3JkOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlr4bnoIHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgICAge21pbjogNSwgbWF4OiAyMCwgbWVzc2FnZTogJ+WvhueggemVv+W6puW/hemhu+S7i+S6jiA1IOWSjCAyMCDkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgIF0sCiAgICAgICAgZW1haWw6IFsKICAgICAgICAgIHt0eXBlOiAiZW1haWwiLCBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE6YKu566x5Zyw5Z2AIiwgdHJpZ2dlcjogWyJibHVyIiwgImNoYW5nZSJdfSwKICAgICAgICBdLAogICAgICAgIHBob25lbnVtYmVyOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLogZTns7vmlrnlvI/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgICAge3BhdHRlcm46IC9eKCgwXGR7MiwzfVxkezUsOH0pfCgxWzMtOV1cZHs5fSkpJC8sIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTogZTns7vmlrnlvI8iLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICAgIC8vIOm7mOiupOWvhueggQogICAgICBpbml0UGFzc3dvcmQ6IG51bGwsCiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgIHZhbHVlOiB7CiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsKICAgICAgICB0aGlzLmhhbmRsZVZhbHVlKG5ld1ZhbCk7CiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUsCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwKICAgIH0sCiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICB0aGlzLnNlbGVjdGVkID0gdGhpcy5tdWx0aXBsZSA/IFtdIDogJyc7CiAgICAgIHRoaXMuaGFuZGxlVmFsdWUodGhpcy52YWx1ZSk7CiAgICB9KTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWkhOeQhuWAvAogICAgaGFuZGxlVmFsdWUodmFsdWUpIHsKICAgICAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gJ3VuZGVmaW5lZCcgJiYgdmFsdWUgIT09IG51bGwgJiYgdmFsdWUgIT09ICcnKSB7CiAgICAgICAgaWYgKHRoaXMubXVsdGlwbGUpIHsKICAgICAgICAgIGxldCBzZWxlY3RlZCA9IEFycmF5LmlzQXJyYXkodmFsdWUpID8gdmFsdWUgOiB2YWx1ZS5zcGxpdCh0aGlzLnNlcGFyYXRvcik7CiAgICAgICAgICBpZiAoc2VsZWN0ZWQubGVuZ3RoID4gMCkgdGhpcy5zZWxlY3RlZCA9IHNlbGVjdGVkLm1hcChpdGVtID0+IHBhcnNlSW50KGl0ZW0pKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5zZWxlY3RlZCA9IHBhcnNlSW50KHZhbHVlKTsKICAgICAgICB9CiAgICAgICAgdGhpcy5oYW5kbGVTZWxlY3RlZCh0aGlzLnNlbGVjdGVkKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnNlbGVjdGVkID0gdGhpcy5tdWx0aXBsZSA/IFtdIDogJyc7CiAgICAgIH0KICAgIH0sCiAgICAvLyDlpITnkIbpgInkuK3pobkKICAgIGhhbmRsZVNlbGVjdGVkKHZhbHVlKSB7CiAgICAgIGlmICh0eXBlb2YgdmFsdWUgIT09ICd1bmRlZmluZWQnIHx8IHZhbHVlICE9PSBudWxsKSB7CiAgICAgICAgaWYgKHRoaXMubXVsdGlwbGUpIHsKICAgICAgICAgIC8vIHRoaXMub3B0aW9ucyA9IFtdOwogICAgICAgICAgaWYgKHZhbHVlLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgLy/lj6rmnInlnKjliJ3lp4vljJbml7bov5vooYzmn6Xor6IKICAgICAgICAgICAgaWYgKHRoaXMub3B0aW9ucy5sZW5ndGggPD0gMCkgewogICAgICAgICAgICAgIGdldFVzZXJzKHZhbHVlKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICAgICAgLy8g5aSE55CG5pWw5o2uCiAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld09wdGlvbnMgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+ICh7CiAgICAgICAgICAgICAgICAgICAgdXNlcklkOiBpdGVtLnVzZXJJZCwKICAgICAgICAgICAgICAgICAgICBuaWNrTmFtZTogaXRlbS5uaWNrTmFtZSwKICAgICAgICAgICAgICAgICAgICBwaG9uZW51bWJlcjogaXRlbS5waG9uZW51bWJlcgogICAgICAgICAgICAgICAgICB9KSk7CiAgICAgICAgICAgICAgICAgIC8vIOmBv+WFjemHjeWkjea3u+WKoOaVsOaNrgogICAgICAgICAgICAgICAgICBjb25zdCB1bmlxdWVPcHRpb25zID0gbmV3T3B0aW9ucy5maWx0ZXIob3B0aW9uID0+ICF0aGlzLm9wdGlvbnMuc29tZShleGlzdGluZ09wdGlvbiA9PiBleGlzdGluZ09wdGlvbi51c2VySWQgPT09IG9wdGlvbi51c2VySWQpKTsKICAgICAgICAgICAgICAgICAgLy8g5pu05pawIG9wdGlvbnMKICAgICAgICAgICAgICAgICAgdGhpcy5vcHRpb25zLnB1c2goLi4udW5pcXVlT3B0aW9ucyk7CiAgICAgICAgICAgICAgICAgIC8vIOabtOaWsCBpZHMKICAgICAgICAgICAgICAgICAgY29uc3QgbmV3SWRzID0gdW5pcXVlT3B0aW9ucy5tYXAob3B0aW9uID0+IG9wdGlvbi51c2VySWQpOwogICAgICAgICAgICAgICAgICB0aGlzLmlkcy5wdXNoKC4uLm5ld0lkcyk7CiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9CgogICAgICAgICAgICAvLyB2YWx1ZS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICAvLyAgIGdldFVzZXIoaXRlbSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgIC8vICAgICB0aGlzLm9wdGlvbnMucHVzaChyZXNwb25zZS5kYXRhKTsKICAgICAgICAgICAgLy8gICB9KTsKICAgICAgICAgICAgLy8gfSk7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGlmICghdGhpcy5vcHRpb25zLnNvbWUoaXRlbSA9PiBpdGVtW3RoaXMuaWROYW1lXSA9PT0gdmFsdWUpKSB7CiAgICAgICAgICAgIGdldFVzZXIodmFsdWUpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMub3B0aW9ucyA9IFtyZXNwb25zZS5kYXRhXTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuaWRzID0gW107CiAgICAgICAgdGhpcy5vcHRpb25zID0gW107CiAgICAgIH0KICAgIH0sCiAgICAvLyDlpITnkIbmuIXnqbrkuovku7YKICAgIGhhbmRsZUNsZWFyKCkgewogICAgICB0aGlzLmlkcyA9IFtdOwogICAgICB0aGlzLm9wdGlvbnMgPSBbXTsKICAgICAgaWYgKEFycmF5LmlzQXJyYXkodGhpcy52YWx1ZSkpIHsKICAgICAgICB0aGlzLiRlbWl0KCdpbnB1dCcsIFtdKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRlbWl0KCdpbnB1dCcsICcnKTsKICAgICAgfQogICAgfSwKICAgIC8vIOWkhOeQhuWkmumAieaooeW8j+S4i+enu+mZpHRhZ+S6i+S7tgogICAgaGFuZGxlUmVtb3ZlVGFnKHZhbHVlKSB7CiAgICAgIC8v5Yig6ZmkaWQKICAgICAgdGhpcy5pZHMuc29tZSgoaXRlbSwgaW5kZXgpID0+IHsKICAgICAgICBpZiAoaXRlbSA9PT0gdmFsdWUpIHsKICAgICAgICAgIHRoaXMuaWRzLnNwbGljZShpbmRleCwgMSk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgLy/liKDpmaRvcHRpb24KICAgICAgdGhpcy5vcHRpb25zLnNvbWUoKGl0ZW0sIGluZGV4KSA9PiB7CiAgICAgICAgaWYgKGl0ZW0udXNlcklkID09PSB2YWx1ZSkgdGhpcy5vcHRpb25zLnNwbGljZShpbmRleCwgMSk7CiAgICAgIH0pOwogICAgICBpZiAoQXJyYXkuaXNBcnJheSh0aGlzLnZhbHVlKSkgewogICAgICAgIHRoaXMuJGVtaXQoJ2lucHV0JywgdGhpcy5zZWxlY3RlZCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kZW1pdCgnaW5wdXQnLCB0aGlzLnNlbGVjdGVkLmpvaW4odGhpcy5zZXBhcmF0b3IpKTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmiZPlvIDlr7nor53moYYgKi8KICAgIG9wZW5EaWFsb2coKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMubmlja05hbWUgPSBudWxsOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBob25lbnVtYmVyID0gbnVsbDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlU2l6ZSA9IDEwOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgLy8gdGhpcy5nZXRDb25maWdLZXkoInN5cy51c2VyLmluaXRQYXNzd29yZCIpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAvLyAgIHRoaXMuaW5pdFBhc3N3b3JkID0gcmVzcG9uc2UubXNnOwogICAgICAvLyB9KTsKICAgICAgdGhpcy52aXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy4kcmVmcy5lbFNlbGVjdC5ibHVyKCk7CiAgICB9LAogICAgLyoqIOWFs+mXreWvueivneahhiAqLwogICAgY2xvc2VEaWFsb2coKSB7CiAgICAgIHRoaXMudmlzaWJsZSA9IGZhbHNlOwogICAgfSwKICAgIC8qKiDlhbPpl63liY3nmoTlm57osIPvvIzkvJrmmoLlgZzlr7nor53moYbnmoTlhbPpl60gKi8KICAgIGhhbmRsZUJlZm9yZUNsb3NlKGRvbmUpIHsKICAgICAgdGhpcy4kbmV4dFRpY2soKCk9PnsKICAgICAgICAvL+WFs+mXreWQju+8jOWwhumAieaLqeahhuWFqOmDqOmHjee9rgogICAgICAgIHRoaXMuaWRzID0gW107CiAgICAgICAgdGhpcy5vcHRpb25zID0gW107CiAgICAgICAgdGhpcy5oYW5kbGVWYWx1ZSh0aGlzLnZhbHVlKTsKICAgICAgICBkb25lKCk7CiAgICAgIH0pCiAgICB9LAogICAgLyoqIOWvueivneahhuaJk+W8gOeahOWbnuiwgyAqLwogICAgaGFuZGxlT3BlbigpIHsKICAgIH0sCiAgICAvKiog5a+56K+d5qGG5YWz6Zet55qE5Zue6LCDICovCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgIH0sCiAgICAvKiog56Gu6K6k5oyJ6ZKuICovCiAgICBoYW5kbGVDb25maXJtKCkgewogICAgICBpZiAodGhpcy5tdWx0aXBsZSkgewogICAgICAgIGlmIChBcnJheS5pc0FycmF5KHRoaXMudmFsdWUpKSB7CiAgICAgICAgICB0aGlzLiRlbWl0KCdpbnB1dCcsIHRoaXMuaWRzKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kZW1pdCgnaW5wdXQnLCB0aGlzLmlkcy5qb2luKHRoaXMuc2VwYXJhdG9yKSk7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIGxldCBpZCA9IHRoaXMuaWRzWzBdOwogICAgICAgIGxldCB1c2VyID0gdGhpcy5saXN0LmZpbmQoaXRlbSA9PiBpdGVtW3RoaXMuaWROYW1lXSA9PT0gaWQpOwogICAgICAgIHRoaXMub3B0aW9ucyA9IFt1c2VyXTsKICAgICAgICB0aGlzLnNlbGVjdGVkID0gaWQ7CiAgICAgICAgdGhpcy4kZW1pdCgnc2V0UGhvbmUnLCB1c2VyLnBob25lbnVtYmVyKTsKICAgICAgICB0aGlzLiRlbWl0KCdpbnB1dCcsIGlkKTsKICAgICAgfQogICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTsKICAgICAgLy8gfQogICAgICAvLyBpZiAodGhpcy5pZHMubGVuZ3RoID09IDApewogICAgICAvLyAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36YCJ5oup5Lq65ZGY77yBIik7CiAgICAgIC8vIH0KICAgIH0sCiAgICAvKiog5Y+W5raI5oyJ6ZKuICovCiAgICBoYW5kbGVDYW5jZWwoKSB7CiAgICAgIHRoaXMuaWRzID0gW107CiAgICAgIHRoaXMub3B0aW9ucyA9IFtdOwogICAgICB0aGlzLmhhbmRsZVZhbHVlKHRoaXMudmFsdWUpOwogICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICAvKiog5p+l6K+i5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm5vSWRzID0gWzk5OTk5OV07CiAgICAgIGxpc3RVc2VyKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIGZvciAobGV0IHJvd2RhdGEgb2YgdGhpcy5saXN0KSB7CiAgICAgICAgICAgIGZvciAobGV0IHJlc2RhdGEgb2YgdGhpcy5vcHRpb25zKSB7CiAgICAgICAgICAgICAgLy8gaWYgKHRoaXMudXNlcmRhdGE9PW51bGwpewogICAgICAgICAgICAgIC8vICAgdGhpcy5vcHRpb25zPVtdOwogICAgICAgICAgICAgIC8vIH0KICAgICAgICAgICAgICBpZiAocm93ZGF0YS51c2VySWQgPT0gcmVzZGF0YS51c2VySWQpIHsKICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMudGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvd2RhdGEsIHRydWUpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSE55CG6YCJ5oup5qGG5LqL5Lu2CiAgICBoYW5kbGVTZWxlY3Qoc2VsZWN0aW9uLCByb3cpIHsKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIGlmICghdGhpcy5tdWx0aXBsZSkgewogICAgICAgICAgaWYgKHNlbGVjdGlvbi5sZW5ndGggPiAxKSB7CiAgICAgICAgICAgIHRoaXMuJHJlZnMudGFibGUuY2xlYXJTZWxlY3Rpb24oKTsKICAgICAgICAgICAgdGhpcy4kcmVmcy50YWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93LCB0cnVlKTsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdmFyIGZsYWcgPSB0aGlzLm9wdGlvbnMuc29tZShpdGVtID0+IHsKICAgICAgICAgICAgcmV0dXJuIGl0ZW0udXNlcklkID09IHJvdy51c2VySWQKICAgICAgICAgIH0pCiAgICAgICAgICBpZiAoIWZsYWcpIHsKICAgICAgICAgICAgLy8g5Zue5pi+5pWw5o2u6YeM5rKh5pyJ5pys5p2h77yM5oqK6L+Z5p2h5Yqg6L+b5p2lKOmAieS4rSkKICAgICAgICAgICAgdmFyIHRlbXBPcHRpb24gPSB7CiAgICAgICAgICAgICAgdXNlcklkOiByb3cudXNlcklkLAogICAgICAgICAgICAgIG5pY2tOYW1lOiByb3cubmlja05hbWUsCiAgICAgICAgICAgICAgcGhvbmVudW1iZXI6IHJvdy5waG9uZW51bWJlcgogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMub3B0aW9ucy5wdXNoKHRlbXBPcHRpb24pOwogICAgICAgICAgICB0aGlzLmlkcy5wdXNoKHJvdy51c2VySWQpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgLy8g5Zue5pi+5pWw5o2u6YeM5pyJ5pys5p2h77yM5oqK6L+Z5p2h5Yig6ZmkKOWPlua2iOmAieS4rSkKICAgICAgICAgICAgdGhpcy5vcHRpb25zLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7CiAgICAgICAgICAgICAgaWYgKGl0ZW0udXNlcklkID09IHJvdy51c2VySWQpIHsKICAgICAgICAgICAgICAgIHRoaXMub3B0aW9ucy5zcGxpY2UoaW5kZXgsIDEpOwogICAgICAgICAgICAgICAgdGhpcy5pZHMuZm9yRWFjaCgodXNlcklkLCBpbmRleCkgPT4gewogICAgICAgICAgICAgICAgICBpZiAodXNlcklkID09IGl0ZW0udXNlcklkKSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5pZHMuc3BsaWNlKGluZGV4LCAxKTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g5aSE55CG5YWo6YCJ5qGG5LqL5Lu2CiAgICBoYW5kbGVTZWxlY3RBbGwoc2VsZWN0aW9uKSB7CiAgICAgIGlmICghdGhpcy5tdWx0aXBsZSkgewogICAgICAgIHRoaXMuJHJlZnMudGFibGUuY2xlYXJTZWxlY3Rpb24oKQogICAgICB9IGVsc2UgewogICAgICAgIC8v5YWo6YCJ5LqL5Lu2CiAgICAgICAgaWYgKHNlbGVjdGlvbi5sZW5ndGggPiAwKSB7CiAgICAgICAgICB0aGlzLmxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgLy/lsIbmnKzpobXmnKrli77pgInnmoTov5vooYzli77pgIkKICAgICAgICAgICAgaWYgKCF0aGlzLm9wdGlvbnMuc29tZShvcHRpb25JdGVtID0+IHsKICAgICAgICAgICAgICByZXR1cm4gaXRlbS51c2VySWQgPT09IG9wdGlvbkl0ZW0udXNlcklkCiAgICAgICAgICAgIH0pKSB7CiAgICAgICAgICAgICAgdGhpcy5oYW5kbGVTZWxlY3Qoc2VsZWN0aW9uLCBpdGVtKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICAgIC8v5Y+W5raI5YWo6YCJ5LqL5Lu2CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMubGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICAvL+WwhuacrOmhteWLvumAieeahOi/m+ihjOWPlua2iAogICAgICAgICAgICBpZiAodGhpcy5vcHRpb25zLnNvbWUob3B0aW9uSXRlbSA9PiB7CiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0udXNlcklkID09PSBvcHRpb25JdGVtLnVzZXJJZAogICAgICAgICAgICB9KSkgewogICAgICAgICAgICAgIHRoaXMuaGFuZGxlU2VsZWN0KHNlbGVjdGlvbiwgaXRlbSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW1bdGhpcy5pZE5hbWVdKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm1hbnkgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvLyDlpITnkIbooYzngrnlh7vkuovku7YKICAgIGhhbmRsZUNsaWNrKHJvdywgY29sdW1uLCBldmVudCkgewogICAgICBpZiAocm93KSB7CiAgICAgICAgbGV0IHJvd3MgPSB0aGlzLiRyZWZzLnRhYmxlLnNlbGVjdGlvbjsKICAgICAgICAvLyBsZXQgc2VsZWN0ZWQgPSByb3dzLmZpbmQoaXRlbSA9PiBpdGVtW3RoaXMuaWROYW1lXSA9PT0gcm93W3RoaXMuaWROYW1lXSk7CiAgICAgICAgLy8gaWYgKCF0aGlzLm11bHRpcGxlKSB7CiAgICAgICAgLy8gICB0aGlzLiRyZWZzLnRhYmxlLmNsZWFyU2VsZWN0aW9uKCk7CiAgICAgICAgLy8gfQogICAgICAgIC8vIHRoaXMuJHJlZnMudGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvdywgc2VsZWN0ZWQgPyB1bmRlZmluZWQgOiB0cnVlKTsKICAgICAgICAvL+WLvumAieaIluiAheWPlua2iOWLvumAiQogICAgICAgIHRoaXMuJHJlZnMudGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvdywgdGhpcy5vcHRpb25zLnNvbWUoaXRlbSA9PiB7CiAgICAgICAgICByZXR1cm4gaXRlbS51c2VySWQgPT0gcm93LnVzZXJJZAogICAgICAgIH0pID8gdW5kZWZpbmVkIDogdHJ1ZSk7CiAgICAgICAgdGhpcy5oYW5kbGVTZWxlY3Qocm93cywgcm93KTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gYOa3u+WKoCR7dGhpcy5mdW5jdGlvbk5hbWV95L+h5oGvYDsKICAgICAgdGhpcy5mb3JtLnBhc3N3b3JkID0gdGhpcy5pbml0UGFzc3dvcmQ7CiAgICAgIHRoaXMuZm9ybS5kZXB0SWQgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLmRlcHRJZDsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3QgaWQgPSByb3dbdGhpcy5pZE5hbWVdIHx8IHRoaXMuaWRzCiAgICAgIGdldFVzZXIoaWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gYOS/ruaUuSR7dGhpcy5mdW5jdGlvbk5hbWV95L+h5oGvYDsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCBpZHMgPSByb3dbdGhpcy5pZE5hbWVdIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKGDmmK/lkKbnoa7orqTliKDpmaQke3RoaXMuZnVuY3Rpb25OYW1lfee8luWPt+S4uiIke2lkc30i55qE5pWw5o2u6aG577yfYCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGRlbFVzZXIoaWRzKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICB0aGlzLmRvd25sb2FkKGAke3RoaXMubW9kdWxlTmFtZX0vJHt0aGlzLmJ1c2luZXNzTmFtZX0vZXhwb3J0YCwgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMsCiAgICAgIH0sIGAke3RoaXMuZnVuY3Rpb25OYW1lfV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtKCkgewogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybVt0aGlzLmlkTmFtZV0gIT0gbnVsbCkgewogICAgICAgICAgICB1cGRhdGVVc2VyKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgLy8g54m55q6K6ZyA5rGC5LiN6L6T5YWl6LSm5Y+35ZKM5a+G56CB5bCx5Y+v5Lul5re75Yqg55So5oi3CiAgICAgICAgICAgIHRoaXMuZm9ybS51c2VyTmFtZSA9IHRoaXMuZm9ybS5waG9uZW51bWJlcjsKICAgICAgICAgICAgYWRkVXNlcih0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlj5bmtojmjInpkq4gKi8KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5pZHMgPSBbXTsKICAgICAgdGhpcy5vcHRpb25zID0gW107CiAgICAgIHRoaXMuaGFuZGxlVmFsdWUodGhpcy52YWx1ZSk7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIHVzZXJJZDogdW5kZWZpbmVkLAogICAgICAgIGRlcHRJZDogdW5kZWZpbmVkLAogICAgICAgIHVzZXJOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgbmlja05hbWU6IHVuZGVmaW5lZCwKICAgICAgICBwYXNzd29yZDogdW5kZWZpbmVkLAogICAgICAgIHBob25lbnVtYmVyOiB1bmRlZmluZWQsCiAgICAgICAgZW1haWw6IHVuZGVmaW5lZCwKICAgICAgICBzZXg6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6ICIwIiwKICAgICAgICByZW1hcms6IHVuZGVmaW5lZCwKICAgICAgICBwb3N0SWRzOiBbXSwKICAgICAgICByb2xlSWRzOiBbXQogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICB9LAp9Cg=="}, {"version": 3, "sources": ["userSelect.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm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file": "userSelect.vue", "sourceRoot": "src/views/hhlCode/component", "sourcesContent": ["<template>\n  <div>\n    <el-select\n      ref=\"elSelect\"\n      v-model=\"selected\"\n      :placeholder=\"$attrs['placeholder']||'请选择'+this.functionName\"\n      :multiple=\"multiple\"\n      :clearable=\"clearable\"\n      class=\"tags-select-input\"\n      collapse-tags\n      @focus=\"openDialog\"\n      @clear=\"handleClear\"\n      @remove-tag=\"handleRemoveTag\"\n      v-bind=\"$attrs\">\n      <el-option\n        v-for=\"item in options\"\n        :key=\"item[valueName]\"\n        :label=\"item[labelName]+'-'+item.phonenumber\"\n        :value=\"item[valueName]\">\n      </el-option>\n    </el-select>\n    <el-dialog\n      :title=\"'选择'+this.functionName\"\n      :visible.sync=\"visible\"\n      :before-close=\"handleBeforeClose\"\n      @open=\"handleOpen\"\n      @close=\"handleClose\"\n      width=\"800px\"\n      top=\"5vh\"\n      append-to-body>\n      <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n        <!--<el-form-item label=\"账号\" prop=\"userName\">-->\n        <!--  <el-input-->\n        <!--    v-model=\"queryParams.userName\"-->\n        <!--    placeholder=\"请输入账号\"-->\n        <!--    clearable-->\n        <!--    @keyup.enter.native=\"handleQuery\"-->\n        <!--  />-->\n        <!--</el-form-item>-->\n        <el-form-item label=\"姓名\" prop=\"nickName\">\n          <el-input\n            v-model=\"queryParams.nickName\"\n            placeholder=\"请输入姓名\"\n            clearable\n            @keyup.enter.native=\"handleQuery\"\n          />\n        </el-form-item>\n        <el-form-item label=\"联系方式\" prop=\"phonenumber\">\n          <el-input\n            v-model=\"queryParams.phonenumber\"\n            placeholder=\"请输入联系方式\"\n            clearable\n            @keyup.enter.native=\"handleQuery\"\n          />\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <!--<el-row :gutter=\"10\" class=\"mb8\">-->\n      <!--  <el-col :span=\"1.5\">-->\n      <!--    <el-button-->\n      <!--      type=\"primary\"-->\n      <!--      plain-->\n      <!--      icon=\"el-icon-plus\"-->\n      <!--      size=\"mini\"-->\n      <!--      @click=\"handleAdd\"-->\n      <!--      v-hasPermi=\"['system:user:add']\"-->\n      <!--    >新增</el-button>-->\n      <!--  </el-col>-->\n      <!--  <el-col :span=\"1.5\">-->\n      <!--    <el-button-->\n      <!--      type=\"success\"-->\n      <!--      plain-->\n      <!--      icon=\"el-icon-edit\"-->\n      <!--      size=\"mini\"-->\n      <!--      :disabled=\"single\"-->\n      <!--      @click=\"handleUpdate\"-->\n      <!--      v-hasPermi=\"['system:user:edit']\"-->\n      <!--    >修改</el-button>-->\n      <!--  </el-col>-->\n      <!--  <el-col :span=\"1.5\">-->\n      <!--    <el-button-->\n      <!--      type=\"danger\"-->\n      <!--      plain-->\n      <!--      icon=\"el-icon-delete\"-->\n      <!--      size=\"mini\"-->\n      <!--      :disabled=\"many\"-->\n      <!--      @click=\"handleDelete\"-->\n      <!--      v-hasPermi=\"['system:user:remove']\"-->\n      <!--    >删除</el-button>-->\n      <!--  </el-col>-->\n      <!--  <el-col :span=\"1.5\">-->\n      <!--    <el-button-->\n      <!--      type=\"warning\"-->\n      <!--      plain-->\n      <!--      icon=\"el-icon-download\"-->\n      <!--      size=\"mini\"-->\n      <!--      @click=\"handleExport\"-->\n      <!--      v-hasPermi=\"['system:user:export']\"-->\n      <!--    >导出</el-button>-->\n      <!--  </el-col>-->\n      <!--  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>-->\n      <!--</el-row>-->\n\n      <el-table ref=\"table\" v-loading=\"loading\" :data=\"list\" @select=\"handleSelect\" @select-all=\"handleSelectAll\"\n                @selection-change=\"handleSelectionChange\" @row-click=\"handleClick\" height=\"260px\">\n        <el-table-column type=\"selection\" width=\"55\" ></el-table-column>\n        <el-table-column label=\"账号\" prop=\"userName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"姓名\" prop=\"nickName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"邮箱\" prop=\"email\" :show-overflow-tooltip=\"true\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.email ? scope.row.email : '--' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"联系方式\" prop=\"phonenumber\" :show-overflow-tooltip=\"true\"/>\n        <!--<el-table-column label=\"状态\"  prop=\"status\">-->\n        <!--  <template slot-scope=\"scope\">-->\n        <!--    <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>-->\n        <!--  </template>-->\n        <!--</el-table-column>-->\n        <!--<el-table-column label=\"创建时间\"  prop=\"createTime\" width=\"180\">-->\n        <!--  <template slot-scope=\"scope\">-->\n        <!--    <span>{{ parseTime(scope.row.createTime) }}</span>-->\n        <!--  </template>-->\n        <!--</el-table-column>-->\n        <!--        <el-table-column label=\"操作\"  class-name=\"small-padding fixed-width\">-->\n        <!--          <template slot-scope=\"scope\">-->\n        <!--            <el-button-->\n        <!--              size=\"mini\"-->\n        <!--              type=\"text\"-->\n        <!--              icon=\"el-icon-edit\"-->\n        <!--              @click=\"handleUpdate(scope.row)\"-->\n        <!--              v-hasPermi=\"['system:user:edit']\"-->\n        <!--            >修改-->\n        <!--            </el-button>-->\n        <!--            <el-button-->\n        <!--              size=\"mini\"-->\n        <!--              type=\"text\"-->\n        <!--              icon=\"el-icon-delete\"-->\n        <!--              @click=\"handleDelete(scope.row)\"-->\n        <!--              v-hasPermi=\"['system:user:remove']\"-->\n        <!--            >删除-->\n        <!--            </el-button>-->\n        <!--          </template>\n                </el-table-column>-->\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n\n      <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"700px\" append-to-body>\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n          <el-row type=\"flex\" style=\"flex-wrap: wrap;\">\n            <el-col :span=\"12\">\n              <el-form-item label=\"姓名\" prop=\"nickName\">\n                <el-input v-model=\"form.nickName\" placeholder=\"请输入姓名\" maxlength=\"30\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"归属部门\" prop=\"deptId\">\n                <dept-select v-model=\"form.deptId\" is-current/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"联系方式\" prop=\"phonenumber\">\n                <el-input v-model=\"form.phonenumber\" placeholder=\"请输入联系方式\" maxlength=\"11\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"邮箱\" prop=\"email\">\n                <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"备注\">\n                <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n          <el-button @click=\"cancel\">取 消</el-button>\n        </div>\n      </el-dialog>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n        >添加{{ functionName }}\n        </el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">确定</el-button>\n        <el-button @click=\"handleCancel\">取消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {addUser, delUser, getUser, getUsers, listUser, updateUser} from \"@/api/system/user\";\nimport DeptSelect from '@/views/components/select/deptSelect';\n\nexport default {\n  name: \"UserSelect\",\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\n  components: {DeptSelect},\n  props: {\n    userdata: {type: String, default: null,},\n    // 值\n    value: {required: true},\n    // 分隔符\n    separator: {type: String, required: false, default: ','},\n    // 是否多选\n    multiple: {type: Boolean, required: false, default: false},\n    // 是否可以清空选项\n    clearable: {type: Boolean, required: false, default: true},\n  },\n  data() {\n    return {\n      // 主键字段名\n      idName: 'userId',\n      // 选项标签字段名\n      labelName: 'nickName',\n      // 选项值字段名\n      valueName: 'userId',\n      // 模块名\n      moduleName: 'system',\n      // 业务名\n      businessName: 'user',\n      // 功能名\n      functionName: '人员',\n      // 选中项\n      selected: null,\n      // 所有选中项的id和name\n      options: [],\n      // 是否显示弹出层\n      visible: false,\n      // 遮罩层\n      loading: true,\n      // 所有选中项的id\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      many: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userName: null,\n        nickName: null,\n        phonenumber: null,\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        userName: [\n          {required: true, message: \"账号不能为空\", trigger: \"blur\"},\n          {min: 2, max: 20, message: '账号长度必须介于 2 和 20 之间', trigger: 'blur'},\n        ],\n        deptId: [\n          {required: true, message: \"部门不能为空\", trigger: \"blur\"},\n        ],\n        nickName: [\n          {required: true, message: \"姓名不能为空\", trigger: \"blur\"},\n        ],\n        password: [\n          {required: true, message: \"密码不能为空\", trigger: \"blur\"},\n          {min: 5, max: 20, message: '密码长度必须介于 5 和 20 之间', trigger: 'blur'},\n        ],\n        email: [\n          {type: \"email\", message: \"请输入正确的邮箱地址\", trigger: [\"blur\", \"change\"]},\n        ],\n        phonenumber: [\n          {required: true, message: \"联系方式不能为空\", trigger: \"blur\"},\n          {pattern: /^((0\\d{2,3}\\d{5,8})|(1[3-9]\\d{9}))$/, message: \"请输入正确的联系方式\", trigger: \"blur\"},\n        ],\n      },\n      // 默认密码\n      initPassword: null,\n    };\n  },\n  watch: {\n    value: {\n      handler(newVal, oldVal) {\n        this.handleValue(newVal);\n      },\n      deep: true,\n      immediate: true,\n    },\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.selected = this.multiple ? [] : '';\n      this.handleValue(this.value);\n    });\n  },\n  methods: {\n    // 处理值\n    handleValue(value) {\n      if (typeof value !== 'undefined' && value !== null && value !== '') {\n        if (this.multiple) {\n          let selected = Array.isArray(value) ? value : value.split(this.separator);\n          if (selected.length > 0) this.selected = selected.map(item => parseInt(item));\n        } else {\n          this.selected = parseInt(value);\n        }\n        this.handleSelected(this.selected);\n      } else {\n        this.selected = this.multiple ? [] : '';\n      }\n    },\n    // 处理选中项\n    handleSelected(value) {\n      if (typeof value !== 'undefined' || value !== null) {\n        if (this.multiple) {\n          // this.options = [];\n          if (value.length > 0) {\n            //只有在初始化时进行查询\n            if (this.options.length <= 0) {\n              getUsers(value).then(response => {\n                  // 处理数据\n                  const newOptions = response.data.map(item => ({\n                    userId: item.userId,\n                    nickName: item.nickName,\n                    phonenumber: item.phonenumber\n                  }));\n                  // 避免重复添加数据\n                  const uniqueOptions = newOptions.filter(option => !this.options.some(existingOption => existingOption.userId === option.userId));\n                  // 更新 options\n                  this.options.push(...uniqueOptions);\n                  // 更新 ids\n                  const newIds = uniqueOptions.map(option => option.userId);\n                  this.ids.push(...newIds);\n                })\n            }\n\n            // value.forEach(item => {\n            //   getUser(item).then(response => {\n            //     this.options.push(response.data);\n            //   });\n            // });\n          }\n        } else {\n          if (!this.options.some(item => item[this.idName] === value)) {\n            getUser(value).then(response => {\n              this.options = [response.data];\n            });\n          }\n        }\n      } else {\n        this.ids = [];\n        this.options = [];\n      }\n    },\n    // 处理清空事件\n    handleClear() {\n      this.ids = [];\n      this.options = [];\n      if (Array.isArray(this.value)) {\n        this.$emit('input', []);\n      } else {\n        this.$emit('input', '');\n      }\n    },\n    // 处理多选模式下移除tag事件\n    handleRemoveTag(value) {\n      //删除id\n      this.ids.some((item, index) => {\n        if (item === value) {\n          this.ids.splice(index, 1);\n        }\n      });\n      //删除option\n      this.options.some((item, index) => {\n        if (item.userId === value) this.options.splice(index, 1);\n      });\n      if (Array.isArray(this.value)) {\n        this.$emit('input', this.selected);\n      } else {\n        this.$emit('input', this.selected.join(this.separator));\n      }\n    },\n    /** 打开对话框 */\n    openDialog() {\n      this.queryParams.nickName = null;\n      this.queryParams.phonenumber = null;\n      this.queryParams.pageNum = 1;\n      this.queryParams.pageSize = 10;\n      this.getList();\n      // this.getConfigKey(\"sys.user.initPassword\").then(response => {\n      //   this.initPassword = response.msg;\n      // });\n      this.visible = true;\n      this.$refs.elSelect.blur();\n    },\n    /** 关闭对话框 */\n    closeDialog() {\n      this.visible = false;\n    },\n    /** 关闭前的回调，会暂停对话框的关闭 */\n    handleBeforeClose(done) {\n      this.$nextTick(()=>{\n        //关闭后，将选择框全部重置\n        this.ids = [];\n        this.options = [];\n        this.handleValue(this.value);\n        done();\n      })\n    },\n    /** 对话框打开的回调 */\n    handleOpen() {\n    },\n    /** 对话框关闭的回调 */\n    handleClose() {\n    },\n    /** 确认按钮 */\n    handleConfirm() {\n      if (this.multiple) {\n        if (Array.isArray(this.value)) {\n          this.$emit('input', this.ids);\n        } else {\n          this.$emit('input', this.ids.join(this.separator));\n        }\n      } else {\n        let id = this.ids[0];\n        let user = this.list.find(item => item[this.idName] === id);\n        this.options = [user];\n        this.selected = id;\n        this.$emit('setPhone', user.phonenumber);\n        this.$emit('input', id);\n      }\n      this.visible = false;\n      // }\n      // if (this.ids.length == 0){\n      //   this.$message.warning(\"请选择人员！\");\n      // }\n    },\n    /** 取消按钮 */\n    handleCancel() {\n      this.ids = [];\n      this.options = [];\n      this.handleValue(this.value);\n      this.visible = false;\n    },\n    /** 查询列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.noIds = [999999];\n      listUser(this.queryParams).then(response => {\n        this.list = response.rows;\n        this.total = response.total;\n        this.$nextTick(() => {\n          for (let rowdata of this.list) {\n            for (let resdata of this.options) {\n              // if (this.userdata==null){\n              //   this.options=[];\n              // }\n              if (rowdata.userId == resdata.userId) {\n                this.$refs.table.toggleRowSelection(rowdata, true);\n              }\n            }\n          }\n        });\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 处理选择框事件\n    handleSelect(selection, row) {\n      this.$nextTick(() => {\n        if (!this.multiple) {\n          if (selection.length > 1) {\n            this.$refs.table.clearSelection();\n            this.$refs.table.toggleRowSelection(row, true);\n          }\n        } else {\n          var flag = this.options.some(item => {\n            return item.userId == row.userId\n          })\n          if (!flag) {\n            // 回显数据里没有本条，把这条加进来(选中)\n            var tempOption = {\n              userId: row.userId,\n              nickName: row.nickName,\n              phonenumber: row.phonenumber\n            }\n            this.options.push(tempOption);\n            this.ids.push(row.userId);\n          } else {\n            // 回显数据里有本条，把这条删除(取消选中)\n            this.options.forEach((item, index) => {\n              if (item.userId == row.userId) {\n                this.options.splice(index, 1);\n                this.ids.forEach((userId, index) => {\n                  if (userId == item.userId) {\n                    this.ids.splice(index, 1);\n                  }\n                })\n              }\n            });\n          }\n        }\n      });\n    },\n    // 处理全选框事件\n    handleSelectAll(selection) {\n      if (!this.multiple) {\n        this.$refs.table.clearSelection()\n      } else {\n        //全选事件\n        if (selection.length > 0) {\n          this.list.forEach(item => {\n            //将本页未勾选的进行勾选\n            if (!this.options.some(optionItem => {\n              return item.userId === optionItem.userId\n            })) {\n              this.handleSelect(selection, item);\n            }\n          })\n          //取消全选事件\n        } else {\n          this.list.forEach(item => {\n            //将本页勾选的进行取消\n            if (this.options.some(optionItem => {\n              return item.userId === optionItem.userId\n            })) {\n              this.handleSelect(selection, item);\n            }\n          })\n        }\n      }\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item[this.idName]);\n      this.single = selection.length !== 1;\n      this.many = !selection.length;\n    },\n    // 处理行点击事件\n    handleClick(row, column, event) {\n      if (row) {\n        let rows = this.$refs.table.selection;\n        // let selected = rows.find(item => item[this.idName] === row[this.idName]);\n        // if (!this.multiple) {\n        //   this.$refs.table.clearSelection();\n        // }\n        // this.$refs.table.toggleRowSelection(row, selected ? undefined : true);\n        //勾选或者取消勾选\n        this.$refs.table.toggleRowSelection(row, this.options.some(item => {\n          return item.userId == row.userId\n        }) ? undefined : true);\n        this.handleSelect(rows, row);\n      }\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = `添加${this.functionName}信息`;\n      this.form.password = this.initPassword;\n      this.form.deptId = this.$store.state.user.deptId;\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row[this.idName] || this.ids\n      getUser(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = `修改${this.functionName}信息`;\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row[this.idName] || this.ids;\n      this.$modal.confirm(`是否确认删除${this.functionName}编号为\"${ids}\"的数据项？`).then(function () {\n        return delUser(ids);\n      }).then(() => {\n        this.$modal.msgSuccess(\"删除成功\");\n        this.getList();\n      }).catch(() => {\n      });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download(`${this.moduleName}/${this.businessName}/export`, {\n        ...this.queryParams,\n      }, `${this.functionName}_${new Date().getTime()}.xlsx`)\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form[this.idName] != null) {\n            updateUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            // 特殊需求不输入账号和密码就可以添加用户\n            this.form.userName = this.form.phonenumber;\n            addUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 取消按钮 */\n    cancel() {\n      this.ids = [];\n      this.options = [];\n      this.handleValue(this.value);\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        userId: undefined,\n        deptId: undefined,\n        userName: undefined,\n        nickName: undefined,\n        password: undefined,\n        phonenumber: undefined,\n        email: undefined,\n        sex: undefined,\n        status: \"0\",\n        remark: undefined,\n        postIds: [],\n        roleIds: []\n      };\n      this.resetForm(\"form\");\n    },\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.tags-select-input ::v-deep .el-select__tags {\n  white-space: nowrap;\n  overflow: hidden;\n  flex-flow: nowrap;\n  display:flex;\n  flex-wrap:nowrap;\n}\n.tags-select-input ::v-deep .el-select__tags-text {\n  display: inline-block;\n  max-width: 90px;//设置最大宽度 超出显示...\n  white-space: nowrap;\n  overflow: hidden;\n  flex-flow: nowrap;\n  vertical-align:bottom;\n  text-overflow:ellipsis;\n}\n</style>\n"]}]}