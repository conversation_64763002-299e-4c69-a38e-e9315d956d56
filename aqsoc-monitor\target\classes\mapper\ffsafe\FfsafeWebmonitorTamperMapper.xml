<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeWebmonitorTamperMapper">

    <resultMap type="FfsafeWebmonitorTamper" id="FfsafeWebmonitorTamperResult">
        <result property="id"    column="id"    />
        <result property="modifyid"    column="modifyId"    />
        <result property="url"    column="url"    />
        <result property="content"    column="content"    />
        <result property="startTime"    column="start_time"    />
        <result property="recentlyTime"    column="recently_time"    />
        <result property="handleStatus"    column="handle_status"    />
        <result property="systemName"    column="system_name"    />
    </resultMap>

    <sql id="selectFfsafeWebmonitorTamperVo">
        select id, modifyId, url, content, start_time, recently_time, handle_status, system_name from ffsafe_webmonitor_tamper
    </sql>

    <select id="selectFfsafeWebmonitorTamperList" parameterType="FfsafeWebmonitorTamper" resultMap="FfsafeWebmonitorTamperResult">
        <include refid="selectFfsafeWebmonitorTamperVo"/>
        <where>
            <if test="modifyid != null "> and modifyId = #{modifyid}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="recentlyTime != null "> and recently_time = #{recentlyTime}</if>
            <if test="handleStatus != null  and handleStatus != ''"> and handle_status = #{handleStatus}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
        </where>
    </select>

    <select id="selectFfsafeWebmonitorTamperById" parameterType="Long" resultMap="FfsafeWebmonitorTamperResult">
        <include refid="selectFfsafeWebmonitorTamperVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeWebmonitorTamperByIds" parameterType="Long" resultMap="FfsafeWebmonitorTamperResult">
        <include refid="selectFfsafeWebmonitorTamperVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeWebmonitorTamper" parameterType="FfsafeWebmonitorTamper" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_webmonitor_tamper
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="modifyid != null">modifyId,</if>
            <if test="url != null">url,</if>
            <if test="content != null">content,</if>
            <if test="startTime != null">start_time,</if>
            <if test="recentlyTime != null">recently_time,</if>
            <if test="handleStatus != null">handle_status,</if>
            <if test="systemName != null">system_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="modifyid != null">#{modifyid},</if>
            <if test="url != null">#{url},</if>
            <if test="content != null">#{content},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="recentlyTime != null">#{recentlyTime},</if>
            <if test="handleStatus != null">#{handleStatus},</if>
            <if test="systemName != null">#{systemName},</if>
        </trim>
    </insert>

    <update id="updateFfsafeWebmonitorTamper" parameterType="FfsafeWebmonitorTamper">
        update ffsafe_webmonitor_tamper
        <trim prefix="SET" suffixOverrides=",">
            <if test="modifyid != null">modifyId = #{modifyid},</if>
            <if test="url != null">url = #{url},</if>
            <if test="content != null">content = #{content},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="recentlyTime != null">recently_time = #{recentlyTime},</if>
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeWebmonitorTamperById" parameterType="Long">
        delete from ffsafe_webmonitor_tamper where id = #{id}
    </delete>

    <delete id="deleteFfsafeWebmonitorTamperByIds" parameterType="String">
        delete from ffsafe_webmonitor_tamper where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>