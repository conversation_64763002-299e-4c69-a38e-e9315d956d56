<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.datainterface.mapper.DataErrorLogMapper">

    <resultMap type="DataErrorLog" id="DataErrorLogResult">
        <result property="id"    column="id"    />
        <result property="errorStage"    column="error_stage"    />
        <result property="errorContent"    column="error_content"    />
        <result property="errorDesc"    column="error_desc"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectDataErrorLogVo">
        select id, error_stage, error_content, error_desc, create_time from data_error_log
    </sql>

    <select id="selectDataErrorLogList" parameterType="DataErrorLog" resultMap="DataErrorLogResult">
        <include refid="selectDataErrorLogVo"/>
        <where>
            <if test="errorStage != null  and errorStage != ''"> and error_stage = #{errorStage}</if>
            <if test="errorContent != null  and errorContent != ''"> and error_content = #{errorContent}</if>
            <if test="errorDesc != null  and errorDesc != ''"> and error_desc = #{errorDesc}</if>
        </where>
    </select>

    <select id="selectDataErrorLogById" parameterType="Long" resultMap="DataErrorLogResult">
        <include refid="selectDataErrorLogVo"/>
        where id = #{id}
    </select>

    <select id="selectDataErrorLogByIds" parameterType="Long" resultMap="DataErrorLogResult">
        <include refid="selectDataErrorLogVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertDataErrorLog" parameterType="DataErrorLog" useGeneratedKeys="true" keyProperty="id">
        insert into data_error_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="errorStage != null">error_stage,</if>
            <if test="errorContent != null">error_content,</if>
            <if test="errorDesc != null">error_desc,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="errorStage != null">#{errorStage},</if>
            <if test="errorContent != null">#{errorContent},</if>
            <if test="errorDesc != null">#{errorDesc},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateDataErrorLog" parameterType="DataErrorLog">
        update data_error_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="errorStage != null">error_stage = #{errorStage},</if>
            <if test="errorContent != null">error_content = #{errorContent},</if>
            <if test="errorDesc != null">error_desc = #{errorDesc},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataErrorLogById" parameterType="Long">
        delete from data_error_log where id = #{id}
    </delete>

    <delete id="deleteDataErrorLogByIds" parameterType="String">
        delete from data_error_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>