package cn.anmte.aqsoc.threaten.strategy.engine;

import cn.anmte.aqsoc.threaten.origin.entity.ThreatenAlarmOrigin;
import cn.anmte.aqsoc.threaten.origin.mapper.ThreatenAlarmOriginMapper;
import cn.anmte.aqsoc.threaten.strategy.ThreatenEvent;
import cn.anmte.aqsoc.threaten.strategy.ThreatenStrategy;
import cn.anmte.aqsoc.threaten.strategy.ThreatenStrategyManager;
import cn.anmte.aqsoc.threaten.strategy.engine.excutor.AlarmExcutor;
import cn.anmte.aqsoc.threaten.strategy.engine.excutor.impl.MergeAlarmExcutor;
import cn.anmte.aqsoc.threaten.strategy.engine.excutor.impl.SimpleAlarmExcutor;
import cn.anmte.aqsoc.threaten.strategy.impl.MergeAlarmStrategy;
import cn.anmte.aqsoc.threaten.strategy.impl.SimpleStrategy;
import cn.anmte.aqsoc.threaten.strategy.impl.TypeBaseStrategy;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import com.ruoyi.threaten.domain.TblHoneypotAlarm;
import com.ruoyi.threaten.domain.TblThreatenAlarm;
import com.ruoyi.threaten.mapper.TblHoneypotAlarmMapper;
import com.ruoyi.threaten.mapper.TblThreatenAlarmMapper;
import com.ruoyi.threaten.service.IAttackDirectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 负责解析策略，生成告警
 */
@Slf4j
@Component
public class ThreatenAlarmEngine {
    @Resource
    // 威胁告警服务
    private TblThreatenAlarmMapper tblThreatenAlarmMapper;
    @Resource
    private TblHoneypotAlarmMapper tblHoneypotAlarmMapper;
    @Resource
    private IAttackDirectionService attackDirectionService;
    @Resource
    private SimpleAlarmExcutor simpleAlarmExcutor;
    @Resource
    private MergeAlarmExcutor mergeAlarmExcutor;
    @Resource
    private ThreatenAlarmOriginMapper threatenAlarmOriginMapper;
    @Resource
    private ThreatenStrategyManager threatenStrategyManager;

    @Transactional
    public void run(ThreatenStrategy threatenStrategy, ThreatenEvent event) {
        log.info("====>开始执行策略：{},事件：{}", threatenStrategy.getName(), event);
        // 根据不同的策略获取告警
        executeAndSaveAlarms(threatenStrategy, event);
    }

    private void executeAndSaveAlarms(ThreatenStrategy threatenStrategy, ThreatenEvent event) {
        List<Pair<TblThreatenAlarm, List<Integer>>> alarms = null;
        if (threatenStrategy instanceof SimpleStrategy) {
            alarms = simpleAlarmExcutor.execute(event, threatenStrategy);
        } else if (threatenStrategy instanceof MergeAlarmStrategy) {
            alarms = mergeAlarmExcutor.execute(event, threatenStrategy);
        } else if (threatenStrategy instanceof TypeBaseStrategy) {
            Object o = threatenStrategyManager.getStrategyExcutorMap().get(((TypeBaseStrategy) threatenStrategy).getClassName());
            if (o instanceof AlarmExcutor) {
                AlarmExcutor<TblThreatenAlarm, Integer> alarmExcutor = (AlarmExcutor<TblThreatenAlarm, Integer>) o;
                alarms = alarmExcutor.execute(event, threatenStrategy);
            }
        } else {
            throw new RuntimeException("not support:" + threatenStrategy);
        }

        if (CollectionUtil.isNotEmpty(alarms)) {
            saveAlarms(alarms);
        } else {
            log.info("====>本次执行未产生告警");
        }
    }

    private void saveAlarms(List<Pair<TblThreatenAlarm, List<Integer>>> alarms) {
        for (Pair<TblThreatenAlarm, List<Integer>> pair : alarms) {
            TblThreatenAlarm alarm = pair.getKey();
            if (alarm.getId() == null) {
                alarm.setCreateTime(DateUtil.date());
                if(alarm.getUpdateTime() == null){
                    alarm.setUpdateTime(alarm.getCreateTime());
                }
                alarm.setAlarmNum(pair.getValue().size());
                log.info("====>插入告警：{}", alarm);
                if(alarm.getDataSource() != null && TblThreatenAlarm.HONEYPOT_DATA == alarm.getDataSource()){
                    //蜜罐
                    TblHoneypotAlarm honeypotAlarm = new TblHoneypotAlarm();
                    BeanUtil.copyProperties(alarm,honeypotAlarm);
                    tblHoneypotAlarmMapper.insertTblThreatenAlarm(honeypotAlarm);
                    alarm.setId(honeypotAlarm.getId());
                }else {
                    // 设置攻击方向
                    alarm.setAttackDirection(attackDirectionService.determineAttackDirection(alarm.getSrcIp(), alarm.getDestIp()));
                    tblThreatenAlarmMapper.insertTblThreatenAlarm(alarm);
                }
            } else {
                if(alarm.getUpdateTime() == null){
                    alarm.setUpdateTime(DateUtil.date());
                }
                alarm.setAlarmNum(alarm.getAlarmNum() + pair.getValue().size());
                log.info("====>更新告警：{}", alarm);
                if(alarm.getDataSource() != null && TblThreatenAlarm.HONEYPOT_DATA == alarm.getDataSource()){
                    //蜜罐
                    TblHoneypotAlarm honeypotAlarm = new TblHoneypotAlarm();
                    BeanUtil.copyProperties(alarm,honeypotAlarm);
                    tblHoneypotAlarmMapper.updateTblThreatenAlarm(honeypotAlarm);
                }else {
                    // 设置攻击方向
                    alarm.setAttackDirection(attackDirectionService.determineAttackDirection(alarm.getSrcIp(), alarm.getDestIp()));
                    tblThreatenAlarmMapper.updateTblThreatenAlarm(alarm);
                }
            }
            //插入关联表
            for (Integer orginId : pair.getValue()) {
                ThreatenAlarmOrigin alarmOrgin = new ThreatenAlarmOrigin();
                alarmOrgin.setAlarmId(alarm.getId().intValue());
                alarmOrgin.setThreatenType(alarm.getThreatenType());
                alarmOrgin.setOriginId(orginId);
                alarmOrgin.setCreateTime(DateUtil.toLocalDateTime(DateUtil.date()));
                threatenAlarmOriginMapper.insert(alarmOrgin);
            }
        }
    }
}
