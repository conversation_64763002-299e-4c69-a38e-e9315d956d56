<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.globalSearch.mapper.GlobalSearchMapper">

    <resultMap id="base" type="com.ruoyi.safe.globalSearch.entity.BaseSearch">
        <result column="asset_id" property="assetId"></result>
        <result column="asset_name" property="assetName"></result>
        <result column="dept_name"  property="deptName"></result>
        <result column="asset_class_desc"  property="assetClassDesc"></result>
        <result column="ip"  property="ip"></result>
        <result column="location_detail"  property="locationDetail"></result>
        <result column="location_name"  property="locationName"></result>
        <result column="state"  property="state"></result>
        <result column="manager"  property="manager"></result>
    </resultMap>

    <resultMap id="forBusinessSystem" type="com.ruoyi.safe.globalSearch.entity.ForBusinessSystem" extends="base">
        <result column="app_name" property="appName"></result>
        <result column="deploy_network"  property="deployNetwork"></result>
        <result column="vendors"  property="vendors"></result>
        <result column="maintenance"  property="maintenance"></result>
        <result column="domain_url"  property="domainUrl"></result>
        <result column="system_type"  property="systemType"></result>
        <result column="internal_ip"  property="internalIp"></result>
        <result column="public_expose_ip"  property="publicExposeIp"></result>
        <result column="protection_grade"  property="protectionGrade"></result>
        <result column="url"  property="url"></result>
        <result column="product_name"  property="productName"></result>
        <result column="version_number"  property="versionNumber"></result>
        <result column="concatDeptName"  property="concatDeptName"></result>
    </resultMap>

    <resultMap id="forServer" type="com.ruoyi.safe.globalSearch.entity.ForServer" extends="base">
        <result column="app_name" property="appName"></result>
        <result column="degree_importance" property="degreeImportance"></result>
        <result column="domain_name"  property="domainName"></result>
        <result column="vendor_str"  property="vendorStr"></result>
        <result column="firm_str"  property="firmStr"></result>
        <result column="maintenance_str"  property="maintenanceStr"></result>
        <result column="asset_code"  property="assetCode"></result>
        <result column="dept_name"  property="deptName"></result>
        <result column="host_ip"  property="hostIp"></result>
        <result column="public_expose_ip"  property="publicExposeIp"></result>
        <result column="internal_ip"  property="internalIp"></result>
        <result column="asset_name"  property="assetName"></result>
        <result column="procType"  property="procType"></result>
        <result column="systemName"  property="systemName"></result>
        <result column="systemName"  property="systemName"></result>
        <result column="software_name"  property="softwareName"></result>
        <result column="software_version"  property="softwareVersion"></result>
        <result column="concatDeptName"  property="concatDeptName"></result>
    </resultMap>

    <resultMap id="forSafety" type="com.ruoyi.safe.globalSearch.entity.ForSafety" extends="base">
        <result column="app_name" property="appName"></result>
        <result column="degree_importance" property="degreeImportance"></result>
        <result column="domain_name"  property="domainName"></result>
        <result column="vendor_str"  property="vendorStr"></result>
        <result column="firm_str"  property="firmStr"></result>
        <result column="asset_code"  property="assetCode"></result>
        <result column="lastScanState"  property="lastScanState"></result>
        <result column="procType"  property="procType"></result>
        <result column="systemName"  property="systemName"></result>
        <result column="concatDeptName"  property="concatDeptName"></result>
    </resultMap>

    <resultMap id="forNetwork" type="com.ruoyi.safe.globalSearch.entity.ForNetwork" extends="base">
        <result column="app_name" property="appName"></result>
        <result column="degree_importance" property="degreeImportance"></result>
        <result column="domain_name"  property="domainName"></result>
        <result column="vendor_str"  property="vendorStr"></result>
        <result column="firm_str"  property="firmStr"></result>
        <result column="asset_code"  property="assetCode"></result>
        <result column="proc_name"  property="procName"></result>
        <result column="systemName"  property="systemName"></result>
        <result column="concatDeptName"  property="concatDeptName"></result>
    </resultMap>

    <resultMap id="forProduct" type="com.ruoyi.safe.globalSearch.entity.ForProduct" extends="base">
        <result column="proc_name" property="procName"></result>
        <result column="proc_type" property="procType"></result>
        <result column="app_name" property="appName"></result>
        <result column="port" property="port"></result>
        <result column="deploy_port" property="deployPort"></result>
        <result column="deploy_position" property="deployPosition"></result>
        <result column="degree_importance" property="degreeImportance"></result>
        <result column="domain_name"  property="domainName"></result>
    </resultMap>

    <select id="getUserIds" resultType="java.lang.Long">
        select user_id from sys_user where nick_name like CONCAT('%', #{queryParam}, '%')  or phonenumber like CONCAT('%', #{queryParam}, '%')
    </select>

    <select id="searchBusinessSystem" resultMap="forBusinessSystem">
        SELECT
        t1.*
        FROM
        (
        SELECT
        t.*
        FROM
        (
        SELECT
        td.proc_name,
        tba.product_name,
        tba.version_number,
        tba.create_time,
        tao1.applicationState,
        tba.system_type AS systemType,
        tba.protect_grade AS classifiedProtectionLevel,
        t1.server_id AS ref_id,
        tba.asset_id,
        tba.ipd,
        tba.domain_url,
        tba.url,
        tfn.internal_ip,
        tfn.public_expose_ip,
        sdd.dict_label system_type,
        sid.dict_label protection_grade,
        std.dict_label technical,
        tba.asset_name AS app_name,
        sd.dept_name,
        IF(sd.concatDeptName IS NOT NULL,CONCAT(sd.concatDeptName,'/',sd.dept_name),sd.dept_name) concatDeptName,
        tnd.domain_name AS deploy_network,
        tba.manager,
        tba.vendors,
        GROUP_CONCAT( tace.eid ) AS maintenance,
        tao.asset_name,
        tao1.state,
        tao.asset_class_desc,
        tnim.ipv4 AS ip,
        tao.location_detail,
        tl.location_name
        FROM
        tbl_business_application tba
        LEFT JOIN (
        SELECT
        d1.*,
        GROUP_CONCAT(d2.dept_name ORDER BY d2.parent_id SEPARATOR'/') concatDeptName
        FROM
        sys_dept d1
        LEFT JOIN sys_dept d2 ON LOCATE(d2.dept_id,d1.ancestors)
        GROUP BY
        d1.dept_id
        ) sd ON tba.dept_id = sd.dept_id
        LEFT JOIN tbl_network_domain tnd ON tba.domain_id = tnd.domain_id
        LEFT JOIN tbl_asset_company_emp tace ON tace.asset_id = tba.asset_id
        LEFT JOIN tbl_application_server t1 ON tba.asset_id = t1.asset_id
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = t1.server_id
        LEFT JOIN tbl_deploy td ON td.asset_id = tao.asset_id
        LEFT JOIN tbl_firewall_nat tfn ON FIND_IN_SET(tba.asset_id,tfn.business_application_id)
        LEFT JOIN (
        SELECT
        *,
        CASE
        WHEN state = '1' THEN
        '在线'
        WHEN state = '0' THEN
        '离线'
        WHEN state = '2' THEN
        '停用'
        END applicationState
        FROM
        tbl_asset_overview
        ) tao1 ON tao1.asset_id = tba.asset_id
        LEFT JOIN tbl_network_ip_mac tnim ON tao.asset_id = tnim.asset_id
        AND tnim.main_ip = '1'
        LEFT JOIN tbl_location tl ON tao.location_id = tl.location_id
        LEFT JOIN (
        SELECT
        *
        FROM
        sys_dict_data
        WHERE
        INSTR(dict_type,'system_type') > 0
        ) sdd ON sdd.dict_value = tba.system_type
        LEFT JOIN (
        SELECT
        *
        FROM
        sys_dict_data
        WHERE
        INSTR(dict_type,'protection_grade') > 0
        ) sid ON sid.dict_value = tba.protect_grade
        LEFT JOIN(
        SELECT
        dict_label,dict_type,dict_value
        FROM
        sys_dict_data
        WHERE
        INSTR(dict_type,'app_technical') > 0
        ) std ON std.dict_value = tba.technical
        <where>
            sd.dept_name IS NOT NULL
            <if test="params.dataScope == null or params.dataScope != ''">${params.dataScope}</if>
        </where>
        GROUP BY
        t1.server_id,
        IFNULL(
        tace.asset_id,
        UUID()) UNION ALL
        SELECT
        td.proc_name,
        tba.product_name,
        tba.version_number,
        tba.create_time,
        tao1.applicationState,
        tba.system_type AS systemType,
        tba.protect_grade AS classifiedProtectionLevel,
        t2.safe_id AS ref_id,
        tba.asset_id,
        tba.ipd,
        tba.domain_url,
        tba.url,
        tfn.internal_ip,
        tfn.public_expose_ip,
        sdd.dict_label system_type,
        sid.dict_label protection_grade,
        std.dict_label technical,
        tba.asset_name AS app_name,
        sd.dept_name,
        IF(sd.concatDeptName IS NOT NULL,CONCAT(sd.concatDeptName,'/',sd.dept_name),sd.dept_name) concatDeptName,
        tnd.domain_name AS deploy_network,
        tba.manager,
        tba.vendors,
        GROUP_CONCAT( tace.eid ) AS maintenance,
        tao.asset_name,
        tao1.state,
        tao.asset_class_desc,
        tnim.ipv4 AS ip,
        tao.location_detail,
        tl.location_name
        FROM
        tbl_business_application tba
        LEFT JOIN (
        SELECT
        d1.*,
        GROUP_CONCAT(d2.dept_name ORDER BY d2.parent_id SEPARATOR'/') concatDeptName
        FROM
        sys_dept d1
        LEFT JOIN sys_dept d2 ON LOCATE(d2.dept_id,d1.ancestors)
        GROUP BY
        d1.dept_id
        ) sd ON tba.dept_id = sd.dept_id
        LEFT JOIN tbl_network_domain tnd ON tba.domain_id = tnd.domain_id
        LEFT JOIN tbl_asset_company_emp tace ON tace.asset_id = tba.asset_id
        LEFT JOIN tbl_application_safe t2 ON tba.asset_id = t2.asset_id
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = t2.safe_id
        LEFT JOIN tbl_deploy td ON td.asset_id = tao.asset_id
        LEFT JOIN tbl_firewall_nat tfn ON FIND_IN_SET(tba.asset_id,tfn.business_application_id)
        LEFT JOIN (
        SELECT
        *,
        CASE
        WHEN state = '1' THEN
        '在线'
        WHEN state = '0' THEN
        '离线'
        WHEN state = '2' THEN
        '停用'
        END applicationState
        FROM
        tbl_asset_overview
        ) tao1 ON tao1.asset_id = tba.asset_id
        LEFT JOIN tbl_network_ip_mac tnim ON tao.asset_id = tnim.asset_id
        AND tnim.main_ip = '1'
        LEFT JOIN tbl_location tl ON tao.location_id = tl.location_id
        LEFT JOIN (
        SELECT
        *
        FROM
        sys_dict_data
        WHERE
        INSTR(dict_type,'system_type') > 0
        ) sdd ON sdd.dict_value = tba.system_type
        LEFT JOIN (
        SELECT
        *
        FROM
        sys_dict_data
        WHERE
        INSTR(dict_type,'protection_grade') > 0
        ) sid ON sid.dict_value = tba.protect_grade
        LEFT JOIN(
        SELECT
        dict_label,dict_type,dict_value
        FROM
        sys_dict_data
        WHERE
        INSTR(dict_type,'app_technical') > 0
        ) std ON std.dict_value = tba.technical
        <where>
            sd.dept_name IS NOT NULL
            <if test="params.dataScope == null or params.dataScope != ''">${params.dataScope}</if>
        </where>
        GROUP BY
        t2.safe_id,
        IFNULL(
        tace.asset_id,
        UUID()) UNION ALL
        SELECT
        td.proc_name,
        tba.product_name,
        tba.version_number,
        tba.create_time,
        tao1.applicationState,
        tba.system_type AS systemType,
        tba.protect_grade AS classifiedProtectionLevel,
        t3.netware_id AS ref_id,
        tba.asset_id,
        tba.ipd,
        tba.domain_url,
        tba.url,
        tfn.internal_ip,
        tfn.public_expose_ip,
        sdd.dict_label system_type,
        sid.dict_label protection_grade,
        std.dict_label technical,
        tba.asset_name AS app_name,
        sd.dept_name,
        IF(sd.concatDeptName IS NOT NULL,CONCAT(sd.concatDeptName,'/',sd.dept_name),sd.dept_name) concatDeptName,
        tnd.domain_name AS deploy_network,
        tba.manager,
        tba.vendors,
        GROUP_CONCAT( tace.eid ) AS maintenance,
        tao.asset_name,
        tao1.state,
        tao.asset_class_desc,
        tnim.ipv4 AS ip,
        tao.location_detail,
        tl.location_name
        FROM
        tbl_business_application tba
        LEFT JOIN (
        SELECT
        d1.*,
        GROUP_CONCAT(d2.dept_name ORDER BY d2.parent_id SEPARATOR'/') concatDeptName
        FROM
        sys_dept d1
        LEFT JOIN sys_dept d2 ON LOCATE(d2.dept_id,d1.ancestors)
        GROUP BY
        d1.dept_id
        ) sd ON tba.dept_id = sd.dept_id
        LEFT JOIN tbl_network_domain tnd ON tba.domain_id = tnd.domain_id
        LEFT JOIN tbl_asset_company_emp tace ON tace.asset_id = tba.asset_id
        LEFT JOIN tbl_application_netware t3 ON tba.asset_id = t3.asset_id
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = t3.netware_id
        LEFT JOIN tbl_deploy td ON td.asset_id = tao.asset_id
        LEFT JOIN tbl_firewall_nat tfn ON FIND_IN_SET(tba.asset_id,tfn.business_application_id)
        LEFT JOIN (
        SELECT
        *,
        CASE
        WHEN state = '1' THEN
        '在线'
        WHEN state = '0' THEN
        '离线'
        WHEN state = '2' THEN
        '停用'
        END applicationState
        FROM
        tbl_asset_overview
        ) tao1 ON tao1.asset_id = tba.asset_id
        LEFT JOIN tbl_network_ip_mac tnim ON tao.asset_id = tnim.asset_id
        AND tnim.main_ip = '1'
        LEFT JOIN tbl_location tl ON tao.location_id = tl.location_id
        LEFT JOIN (
        SELECT
        *
        FROM
        sys_dict_data
        WHERE
        INSTR(dict_type,'system_type') > 0
        ) sdd ON sdd.dict_value = tba.system_type
        LEFT JOIN (
        SELECT
        *
        FROM
        sys_dict_data
        WHERE
        INSTR(dict_type,'protection_grade') > 0
        ) sid ON sid.dict_value = tba.protect_grade
        LEFT JOIN(
        SELECT
        dict_label,dict_type,dict_value
        FROM
        sys_dict_data
        WHERE
        INSTR(dict_type,'app_technical') > 0
        ) std ON std.dict_value = tba.technical
        <where>
            sd.dept_name IS NOT NULL
            <if test="params.dataScope == null or params.dataScope != ''">${params.dataScope}</if>
        </where>
        GROUP BY
        t3.netware_id,
        IFNULL(
        tace.asset_id,
        UUID())) t
        <where>
            <if test="queryParam != null and queryParam != ''">
                INSTR(t.app_name, #{queryParam}) > 0 OR t.dept_name like concat('%',#{queryParam},'%') OR t.ipd like concat('%',#{queryParam},'%') OR t.domain_url like concat('%',#{queryParam},'%')
                OR t.system_type like concat('%',#{queryParam},'%') OR t.protection_grade like concat('%',#{queryParam},'%') OR t.technical like concat('%',#{queryParam},'%') OR t.applicationState like concat('%',#{queryParam},'%') OR t.url like concat('%',#{queryParam},'%') OR t.internal_ip like concat('%',#{queryParam},'%') OR t.public_expose_ip like concat('%',#{queryParam},'%')
                OR t.product_name like concat('%',#{queryParam},'%')
                OR t.version_number like concat('%',#{queryParam},'%')
                OR t.proc_name like concat('%',#{queryParam},'%')
            </if>
            <if test="userIds != null and userIds.size() > 0">
                <foreach collection="userIds" item="userId">
                    or FIND_IN_SET(#{userId},t.manager)
                </foreach>
            </if>
            <!--AND t.ref_id IS NOT NULL-->
        </where>
        <!--group by IFNULL(t.ref_id, 0)-->
        order by t.create_time desc, t.app_name
        ) t1
        GROUP BY t1.asset_id
    </select>


    <select id="searchServer" resultMap="forServer">
        SELECT
        tbl1.*
        FROM
        (
        SELECT
        t.*
        FROM
        (
        SELECT DISTINCT
        tas.asset_id AS app_id,
        fsd.software_name,
        fsd.software_version,
        tfn.internal_ip,
        tba.ipd,
        tba.url,
        tba.domain_url,
        tfn.public_expose_ip,
        tnim.lastScanState,
        ts.serial,
        ts.create_time,
        ts.asset_id,
        ts.asset_code,
        ts.asset_name,
        t4.proc_name,
        CASE
        WHEN t4.softlx = '1' THEN
        '操作系统'
        WHEN t4.softlx = '2' THEN
        '数据库'
        WHEN t4.softlx = '3' THEN
        '中间件'
        END procType,
        td.proc_name AS systemName,
        ts.degree_importance,
        sd.dept_name,
        IF(sd.concatDeptName IS NOT NULL,CONCAT(sd.concatDeptName,'/',sd.dept_name),sd.dept_name) concatDeptName,
        tao.asset_class_desc,
        tao.state,
        tao.location_detail,
        tl.location_name,
        tnim.ipv4 AS ip,
        tnd.domain_name,
        CONCAT( t1.vendor_name, '&lt;br&gt;', t1.vendor_manage_name, '-', t1.vendor_phone ) AS vendor_str,
        tba.asset_name AS app_name,
        tba.manager,
        CONCAT( t2.vendor_name, '&lt;br&gt;', t2.vendor_manage_name, '-', t2.vendor_phone ) AS firm_str,
        CONCAT( t3.vendor_name, '&lt;br&gt;', t3.vendor_manage_name, '-', t3.vendor_phone ) AS maintenance_str
        FROM
        tbl_server ts
        LEFT JOIN (
        SELECT
        *
        FROM
        ffsafe_software_details
        WHERE
        software_name LIKE concat( '%', #{queryParam}, '%' )
        OR software_version LIKE concat( '%', #{queryParam}, '%' )
        ) fsd ON fsd.asset_id = ts.asset_id
        LEFT JOIN (
        SELECT
        d1.*,
        GROUP_CONCAT(d2.dept_name ORDER BY d2.parent_id SEPARATOR'/') concatDeptName
        FROM
        sys_dept d1
        LEFT JOIN sys_dept d2 ON LOCATE(d2.dept_id,d1.ancestors)
        GROUP BY
        d1.dept_id
        ) sd ON ts.dept_id = sd.dept_id
        LEFT JOIN tbl_asset_overview tao ON ts.asset_id = tao.asset_id
        LEFT JOIN ( SELECT *, CASE WHEN last_scan_state = 1 THEN '在线' WHEN last_scan_state = 0 THEN '离线' ELSE '离线' END AS lastScanState FROM tbl_network_ip_mac ) tnim ON ts.asset_id = tnim.asset_id
        AND tnim.main_ip = '1'
        LEFT JOIN tbl_location tl ON tao.location_id = tl.location_id
        LEFT JOIN tbl_application_server tas ON tas.server_id = ts.asset_id
        LEFT JOIN tbl_business_application tba ON tas.asset_id = tba.asset_id
        LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = tnim.domain_id
        LEFT JOIN tbl_vendor t1 ON t1.id = ts.vendor
        LEFT JOIN tbl_vendor t2 ON t2.id = ts.facility_manufacturer
        LEFT JOIN tbl_vendor t3 ON t3.id = ts.maintain_unit
        LEFT JOIN tbl_deploy t4 ON t4.asset_id = ts.asset_id
        LEFT JOIN tbl_firewall_nat tfn ON tfn.server_id = ts.asset_id
        LEFT JOIN tbl_deploy td ON ts.asset_id = td.asset_id
        AND td.softlx = '1'
        <where>
            sd.dept_name IS NOT NULL
            <if test="params.dataScope == null or params.dataScope != ''">${params.dataScope}</if>
        </where>
        ) t
        <where>
            <if test="queryParam != null and queryParam != ''">
                INSTR( t.asset_name, #{queryParam} ) > 0
                OR t.ip LIKE concat( '%', #{queryParam}, '%' )
                OR t.asset_code LIKE concat( '%', #{queryParam}, '%' )
                OR t.dept_name LIKE concat( '%', #{queryParam}, '%' )
                OR t.ip LIKE concat( '%', #{queryParam}, '%' )
                OR t.proc_name LIKE concat( '%', #{queryParam}, '%' )
                OR t.lastScanState LIKE concat( '%', #{queryParam}, '%' )
                OR t.internal_ip LIKE concat( '%', #{queryParam}, '%' )
                OR t.public_expose_ip LIKE concat( '%', #{queryParam}, '%' )
                OR t.procType LIKE concat( '%', #{queryParam}, '%' )
                OR t.software_name LIKE concat( '%', #{queryParam}, '%' )
                OR t.software_version LIKE concat( '%', #{queryParam}, '%' )
            </if>
            <if test="serverIds != null and serverIds != ''">
                OR FIND_IN_SET(t.asset_id,#{serverIds})
            </if>
            <if test="userIds != null and userIds.size() > 0">
                <foreach collection="userIds" item="userId">
                    or FIND_IN_SET(#{userId},t.manager)
                </foreach>
            </if>
        </where>
        ) AS tbl1
        GROUP BY
        tbl1.asset_id
    </select>

    <select id="searchSafety" resultMap="forSafety">
        SELECT
        tbl1.*
        FROM
        (
        SELECT
        t.*
        FROM
        (
        SELECT DISTINCT
        tas.asset_id AS app_id,
        ts.create_time,
        ts.asset_id,
        ts.asset_code,
        tnim.lastScanState,
        tbd.proc_name,
        pt.dict_label AS procType,
        (SELECT proc_name FROM tbl_deploy WHERE asset_id = ts.asset_id AND deploy_type = 'system') AS systemName,
        ts.asset_name,
        ts.degree_importance,
        sd.dept_name,
        IF(sd.concatDeptName IS NOT NULL,CONCAT(sd.concatDeptName,'/',sd.dept_name),sd.dept_name) concatDeptName,
        tao.asset_class_desc,
        tao.state,
        tao.location_detail,
        tl.location_name,
        tnim.ipv4 AS ip,
        tnd.domain_name,
        CONCAT( t1.vendor_name, '&lt;br&gt;', t1.vendor_manage_name, '-', t1.vendor_phone ) AS vendor_str,
        tba.asset_name AS app_name,
        tba.manager,
        ts.brand_model AS firm_str
        FROM
        tbl_safety ts
        LEFT JOIN (
        SELECT
        d1.*,
        GROUP_CONCAT(d2.dept_name ORDER BY d2.parent_id SEPARATOR'/') concatDeptName
        FROM
        sys_dept d1
        LEFT JOIN sys_dept d2 ON LOCATE(d2.dept_id,d1.ancestors)
        GROUP BY
        d1.dept_id
        ) sd ON ts.dept_id = sd.dept_id
        LEFT JOIN tbl_asset_overview tao ON ts.asset_id = tao.asset_id
        LEFT JOIN (
        SELECT
        *,
        CASE
        WHEN last_scan_state = 1 THEN
        '在线'
        WHEN last_scan_state = 0 THEN
        '离线'
        ELSE
        '离线'
        END lastScanState
        FROM
        tbl_network_ip_mac
        ) tnim ON ts.asset_id = tnim.asset_id
        AND tnim.main_ip = '1'
        LEFT JOIN tbl_location tl ON tao.location_id = tl.location_id
        LEFT JOIN tbl_application_safe tas ON tas.safe_id = ts.asset_id
        LEFT JOIN tbl_business_application tba ON tas.asset_id = tba.asset_id
        LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = tnim.domain_id
        LEFT JOIN tbl_vendor t1 ON t1.id = ts.vendor
        LEFT JOIN tbl_deploy tbd ON tbd.asset_id = ts.asset_id
        LEFT JOIN (
        SELECT
        dict_label,dict_type,dict_value
        FROM
        sys_dict_data
        WHERE
        INSTR(dict_type,'proc_type') > 0
        ) pt ON pt.dict_value = tbd.deploy_type
        <where>
            sd.dept_name IS NOT NULL
            <if test="params.dataScope == null or params.dataScope != ''">${params.dataScope}</if>
        </where>
        ) t
        <where>
            <if test="queryParam != null and queryParam != ''">
                INSTR(t.asset_name, #{queryParam}) > 0 or t.ip like concat('%',#{queryParam},'%')
                OR t.asset_code like concat('%',#{queryParam},'%')
                OR t.dept_name like concat('%',#{queryParam},'%')
                OR t.lastScanState like concat('%',#{queryParam},'%')
                OR t.domain_name like concat('%',#{queryParam},'%')
                OR t.proc_name like concat('%',#{queryParam},'%')
                OR t.procType like concat('%',#{queryParam},'%')
            </if>
            <if test="userIds != null and userIds.size() > 0">
                <foreach collection="userIds" item="userId">
                    or FIND_IN_SET(#{userId},t.manager)
                </foreach>
            </if>
        </where>
        ORDER BY
        t.create_time DESC,
        t.asset_name
        ) AS tbl1 LEFT JOIN (
        SELECT
        *
        FROM
        tbl_deploy
        GROUP BY
        asset_id
        ) tbl2 ON tbl1.asset_id = tbl2.asset_id
        GROUP BY
        tbl1.asset_id
    </select>

    <select id="searchNetwork" resultMap="forNetwork">
        SELECT
        tbl1.*,tbl2.proc_name procName,COUNT(tbl1.asset_id) findCount
        FROM
        (
        SELECT
        t.*
        FROM
        (
        SELECT DISTINCT
        tan.asset_id AS app_id,
        tn.asset_code,
        tdy.proc_name,
        pt.dict_label AS procType,
        (SELECT proc_name FROM tbl_deploy WHERE asset_id = tn.asset_id AND deploy_type = 'system') AS systemName,
        tnim.lastScanState,
        tn.create_time,
        tn.asset_id,
        tn.asset_name,
        tn.degree_importance,
        sd.dept_name,
        IF(sd.concatDeptName IS NOT NULL,CONCAT(sd.concatDeptName,'/',sd.dept_name),sd.dept_name) concatDeptName,
        tao.asset_class_desc,
        tao.state,
        tao.location_detail,
        tl.location_name,
        tnim.ipv4 AS ip,
        tnd.domain_name,
        CONCAT( t1.vendor_name, '&lt;br&gt;', t1.vendor_manage_name, '-', t1.vendor_phone ) AS vendor_str,
        tba.asset_name AS app_name,
        tba.manager,
        tn.brand_model AS firm_str
        FROM
        tbl_network_devices tn
        LEFT JOIN (
        SELECT
        d1.*,
        GROUP_CONCAT(d2.dept_name ORDER BY d2.parent_id SEPARATOR'/') concatDeptName
        FROM
        sys_dept d1
        LEFT JOIN sys_dept d2 ON LOCATE(d2.dept_id,d1.ancestors)
        GROUP BY
        d1.dept_id
        ) sd ON tn.dept_id = sd.dept_id
        LEFT JOIN tbl_asset_overview tao ON tn.asset_id = tao.asset_id
        LEFT JOIN (
        SELECT
        *,
        CASE
        WHEN last_scan_state = 1 THEN
        '在线'
        WHEN last_scan_state = 0 THEN
        '离线'
        ELSE
        '离线'
        END lastScanState
        FROM
        tbl_network_ip_mac
        ) tnim ON tn.asset_id = tnim.asset_id
        AND tnim.main_ip = '1'
        LEFT JOIN tbl_location tl ON tao.location_id = tl.location_id
        LEFT JOIN tbl_application_netware tan ON tan.netware_id = tn.asset_id
        LEFT JOIN tbl_business_application tba ON tan.asset_id = tba.asset_id
        LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = tnim.domain_id
        LEFT JOIN tbl_vendor t1 ON t1.id = tn.vendor
        LEFT JOIN tbl_deploy tdy ON tdy.asset_id = tn.asset_id
        LEFT JOIN (
        SELECT
        dict_label,dict_type,dict_value
        FROM
        sys_dict_data
        WHERE
        INSTR(dict_type,'proc_type') > 0
        ) pt ON pt.dict_value = tdy.deploy_type
        <where>
            sd.dept_name IS NOT NULL
            <if test="params.dataScope == null or params.dataScope != ''">${params.dataScope}</if>
        </where>
        ) t
        <where>
            <if test="queryParam != null and queryParam != ''">
                INSTR(t.asset_name, #{queryParam}) > 0 or t.ip like concat('%',#{queryParam},'%') OR t.asset_code like concat('%',#{queryParam},'%')
                OR t.dept_name like concat('%',#{queryParam},'%') OR t.ip like concat('%',#{queryParam},'%') OR t.lastScanState like concat('%',#{queryParam},'%') OR t.domain_name like concat('%',#{queryParam},'%') OR t.proc_name like concat('%',#{queryParam},'%')
                OR t.procType like concat('%',#{queryParam},'%')
            </if>
            <if test="userIds != null and userIds.size() > 0">
                <foreach collection="userIds" item="userId">
                    or FIND_IN_SET(#{userId},t.manager)
                </foreach>
            </if>
        </where>
        order by t.create_time desc, t.asset_name
        ) AS tbl1 LEFT JOIN (
        SELECT
        *
        FROM
        tbl_deploy
        GROUP BY
        asset_id
        ) tbl2 ON tbl1.asset_id = tbl2.asset_id
        GROUP BY
        tbl1.asset_id
    </select>

    <select id="searchProduct" resultMap="forProduct">
        select t.* from (select DISTINCT tas.asset_id as app_id, tp.update_time, tp.proc_name, td.asset_id,  ts.asset_name, tp.proc_type, sd.dept_name, td.port, td.deploy_position, tnd.domain_name,ts.degree_importance,
          tnim.ipv4 as ip, tao.location_detail, tl.location_name, tao.asset_class_desc, tao.state, tnim.last_scan_state AS lastScanState, tba.asset_name as app_name, tba.manager, tas.port as deploy_port
        from tbl_product tp
        left join tbl_deploy td on td.prdid = tp.prdid
        left join tbl_asset_overview tao on td.asset_id = tao.asset_id
        left join tbl_server ts on td.asset_id = ts.asset_id
        left join tbl_network_ip_mac tnim on td.asset_id = tnim.asset_id and tnim.main_ip = '1'
        left join tbl_location tl on tao.location_id = tl.location_id
        left join sys_dept sd on sd.dept_id = ts.dept_id
        left join tbl_network_domain tnd on tnd.domain_id = tnim.domain_id
        left join tbl_application_server tas on tas.server_id = ts.asset_id
        left join tbl_business_application tba on tas.asset_id = tba.asset_id) t
        <where>
            <if test="queryParam != null and queryParam != ''">
                INSTR(t.proc_name, #{queryParam}) > 0 or INSTR(t.app_name, #{queryParam}) > 0 or t.ip = #{queryParam} or t.port = #{queryParam} or t.deploy_port = #{queryParam}
            </if>
            <if test="userIds != null and userIds.size() > 0">
                <foreach collection="userIds" item="userId">
                    or FIND_IN_SET(#{userId},t.manager)
                </foreach>
            </if>
        </where>
        order by t.update_time desc, t.proc_name
    </select>

    <select id="selectMonitorBssVulnDealServerIds" resultType="java.lang.String">
        SELECT
            nim.asset_id serverId,
            vd.category,
            vd.host_ip,
            CASE
                WHEN wo.flow_state = 0 THEN
                    '待审核'
                WHEN wo.flow_state = 1 THEN
                    '待处置'
                WHEN wo.flow_state = 2 THEN
                    '待审核'
                WHEN wo.flow_state = 3 THEN
                    '待验证'
                WHEN wo.flow_state = 4 THEN
                    '已完成'
                WHEN wo.flow_state = - 1 THEN
                    '待提交'
                WHEN wo.flow_state = 99 THEN
                    '未分配' ELSE '未分配'
                END AS flowState,
            vd.title,
            wo.flow_state,
            wo.id AS workId,
            wo.prod_id,
            wh.handle_state
        FROM
            (
                SELECT
                    id,
                    category,
                    host_ip,
                    title
                FROM
                    monitor_bss_vuln_deal
                WHERE
                    category  = #{queryParam}
                   OR title LIKE concat( '%', #{queryParam}, '%' )
                <if test="serverIp != null and serverIp !=''">
                 OR FIND_IN_SET(host_ip,#{serverIp})
                </if>
            ) vd
                LEFT JOIN tbl_work_order wo ON FIND_IN_SET( vd.id, wo.event_ids )
                AND wo.work_type = '1'
                LEFT JOIN tbl_work_history wh ON wh.work_id = wo.id
                AND wh.id = ( SELECT MAX( twh.id ) FROM tbl_work_history twh WHERE twh.work_id = wo.id )
                LEFT JOIN tbl_network_ip_mac nim ON vd.host_ip = nim.ipv4
        GROUP BY
            nim.asset_id
    </select>

    <select id="selectMonitorServerPortIp" resultType="java.lang.String">
        SELECT
            IP AS server_ip
        FROM
            monitor_server_port
        WHERE
            port LIKE concat( '%', #{queryParam}, '%' )
           OR app LIKE concat( '%', #{queryParam}, '%' )
        GROUP BY
            IP
    </select>
</mapper>
