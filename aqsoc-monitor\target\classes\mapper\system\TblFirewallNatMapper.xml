<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblFirewallNatMapper">

    <resultMap type="TblFirewallNat" id="TblFirewallNatResult">
        <result property="id"    column="id"    />
        <result property="platform"    column="platform"    />
        <result property="assetId"    column="asset_id"    />
        <result property="name"    column="name"    />
        <result property="internalIp"    column="internal_ip"    />
        <result property="internalPort"    column="internal_port"    />
        <result property="purpose"    column="purpose"    />
        <result property="publicExposeIp"    column="public_expose_ip"    />
        <result property="publicExposePort"    column="public_expose_port"    />
        <result property="applicant"    column="applicant"    />
        <result property="outCrtTime"    column="out_crt_time"    />
        <result property="businessApplicationId"    column="business_application_id"    />
        <result property="serverId"    column="server_id"    />
        <result property="assetName" column="asset_name" />
        <result property="outId" column="out_id" />
    </resultMap>

    <sql id="selectTblFirewallNatVo">
        select DISTINCT t1.id, t1.platform, t1.asset_id, t1.name, t1.internal_ip, t1.internal_port, t1.purpose, t1.public_expose_ip, t1.public_expose_port, t1.applicant, t1.out_crt_time, t1.business_application_id, t1.server_id,t1.out_id,t2.asset_name from tbl_firewall_nat t1
        left join tbl_safety t2 on t2.asset_id = t1.asset_id
        LEFT JOIN tbl_business_application tba ON FIND_IN_SET(tba.asset_id,t1.business_application_id)
    </sql>

    <select id="selectTblFirewallNatList" parameterType="TblFirewallNat" resultMap="TblFirewallNatResult">
        <include refid="selectTblFirewallNatVo"/>
        <where>
            <if test="assetId != null "> and t1.asset_id = #{assetId}</if>
            <if test="name != null  and name != ''"> and t1.name like concat('%', #{name}, '%')</if>
            <if test="internalIp != null  and internalIp != ''"> and t1.internal_ip like concat('%',  #{internalIp}, '%')</if>
            <if test="internalPort != null  and internalPort != ''"> and t1.internal_port = #{internalPort}</if>
            <if test="purpose != null  and purpose != ''"> and t1.purpose = #{purpose}</if>
            <if test="publicExposeIp != null  and publicExposeIp != ''"> and t1.public_expose_ip like concat('%',  #{publicExposeIp}, '%')</if>
            <if test="publicExposePort != null  and publicExposePort != ''"> and t1.public_expose_port = #{publicExposePort}</if>
            <if test="applicant != null  and applicant != ''"> and t1.applicant = #{applicant}</if>
            <if test="outCrtTime != null "> and t1.out_crt_time = #{outCrtTime}</if>
            <if test="businessApplicationId != null "> and FIND_IN_SET(#{businessApplicationId},t1.business_application_id) </if>
            <if test="serverId != null "> and FIND_IN_SET(#{serverId},t1.server_id)</if>
            <if test="outId != null and outId != ''"> and t1.out_id = #{outId}</if>
            <if test="deptId != null and deptId != 0">
                and (t2.dept_id = #{deptId} or t2.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="searchPort != null and searchPort != '' ">
                and (t1.internal_port like concat('%',  #{searchPort}, '%') or t1.public_expose_port like concat('%',  #{searchPort}, '%') )
            </if>
            <if test="applicationName != null and applicationName != '' ">
                and tba.asset_name like concat('%',  #{applicationName}, '%')
            </if>
        </where>
    </select>

    <select id="selectTblFirewallNatById" parameterType="Long" resultMap="TblFirewallNatResult">
        <include refid="selectTblFirewallNatVo"/>
        where t1.id = #{id}
    </select>

    <select id="selectTblFirewallNatByIds" parameterType="Long" resultMap="TblFirewallNatResult">
        <include refid="selectTblFirewallNatVo"/>
        where t1.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectByAssetIds" resultType="com.ruoyi.safe.domain.TblFirewallNat" resultMap="TblFirewallNatResult">
        <include refid="selectTblFirewallNatVo"/>
        where t1.asset_id in
        <foreach item="assetId" collection="assetIds" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </select>
    <select id="countNum" resultType="java.lang.Integer">
        select count(1) from tbl_firewall_nat
    </select>

    <insert id="insertTblFirewallNat" parameterType="TblFirewallNat" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_firewall_nat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platform != null">platform,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="name != null">`name`,</if>
            <if test="internalIp != null">internal_ip,</if>
            <if test="internalPort != null">internal_port,</if>
            <if test="purpose != null">purpose,</if>
            <if test="publicExposeIp != null">public_expose_ip,</if>
            <if test="publicExposePort != null">public_expose_port,</if>
            <if test="applicant != null">applicant,</if>
            <if test="outCrtTime != null">out_crt_time,</if>
            <if test="businessApplicationId != null">business_application_id,</if>
            <if test="serverId != null">server_id,</if>
            <if test="outId != null and outId != ''">out_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platform != null">#{platform},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="name != null">#{name},</if>
            <if test="internalIp != null">#{internalIp},</if>
            <if test="internalPort != null">#{internalPort},</if>
            <if test="purpose != null">#{purpose},</if>
            <if test="publicExposeIp != null">#{publicExposeIp},</if>
            <if test="publicExposePort != null">#{publicExposePort},</if>
            <if test="applicant != null">#{applicant},</if>
            <if test="outCrtTime != null">#{outCrtTime},</if>
            <if test="businessApplicationId != null">#{businessApplicationId},</if>
            <if test="serverId != null">#{serverId},</if>
            <if test="outId != null and outId != ''">#{outId},</if>
         </trim>
    </insert>
    <insert id="insertBatch">
        insert into tbl_firewall_nat(platform, asset_id, name, internal_ip, internal_port, purpose, public_expose_ip, public_expose_port, applicant, out_crt_time, business_application_id, server_id,out_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.platform},#{item.assetId},#{item.name},#{item.internalIp},#{item.internalPort},#{item.purpose},#{item.publicExposeIp},#{item.publicExposePort},#{item.applicant},#{item.outCrtTime},#{item.businessApplicationId},#{item.serverId},#{item.outId})
        </foreach>
    </insert>

    <update id="updateTblFirewallNat" parameterType="TblFirewallNat">
        update tbl_firewall_nat
        <trim prefix="SET" suffixOverrides=",">
            <if test="platform != null">platform = #{platform},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="internalIp != null">internal_ip = #{internalIp},</if>
            <if test="internalPort != null">internal_port = #{internalPort},</if>
            <if test="purpose != null">purpose = #{purpose},</if>
            <if test="publicExposeIp != null">public_expose_ip = #{publicExposeIp},</if>
            <if test="publicExposePort != null">public_expose_port = #{publicExposePort},</if>
            <if test="applicant != null">applicant = #{applicant},</if>
            <if test="outCrtTime != null">out_crt_time = #{outCrtTime},</if>
            <if test="businessApplicationId != null">business_application_id = #{businessApplicationId},</if>
            <if test="serverId != null">server_id = #{serverId},</if>
            <if test="outId != null and outId != ''">out_id = #{outId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblFirewallNatById" parameterType="Long">
        delete from tbl_firewall_nat where id = #{id}
    </delete>

    <delete id="deleteTblFirewallNatByIds" parameterType="String">
        delete from tbl_firewall_nat where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTblFirewallNatByAssetIds">
        delete from tbl_firewall_nat where asset_id in
        <foreach item="assetId" collection="assetIds" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
    <delete id="deleteAll">
        delete from tbl_firewall_nat
    </delete>
</mapper>
