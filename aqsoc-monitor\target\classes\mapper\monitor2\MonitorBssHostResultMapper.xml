<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.MonitorBssHostResultMapper">

    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssHostResult" id="MonitorBssHostResultResult">
        <result property="id"    column="id"    />
        <result property="planId"    column="plan_id"    />
        <result property="xprocessId"    column="xprocess_id"    />
        <result property="ipAddr"    column="ip_addr"    />
        <result property="osMatches"    column="os_matches"    />
        <result property="state"    column="state"    />
        <result property="exposures"    column="exposures"    />
        <result property="severity"    column="severity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMonitorBssHostResultVo">
        select id, plan_id, xprocess_id, ip_addr, os_matches, state, exposures, severity, create_by, create_time, update_by, update_time from monitor_bss_host_result
    </sql>

    <select id="selectMonitorBssHostResultList" parameterType="com.ruoyi.monitor2.domain.MonitorBssHostResult" resultMap="MonitorBssHostResultResult">
        <include refid="selectMonitorBssHostResultVo"/>
        <where>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="xprocessId != null "> and xprocess_id = #{xprocessId}</if>
            <if test="ipAddr != null  and ipAddr != ''"> and ip_addr = #{ipAddr}</if>
            <if test="osMatches != null  and osMatches != ''"> and os_matches = #{osMatches}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="exposures != null  and exposures != ''"> and exposures = #{exposures}</if>
            <if test="severity != null "> and severity = #{severity}</if>
        </where>
    </select>

    <select id="selectMonitorBssHostResultById" parameterType="java.lang.Long" resultMap="MonitorBssHostResultResult">
        <include refid="selectMonitorBssHostResultVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorBssHostResultByIds" parameterType="java.lang.Long" resultMap="MonitorBssHostResultResult">
        <include refid="selectMonitorBssHostResultVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMonitorBssHostResult" parameterType="com.ruoyi.monitor2.domain.MonitorBssHostResult" useGeneratedKeys="true" keyProperty="id">
        insert into monitor_bss_host_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="xprocessId != null">xprocess_id,</if>
            <if test="ipAddr != null and ipAddr != ''">ip_addr,</if>
            <if test="osMatches != null">os_matches,</if>
            <if test="state != null">state,</if>
            <if test="exposures != null">exposures,</if>
            <if test="severity != null">severity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},</if>
            <if test="xprocessId != null">#{xprocessId},</if>
            <if test="ipAddr != null and ipAddr != ''">#{ipAddr},</if>
            <if test="osMatches != null">#{osMatches},</if>
            <if test="state != null">#{state},</if>
            <if test="exposures != null">#{exposures},</if>
            <if test="severity != null">#{severity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertMonitorBssHostResultList" parameterType="list" useGeneratedKeys="true" keyProperty="id">
        insert into monitor_bss_host_result(plan_id, xprocess_Id, ip_addr, os_matches, state, exposures, severity, create_by, create_time, update_by, update_time) values
        <foreach collection="list" item="item" separator=",">
            (#{item.planId}, #{item.xprocessId}, #{item.ipAddr}, #{item.osMatches}, #{item.state}, #{item.exposures}, #{item.severity}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <update id="updateMonitorBssHostResult" parameterType="com.ruoyi.monitor2.domain.MonitorBssHostResult">
        update monitor_bss_host_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="xprocessId != null">xprocess_id = #{xprocessId},</if>
            <if test="ipAddr != null and ipAddr != ''">ip_addr = #{ipAddr},</if>
            <if test="osMatches != null">os_matches = #{osMatches},</if>
            <if test="state != null">state = #{state},</if>
            <if test="exposures != null">exposures = #{exposures},</if>
            <if test="severity != null">severity = #{severity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorBssHostResultById" parameterType="java.lang.Long">
        delete from monitor_bss_host_result where id = #{id}
    </delete>

    <delete id="deleteMonitorBssHostResultByIds" parameterType="java.lang.String">
        delete from monitor_bss_host_result where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssHostResult" id="hostResult">
        <result property="planId"    column="plan_id"    />
        <result property="xprocessId"    column="xprocess_id"    />
        <result property="ipAddr"    column="ip_addr"    />
        <result property="osMatches"    column="os_matches"    />
        <result property="state"    column="state"    />
        <result property="exposures"    column="exposures"    />
        <result property="severity"    column="severity"    />
        <collection property="gapList" javaType="ArrayList" column="{planId=plan_id,xprocessId=xprocess_id,hostIp=ip_addr}" select="selectSunList" />
    </resultMap>

    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssVulnResult" id="vulnResult">
        <result property="hostIp" column="host_ip" />
        <result property="hostPort" column="host_port" />
        <result property="countNum" column="countNum"/>
    </resultMap>

    <select id="findServerPage" parameterType="com.ruoyi.monitor2.domain.MonitorBssServiceResult" resultMap="hostResult">
        SELECT
        plan_id, xprocess_id, ip_addr, os_matches,state
        FROM monitor_bss_host_result mbhr
        <where>
            <if test="planId != null">plan_id = #{planId}</if>
            <if test="xprocessId != null">AND xprocess_id = #{xprocessId}</if>
            <if test="ipAddr != null and ipAddr != ''">AND ip_addr = #{ipAddr}</if>
        </where>
        GROUP BY plan_id, xprocess_id, ip_addr
    </select>

    <select id="selectSunList" parameterType="map" resultMap="vulnResult" >
        SELECT host_ip, host_port, severity, count(0) as countNum
        FROM monitor_bss_vuln_result
        <where>
            <if test="planId != null">plan_id = #{planId}</if>
            <if test="xprocessId != null">AND xprocess_id = #{xprocessId}</if>
            <if test="hostIp != null and hostIp != ''">AND host_ip = #{hostIp}</if>
            <if test="hostPort != null">AND host_port = #{hostPort}</if>
        </where>
        GROUP BY severity
    </select>
</mapper>