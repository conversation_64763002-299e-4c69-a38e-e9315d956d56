<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeWebmonitorBlackchainMapper">

    <resultMap type="FfsafeWebmonitorBlackchain" id="FfsafeWebmonitorBlackchainResult">
        <result property="id"    column="id"    />
        <result property="blackchainId"    column="blackchain_id"    />
        <result property="source"    column="source"    />
        <result property="illegalUrl"    column="illegal_url"    />
        <result property="illegalInformation"    column="illegal_information"    />
        <result property="virusType"    column="virus_type"    />
        <result property="startTime"    column="start_time"    />
        <result property="recentlyTime"    column="recently_time"    />
        <result property="handleStatus"    column="handle_status"    />
    </resultMap>

    <sql id="selectFfsafeWebmonitorBlackchainVo">
        select id, blackchain_id, source, illegal_url, illegal_information, virus_type, start_time, recently_time, handle_status from ffsafe_webmonitor_blackchain
    </sql>

    <select id="selectFfsafeWebmonitorBlackchainList" parameterType="FfsafeWebmonitorBlackchain" resultMap="FfsafeWebmonitorBlackchainResult">
        <include refid="selectFfsafeWebmonitorBlackchainVo"/>
        <where>
            <if test="blackchainId != null "> and blackchain_id = #{blackchainId}</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
            <if test="illegalUrl != null  and illegalUrl != ''"> and illegal_url = #{illegalUrl}</if>
            <if test="illegalInformation != null  and illegalInformation != ''"> and illegal_information = #{illegalInformation}</if>
            <if test="virusType != null  and virusType != ''"> and virus_type = #{virusType}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="recentlyTime != null "> and recently_time = #{recentlyTime}</if>
            <if test="handleStatus != null  and handleStatus != ''"> and handle_status = #{handleStatus}</if>
        </where>
    </select>

    <select id="selectFfsafeWebmonitorBlackchainById" parameterType="Long" resultMap="FfsafeWebmonitorBlackchainResult">
        <include refid="selectFfsafeWebmonitorBlackchainVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeWebmonitorBlackchainByIds" parameterType="Long" resultMap="FfsafeWebmonitorBlackchainResult">
        <include refid="selectFfsafeWebmonitorBlackchainVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeWebmonitorBlackchain" parameterType="FfsafeWebmonitorBlackchain" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_webmonitor_blackchain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="blackchainId != null">blackchain_id,</if>
            <if test="source != null">source,</if>
            <if test="illegalUrl != null">illegal_url,</if>
            <if test="illegalInformation != null">illegal_information,</if>
            <if test="virusType != null">virus_type,</if>
            <if test="startTime != null">start_time,</if>
            <if test="recentlyTime != null">recently_time,</if>
            <if test="handleStatus != null">handle_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="blackchainId != null">#{blackchainId},</if>
            <if test="source != null">#{source},</if>
            <if test="illegalUrl != null">#{illegalUrl},</if>
            <if test="illegalInformation != null">#{illegalInformation},</if>
            <if test="virusType != null">#{virusType},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="recentlyTime != null">#{recentlyTime},</if>
            <if test="handleStatus != null">#{handleStatus},</if>
        </trim>
    </insert>

    <update id="updateFfsafeWebmonitorBlackchain" parameterType="FfsafeWebmonitorBlackchain">
        update ffsafe_webmonitor_blackchain
        <trim prefix="SET" suffixOverrides=",">
            <if test="blackchainId != null">blackchain_id = #{blackchainId},</if>
            <if test="source != null">source = #{source},</if>
            <if test="illegalUrl != null">illegal_url = #{illegalUrl},</if>
            <if test="illegalInformation != null">illegal_information = #{illegalInformation},</if>
            <if test="virusType != null">virus_type = #{virusType},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="recentlyTime != null">recently_time = #{recentlyTime},</if>
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeWebmonitorBlackchainById" parameterType="Long">
        delete from ffsafe_webmonitor_blackchain where id = #{id}
    </delete>

    <delete id="deleteFfsafeWebmonitorBlackchainByIds" parameterType="String">
        delete from ffsafe_webmonitor_blackchain where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>