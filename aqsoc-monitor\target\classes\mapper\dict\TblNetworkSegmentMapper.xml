<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dict.mapper.TblNetworkSegmentMapper">
    
    <resultMap type="TblNetworkSegment" id="TblNetworkSegmentResult">
        <result property="id"    column="id"    />
        <result property="domainId"    column="domain_id"    />
        <result property="networkSeg"    column="network_seg"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblNetworkSegmentVo">
        select id, domain_id, network_seg, remark, create_by, create_time, update_by, update_time from tbl_network_segment
    </sql>

    <select id="selectTblNetworkSegmentList" parameterType="TblNetworkSegment" resultMap="TblNetworkSegmentResult">
        <include refid="selectTblNetworkSegmentVo"/>
        <where>
            <if test="id != null">id=#{id}</if>
            <if test="domainId != null">domain_id=#{domainId}</if>
            <if test="networkSeg != null">network_seg=#{networkSeg},</if>
        </where>
    </select>
    
    <select id="selectTblNetworkSegmentById" parameterType="Integer" resultMap="TblNetworkSegmentResult">
        <include refid="selectTblNetworkSegmentVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblNetworkSegment" parameterType="TblNetworkSegment">
        insert into tbl_network_segment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="domainId != null">domain_id,</if>
            <if test="networkSeg != null">network_seg,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="domainId != null">#{domainId},</if>
            <if test="networkSeg != null">#{networkSeg},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblNetworkSegment" parameterType="TblNetworkSegment">
        update tbl_network_segment
        <trim prefix="SET" suffixOverrides=",">
            <if test="domainId != null">domain_id = #{domainId},</if>
            <if test="networkSeg != null">network_seg = #{networkSeg},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblNetworkSegmentById" parameterType="Integer">
        delete from tbl_network_segment where id = #{id}
    </delete>

    <delete id="deleteTblNetworkSegmentByIds" parameterType="String">
        delete from tbl_network_segment where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>