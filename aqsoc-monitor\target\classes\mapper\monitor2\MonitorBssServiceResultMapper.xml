<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.MonitorBssServiceResultMapper">

    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssServiceResult" id="MonitorBssServiceResultResult">
        <result property="id"    column="id"    />
        <result property="planId"    column="plan_id"    />
        <result property="xprocessId"    column="xprocess_id"    />
        <result property="hostIp"    column="host_ip"    />
        <result property="hostPort"    column="host_port"    />
        <result property="protocol"    column="protocol"    />
        <result property="servcieName"    column="servcie_name"    />
        <result property="hostName"    column="host_name"    />
        <result property="deviceType"    column="device_type"    />
        <result property="product"    column="product"    />
        <result property="version"    column="version"    />
        <result property="banner"    column="banner"    />
        <result property="exposures"    column="exposures"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMonitorBssServiceResultVo">
        select id, plan_id, xprocess_id, host_ip, host_port, protocol, servcie_name, host_name, device_type, product, version, banner, exposures, create_by, create_time, update_by, update_time from monitor_bss_service_result
    </sql>

    <select id="selectMonitorBssServiceResultList" parameterType="com.ruoyi.monitor2.domain.MonitorBssServiceResult" resultMap="MonitorBssServiceResultResult">
        <include refid="selectMonitorBssServiceResultVo"/>
        <where>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="xprocessId != null "> and xprocess_id = #{xprocessId}</if>
            <if test="hostIp != null  and hostIp != ''"> and host_ip = #{hostIp}</if>
            <if test="hostPort != null and hostPort != 0"> and host_port = #{hostPort}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="servcieName != null  and servcieName != ''"> and servcie_name like concat('%', #{servcieName}, '%')</if>
            <if test="hostName != null  and hostName != ''"> and host_name like concat('%', #{hostName}, '%')</if>
            <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
            <if test="product != null  and product != ''"> and product = #{product}</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="banner != null  and banner != ''"> and banner = #{banner}</if>
            <if test="exposures != null  and exposures != ''"> and exposures = #{exposures}</if>
        </where>
    </select>

    <select id="selectMonitorBssServiceResultById" parameterType="java.lang.Long" resultMap="MonitorBssServiceResultResult">
        <include refid="selectMonitorBssServiceResultVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorBssServiceResultByIds" parameterType="java.lang.Long" resultMap="MonitorBssServiceResultResult">
        <include refid="selectMonitorBssServiceResultVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMonitorBssServiceResult" parameterType="com.ruoyi.monitor2.domain.MonitorBssServiceResult" useGeneratedKeys="true" keyProperty="id">
        insert into monitor_bss_service_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="xprocessId != null">xprocess_id,</if>
            <if test="hostIp != null and hostIp != ''">host_ip,</if>
            <if test="hostPort != null">host_port,</if>
            <if test="protocol != null">protocol,</if>
            <if test="servcieName != null">servcie_name,</if>
            <if test="hostName != null">host_name,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="product != null">product,</if>
            <if test="version != null">version,</if>
            <if test="banner != null">banner,</if>
            <if test="exposures != null">exposures,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},</if>
            <if test="xprocessId != null">#{xprocessId},</if>
            <if test="hostIp != null and hostIp != ''">#{hostIp},</if>
            <if test="hostPort != null">#{hostPort},</if>
            <if test="protocol != null">#{protocol},</if>
            <if test="servcieName != null">#{servcieName},</if>
            <if test="hostName != null">#{hostName},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="product != null">#{product},</if>
            <if test="version != null">#{version},</if>
            <if test="banner != null">#{banner},</if>
            <if test="exposures != null">#{exposures},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertMonitorBssServiceResultList" parameterType="list" useGeneratedKeys="true" keyProperty="id">
        insert into monitor_bss_service_result values
        <foreach collection="list" item="item" separator=",">
            (#{item.planId}, #{item.xprocessId}, #{item.hostIp}, #{item.hostPort}, #{item.protocol}, #{item.servcieName}, #{item.hostName}, #{item.deviceType}, #{item.product}, #{item.version}, #{item.banner}, #{item.exposures}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <update id="updateMonitorBssServiceResult" parameterType="com.ruoyi.monitor2.domain.MonitorBssServiceResult">
        update monitor_bss_service_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="xprocessId != null">xprocess_id = #{xprocessId},</if>
            <if test="hostIp != null and hostIp != ''">host_ip = #{hostIp},</if>
            <if test="hostPort != null">host_port = #{hostPort},</if>
            <if test="protocol != null">protocol = #{protocol},</if>
            <if test="servcieName != null">servcie_name = #{servcieName},</if>
            <if test="hostName != null">host_name = #{hostName},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="product != null">product = #{product},</if>
            <if test="version != null">version = #{version},</if>
            <if test="banner != null">banner = #{banner},</if>
            <if test="exposures != null">exposures = #{exposures},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorBssServiceResultById" parameterType="java.lang.Long">
        delete from monitor_bss_service_result where id = #{id}
    </delete>

    <delete id="deleteMonitorBssServiceResultByIds" parameterType="java.lang.String">
        delete from monitor_bss_service_result where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssServiceResult" id="serviceResult">
        <result property="planId"    column="plan_id"    />
        <result property="xprocessId"    column="xprocess_id"    />
        <result property="hostIp"    column="host_ip"    />
        <result property="hostPort"    column="host_port"    />
        <result property="protocol"    column="protocol"    />
        <result property="product"    column="product"    />
        <result property="version"    column="version"    />
        <collection property="gapList" javaType="ArrayList" column="{planId=plan_id,xprocessId=xprocess_id,hostIp=host_ip,hostPort=host_port }" select="selectSunList" />
    </resultMap>

    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssVulnResult" id="vulnResult">
        <result property="hostIp" column="host_ip" />
        <result property="hostPort" column="host_port" />
        <result property="countNum" column="countNum"/>
    </resultMap>

    <select id="findServerPage" parameterType="com.ruoyi.monitor2.domain.MonitorBssServiceResult" resultMap="serviceResult">
        SELECT
               plan_id, xprocess_id, host_ip, host_port, protocol, product
        FROM monitor_bss_service_result mbsr
        <where>
            <if test="planId != null">plan_id = #{planId}</if>
            <if test="xprocessId != null">AND xprocess_id = #{xprocessId}</if>
            <if test="hostIp != null and hostIp != ''">AND host_ip = #{hostIp}</if>
            <if test="hostPort != null and hostPort!= 0 ">AND host_port = #{hostPort}</if>
        </where>
        GROUP BY plan_id, xprocess_id, host_ip, host_port, protocol, product
    </select>

    <select id="selectSunList" parameterType="map" resultMap="vulnResult" >
        SELECT host_ip, host_port, severity, count(0) as countNum
        FROM monitor_bss_vuln_result
        <where>
            <if test="planId != null">plan_id = #{planId}</if>
            <if test="xprocessId != null">AND xprocess_id = #{xprocessId}</if>
            <if test="hostIp != null and hostIp != ''">AND host_ip = #{hostIp}</if>
            <if test="hostPort != null">AND host_port = #{hostPort}</if>
        </where>
        GROUP BY severity
    </select>
</mapper>