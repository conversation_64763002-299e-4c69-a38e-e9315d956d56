{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\editServer.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\editServer.vue", "mtime": 1756381475338}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_server", "require", "_vendor", "_overview", "_vendorSelect", "_interopRequireDefault", "_productSelect", "_deptSelect", "_networkSelect", "_locationSelect", "_DynamicTag", "_overViewSelect", "_host", "_product", "_domain", "_assetRegister", "name", "mixins", "assetRegister", "dicts", "components", "vendorSelect", "productSelect", "DeptSelect", "NetworkSelect", "LocationSelect", "DynamicTag", "OverViewSelect", "props", "assetId", "type", "Number", "String", "default", "required", "handleData", "Object", "title", "editFlagVisible", "Boolean", "data", "host", "undefined", "assetHosts", "assetHostDialog", "myTags", "classId", "children", "typeTreeData", "typelist", "vendorDialog", "softDialog", "softType", "softField", "editItem", "addEvent", "multiple", "form", "ipMacArr", "assetName", "exposedIp", "handleId", "dbsystemArr", "mdsystemArr", "rules", "assetCode", "min", "max", "message", "trigger", "assetType", "ip", "pattern", "mac", "domainId", "locationDetail", "deptId", "baseAssetDialog", "assetAllocationType", "computed", "visible", "get", "set", "val", "$emit", "visibleAssetFields", "assetList", "map", "group", "newGroup", "_objectSpread2", "fieldsItems", "filter", "item", "isShow", "dynamicRules", "_this", "for<PERSON>ach", "<PERSON><PERSON><PERSON>", "_rules$fieldKey", "filteredRules", "rule", "push", "apply", "_toConsumableArray2", "hasRequiredRule", "some", "concat", "fieldName", "netinfoTable", "assetFields", "formName", "mounted", "methods", "getDictDataInfo", "dict", "sys_yes_no", "is_sparing", "openDialog", "_this2", "init", "initData", "$nextTick", "getFormattedDate", "chooseHost", "$message", "gethost", "_this3", "getAssetTypeChildrenByid", "then", "res", "rows", "i", "length", "typeGradedProtection", "splice", "handleTree", "_this4", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "ipMacItem", "wrap", "_callee$", "_context", "prev", "next", "getServer", "response", "e", "mainIp", "isMainIp", "optsystem", "optId", "procName", "dbId", "mdId", "tags", "split", "facilityManufacturer", "get<PERSON>endor", "facilityManufacturerName", "vendorName", "maintainUnit", "maintainUnitName", "ipv4", "listDomain", "iparea", "domainRes", "listProduct", "productRes", "matchArr", "$set", "prdid", "hostId", "indexOf", "getHostList", "pageSize", "hostIds", "console", "log", "list", "$confirm", "stop", "_this5", "getHostInfo", "hostName", "cpuFrame", "hostPhysical", "arch", "hostNicList", "row", "hostIp", "oldIpMacArr", "replace", "ipv6", "id", "cwHostId", "osReleaseName", "_iterator", "_createForOfIteratorHelper2", "hostApplicationList", "_step", "s", "n", "done", "value", "category", "app", "err", "f", "showVendorDialog", "vendor", "vendorType", "getSelectedVendor", "selectConfirm2", "userCancel2", "setupOptSystem", "field", "getSelectedProduct", "softConfirm", "_this6", "softCancel", "submitForm", "_this7", "assetClass", "assetClassDesc", "className", "$refs", "validate", "valid", "checkIpMacArr", "flag", "assetTypeDesc", "updateServer2", "$modal", "msgSuccess", "resetFields", "$parent", "getList", "getServerCountByDictData", "addServer2", "open", "$route", "params", "add", "$router", "back", "backAdd", "cancel", "addrow", "$root", "addIpMac", "removeIpMac", "index", "msgError", "mainIpCount", "isValidIP", "inputLossOfFocus", "assetTypeChange", "find", "typeName", "getRulesForField", "getSpan", "shouldShowField", "isVirtual", "now", "Date", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "ipRegex", "test", "baseAssetSelectHandle", "baseAssetCancel", "baseAssetConfirm", "baseAsset", "baseAssetName", "valueInit", "changeIsVirtual"], "sources": ["src/views/safe/server/editServer.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"title\"\n    :visible.sync=\"visible\"\n    width=\"1200px\"\n    @open=\"openDialog\"\n    class=\"customForm\"\n    append-to-body>\n    <div class=\"customForm-container\" style=\"height: 100%; padding: 0 20px;\">\n      <el-form\n        ref=\"form\"\n        :model=\"form\"\n        :rules=\"rules\"\n        label-position=\"top\">\n        <el-row>\n          <el-col :span=\"24\">\n            <template v-for=\"ele in visibleAssetFields\">\n              <el-row\n                :gutter=\"20\"\n                :key=\"ele.id\"\n                type=\"flex\"\n                v-if=\"ele.isShow\"\n                style=\"flex-wrap: wrap;margin-bottom: 10px;\">\n                <!-- 基本信息、硬件/软件概况信息、位置信息等模块 -->\n                <el-col\n                  :span=\"24\"\n                  style=\"display: flex; flex-wrap: wrap;\"\n                  v-if=\"ele.formName === '基本信息' || ele.formName === '硬件/软件概况信息' || ele.formName === '位置信息'\">\n                  <el-col class=\"my-title\">\n                    <img v-if=\"ele.formName === '基本信息' && ele.isShow\" src=\"@/assets/images/application/jbxx.png\" alt=\"\"/>\n                    <i v-else-if=\"ele.formName === '硬件/软件概况信息' && ele.isShow\" class=\"el-icon-cpu icon\" />\n                    <i v-else-if=\"ele.formName === '位置信息' && ele.isShow\" class=\"el-icon-location-information icon\" />\n                    {{ ele.formName }}\n                  </el-col>\n                  <template v-for=\"item in ele.fieldsItems\">\n                    <el-col :key=\"item.fieldKey\" :span=\"getSpan(item.fieldKey)\" v-if=\"shouldShowField(item)\">\n                      <el-form-item\n                        :label=\"item.fieldName\"\n                        :prop=\"item.fieldKey\"\n                        :rules=\"getRulesForField(item)\">\n                        <!-- 资产类型字段 -->\n                        <template v-if=\"item.fieldKey === 'assetType'\">\n                          <el-select v-model=\"form.assetType\" placeholder=\"请选择资产类型\" @change=\"assetTypeChange\">\n                            <el-option v-for=\"children in children\" :key=\"children.id\" :label=\"children.typeName\"\n                                       :value=\"children.id\"/>\n                          </el-select>\n                        </template>\n\n                        <!-- 重要程度字段 -->\n                        <template v-else-if=\"item.fieldKey === 'degreeImportance'\">\n                          <el-select v-model=\"form.degreeImportance\" :placeholder=\"'请选择' + item.fieldName\">\n                            <el-option\n                              v-for=\"dict in dict.type.impt_grade\"\n                              :key=\"dict.value\"\n                              :label=\"dict.label\"\n                              :value=\"dict.value\"\n                            ></el-option>\n                          </el-select>\n                        </template>\n\n                        <!-- 所属部门字段 -->\n                        <template v-else-if=\"item.fieldKey === 'deptId'\">\n                          <dept-select v-model=\"form.deptId\" is-current/>\n                        </template>\n\n                        <!-- 供应商字段 -->\n                        <template v-else-if=\"item.fieldKey === 'vendor'\">\n                          <el-input\n                            v-model=\"form.vendorName\"\n                            @focus=\"showVendorDialog('vendor')\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n\n                        <!-- 设备厂商字段 -->\n                        <template v-else-if=\"item.fieldKey === 'facilityManufacturer'\">\n                          <el-input\n                            v-model=\"form.facilityManufacturerName\"\n                            @focus=\"showVendorDialog('facilityManufacturer')\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n\n                        <!-- 维保单位字段 -->\n                        <template v-else-if=\"item.fieldKey === 'maintainUnit'\">\n                          <el-input\n                            v-model=\"form.maintainUnitName\"\n                            @focus=\"showVendorDialog('maintainUnit')\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n\n                        <!-- 虚拟设备字段 -->\n                        <template v-else-if=\"['isVirtual', 'isSparing'].includes(item.fieldKey)\">\n                          <el-radio-group\n                            v-model=\"form[item.fieldKey]\"\n                            @input=\"item.fieldKey === 'isVirtual' ? changeIsVirtual : undefined\"\n                          >\n                            <el-radio\n                              v-for=\"dict in getDictDataInfo(item.fieldKey)\"\n                              :key=\"dict.value\"\n                              :label=\"dict.value\"\n                            >\n                              {{ dict.label }}\n                            </el-radio>\n                          </el-radio-group>\n                        </template>\n\n                        <!-- 承载设备字段 -->\n                        <template v-else-if=\"item.fieldKey === 'baseAsset'\">\n                          <el-input\n                            v-model=\"form.baseAssetName\"\n                            @focus=\"baseAssetSelectHandle\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n\n                        <template v-else-if=\"item.fieldKey === 'optId'\">\n                          <el-input\n                            v-model=\"form.optId\"\n                            @focus=\"setupOptSystem('optId')\"\n                            placeholder=\"请输入操作系统\"/>\n                        </template>\n\n                        <template v-else-if=\"item.fieldKey === 'mdId'\">\n                          <el-input\n                          v-model=\"form.mdId\"\n                          @focus=\"setupOptSystem('mdId')\"\n                          placeholder=\"请输入中间件\"/>\n                        </template>\n\n                        <template v-else-if=\"item.fieldKey === 'dbId'\">\n                          <el-input\n                            v-model=\"form.dbId\"\n                            @focus=\"setupOptSystem('dbId')\"\n                            placeholder=\"请输入数据库\"/>\n                        </template>\n\n                        <!-- 资产标签字段 -->\n                        <template v-else-if=\"item.fieldKey === 'tags'\">\n                          <dynamic-tag v-model=\"form.tags\"/>\n                        </template>\n\n                        <!-- 所在机房 -->\n                        <template v-else-if=\"item.fieldKey === 'locationId'\">\n                          <location-select v-model=\"form.locationId\"/>\n                        </template>\n\n                        <!-- 默认文本输入框 -->\n                        <template v-else>\n                          <el-input\n                            v-model=\"form[item.fieldKey]\"\n                            :type=\"item.fieldKey === 'remark' ? 'textarea' : 'text'\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n                      </el-form-item>\n                    </el-col>\n                  </template>\n                </el-col>\n\n                <!-- 网络信息模块单独处理 -->\n                <el-col :span=\"24\" v-if=\"ele.formName === '网络信息' && ele.isShow\">\n                  <div class=\"my-title\"><img src=\"@/assets/images/application/netinfo.png\" alt=\"\"/>网络信息</div>\n                  <template v-for=\"item in ele.fieldsItems\">\n                    <el-form-item\n                      :key=\"item.fieldKey\"\n                      :label=\"item.fieldName\"\n                      :prop=\"item.fieldKey\"\n                      v-if=\"item.fieldKey === 'exposedIp'\"\n                      :rules=\"getRulesForField(item)\">\n                      <el-input v-model=\"form.exposedIp\" placeholder=\"请输入外网IP\"/>\n                    </el-form-item>\n                  </template>\n                  <el-table :data=\"form.ipMacArr\" v-if=\"netinfoTable.length\" style=\"width: 100%\">\n                    <el-table-column\n                      v-for=\"(colum, index) in netinfoTable\"\n                      :key=\"index\"\n                      :label=\"colum.fieldName\"\n                      :width=\"colum.fieldKey === 'isMainIp' ? '60px' : ''\">\n                      <template slot-scope=\"scope\">\n                        <!-- 主IP复选框 -->\n                        <el-checkbox\n                          v-if=\"colum.fieldKey === 'isMainIp'\"\n                          v-model=\"scope.row.isMainIp\"/>\n\n                        <!-- 网络区域选择器 -->\n                        <NetworkSelect\n                          v-else-if=\"colum.fieldKey === 'domainId'\"\n                          v-model=\"scope.row.domainId\"/>\n\n                        <!-- IP地址输入框 -->\n                        <el-input\n                          v-else-if=\"colum.fieldKey === 'ipv4'\"\n                          v-model=\"scope.row.ipv4\"\n                          placeholder=\"请输入IP\"/>\n\n                        <!-- MAC地址输入框 -->\n                        <el-input\n                          v-else-if=\"colum.fieldKey === 'mac'\"\n                          v-model=\"scope.row.mac\"\n                          placeholder=\"请输入MAC地址\"/>\n                      </template>\n                    </el-table-column>\n                    <el-table-column align=\"center\">\n                      <template slot=\"header\">\n                        <div style=\"display: inline;\">操作</div>\n                        <div style=\"display: inline;float: right;font-size: 18px;margin-left: 10px;\"><i\n                          class=\"el-icon-circle-plus-outline\" @click=\"addIpMac\"/></div>\n                      </template>\n                      <template slot-scope=\"scope\">\n                        <span style=\"font-size: 18px;\">\n                          <i class=\"el-icon-remove-outline\" @click=\"removeIpMac(scope.$index)\"/>\n                        </span>\n                      </template>\n                    </el-table-column>\n                  </el-table>\n                </el-col>\n              </el-row>\n            </template>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <el-dialog title=\"选择供应商\" :visible.sync=\"vendorDialog\" width=\"900px\" append-to-body>\n        <vendor-select v-if=\"vendorDialog\" :value=\"getSelectedVendor()\" @confirm=\"selectConfirm2\" @cancel=\"userCancel2\"/>\n      </el-dialog>\n      <el-dialog title=\"选择系统软件\" :visible.sync=\"softDialog\" width=\"800px\" append-to-body>\n        <product-select v-if=\"softDialog\" :type=\"softType\" :multipleMode=\"multiple\" :value=\"getSelectedProduct()\"\n                        @confirm=\"softConfirm\"\n                        @cancel=\"softCancel\" @change=\"softDialog= false\" ref=\"softForm\"/>\n      </el-dialog>\n      <el-dialog title=\"选择服务器\" :visible.sync=\"baseAssetDialog\" width=\"800px\" append-to-body>\n        <over-view-select v-if=\"baseAssetDialog\" :asset-class=\"4\" :type=\"softType\" :value=\"valueInit()\"\n                          @confirm=\"baseAssetConfirm\" @cancel=\"baseAssetCancel\"></over-view-select>\n      </el-dialog>\n      <el-dialog title=\"选择绑定主机\" :visible.sync=\"assetHostDialog\" width=\"800px\" append-to-body :show-close=\"false\">\n        <el-select v-model=\"host\">\n          <el-option v-for=\"host in assetHosts\"\n                     :key=\"host.id\"\n                     :label=\"host.hostName\"\n                     :value=\"host.id\"></el-option>\n        </el-select>\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"chooseHost\">确定</el-button>\n        </div>\n      </el-dialog>\n    </div>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button v-if=\"editItem === 'edit'\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\n      <el-button @click=\"cancel\">取 消</el-button>\n    </div>\n  </el-dialog>\n\n</template>\n\n<script>\nimport {addServer2, updateServer2, getServer} from \"@/api/safe/server\";\nimport {getVendor} from \"@/api/safe/vendor\";\nimport {getAssetTypeChildrenByid} from \"@/api/safe/overview\";\nimport vendorSelect from \"@/views/components/select/vendorSelect\";\nimport productSelect from \"@/views/components/select/productSelect\";\nimport DeptSelect from \"@/views/components/select/deptSelect\";\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport LocationSelect from \"@/views/components/select/locationSelect\";\nimport DynamicTag from \"@/components/DynamicTag\";\nimport OverViewSelect from \"@/views/components/select/overViewSelect\";\nimport {getHostInfo, getHostList} from \"@/api/asset/host\";\nimport {listProduct} from \"@/api/monitor2/product\";\nimport {listDomain} from \"@/api/dict/domain\";\nimport assetRegister from \"@/mixins/assetRegister\";\n\nexport default {\n  name: 'EditServer',\n  mixins: [assetRegister],\n  dicts: ['impt_grade', 'sys_yes_no', 'proc_type','is_sparing'],\n  components: {\n    vendorSelect,\n    productSelect,\n    DeptSelect,\n    NetworkSelect,\n    LocationSelect,\n    DynamicTag,\n    OverViewSelect,\n  },\n  props: {\n    assetId: {\n      type: [Number, String],\n      default: null,\n      required: false\n    },\n    handleData: {\n      type: Object,\n      default: null,\n      required: false\n    },\n    title: {\n      type: String,\n      default: '新增服务器'\n    },\n    editFlagVisible: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      host: undefined,\n      assetHosts: [],\n      assetHostDialog: false,\n      myTags: ['国产化', '安可'],\n      classId: 4,\n      children: [],\n      typeTreeData: [],\n      typelist: [],\n      vendorDialog: false,\n      softDialog: false,\n      softType: 'system',\n      softField: '',\n      editItem: 'edit',\n      addEvent: '',\n      multiple: false,\n      form: {\n        ipMacArr: [],\n        assetName: '',\n        exposedIp: '',\n        handleId: undefined,\n        dbsystemArr: [],\n        mdsystemArr: [],\n      },\n      rules: {\n        assetCode: [\n          /*{required: true, message: \"资产编码不能为空\", trigger: \"blur\"},*/\n          {min: 0, max: 64, message: '资产编码不能超过 64 个字符', trigger: 'blur'},\n        ],\n        assetName: [\n          {required: true, message: \"资产名称不能为空\", trigger: \"blur\"},\n          {min: 0, max: 64, message: '资产名称不能超过 64 个字符', trigger: 'blur'},\n        ],\n        assetType: [\n          {required: true, message: \"资产类型不能为空\", trigger: \"blur\"},\n        ],\n        ip: [\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: \"IP地址不能为空或格式不正确\",\n            trigger: \"blur\"\n          },\n        ],\n        mac: [\n          {pattern: '^([0-9a-fA-F]{2}(:|-)){5}[0-9a-fA-F]{2}$', message: \"Mac地址格式不正确\", trigger: \"blur\"},\n        ],\n        domainId: [\n          {required: true, message: \"网络区域不能为空！\", trigger: 'blur'}\n        ],\n        locationDetail: [\n          {min: 0, max: 128, message: '详细地址不能超过 128 个字符', trigger: 'blur'},\n        ],\n        deptId: [\n          {required: true, message: \"所属部门不能为空\", trigger: \"blur\"},\n        ]\n      },\n      baseAssetDialog: false,\n      assetAllocationType: '2',\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.editFlagVisible\n      },\n      set(val) {\n        this.$emit('update:editFlagVisible', val)\n      }\n    },\n    // 动态字段\n    visibleAssetFields() {\n      return this.assetList.map(group => {\n        const newGroup = { ...group };\n        newGroup.fieldsItems = group.fieldsItems.filter(item => item.isShow);\n        return newGroup;\n      });\n    },\n    // 动态规则\n    dynamicRules() {\n      const rules = {};\n      this.visibleAssetFields.forEach(group => {\n        group.fieldsItems.forEach(item => {\n          const fieldKey = item.fieldKey;\n\n          if (!rules[fieldKey]) {\n            rules[fieldKey] = [];\n          }\n\n          if (this.rules[fieldKey]) {\n            const filteredRules = this.rules[fieldKey].filter(rule => !rule.required);\n            rules[fieldKey].push(...filteredRules);\n          }\n\n          if (item.required) {\n            const hasRequiredRule = rules[fieldKey].some(rule => rule.required);\n            if (!hasRequiredRule) {\n              rules[fieldKey].push({\n                required: true,\n                message: `${item.fieldName}不能为空`,\n                trigger: 'blur'\n              });\n            }\n          }\n        });\n      });\n      return rules;\n    },\n\n    // 网络信息列表字段\n    netinfoTable() {\n      let assetFields = this.assetList.filter(group => group.formName === '网络信息')\n      return assetFields[0].fieldsItems.filter(item => item.fieldKey !== 'exposedIp' && item.isShow)\n    }\n  },\n  mounted() {\n\n  },\n  methods: {\n    getDictDataInfo(fieldKey) {\n      switch (fieldKey) {\n        case 'isVirtual':\n          return this.dict.type.sys_yes_no;\n        case 'isSparing':\n          return this.dict.type.is_sparing;\n        default:\n          return [];\n      }\n    },\n\n    openDialog() {\n      this.init();\n      this.initData();\n      this.$nextTick(() => {\n        if (!this.form.assetCode){\n          this.form.assetCode = 'ZJ' + this.getFormattedDate()\n        }\n      })\n    },\n    chooseHost() {\n      if(!this.host){\n        this.$message('请选择一台主机！')\n      }else{\n        this.gethost(this.host)\n        this.assetHostDialog = false\n      }\n    },\n    init() {\n      getAssetTypeChildrenByid(this.classId).then(res => {\n        this.typelist = res.rows;\n        for (let i = 0; i < this.typelist.length; i++) {\n          if (this.typelist[i].typeGradedProtection == 1) {\n            this.typelist.splice(Number(i), 1);\n            i = i - 1;\n          }\n        }\n        this.typeTreeData = this.handleTree(this.typelist, 'id', 'pid');\n        this.children = this.typeTreeData[0].children;\n        if (this.children && this.children.length) this.rules.assetType = [\n          {required: true, message: \"资产类型不能为空\", trigger: \"blur\"},\n        ]\n      });\n    },\n    async initData() {\n      if (this.assetId) {\n        await getServer(this.assetId).then(response => {\n          if (response.data.ipMacArr) {\n            response.data.ipMacArr.forEach(e => {\n              if (e.mainIp === '1') {\n                e.isMainIp = true\n              } else if (e.mainIp === '0') {\n                e.isMainIp = false\n              }\n            })\n          }\n          this.form = response.data;\n          if (this.form.optsystem != null) this.form.optId = this.form.optsystem.procName;\n          if (this.form.dbsystemArr !== null && this.form.dbsystemArr !== undefined && this.form.dbsystemArr.length) {\n            this.form.dbId = ''\n            this.form.dbsystemArr.forEach(e => {\n              this.form.dbId += '【' + e.procName + '】'\n            })\n          }\n          if (this.form.mdsystemArr !== null && this.form.mdsystemArr !== undefined && this.form.mdsystemArr.length) {\n            this.form.mdId = ''\n            this.form.mdsystemArr.forEach(e => {\n              this.form.mdId += '【' + e.procName + '】'\n            })\n          }\n          this.myTags = this.form.tags ? this.form.tags.split(',') : [];\n\n          if (this.form.facilityManufacturer) {\n            getVendor(this.form.facilityManufacturer).then(res => {\n              this.form.facilityManufacturerName = res.data.vendorName;\n            });\n          }\n\n          if (this.form.maintainUnit) {\n            getVendor(this.form.maintainUnit).then(res => {\n              this.form.maintainUnitName = res.data.vendorName;\n            });\n          }\n        })\n      }\n      if (this.handleData) {\n        if(this.handleData.deptId){\n          this.form.deptId = this.handleData.deptId;\n        }\n        this.form.handleId = this.handleData.handleId\n        if(this.handleData.ip){\n          let ipMacItem = {\n            isMainIp: true,\n            ipv4: this.handleData.ip\n          }\n          await listDomain({iparea: this.handleData.ip}).then(domainRes => {\n            if(domainRes.data && domainRes.data.length>0){\n              ipMacItem.domainId = domainRes.data[0].domainId;\n            }\n          })\n          this.form.ipMacArr.push(ipMacItem);\n        }\n        if(this.handleData.name){\n          await listProduct({procName: this.handleData.name}).then(productRes => {\n            if(productRes.rows){\n              let matchArr = productRes.rows.filter(item => this.handleData.name === item.procName);\n              if(matchArr && matchArr.length > 0){\n                this.$set(this.form,'optId',matchArr[0].procName);\n                let optsystem = this.form.optsystem || {};\n                optsystem.prdid = matchArr[0].prdid;\n                optsystem.procName = matchArr[0].procName;\n                this.$set(this.form,'optsystem',optsystem);\n              }\n            }\n          })\n        }\n        if (this.handleData.hostId) {\n          if (this.assetId) {\n            if (this.handleData.hostId.indexOf(',') > 0) {\n              this.assetHostDialog = true\n\n              await getHostList({pageSize: -1, hostIds: this.handleData.hostId.split(',')}).then(res => {\n                console.log('res', res)\n                this.assetHosts = res.data.list\n              })\n            } else {\n              this.$confirm('是否使用探测数据替换本地数据?').then(() => {\n                this.gethost(this.handleData.hostId)\n              })\n            }\n          } else {\n            this.gethost(this.handleData.hostId)\n          }\n        }\n      }\n    },\n    gethost(hostId) {\n      getHostInfo(hostId).then(res => {\n        const host = res.data\n        this.form.assetName = host.hostName\n        this.form.cpuFrame = host.hostPhysical.arch\n        let ipMacArr = host.hostNicList.filter(row => row.ipv4.indexOf(host.hostIp) > -1)\n        const oldIpMacArr = this.form.ipMacArr.filter(row => row.ipv4.indexOf(host.hostIp) > -1)\n        console.log('oldIpMacArr', oldIpMacArr)\n        ipMacArr.forEach(row => {\n          row.ipv4 = row.ipv4.replace(/[\\[\\]]/g, '').split('/')[0]\n          row.ipv6 = ''\n          row.id = ''\n          if (oldIpMacArr.length > 0) {\n            row.domainId = oldIpMacArr[0].domainId\n            row.isMainIp = oldIpMacArr[0].isMainIp\n          }\n        })\n        this.form.exposedIp = host.exposedIp\n        this.form.ipMacArr = ipMacArr\n        this.form.cwHostId = host.id\n        this.form.optsystem = {prdid: host.prdid, procName: host.osReleaseName}\n        this.form.optId = this.form.optsystem.procName;\n        this.form.dbsystemArr = []\n        this.form.mdsystemArr = []\n        for (let row of host.hostApplicationList) {\n          if (row.category === 'database') {\n            this.form.dbsystemArr.push({prdid: row.prdid, procName: row.app})\n          } else if (row.category === 'web_server') {\n            this.form.mdsystemArr.push({prdid: row.prdid, procName: row.app})\n          }\n        }\n        this.form.dbId = ''\n        this.form.dbsystemArr.forEach(e => {\n          this.form.dbId += '【' + e.procName + '】'\n        })\n        this.form.mdId = ''\n        this.form.mdsystemArr.forEach(e => {\n          this.form.mdId += '【' + e.procName + '】'\n        })\n      })\n    },\n    /**\n     * 选择供应商\n     */\n    showVendorDialog(vendor) {\n      if (vendor == 'vendor') {\n        this.vendorType = 'vendor';\n      }\n      if (vendor == 'facilityManufacturer') {\n        this.vendorType = 'facilityManufacturer';\n      }\n      if (vendor == 'maintainUnit') {\n        this.vendorType = 'maintainUnit';\n      }\n      this.vendorDialog = true\n    },\n    getSelectedVendor() {\n      if (this.vendorType == 'vendor' && this.form.vendor != undefined) {\n        return this.form.vendor;\n      } else if (this.vendorType == 'facilityManufacturer' && this.form.facilityManufacturer != undefined) {\n        return this.form.facilityManufacturer;\n      } else if (this.vendorType == 'maintainUnit' && this.form.maintainUnit != undefined) {\n        return this.form.maintainUnit;\n      }\n    },\n    /**\n     * 选择产商\n     */\n    selectConfirm2(vendor) {\n      if (this.vendorType == 'vendor') {\n        this.form.vendor = vendor.id;\n        this.form.vendorName = vendor.vendorName;\n      }\n      if (this.vendorType == 'facilityManufacturer') {\n        this.form.facilityManufacturer = vendor.id;\n        this.form.facilityManufacturerName = vendor.vendorName;\n      }\n      if (this.vendorType == 'maintainUnit') {\n        this.form.maintainUnit = vendor.id;\n        this.form.maintainUnitName = vendor.vendorName;\n      }\n      this.vendorDialog = false;\n    },\n    userCancel2() {\n      this.vendorDialog = false;\n    },\n    //选择操作系统\n    setupOptSystem(field) {\n      this.softField = field;\n      if (field == 'optId') {\n        this.multiple = false\n        this.softType = 'system'\n      }\n      if (field == 'dbId') {\n        this.multiple = true\n        this.softType = 'database';\n      }\n      if (field == 'mdId') {\n        this.multiple = true\n        this.softType = 'middleware';\n      }\n      this.softDialog = true;\n    },\n    getSelectedProduct() {\n      if (this.softField == 'optId' && this.form.optsystem != undefined) {\n        return this.form.optsystem.prdid;\n      } else if (this.softField == 'dbId' && this.form.dbsystemArr != null && this.form.dbsystemArr != undefined && this.form.dbsystemArr.length > 0) {\n        return this.form.dbsystemArr.map(e => e.prdid)\n      } else if (this.softField == 'mdId' && this.form.mdsystemArr != null && this.form.mdsystemArr != undefined && this.form.mdsystemArr.length > 0) {\n        return this.form.mdsystemArr.map(e => e.prdid)\n      }\n    },\n    softConfirm(row) {\n      let value = ''\n      if (this.softField == 'optId') {\n        if (row == null) {\n          this.form.optsystem = null;\n        } else {\n          if (this.form.optsystem == null) this.form.optsystem = {};\n          this.form.optsystem.prdid = row.prdid;\n          this.form.optsystem.procName = row.procName;\n        }\n        value = row.procName\n      }\n      if (this.softField == 'dbId') {\n        if (row == null || row.length === 0) {\n          this.form.dbsystemArr = [];\n        } else {\n          this.form.dbsystemArr = [];\n          row.forEach(e => {\n            this.form.dbsystemArr.push({prdid: e.prdid, procName: e.procName})\n            value += '【' + e.procName + '】'\n          })\n        }\n      }\n      if (this.softField == 'mdId') {\n        if (row == null || row.length === 0) {\n          this.form.mdsystemArr = [];\n        } else {\n          this.form.mdsystemArr = [];\n          row.forEach(e => {\n            this.form.mdsystemArr.push({prdid: e.prdid, procName: e.procName})\n            value += '【' + e.procName + '】'\n          })\n        }\n      }\n      this.form[this.softField] = value\n      this.softDialog = false;\n    },\n    softCancel() {\n      this.softDialog = false;\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.form = {...this.form}\n      this.form.assetClass = this.classId\n      this.form.assetClassDesc = this.className\n      this.$refs['form'].validate(valid => {\n        if (valid && this.checkIpMacArr()) {\n          const flag = (this.children && this.children.length > 0)\n          if (flag == undefined) {\n            this.form.assetType = this.classId\n            this.form.assetTypeDesc = this.className\n          }\n          this.form.ipMacArr.forEach(e => {\n            if (e.isMainIp) {\n              e.mainIp = '1'\n            } else {\n              e.mainIp = '0'\n            }\n          })\n          if (this.form.assetId != null) {\n            updateServer2(this.form).then(response => {\n              this.$modal.msgSuccess('修改成功')\n              this.form.ipMacArr = [];\n              this.$refs.form.resetFields()\n              this.$emit('confirm', response.data)\n              this.visible = false;\n              if(this.$parent.$parent){\n                // this.$parent.$parent.editFlag = false;\n                this.$parent.$parent.getList && this.$parent.$parent.getList();\n                this.$parent.$parent.getServerCountByDictData && this.$parent.$parent.getServerCountByDictData();\n              }\n            });\n          } else {\n            addServer2(this.form).then(response => {\n              this.$modal.msgSuccess('新增成功')\n              this.$refs.form.resetFields()\n              this.form.ipMacArr = [];\n              this.$emit('confirm', response.data)\n              this.open = false\n              if (this.$route.params.add) {\n                this.$router.back()\n              }\n              this.backAdd(this.form)\n              this.visible = false;\n              if(this.$parent.$parent){\n                // this.$parent.$parent.editFlag = false;\n                this.$parent.$parent.getList && this.$parent.$parent.getList();\n                this.$parent.$parent.getServerCountByDictData && this.$parent.$parent.getServerCountByDictData();\n              }\n            })\n          }\n        }\n      });\n    },\n    cancel() {\n      this.$refs['form'].resetFields()\n      this.$emit('cancel')\n    },\n    /**\n     * 其他页面增加的回调\n     */\n    backAdd(addrow) {\n      this.$root.$emit(this.addEvent, addrow)\n    },\n    addIpMac() {\n      this.form.ipMacArr.push({isMainIp: false})\n    },\n    removeIpMac(index) {\n      this.form.ipMacArr.splice(index, 1)\n    },\n    checkIpMacArr() {\n      if (this.form.ipMacArr === null || this.form.ipMacArr.length <= 0) {\n        this.$modal.msgError('网络信息不可为空')\n        return false\n      }\n\n      let mainIpCount = 0\n      for (let i = 0; i < this.form.ipMacArr.length; i++) {\n        const e = this.form.ipMacArr[i]\n        if (e.domainId === null || e.domainId === '' || e.domainId === undefined) {\n          this.$modal.msgError('所属网络不可为空')\n          return false\n        }\n        if (e.ipv4 === null || e.ipv4 === '' || e.ipv4 === undefined) {\n          this.$modal.msgError('IP不可为空')\n          return false\n        } else {\n          if (!this.isValidIP(e.ipv4)) {\n            this.$modal.msgError('请输入正确格式的IP')\n            return false\n          }\n        }\n        if (e.isMainIp) {\n          mainIpCount++\n        }\n      }\n      if (mainIpCount === 0) {\n        this.$modal.msgError('必须选择一个主IP')\n        return false\n      } else if (mainIpCount > 1) {\n        this.$modal.msgError('只能选择一个主IP')\n        return false\n      }\n\n      return true\n    },\n    inputLossOfFocus(val, index){\n      if (this.form.assetCode == '' || !this.form.assetCode){\n        this.form.assetCode = 'ZJ' + this.getFormattedDate()\n      }\n    },\n    /**\n     * 资产类型变化\n     */\n    assetTypeChange(data) {\n      let item = this.children.find(item => {\n        return item.id == data;\n      })\n      if (item) {\n        this.form.assetTypeDesc = item.typeName;\n      }\n    },\n\n    getRulesForField(item) {\n      return this.dynamicRules[item.fieldKey] || [];\n    },\n\n    // 获取字段所占列数\n    getSpan(fieldKey) {\n      return fieldKey === 'remark' ? 16 : 8;\n    },\n\n    // 判断字段是否应该显示\n    shouldShowField(item) {\n      // 承载设备只在虚拟设备为Y时显示\n      if (item.fieldKey === 'baseAsset') {\n        return this.form.isVirtual === 'Y';\n      }\n      // 资产类型只在有子类型时显示\n      if (item.fieldKey === 'assetType') {\n        return this.children && this.children.length;\n      }\n      return true;\n    },\n\n    getFormattedDate() {\n      const now = new Date();\n      const year = now.getFullYear();\n      const month = String(now.getMonth() + 1).padStart(2, '0');\n      const day = String(now.getDate()).padStart(2, '0');\n      const hours = String(now.getHours()).padStart(2, '0');\n      const minutes = String(now.getMinutes()).padStart(2, '0');\n      const seconds = String(now.getSeconds()).padStart(2, '0');\n\n      return `${year}${month}${day}${hours}${minutes}${seconds}`;\n    },\n    isValidIP(ip) {\n      const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/\n      const flag = ipRegex.test(ip)\n      console.log(flag)\n      return flag\n    },\n    baseAssetSelectHandle() {\n      this.baseAssetDialog = true;\n    },\n    baseAssetCancel() {\n      this.baseAssetDialog = false;\n    },\n    baseAssetConfirm(row) {\n      this.form.baseAsset = row.assetId;\n      this.form.baseAssetName = row.assetName;\n      this.baseAssetCancel();\n    },\n    valueInit() {\n      if (this.form.baseAsset != null && this.form.baseAsset != undefined) {\n        return [{assetId: this.form.baseAsset}];\n      }\n    },\n    changeIsVirtual(value) {\n      if (value === 'N') {\n        this.form.baseAsset = ''\n        this.form.baseAssetName = ''\n      }\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"@/assets/styles/customForm\";\n.customForm {\n  ::v-deep .el-dialog__body {\n    height: 75vh;\n    overflow-y: auto;\n    padding: 0 !important;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4PA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,aAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,cAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,WAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,cAAA,GAAAH,sBAAA,CAAAJ,OAAA;AACA,IAAAQ,eAAA,GAAAJ,sBAAA,CAAAJ,OAAA;AACA,IAAAS,WAAA,GAAAL,sBAAA,CAAAJ,OAAA;AACA,IAAAU,eAAA,GAAAN,sBAAA,CAAAJ,OAAA;AACA,IAAAW,KAAA,GAAAX,OAAA;AACA,IAAAY,QAAA,GAAAZ,OAAA;AACA,IAAAa,OAAA,GAAAb,OAAA;AACA,IAAAc,cAAA,GAAAV,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAe,IAAA;EACAC,MAAA,GAAAC,sBAAA;EACAC,KAAA;EACAC,UAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA;EACA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,OAAA;MACAC,QAAA;IACA;IACAC,UAAA;MACAL,IAAA,EAAAM,MAAA;MACAH,OAAA;MACAC,QAAA;IACA;IACAG,KAAA;MACAP,IAAA,EAAAE,MAAA;MACAC,OAAA;IACA;IACAK,eAAA;MACAR,IAAA,EAAAS,OAAA;MACAN,OAAA;IACA;EACA;EACAO,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,EAAAC,SAAA;MACAC,UAAA;MACAC,eAAA;MACAC,MAAA;MACAC,OAAA;MACAC,QAAA;MACAC,YAAA;MACAC,QAAA;MACAC,YAAA;MACAC,UAAA;MACAC,QAAA;MACAC,SAAA;MACAC,QAAA;MACAC,QAAA;MACAC,QAAA;MACAC,IAAA;QACAC,QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,QAAA,EAAAnB,SAAA;QACAoB,WAAA;QACAC,WAAA;MACA;MACAC,KAAA;QACAC,SAAA,GACA;QACA;UAAAC,GAAA;UAAAC,GAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,SAAA,GACA;UAAAzB,QAAA;UAAAkC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAH,GAAA;UAAAC,GAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,SAAA,GACA;UAAApC,QAAA;UAAAkC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,EAAA,GACA;UACArC,QAAA;UACAsC,OAAA;UACAJ,OAAA;UACAC,OAAA;QACA,EACA;QACAI,GAAA,GACA;UAAAD,OAAA;UAAAJ,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,QAAA,GACA;UAAAxC,QAAA;UAAAkC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAM,cAAA,GACA;UAAAT,GAAA;UAAAC,GAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAO,MAAA,GACA;UAAA1C,QAAA;UAAAkC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAQ,eAAA;MACAC,mBAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAA3C,eAAA;MACA;MACA4C,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,2BAAAD,GAAA;MACA;IACA;IACA;IACAE,kBAAA,WAAAA,mBAAA;MACA,YAAAC,SAAA,CAAAC,GAAA,WAAAC,KAAA;QACA,IAAAC,QAAA,OAAAC,cAAA,CAAAzD,OAAA,MAAAuD,KAAA;QACAC,QAAA,CAAAE,WAAA,GAAAH,KAAA,CAAAG,WAAA,CAAAC,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,MAAA;QAAA;QACA,OAAAL,QAAA;MACA;IACA;IACA;IACAM,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,IAAAhC,KAAA;MACA,KAAAqB,kBAAA,CAAAY,OAAA,WAAAT,KAAA;QACAA,KAAA,CAAAG,WAAA,CAAAM,OAAA,WAAAJ,IAAA;UACA,IAAAK,QAAA,GAAAL,IAAA,CAAAK,QAAA;UAEA,KAAAlC,KAAA,CAAAkC,QAAA;YACAlC,KAAA,CAAAkC,QAAA;UACA;UAEA,IAAAF,KAAA,CAAAhC,KAAA,CAAAkC,QAAA;YAAA,IAAAC,eAAA;YACA,IAAAC,aAAA,GAAAJ,KAAA,CAAAhC,KAAA,CAAAkC,QAAA,EAAAN,MAAA,WAAAS,IAAA;cAAA,QAAAA,IAAA,CAAAnE,QAAA;YAAA;YACA,CAAAiE,eAAA,GAAAnC,KAAA,CAAAkC,QAAA,GAAAI,IAAA,CAAAC,KAAA,CAAAJ,eAAA,MAAAK,mBAAA,CAAAvE,OAAA,EAAAmE,aAAA;UACA;UAEA,IAAAP,IAAA,CAAA3D,QAAA;YACA,IAAAuE,eAAA,GAAAzC,KAAA,CAAAkC,QAAA,EAAAQ,IAAA,WAAAL,IAAA;cAAA,OAAAA,IAAA,CAAAnE,QAAA;YAAA;YACA,KAAAuE,eAAA;cACAzC,KAAA,CAAAkC,QAAA,EAAAI,IAAA;gBACApE,QAAA;gBACAkC,OAAA,KAAAuC,MAAA,CAAAd,IAAA,CAAAe,SAAA;gBACAvC,OAAA;cACA;YACA;UACA;QACA;MACA;MACA,OAAAL,KAAA;IACA;IAEA;IACA6C,YAAA,WAAAA,aAAA;MACA,IAAAC,WAAA,QAAAxB,SAAA,CAAAM,MAAA,WAAAJ,KAAA;QAAA,OAAAA,KAAA,CAAAuB,QAAA;MAAA;MACA,OAAAD,WAAA,IAAAnB,WAAA,CAAAC,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAK,QAAA,oBAAAL,IAAA,CAAAC,MAAA;MAAA;IACA;EACA;EACAkB,OAAA,WAAAA,QAAA,GAEA;EACAC,OAAA;IACAC,eAAA,WAAAA,gBAAAhB,QAAA;MACA,QAAAA,QAAA;QACA;UACA,YAAAiB,IAAA,CAAArF,IAAA,CAAAsF,UAAA;QACA;UACA,YAAAD,IAAA,CAAArF,IAAA,CAAAuF,UAAA;QACA;UACA;MACA;IACA;IAEAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,IAAA;MACA,KAAAC,QAAA;MACA,KAAAC,SAAA;QACA,KAAAH,MAAA,CAAA9D,IAAA,CAAAQ,SAAA;UACAsD,MAAA,CAAA9D,IAAA,CAAAQ,SAAA,UAAAsD,MAAA,CAAAI,gBAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,UAAAnF,IAAA;QACA,KAAAoF,QAAA;MACA;QACA,KAAAC,OAAA,MAAArF,IAAA;QACA,KAAAG,eAAA;MACA;IACA;IACA4E,IAAA,WAAAA,KAAA;MAAA,IAAAO,MAAA;MACA,IAAAC,kCAAA,OAAAlF,OAAA,EAAAmF,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAA9E,QAAA,GAAAiF,GAAA,CAAAC,IAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAL,MAAA,CAAA9E,QAAA,CAAAoF,MAAA,EAAAD,CAAA;UACA,IAAAL,MAAA,CAAA9E,QAAA,CAAAmF,CAAA,EAAAE,oBAAA;YACAP,MAAA,CAAA9E,QAAA,CAAAsF,MAAA,CAAAxG,MAAA,CAAAqG,CAAA;YACAA,CAAA,GAAAA,CAAA;UACA;QACA;QACAL,MAAA,CAAA/E,YAAA,GAAA+E,MAAA,CAAAS,UAAA,CAAAT,MAAA,CAAA9E,QAAA;QACA8E,MAAA,CAAAhF,QAAA,GAAAgF,MAAA,CAAA/E,YAAA,IAAAD,QAAA;QACA,IAAAgF,MAAA,CAAAhF,QAAA,IAAAgF,MAAA,CAAAhF,QAAA,CAAAsF,MAAA,EAAAN,MAAA,CAAA/D,KAAA,CAAAM,SAAA,IACA;UAAApC,QAAA;UAAAkC,OAAA;UAAAC,OAAA;QAAA,EACA;MACA;IACA;IACAoD,QAAA,WAAAA,SAAA;MAAA,IAAAgB,MAAA;MAAA,WAAAC,kBAAA,CAAAzG,OAAA,mBAAA0G,oBAAA,CAAA1G,OAAA,IAAA2G,IAAA,UAAAC,QAAA;QAAA,IAAAC,SAAA;QAAA,WAAAH,oBAAA,CAAA1G,OAAA,IAAA8G,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,KACAV,MAAA,CAAA5G,OAAA;gBAAAoH,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAC,iBAAA,EAAAX,MAAA,CAAA5G,OAAA,EAAAoG,IAAA,WAAAoB,QAAA;gBACA,IAAAA,QAAA,CAAA7G,IAAA,CAAAkB,QAAA;kBACA2F,QAAA,CAAA7G,IAAA,CAAAkB,QAAA,CAAAuC,OAAA,WAAAqD,CAAA;oBACA,IAAAA,CAAA,CAAAC,MAAA;sBACAD,CAAA,CAAAE,QAAA;oBACA,WAAAF,CAAA,CAAAC,MAAA;sBACAD,CAAA,CAAAE,QAAA;oBACA;kBACA;gBACA;gBACAf,MAAA,CAAAhF,IAAA,GAAA4F,QAAA,CAAA7G,IAAA;gBACA,IAAAiG,MAAA,CAAAhF,IAAA,CAAAgG,SAAA,UAAAhB,MAAA,CAAAhF,IAAA,CAAAiG,KAAA,GAAAjB,MAAA,CAAAhF,IAAA,CAAAgG,SAAA,CAAAE,QAAA;gBACA,IAAAlB,MAAA,CAAAhF,IAAA,CAAAK,WAAA,aAAA2E,MAAA,CAAAhF,IAAA,CAAAK,WAAA,KAAApB,SAAA,IAAA+F,MAAA,CAAAhF,IAAA,CAAAK,WAAA,CAAAuE,MAAA;kBACAI,MAAA,CAAAhF,IAAA,CAAAmG,IAAA;kBACAnB,MAAA,CAAAhF,IAAA,CAAAK,WAAA,CAAAmC,OAAA,WAAAqD,CAAA;oBACAb,MAAA,CAAAhF,IAAA,CAAAmG,IAAA,UAAAN,CAAA,CAAAK,QAAA;kBACA;gBACA;gBACA,IAAAlB,MAAA,CAAAhF,IAAA,CAAAM,WAAA,aAAA0E,MAAA,CAAAhF,IAAA,CAAAM,WAAA,KAAArB,SAAA,IAAA+F,MAAA,CAAAhF,IAAA,CAAAM,WAAA,CAAAsE,MAAA;kBACAI,MAAA,CAAAhF,IAAA,CAAAoG,IAAA;kBACApB,MAAA,CAAAhF,IAAA,CAAAM,WAAA,CAAAkC,OAAA,WAAAqD,CAAA;oBACAb,MAAA,CAAAhF,IAAA,CAAAoG,IAAA,UAAAP,CAAA,CAAAK,QAAA;kBACA;gBACA;gBACAlB,MAAA,CAAA5F,MAAA,GAAA4F,MAAA,CAAAhF,IAAA,CAAAqG,IAAA,GAAArB,MAAA,CAAAhF,IAAA,CAAAqG,IAAA,CAAAC,KAAA;gBAEA,IAAAtB,MAAA,CAAAhF,IAAA,CAAAuG,oBAAA;kBACA,IAAAC,iBAAA,EAAAxB,MAAA,CAAAhF,IAAA,CAAAuG,oBAAA,EAAA/B,IAAA,WAAAC,GAAA;oBACAO,MAAA,CAAAhF,IAAA,CAAAyG,wBAAA,GAAAhC,GAAA,CAAA1F,IAAA,CAAA2H,UAAA;kBACA;gBACA;gBAEA,IAAA1B,MAAA,CAAAhF,IAAA,CAAA2G,YAAA;kBACA,IAAAH,iBAAA,EAAAxB,MAAA,CAAAhF,IAAA,CAAA2G,YAAA,EAAAnC,IAAA,WAAAC,GAAA;oBACAO,MAAA,CAAAhF,IAAA,CAAA4G,gBAAA,GAAAnC,GAAA,CAAA1F,IAAA,CAAA2H,UAAA;kBACA;gBACA;cACA;YAAA;cAAA,KAEA1B,MAAA,CAAAtG,UAAA;gBAAA8G,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACA,IAAAV,MAAA,CAAAtG,UAAA,CAAAyC,MAAA;gBACA6D,MAAA,CAAAhF,IAAA,CAAAmB,MAAA,GAAA6D,MAAA,CAAAtG,UAAA,CAAAyC,MAAA;cACA;cACA6D,MAAA,CAAAhF,IAAA,CAAAI,QAAA,GAAA4E,MAAA,CAAAtG,UAAA,CAAA0B,QAAA;cAAA,KACA4E,MAAA,CAAAtG,UAAA,CAAAoC,EAAA;gBAAA0E,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAL,SAAA;gBACAU,QAAA;gBACAc,IAAA,EAAA7B,MAAA,CAAAtG,UAAA,CAAAoC;cACA;cAAA0E,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAoB,kBAAA;gBAAAC,MAAA,EAAA/B,MAAA,CAAAtG,UAAA,CAAAoC;cAAA,GAAA0D,IAAA,WAAAwC,SAAA;gBACA,IAAAA,SAAA,CAAAjI,IAAA,IAAAiI,SAAA,CAAAjI,IAAA,CAAA6F,MAAA;kBACAS,SAAA,CAAApE,QAAA,GAAA+F,SAAA,CAAAjI,IAAA,IAAAkC,QAAA;gBACA;cACA;YAAA;cACA+D,MAAA,CAAAhF,IAAA,CAAAC,QAAA,CAAA4C,IAAA,CAAAwC,SAAA;YAAA;cAAA,KAEAL,MAAA,CAAAtG,UAAA,CAAAnB,IAAA;gBAAAiI,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAuB,oBAAA;gBAAAf,QAAA,EAAAlB,MAAA,CAAAtG,UAAA,CAAAnB;cAAA,GAAAiH,IAAA,WAAA0C,UAAA;gBACA,IAAAA,UAAA,CAAAxC,IAAA;kBACA,IAAAyC,QAAA,GAAAD,UAAA,CAAAxC,IAAA,CAAAvC,MAAA,WAAAC,IAAA;oBAAA,OAAA4C,MAAA,CAAAtG,UAAA,CAAAnB,IAAA,KAAA6E,IAAA,CAAA8D,QAAA;kBAAA;kBACA,IAAAiB,QAAA,IAAAA,QAAA,CAAAvC,MAAA;oBACAI,MAAA,CAAAoC,IAAA,CAAApC,MAAA,CAAAhF,IAAA,WAAAmH,QAAA,IAAAjB,QAAA;oBACA,IAAAF,SAAA,GAAAhB,MAAA,CAAAhF,IAAA,CAAAgG,SAAA;oBACAA,SAAA,CAAAqB,KAAA,GAAAF,QAAA,IAAAE,KAAA;oBACArB,SAAA,CAAAE,QAAA,GAAAiB,QAAA,IAAAjB,QAAA;oBACAlB,MAAA,CAAAoC,IAAA,CAAApC,MAAA,CAAAhF,IAAA,eAAAgG,SAAA;kBACA;gBACA;cACA;YAAA;cAAA,KAEAhB,MAAA,CAAAtG,UAAA,CAAA4I,MAAA;gBAAA9B,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,KACAV,MAAA,CAAA5G,OAAA;gBAAAoH,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,MACAV,MAAA,CAAAtG,UAAA,CAAA4I,MAAA,CAAAC,OAAA;gBAAA/B,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAV,MAAA,CAAA7F,eAAA;cAAAqG,QAAA,CAAAE,IAAA;cAAA,OAEA,IAAA8B,iBAAA;gBAAAC,QAAA;gBAAAC,OAAA,EAAA1C,MAAA,CAAAtG,UAAA,CAAA4I,MAAA,CAAAhB,KAAA;cAAA,GAAA9B,IAAA,WAAAC,GAAA;gBACAkD,OAAA,CAAAC,GAAA,QAAAnD,GAAA;gBACAO,MAAA,CAAA9F,UAAA,GAAAuF,GAAA,CAAA1F,IAAA,CAAA8I,IAAA;cACA;YAAA;cAAArC,QAAA,CAAAE,IAAA;cAAA;YAAA;cAEAV,MAAA,CAAA8C,QAAA,oBAAAtD,IAAA;gBACAQ,MAAA,CAAAX,OAAA,CAAAW,MAAA,CAAAtG,UAAA,CAAA4I,MAAA;cACA;YAAA;cAAA9B,QAAA,CAAAE,IAAA;cAAA;YAAA;cAGAV,MAAA,CAAAX,OAAA,CAAAW,MAAA,CAAAtG,UAAA,CAAA4I,MAAA;YAAA;YAAA;cAAA,OAAA9B,QAAA,CAAAuC,IAAA;UAAA;QAAA,GAAA3C,OAAA;MAAA;IAIA;IACAf,OAAA,WAAAA,QAAAiD,MAAA;MAAA,IAAAU,MAAA;MACA,IAAAC,iBAAA,EAAAX,MAAA,EAAA9C,IAAA,WAAAC,GAAA;QACA,IAAAzF,IAAA,GAAAyF,GAAA,CAAA1F,IAAA;QACAiJ,MAAA,CAAAhI,IAAA,CAAAE,SAAA,GAAAlB,IAAA,CAAAkJ,QAAA;QACAF,MAAA,CAAAhI,IAAA,CAAAmI,QAAA,GAAAnJ,IAAA,CAAAoJ,YAAA,CAAAC,IAAA;QACA,IAAApI,QAAA,GAAAjB,IAAA,CAAAsJ,WAAA,CAAAnG,MAAA,WAAAoG,GAAA;UAAA,OAAAA,GAAA,CAAA1B,IAAA,CAAAU,OAAA,CAAAvI,IAAA,CAAAwJ,MAAA;QAAA;QACA,IAAAC,WAAA,GAAAT,MAAA,CAAAhI,IAAA,CAAAC,QAAA,CAAAkC,MAAA,WAAAoG,GAAA;UAAA,OAAAA,GAAA,CAAA1B,IAAA,CAAAU,OAAA,CAAAvI,IAAA,CAAAwJ,MAAA;QAAA;QACAb,OAAA,CAAAC,GAAA,gBAAAa,WAAA;QACAxI,QAAA,CAAAuC,OAAA,WAAA+F,GAAA;UACAA,GAAA,CAAA1B,IAAA,GAAA0B,GAAA,CAAA1B,IAAA,CAAA6B,OAAA,gBAAApC,KAAA;UACAiC,GAAA,CAAAI,IAAA;UACAJ,GAAA,CAAAK,EAAA;UACA,IAAAH,WAAA,CAAA7D,MAAA;YACA2D,GAAA,CAAAtH,QAAA,GAAAwH,WAAA,IAAAxH,QAAA;YACAsH,GAAA,CAAAxC,QAAA,GAAA0C,WAAA,IAAA1C,QAAA;UACA;QACA;QACAiC,MAAA,CAAAhI,IAAA,CAAAG,SAAA,GAAAnB,IAAA,CAAAmB,SAAA;QACA6H,MAAA,CAAAhI,IAAA,CAAAC,QAAA,GAAAA,QAAA;QACA+H,MAAA,CAAAhI,IAAA,CAAA6I,QAAA,GAAA7J,IAAA,CAAA4J,EAAA;QACAZ,MAAA,CAAAhI,IAAA,CAAAgG,SAAA;UAAAqB,KAAA,EAAArI,IAAA,CAAAqI,KAAA;UAAAnB,QAAA,EAAAlH,IAAA,CAAA8J;QAAA;QACAd,MAAA,CAAAhI,IAAA,CAAAiG,KAAA,GAAA+B,MAAA,CAAAhI,IAAA,CAAAgG,SAAA,CAAAE,QAAA;QACA8B,MAAA,CAAAhI,IAAA,CAAAK,WAAA;QACA2H,MAAA,CAAAhI,IAAA,CAAAM,WAAA;QAAA,IAAAyI,SAAA,OAAAC,2BAAA,CAAAxK,OAAA,EACAQ,IAAA,CAAAiK,mBAAA;UAAAC,KAAA;QAAA;UAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAK,CAAA,IAAAC,IAAA;YAAA,IAAAd,GAAA,GAAAW,KAAA,CAAAI,KAAA;YACA,IAAAf,GAAA,CAAAgB,QAAA;cACAvB,MAAA,CAAAhI,IAAA,CAAAK,WAAA,CAAAwC,IAAA;gBAAAwE,KAAA,EAAAkB,GAAA,CAAAlB,KAAA;gBAAAnB,QAAA,EAAAqC,GAAA,CAAAiB;cAAA;YACA,WAAAjB,GAAA,CAAAgB,QAAA;cACAvB,MAAA,CAAAhI,IAAA,CAAAM,WAAA,CAAAuC,IAAA;gBAAAwE,KAAA,EAAAkB,GAAA,CAAAlB,KAAA;gBAAAnB,QAAA,EAAAqC,GAAA,CAAAiB;cAAA;YACA;UACA;QAAA,SAAAC,GAAA;UAAAV,SAAA,CAAAlD,CAAA,CAAA4D,GAAA;QAAA;UAAAV,SAAA,CAAAW,CAAA;QAAA;QACA1B,MAAA,CAAAhI,IAAA,CAAAmG,IAAA;QACA6B,MAAA,CAAAhI,IAAA,CAAAK,WAAA,CAAAmC,OAAA,WAAAqD,CAAA;UACAmC,MAAA,CAAAhI,IAAA,CAAAmG,IAAA,UAAAN,CAAA,CAAAK,QAAA;QACA;QACA8B,MAAA,CAAAhI,IAAA,CAAAoG,IAAA;QACA4B,MAAA,CAAAhI,IAAA,CAAAM,WAAA,CAAAkC,OAAA,WAAAqD,CAAA;UACAmC,MAAA,CAAAhI,IAAA,CAAAoG,IAAA,UAAAP,CAAA,CAAAK,QAAA;QACA;MACA;IACA;IACA;AACA;AACA;IACAyD,gBAAA,WAAAA,iBAAAC,MAAA;MACA,IAAAA,MAAA;QACA,KAAAC,UAAA;MACA;MACA,IAAAD,MAAA;QACA,KAAAC,UAAA;MACA;MACA,IAAAD,MAAA;QACA,KAAAC,UAAA;MACA;MACA,KAAApK,YAAA;IACA;IACAqK,iBAAA,WAAAA,kBAAA;MACA,SAAAD,UAAA,qBAAA7J,IAAA,CAAA4J,MAAA,IAAA3K,SAAA;QACA,YAAAe,IAAA,CAAA4J,MAAA;MACA,gBAAAC,UAAA,mCAAA7J,IAAA,CAAAuG,oBAAA,IAAAtH,SAAA;QACA,YAAAe,IAAA,CAAAuG,oBAAA;MACA,gBAAAsD,UAAA,2BAAA7J,IAAA,CAAA2G,YAAA,IAAA1H,SAAA;QACA,YAAAe,IAAA,CAAA2G,YAAA;MACA;IACA;IACA;AACA;AACA;IACAoD,cAAA,WAAAA,eAAAH,MAAA;MACA,SAAAC,UAAA;QACA,KAAA7J,IAAA,CAAA4J,MAAA,GAAAA,MAAA,CAAAhB,EAAA;QACA,KAAA5I,IAAA,CAAA0G,UAAA,GAAAkD,MAAA,CAAAlD,UAAA;MACA;MACA,SAAAmD,UAAA;QACA,KAAA7J,IAAA,CAAAuG,oBAAA,GAAAqD,MAAA,CAAAhB,EAAA;QACA,KAAA5I,IAAA,CAAAyG,wBAAA,GAAAmD,MAAA,CAAAlD,UAAA;MACA;MACA,SAAAmD,UAAA;QACA,KAAA7J,IAAA,CAAA2G,YAAA,GAAAiD,MAAA,CAAAhB,EAAA;QACA,KAAA5I,IAAA,CAAA4G,gBAAA,GAAAgD,MAAA,CAAAlD,UAAA;MACA;MACA,KAAAjH,YAAA;IACA;IACAuK,WAAA,WAAAA,YAAA;MACA,KAAAvK,YAAA;IACA;IACA;IACAwK,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAAtK,SAAA,GAAAsK,KAAA;MACA,IAAAA,KAAA;QACA,KAAAnK,QAAA;QACA,KAAAJ,QAAA;MACA;MACA,IAAAuK,KAAA;QACA,KAAAnK,QAAA;QACA,KAAAJ,QAAA;MACA;MACA,IAAAuK,KAAA;QACA,KAAAnK,QAAA;QACA,KAAAJ,QAAA;MACA;MACA,KAAAD,UAAA;IACA;IACAyK,kBAAA,WAAAA,mBAAA;MACA,SAAAvK,SAAA,oBAAAI,IAAA,CAAAgG,SAAA,IAAA/G,SAAA;QACA,YAAAe,IAAA,CAAAgG,SAAA,CAAAqB,KAAA;MACA,gBAAAzH,SAAA,mBAAAI,IAAA,CAAAK,WAAA,iBAAAL,IAAA,CAAAK,WAAA,IAAApB,SAAA,SAAAe,IAAA,CAAAK,WAAA,CAAAuE,MAAA;QACA,YAAA5E,IAAA,CAAAK,WAAA,CAAAyB,GAAA,WAAA+D,CAAA;UAAA,OAAAA,CAAA,CAAAwB,KAAA;QAAA;MACA,gBAAAzH,SAAA,mBAAAI,IAAA,CAAAM,WAAA,iBAAAN,IAAA,CAAAM,WAAA,IAAArB,SAAA,SAAAe,IAAA,CAAAM,WAAA,CAAAsE,MAAA;QACA,YAAA5E,IAAA,CAAAM,WAAA,CAAAwB,GAAA,WAAA+D,CAAA;UAAA,OAAAA,CAAA,CAAAwB,KAAA;QAAA;MACA;IACA;IACA+C,WAAA,WAAAA,YAAA7B,GAAA;MAAA,IAAA8B,MAAA;MACA,IAAAf,KAAA;MACA,SAAA1J,SAAA;QACA,IAAA2I,GAAA;UACA,KAAAvI,IAAA,CAAAgG,SAAA;QACA;UACA,SAAAhG,IAAA,CAAAgG,SAAA,eAAAhG,IAAA,CAAAgG,SAAA;UACA,KAAAhG,IAAA,CAAAgG,SAAA,CAAAqB,KAAA,GAAAkB,GAAA,CAAAlB,KAAA;UACA,KAAArH,IAAA,CAAAgG,SAAA,CAAAE,QAAA,GAAAqC,GAAA,CAAArC,QAAA;QACA;QACAoD,KAAA,GAAAf,GAAA,CAAArC,QAAA;MACA;MACA,SAAAtG,SAAA;QACA,IAAA2I,GAAA,YAAAA,GAAA,CAAA3D,MAAA;UACA,KAAA5E,IAAA,CAAAK,WAAA;QACA;UACA,KAAAL,IAAA,CAAAK,WAAA;UACAkI,GAAA,CAAA/F,OAAA,WAAAqD,CAAA;YACAwE,MAAA,CAAArK,IAAA,CAAAK,WAAA,CAAAwC,IAAA;cAAAwE,KAAA,EAAAxB,CAAA,CAAAwB,KAAA;cAAAnB,QAAA,EAAAL,CAAA,CAAAK;YAAA;YACAoD,KAAA,UAAAzD,CAAA,CAAAK,QAAA;UACA;QACA;MACA;MACA,SAAAtG,SAAA;QACA,IAAA2I,GAAA,YAAAA,GAAA,CAAA3D,MAAA;UACA,KAAA5E,IAAA,CAAAM,WAAA;QACA;UACA,KAAAN,IAAA,CAAAM,WAAA;UACAiI,GAAA,CAAA/F,OAAA,WAAAqD,CAAA;YACAwE,MAAA,CAAArK,IAAA,CAAAM,WAAA,CAAAuC,IAAA;cAAAwE,KAAA,EAAAxB,CAAA,CAAAwB,KAAA;cAAAnB,QAAA,EAAAL,CAAA,CAAAK;YAAA;YACAoD,KAAA,UAAAzD,CAAA,CAAAK,QAAA;UACA;QACA;MACA;MACA,KAAAlG,IAAA,MAAAJ,SAAA,IAAA0J,KAAA;MACA,KAAA5J,UAAA;IACA;IACA4K,UAAA,WAAAA,WAAA;MACA,KAAA5K,UAAA;IACA;IACA,WACA6K,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAxK,IAAA,OAAAiC,cAAA,CAAAzD,OAAA,WAAAwB,IAAA;MACA,KAAAA,IAAA,CAAAyK,UAAA,QAAApL,OAAA;MACA,KAAAW,IAAA,CAAA0K,cAAA,QAAAC,SAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA,IAAAN,MAAA,CAAAO,aAAA;UACA,IAAAC,IAAA,GAAAR,MAAA,CAAAlL,QAAA,IAAAkL,MAAA,CAAAlL,QAAA,CAAAsF,MAAA;UACA,IAAAoG,IAAA,IAAA/L,SAAA;YACAuL,MAAA,CAAAxK,IAAA,CAAAa,SAAA,GAAA2J,MAAA,CAAAnL,OAAA;YACAmL,MAAA,CAAAxK,IAAA,CAAAiL,aAAA,GAAAT,MAAA,CAAAG,SAAA;UACA;UACAH,MAAA,CAAAxK,IAAA,CAAAC,QAAA,CAAAuC,OAAA,WAAAqD,CAAA;YACA,IAAAA,CAAA,CAAAE,QAAA;cACAF,CAAA,CAAAC,MAAA;YACA;cACAD,CAAA,CAAAC,MAAA;YACA;UACA;UACA,IAAA0E,MAAA,CAAAxK,IAAA,CAAA5B,OAAA;YACA,IAAA8M,qBAAA,EAAAV,MAAA,CAAAxK,IAAA,EAAAwE,IAAA,WAAAoB,QAAA;cACA4E,MAAA,CAAAW,MAAA,CAAAC,UAAA;cACAZ,MAAA,CAAAxK,IAAA,CAAAC,QAAA;cACAuK,MAAA,CAAAI,KAAA,CAAA5K,IAAA,CAAAqL,WAAA;cACAb,MAAA,CAAA7I,KAAA,YAAAiE,QAAA,CAAA7G,IAAA;cACAyL,MAAA,CAAAjJ,OAAA;cACA,IAAAiJ,MAAA,CAAAc,OAAA,CAAAA,OAAA;gBACA;gBACAd,MAAA,CAAAc,OAAA,CAAAA,OAAA,CAAAC,OAAA,IAAAf,MAAA,CAAAc,OAAA,CAAAA,OAAA,CAAAC,OAAA;gBACAf,MAAA,CAAAc,OAAA,CAAAA,OAAA,CAAAE,wBAAA,IAAAhB,MAAA,CAAAc,OAAA,CAAAA,OAAA,CAAAE,wBAAA;cACA;YACA;UACA;YACA,IAAAC,kBAAA,EAAAjB,MAAA,CAAAxK,IAAA,EAAAwE,IAAA,WAAAoB,QAAA;cACA4E,MAAA,CAAAW,MAAA,CAAAC,UAAA;cACAZ,MAAA,CAAAI,KAAA,CAAA5K,IAAA,CAAAqL,WAAA;cACAb,MAAA,CAAAxK,IAAA,CAAAC,QAAA;cACAuK,MAAA,CAAA7I,KAAA,YAAAiE,QAAA,CAAA7G,IAAA;cACAyL,MAAA,CAAAkB,IAAA;cACA,IAAAlB,MAAA,CAAAmB,MAAA,CAAAC,MAAA,CAAAC,GAAA;gBACArB,MAAA,CAAAsB,OAAA,CAAAC,IAAA;cACA;cACAvB,MAAA,CAAAwB,OAAA,CAAAxB,MAAA,CAAAxK,IAAA;cACAwK,MAAA,CAAAjJ,OAAA;cACA,IAAAiJ,MAAA,CAAAc,OAAA,CAAAA,OAAA;gBACA;gBACAd,MAAA,CAAAc,OAAA,CAAAA,OAAA,CAAAC,OAAA,IAAAf,MAAA,CAAAc,OAAA,CAAAA,OAAA,CAAAC,OAAA;gBACAf,MAAA,CAAAc,OAAA,CAAAA,OAAA,CAAAE,wBAAA,IAAAhB,MAAA,CAAAc,OAAA,CAAAA,OAAA,CAAAE,wBAAA;cACA;YACA;UACA;QACA;MACA;IACA;IACAS,MAAA,WAAAA,OAAA;MACA,KAAArB,KAAA,SAAAS,WAAA;MACA,KAAA1J,KAAA;IACA;IACA;AACA;AACA;IACAqK,OAAA,WAAAA,QAAAE,MAAA;MACA,KAAAC,KAAA,CAAAxK,KAAA,MAAA7B,QAAA,EAAAoM,MAAA;IACA;IACAE,QAAA,WAAAA,SAAA;MACA,KAAApM,IAAA,CAAAC,QAAA,CAAA4C,IAAA;QAAAkD,QAAA;MAAA;IACA;IACAsG,WAAA,WAAAA,YAAAC,KAAA;MACA,KAAAtM,IAAA,CAAAC,QAAA,CAAA6E,MAAA,CAAAwH,KAAA;IACA;IACAvB,aAAA,WAAAA,cAAA;MACA,SAAA/K,IAAA,CAAAC,QAAA,kBAAAD,IAAA,CAAAC,QAAA,CAAA2E,MAAA;QACA,KAAAuG,MAAA,CAAAoB,QAAA;QACA;MACA;MAEA,IAAAC,WAAA;MACA,SAAA7H,CAAA,MAAAA,CAAA,QAAA3E,IAAA,CAAAC,QAAA,CAAA2E,MAAA,EAAAD,CAAA;QACA,IAAAkB,CAAA,QAAA7F,IAAA,CAAAC,QAAA,CAAA0E,CAAA;QACA,IAAAkB,CAAA,CAAA5E,QAAA,aAAA4E,CAAA,CAAA5E,QAAA,WAAA4E,CAAA,CAAA5E,QAAA,KAAAhC,SAAA;UACA,KAAAkM,MAAA,CAAAoB,QAAA;UACA;QACA;QACA,IAAA1G,CAAA,CAAAgB,IAAA,aAAAhB,CAAA,CAAAgB,IAAA,WAAAhB,CAAA,CAAAgB,IAAA,KAAA5H,SAAA;UACA,KAAAkM,MAAA,CAAAoB,QAAA;UACA;QACA;UACA,UAAAE,SAAA,CAAA5G,CAAA,CAAAgB,IAAA;YACA,KAAAsE,MAAA,CAAAoB,QAAA;YACA;UACA;QACA;QACA,IAAA1G,CAAA,CAAAE,QAAA;UACAyG,WAAA;QACA;MACA;MACA,IAAAA,WAAA;QACA,KAAArB,MAAA,CAAAoB,QAAA;QACA;MACA,WAAAC,WAAA;QACA,KAAArB,MAAA,CAAAoB,QAAA;QACA;MACA;MAEA;IACA;IACAG,gBAAA,WAAAA,iBAAAhL,GAAA,EAAA4K,KAAA;MACA,SAAAtM,IAAA,CAAAQ,SAAA,gBAAAR,IAAA,CAAAQ,SAAA;QACA,KAAAR,IAAA,CAAAQ,SAAA,eAAA0D,gBAAA;MACA;IACA;IACA;AACA;AACA;IACAyI,eAAA,WAAAA,gBAAA5N,IAAA;MACA,IAAAqD,IAAA,QAAA9C,QAAA,CAAAsN,IAAA,WAAAxK,IAAA;QACA,OAAAA,IAAA,CAAAwG,EAAA,IAAA7J,IAAA;MACA;MACA,IAAAqD,IAAA;QACA,KAAApC,IAAA,CAAAiL,aAAA,GAAA7I,IAAA,CAAAyK,QAAA;MACA;IACA;IAEAC,gBAAA,WAAAA,iBAAA1K,IAAA;MACA,YAAAE,YAAA,CAAAF,IAAA,CAAAK,QAAA;IACA;IAEA;IACAsK,OAAA,WAAAA,QAAAtK,QAAA;MACA,OAAAA,QAAA;IACA;IAEA;IACAuK,eAAA,WAAAA,gBAAA5K,IAAA;MACA;MACA,IAAAA,IAAA,CAAAK,QAAA;QACA,YAAAzC,IAAA,CAAAiN,SAAA;MACA;MACA;MACA,IAAA7K,IAAA,CAAAK,QAAA;QACA,YAAAnD,QAAA,SAAAA,QAAA,CAAAsF,MAAA;MACA;MACA;IACA;IAEAV,gBAAA,WAAAA,iBAAA;MACA,IAAAgJ,GAAA,OAAAC,IAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAA/O,MAAA,CAAA2O,GAAA,CAAAK,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAlP,MAAA,CAAA2O,GAAA,CAAAQ,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAApP,MAAA,CAAA2O,GAAA,CAAAU,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAtP,MAAA,CAAA2O,GAAA,CAAAY,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAxP,MAAA,CAAA2O,GAAA,CAAAc,UAAA,IAAAR,QAAA;MAEA,UAAAtK,MAAA,CAAAkK,IAAA,EAAAlK,MAAA,CAAAoK,KAAA,EAAApK,MAAA,CAAAuK,GAAA,EAAAvK,MAAA,CAAAyK,KAAA,EAAAzK,MAAA,CAAA2K,OAAA,EAAA3K,MAAA,CAAA6K,OAAA;IACA;IACAtB,SAAA,WAAAA,UAAA3L,EAAA;MACA,IAAAmN,OAAA;MACA,IAAAjD,IAAA,GAAAiD,OAAA,CAAAC,IAAA,CAAApN,EAAA;MACA6G,OAAA,CAAAC,GAAA,CAAAoD,IAAA;MACA,OAAAA,IAAA;IACA;IACAmD,qBAAA,WAAAA,sBAAA;MACA,KAAA/M,eAAA;IACA;IACAgN,eAAA,WAAAA,gBAAA;MACA,KAAAhN,eAAA;IACA;IACAiN,gBAAA,WAAAA,iBAAA9F,GAAA;MACA,KAAAvI,IAAA,CAAAsO,SAAA,GAAA/F,GAAA,CAAAnK,OAAA;MACA,KAAA4B,IAAA,CAAAuO,aAAA,GAAAhG,GAAA,CAAArI,SAAA;MACA,KAAAkO,eAAA;IACA;IACAI,SAAA,WAAAA,UAAA;MACA,SAAAxO,IAAA,CAAAsO,SAAA,iBAAAtO,IAAA,CAAAsO,SAAA,IAAArP,SAAA;QACA;UAAAb,OAAA,OAAA4B,IAAA,CAAAsO;QAAA;MACA;IACA;IACAG,eAAA,WAAAA,gBAAAnF,KAAA;MACA,IAAAA,KAAA;QACA,KAAAtJ,IAAA,CAAAsO,SAAA;QACA,KAAAtO,IAAA,CAAAuO,aAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}