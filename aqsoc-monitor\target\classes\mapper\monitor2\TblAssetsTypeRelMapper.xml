<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblAssetsTypeRelMapper">
    
    <resultMap type="TblAssetsTypeRel" id="TblAssetsTypeRelResult">
        <result property="rid"    column="rid"    />
        <result property="assetId"    column="asset_id"    />
        <result property="tcd"    column="TCD"    />
    </resultMap>

    <sql id="selectTblAssetsTypeRelVo">
        select rid, asset_id, TCD from tbl_assets_type_rel
    </sql>

    <select id="selectTblAssetsTypeRelList" parameterType="TblAssetsTypeRel" resultMap="TblAssetsTypeRelResult">
        <include refid="selectTblAssetsTypeRelVo"/>
        <where>  
            <if test="assetId != null  and assetId != ''"> and asset_id = #{assetId}</if>
            <if test="tcd != null "> and TCD = #{tcd}</if>
        </where>
    </select>
    
    <select id="selectTblAssetsTypeRelByRid" parameterType="Long" resultMap="TblAssetsTypeRelResult">
        <include refid="selectTblAssetsTypeRelVo"/>
        where rid = #{rid}
    </select>
    <select id="selectTblAssetsTypeRelByTid" parameterType="String" resultMap="TblAssetsTypeRelResult">
        <include refid="selectTblAssetsTypeRelVo"/>
        where asset_id = #{assetId}
    </select>
    <select id="selectTblAssetsTypeRelByTcd" parameterType="Long" resultMap="TblAssetsTypeRelResult">
        <include refid="selectTblAssetsTypeRelVo"></include>
        where TCD = #{tcd}
    </select>
    <select id="selectTblAssetsTypeRelByTcds" parameterType="Long" resultMap="TblAssetsTypeRelResult">
        <include refid="selectTblAssetsTypeRelVo"></include>
         where TCD in
        <foreach item="tcd" collection="list" open="(" separator="," close=")">
            #{tcd}
        </foreach>
    </select>
    <insert id="insertTblAssetsTypeRel" parameterType="TblAssetsTypeRel" useGeneratedKeys="true" keyProperty="rid">
        insert into tbl_assets_type_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null and assetId != ''">asset_id,</if>
            <if test="tcd != null">TCD,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null and assetId != ''">#{assetId},</if>
            <if test="tcd != null">#{tcd},</if>
         </trim>
    </insert>

    <update id="updateTblAssetsTypeRel" parameterType="TblAssetsTypeRel">
        update tbl_assets_type_rel
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null and assetId != ''">asset_id = #{assetId},</if>
            <if test="tcd != null">TCD = #{tcd},</if>
        </trim>
        where rid = #{rid}
    </update>

    <delete id="deleteTblAssetsTypeRel" parameterType="TblAssetsTypeRel">
        delete from tbl_assets_type_rel
        <where>
            <if test="assetId != null  and assetId != ''"> and asset_id = #{assetId}</if>
            <if test="tcd != null "> and TCD = #{tcd}</if>
        </where>
    </delete>

    <delete id="deleteTblAssetsTypeRelByRid" parameterType="Long">
        delete from tbl_assets_type_rel where rid = #{rid}
    </delete>

    <delete id="deleteTblAssetsTypeRelByRids" parameterType="String">
        delete from tbl_assets_type_rel where rid in 
        <foreach item="rid" collection="array" open="(" separator="," close=")">
            #{rid}
        </foreach>
    </delete>
    <delete id="deleteTblAssetsTypeRelByTid" parameterType="String">
        delete from tbl_assets_type_rel where asset_id = #{assetId}

    </delete>
</mapper>