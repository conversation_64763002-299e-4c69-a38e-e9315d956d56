<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenOtherCommonMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenOtherCommonListVO">
        select a.* from threaten_other_common a left join threaten_alarm_origin b on a.id = b.origin_id and (b.threaten_type = 'threaten_other_common' or b.threaten_type = '其他事件')
        <where>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.baseId!=null">
                and a.base_id = #{query.baseId}
            </if>
            <if test="query.attackNum!=null">
                and a.attack_num = #{query.attackNum}
            </if>
            <if test="query.attackDirection!=null">
                and a.attack_direction = #{query.attackDirection}
            </if>
            <if test="query.attackStage!=null">
                and a.attack_stage = #{query.attackStage}
            </if>
            <if test="query.attackResult!=null">
                and a.attack_result = #{query.attackResult}
            </if>
            <if test="query.pid!=null">
                and a.pid = #{query.pid}
            </if>
            <if test="query.processTree!=null and query.processTree!=''">
                and a.process_tree like concat('%', #{query.processTree}, '%')
            </if>
            <if test="query.processName!=null and query.processName!=''">
                and a.process_name like concat('%', #{query.processName}, '%')
            </if>
            <if test="query.filePath!=null and query.filePath!=''">
                and a.file_path like concat('%', #{query.filePath}, '%')
            </if>
            <if test="query.fileAuthority!=null and query.fileAuthority!=''">
                and a.file_authority like concat('%', #{query.fileAuthority}, '%')
            </if>
            <if test="query.fileSize!=null">
                and a.file_size = #{query.fileSize}
            </if>
            <if test="query.fileMd5!=null and query.fileMd5!=''">
                and a.file_md5 like concat('%', #{query.fileMd5}, '%')
            </if>
            <if test="query.fileSha256!=null and query.fileSha256!=''">
                and a.file_sha256 like concat('%', #{query.fileSha256}, '%')
            </if>
            <if test="query.createTime!=null">
                and a.create_time = #{query.createTime}
            </if>
            <if test="query.alarmId!=null">
                and b.alarm_id = #{query.alarmId}
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenOtherCommonListVO">
        select a.* from threaten_other_common a 
        where a.id=#{id}
    </select>
</mapper>
