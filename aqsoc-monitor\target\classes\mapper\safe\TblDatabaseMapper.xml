<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblDatabaseMapper">

    <resultMap type="TblDatabase" id="TblDatabaseResult">
        <result property="assetId" column="asset_id"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="isVirtual" column="is_virtual"/>
        <result property="databaseVersion" column="database_version"/>
        <result property="application" column="application"/>
        <result property="systemVersion" column="system_version"/>
        <result property="middleware" column="middleware"/>
        <result property="deviceName" column="device_name"/>
        <result property="function" column="function"/>
        <result property="manger" column="manger"/>
        <result property="vendor" column="vendor"/>
        <result property="degreeImportance" column="degree_importance"/>
        <result property="assetType" column="asset_type"/>
        <result property="assetTypeDesc" column="asset_type_desc"/>
        <result property="assetClass" column="asset_class"/>
        <result property="assetClassDesc" column="asset_class_desc"/>
        <result property="netType" column="net_type"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="orgnId" column="orgn_id"/>
        <result property="userName" column="user_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="mangerName" column="manger_name"/>
    </resultMap>

    <sql id="selectTblDatabaseVo">
        select a.asset_id,
               a.asset_code,
               a.asset_name,
               a.is_virtual,
               a.database_version,
               a.application,
               a.system_version,
               a.middleware,
               a.device_name,
               a.function,
               a.vendor,
               a.manger,
               a.degree_importance,
               a.asset_type,
               a.asset_type_desc,
               a.asset_class,
               a.asset_class_desc,
               a.remark,
               a.create_by,
               a.create_time,
               a.update_by,
               a.update_time,
               a.user_id,
               a.dept_id,
               a.orgn_id,
               b.user_name,
               c.dept_name,
               d.vendor_name,
               e.nick_name as manger_name
        from tbl_database a
                 left join sys_user b on a.user_id = b.user_id
                 left join sys_dept c on a.dept_id = c.dept_id
                 left join tbl_vendor d on a.vendor = d.id
                 left join sys_user e on a.manger = e.user_id
    </sql>

    <select id="selectTblDatabaseList" parameterType="TblDatabase" resultMap="TblDatabaseResult">
        <include refid="selectTblDatabaseVo"/>
        <where>
            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>
            <if test="assetCode != null  and assetCode != ''"> and asset_code = #{assetCode}</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="isVirtual != null  and isVirtual != ''"> and is_virtual = #{isVirtual}</if>
            <if test="databaseVersion != null  and databaseVersion != ''"> and database_version = #{databaseVersion}</if>
            <if test="application != null  and application != ''"> and application = #{application}</if>
            <if test="systemVersion != null  and systemVersion != ''"> and system_version = #{systemVersion}</if>
            <if test="middleware != null  and middleware != ''"> and middleware = #{middleware}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="function != null  and function != ''"> and function = #{function}</if>
            <if test="vendor != null  and vendor != ''"> and vendor = #{vendor}</if>
            <if test="degreeImportance != null  and degreeImportance != ''"> and degree_importance = #{degreeImportance}</if>
            <if test="assetType != null  and assetType != ''"> and asset_type = #{assetType}</if>
            <if test="assetTypeDesc != null  and assetTypeDesc != ''"> and asset_type_desc = #{assetTypeDesc}</if>
            <if test="assetClass != null  and assetClass != ''"> and asset_class = #{assetClass}</if>
            <if test="assetClassDesc != null  and assetClassDesc != ''"> and asset_class_desc = #{assetClassDesc}</if>
            <if test="deptId != null and deptId != 0">
                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
    </select>

    <select id="selectTblDatabaseByAssetId" parameterType="Long" resultMap="TblDatabaseResult">
        <include refid="selectTblDatabaseVo"/>
        where a.asset_id = #{assetId}
    </select>

    <select id="selectTblDatabaseByAssetIds" parameterType="Long" resultMap="TblDatabaseResult">
        <include refid="selectTblDatabaseVo"/>
        where a.asset_id in
        <foreach item="assetId" collection="array" open="(" close=")" separator=",">
            #{assetId}
        </foreach>
    </select>

    <insert id="insertTblDatabase" parameterType="TblDatabase">
        insert into tbl_database
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="assetCode != null">asset_code,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="isVirtual != null">is_virtual,</if>
            <if test="databaseVersion != null">database_version,</if>
            <if test="application != null">application,</if>
            <if test="systemVersion != null">system_version,</if>
            <if test="middleware != null">middleware,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="function != null">function,</if>
            <if test="manger != null">manger,</if>
            <if test="vendor != null">vendor,</if>
            <if test="degreeImportance != null">degree_importance,</if>
            <if test="assetType != null">asset_type,</if>
            <if test="assetTypeDesc != null">asset_type_desc,</if>
            <if test="assetClass != null and assetClass != ''">asset_class,</if>
            <if test="assetClassDesc != null">asset_class_desc,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="orgnId != null">orgn_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="assetCode != null">#{assetCode},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="isVirtual != null">#{isVirtual},</if>
            <if test="databaseVersion != null">#{databaseVersion},</if>
            <if test="application != null">#{application},</if>
            <if test="systemVersion != null">#{systemVersion},</if>
            <if test="middleware != null">#{middleware},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="function != null">#{function},</if>
            <if test="manger != null">#{manger},</if>
            <if test="vendor != null">#{vendor},</if>
            <if test="degreeImportance != null">#{degreeImportance},</if>
            <if test="assetType != null">#{assetType},</if>
            <if test="assetTypeDesc != null">#{assetTypeDesc},</if>
            <if test="assetClass != null and assetClass != ''">#{assetClass},</if>
            <if test="assetClassDesc != null">#{assetClassDesc},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="orgnId != null">#{orgnId},</if>
        </trim>
    </insert>

    <update id="updateTblDatabase" parameterType="TblDatabase">
        update tbl_database
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="isVirtual != null">is_virtual = #{isVirtual},</if>
            <if test="databaseVersion != null">database_version = #{databaseVersion},</if>
            <if test="application != null">application = #{application},</if>
            <if test="systemVersion != null">system_version = #{systemVersion},</if>
            <if test="middleware != null">middleware = #{middleware},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="function != null">function = #{function},</if>
            <if test="manger != null">manger = #{manger},</if>
            <if test="vendor != null">vendor = #{vendor},</if>
            <if test="degreeImportance != null">degree_importance = #{degreeImportance},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="assetTypeDesc != null">asset_type_desc = #{assetTypeDesc},</if>
            <if test="assetClass != null and assetClass != ''">asset_class = #{assetClass},</if>
            <if test="assetClassDesc != null">asset_class_desc = #{assetClassDesc},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="orgnId != null">orgn_id = #{orgnId},</if>
        </trim>
        where asset_id = #{assetId}
    </update>

    <delete id="deleteTblDatabaseByAssetId" parameterType="Long">
        delete from tbl_database where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblDatabaseByAssetIds" parameterType="String">
        delete from tbl_database where asset_id in 
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
</mapper>