<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblInstitutionMapper">

    <resultMap type="TblInstitution" id="TblInstitutionResult">
        <result property="id"    column="id"    />
        <result property="typeId"    column="type_id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="createBy"    column="create_by"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblInstitutionVo">
        select id, type_id, name, description, create_by,dept_id as dpId,(select dept_name from sys_dept where dept_id=dpId) as deptName, create_time, update_by, update_time from tbl_institution
    </sql>

    <select id="selectTblInstitutionList" parameterType="TblInstitution" resultMap="TblInstitutionResult">
        <include refid="selectTblInstitutionVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="createBy != null  and createBy != ''"> and create_by like concat('%', #{createBy}, '%')</if>
            <if test="deptId != null and deptId != 0">
                and (dept_id = #{deptId} or dept_id in ( select t.dept_id from sys_dept t where
                find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="typeId != null">
                and (type_id = #{typeId} or type_id in ( select t.id from tbl_institution_type t where
                find_in_set(#{typeId}, ancestors) and t.type=1 ))
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectTblInstitutionByIds" parameterType="Long" resultMap="TblInstitutionResult">
        <include refid="selectTblInstitutionVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblInstitution" parameterType="TblInstitution" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_institution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeId != null">type_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeId != null">#{typeId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblInstitution" parameterType="TblInstitution">
        update tbl_institution
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeId != null">type_id = #{typeId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblInstitutionById" parameterType="Long">
        delete from tbl_institution where id = #{id}
    </delete>

    <delete id="deleteTblInstitutionByIds" parameterType="String">
        delete from tbl_institution where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="count" resultType="java.lang.Integer">
        select count(1)
        from tbl_institution
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="createBy != null  and createBy != ''"> and create_by like concat('%', #{createBy}, '%')</if>
            <if test="deptId != null and deptId != 0">
                and (dept_id = #{deptId} or dept_id in ( select t.dept_id from sys_dept t where
                find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="typeId != null">
                and type_id = #{typeId}
            </if>
        </where>
    </select>

    <resultMap id="institutionResult" type="com.ruoyi.monitor2.domain.TblInstitution">
        <result property="id"    column="id"    />
        <result property="typeId"    column="type_id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="createBy"    column="create_by"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <collection property="lawRelList" javaType="ArrayList" column="{id=id}" select="selectSonList" />
    </resultMap>

    <select id="selectTblInstitutionById" parameterType="Long" resultMap="institutionResult">
        select t.id, t.type_id, t.name,t.description,t.create_by, t.create_time, t.update_by, t.update_time,t.dept_id,f.file_url as fileUrl,f.id as fileId from tbl_institution t left join tbl_institution_file f
        on f.institution_id=t.id
        where t.id = #{id}
    </select>

    <resultMap id="lawRelResult" type="com.ruoyi.monitor2.domain.TblSystemLawRel">
        <result property="institutionId" column="institution_id" />
        <result property="lawId" column="law_id" />
        <result property="lawDesc" column="law_desc" />
        <result property="name" column="name" />
    </resultMap>

    <select id="selectSonList" parameterType="map" resultMap="lawRelResult">
        SELECT law_id, law_desc, title AS name FROM tbl_system_law_rel slr
            LEFT JOIN tbl_knowledge kl ON slr.law_id = kl.kid
        <where>
            <if test="id != null "> and institution_id = #{id}</if>
            <if test="lawId != null "> and law_id = #{lawId}</if>
        </where>
    </select>

    <select id="totalInstitutionNumber" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            COUNT(t1.id) institutionTotal,
            SUM(IF(t2.necessary = 0,1,0)) basisInstitutionTotal,
            SUM(IF(t2.necessary = 1,1,0)) necessaryInstitutionTotal
        FROM
            tbl_institution t1
                LEFT JOIN tbl_institution_type t2 ON t1.type_id = t2.id
    </select>

    <select id="totalInstitutionTypeList" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t1.id,
            t1.`name` institutionName,
            SUM(IF(t1.id = t2.type_id AND t1.necessary = 0,1,0)) basisInstitutionNumber,
            SUM(IF(t1.id = t2.type_id AND t1.necessary = 1,1,0)) necessaryInstitutionNumber
        FROM
            tbl_institution_type t1
                LEFT JOIN tbl_institution t2 ON t2.type_id = t1.id
        GROUP BY
            t1.`name`
    </select>

    <select id="selectInstitutionTypeList" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t1.id,
            t1.`name` institutionName
        FROM
            tbl_institution_type t1
                LEFT JOIN tbl_institution t2 ON t2.type_id = t1.id
                JOIN(
                SELECT
                    t1.*
                FROM
                    tbl_institution_type t1
                WHERE
                    t1.type = 0 AND t1.parent_id = 0
            ) tt ON t1.parent_id = tt.id
    </select>

    <select id="selectInstitutionTypeNumberList" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t1.id,
            t1.`name` institutionName,
            t1.parent_id,
            SUM(IF(t1.id = t2.type_id AND t1.necessary = 0,1,0)) basisInstitutionNumber,
            SUM(IF(t1.id = t2.type_id AND t1.necessary = 1,1,0)) necessaryInstitutionNumber
        FROM
            tbl_institution_type t1
                LEFT JOIN tbl_institution t2 ON t2.type_id = t1.id
        WHERE
            t1.type = 1 AND t2.type_id = t1.id
        GROUP BY
            t2.id
    </select>

    <select id="selectInstitutionByParentId" resultType="com.ruoyi.monitor2.domain.TblInstitutionType">
        SELECT
            id,
            name,
            type,
            parent_id parentId,
            ancestors,
            necessary
        FROM
            tbl_institution_type
        WHERE
            id =#{parentId}
    </select>

    <select id="selectInstitutionByParentIds" resultType="com.ruoyi.monitor2.domain.TblInstitutionType">
        SELECT
        id,
        name,
        type,
        parent_id parentId,
        ancestors,
        necessary
        FROM
        tbl_institution_type
        WHERE id in
        <foreach collection="parentIdList" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </select>

    <select id="selectInstitutionNameList" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t1.`name` institutionName,
            IF(t2.necessary = 0,'基础','必要') necessary
        FROM
            tbl_institution t1
                LEFT JOIN tbl_institution_type t2 ON t1.type_id = t2.id
    </select>
</mapper>
