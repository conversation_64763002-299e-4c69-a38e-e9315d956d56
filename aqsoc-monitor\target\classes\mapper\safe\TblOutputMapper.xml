<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblOutputMapper">
    
    <resultMap type="TblOutput" id="TblOutputResult">
        <result property="id"    column="id"    />
        <result property="outputName"    column="output_name"    />
        <result property="outputType"    column="output_type"    />
        <result property="assetId"    column="asset_id"    />
        <result property="assetName"    column="asset_name"    />
        <result property="outputAssetName"    column="output_asset_name"    />
        <result property="outputAssetId"    column="output_asset_id"    />
        <result property="outputFilePath"    column="output_file_path"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <sql id="selectTblOutputVo">
        select id, output_name, output_type, asset_id, asset_name, output_asset_name, output_asset_id, output_file_path,remark ,create_time, create_by, update_time, update_by from tbl_output a
    </sql>

    <select id="selectTblOutputList" parameterType="TblOutput" resultMap="TblOutputResult">
        <include refid="selectTblOutputVo"/>
        <where>  
            <if test="outputName != null  and outputName != ''"> and output_name like concat('%', #{outputName}, '%')</if>
            <if test="assetId != null  and assetId != ''"> and asset_id =#{assetId}</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="outputAssetName != null  and outputAssetName != ''"> and output_asset_name like concat('%', #{outputAssetName}, '%')</if>
            <if test="outputAssetId != null  and outputAssetId != ''"> and output_asset_id =#{outputAssetId}</if>
        </where>
    </select>
    
    <select id="selectTblOutputById" parameterType="Long" resultMap="TblOutputResult">
        <include refid="selectTblOutputVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblOutput" parameterType="TblOutput" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_output
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="outputName != null and outputName != ''">output_name,</if>
            <if test="outputType != null">output_type,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="assetName != null">asset_name,</if>
            <if test="outputAssetName != null">output_asset_name,</if>
            <if test="outputAssetId != null">output_asset_id,</if>
            <if test="outputFilePath != null">output_file_path,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="outputName != null and outputName != ''">#{outputName},</if>
            <if test="outputType != null">#{outputType},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="assetName != null">#{assetName},</if>
            <if test="outputAssetName != null">#{outputAssetName},</if>
            <if test="outputAssetId != null">#{outputAssetId},</if>
            <if test="outputFilePath != null">#{outputFilePath},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTblOutput" parameterType="TblOutput">
        update tbl_output
        <trim prefix="SET" suffixOverrides=",">
            <if test="outputName != null and outputName != ''">output_name = #{outputName},</if>
            <if test="outputType != null">output_type = #{outputType},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="assetName != null">asset_name = #{assetName},</if>
            <if test="outputAssetName != null">output_asset_name = #{outputAssetName},</if>
            <if test="outputAssetId != null">output_asset_id = #{outputAssetId},</if>
            <if test="outputFilePath != null">output_file_path = #{outputFilePath},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblOutputById" parameterType="Long">
        delete from tbl_output where id = #{id}
    </delete>

    <delete id="deleteTblOutputByIds" parameterType="String">
        delete from tbl_output where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>