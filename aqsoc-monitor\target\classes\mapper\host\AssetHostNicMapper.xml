<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.host.mapper.AssetHostNicMapper">

    <select id="queryPage" resultType="com.ruoyi.monitor2.host.model.AssetHostNicListVO">
        select a.* from asset_host_nic a 
        <where>
                <if test="query.id!=null">
                    and a.id = #{query.id}
                </if>
                <if test="query.hostId!=null">
                    and a.host_id = #{query.hostId}
                </if>
                <if test="query.ipv4!=null and query.ipv4!=''">
                    and a.ipv4 like concat('%', #{query.ipv4}, '%')
                </if>
                <if test="query.ipv6!=null and query.ipv6!=''">
                    and a.ipv6 like concat('%', #{query.ipv6}, '%')
                </if>
                <if test="query.mac!=null and query.mac!=''">
                    and a.mac like concat('%', #{query.mac}, '%')
                </if>
                <if test="query.name!=null and query.name!=''">
                    and a.name like concat('%', #{query.name}, '%')
                </if>
                <if test="query.type!=null and query.type!=''">
                    and a.type like concat('%', #{query.type}, '%')
                </if>
        </where>
    </select>
    <select id="queryById" resultType="com.ruoyi.monitor2.host.model.AssetHostNicListVO">
        select a.* from asset_host_nic a 
        where a.id=#{id}
    </select>
</mapper>
