<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.datainterface.mapper.DataSrclogMapper">

    <resultMap type="DataSrclog" id="DataSrclogResult">
        <result property="id"    column="id"    />
        <result property="bussinessid"    column="bussinessId"    />
        <result property="bussinessTable"    column="bussiness_table"    />
        <result property="originData"    column="origin_data"    />
        <result property="dataSource"    column="data_source"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectDataSrclogVo">
        select id, bussinessId, bussiness_table, origin_data, data_source, create_time from data_srclog
    </sql>

    <select id="selectDataSrclogList" parameterType="DataSrclog" resultMap="DataSrclogResult">
        <include refid="selectDataSrclogVo"/>
        <where>
            <if test="bussinessid != null "> and bussinessId = #{bussinessid}</if>
            <if test="bussinessTable != null  and bussinessTable != ''"> and bussiness_table = #{bussinessTable}</if>
            <if test="originData != null  and originData != ''"> and origin_data = #{originData}</if>
            <if test="dataSource != null  and dataSource != ''"> and data_source = #{dataSource}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectDataSrclogById" parameterType="Long" resultMap="DataSrclogResult">
        <include refid="selectDataSrclogVo"/>
        where id = #{id}
    </select>

    <select id="selectDataSrclogByIds" parameterType="Long" resultMap="DataSrclogResult">
        <include refid="selectDataSrclogVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertDataSrclog" parameterType="DataSrclog" useGeneratedKeys="true" keyProperty="id">
        insert into data_srclog
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bussinessid != null">bussinessId,</if>
            <if test="bussinessTable != null">bussiness_table,</if>
            <if test="originData != null">origin_data,</if>
            <if test="dataSource != null">data_source,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bussinessid != null">#{bussinessid},</if>
            <if test="bussinessTable != null">#{bussinessTable},</if>
            <if test="originData != null">#{originData},</if>
            <if test="dataSource != null">#{dataSource},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateDataSrclog" parameterType="DataSrclog">
        update data_srclog
        <trim prefix="SET" suffixOverrides=",">
            <if test="bussinessid != null">bussinessId = #{bussinessid},</if>
            <if test="bussinessTable != null">bussiness_table = #{bussinessTable},</if>
            <if test="originData != null">origin_data = #{originData},</if>
            <if test="dataSource != null">data_source = #{dataSource},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataSrclogById" parameterType="Long">
        delete from data_srclog where id = #{id}
    </delete>

    <delete id="deleteDataSrclogByIds" parameterType="String">
        delete from data_srclog where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
