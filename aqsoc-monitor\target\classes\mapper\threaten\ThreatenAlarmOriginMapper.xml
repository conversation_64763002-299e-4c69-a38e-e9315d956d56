<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenAlarmOriginMapper">

    <delete id="deleteThreatenAlarmOriginByAlarmIds">
        delete from threaten_alarm_origin where alarm_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenAlarmOriginListVO">
        select a.* from threaten_alarm_origin a
        <where>
                <if test="query.id!=null">
                    and a.id = #{query.id}
                </if>
                <if test="query.alarmId!=null">
                    and a.alarm_id = #{query.alarmId}
                </if>
                <if test="query.threatenType!=null and query.threatenType!=''">
                    and a.threaten_type like concat('%', #{query.threatenType}, '%')
                </if>
                <if test="query.originId!=null">
                    and a.origin_id = #{query.originId}
                </if>
                <if test="query.createTime!=null">
                    and a.create_time = #{query.createTime}
                </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenAlarmOriginListVO">
        select a.* from threaten_alarm_origin a
        where a.id=#{id}
    </select>
</mapper>
