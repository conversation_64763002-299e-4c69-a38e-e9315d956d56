package com.ruoyi.threaten.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.monitor2.vo.VulnGradeStatisticsVO;
import com.ruoyi.rabbitmq.service.handler.DeviceMessageHandler;
import com.ruoyi.safe.domain.FfsafeIpfilterBlocking;
import com.ruoyi.safe.domain.ThreatTrendForm;
import com.ruoyi.safe.domain.dto.OverviewParams;
import com.ruoyi.threaten.domain.FfsafeHoneypotAttackdetail;
import com.ruoyi.threaten.domain.TblThreatenAlarm;
import com.ruoyi.threaten.domain.TblThreatenTypeSeg;
import com.ruoyi.threaten.domain.vo.AttackDirectionRefreshVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 威胁情报Service接口
 *
 * <AUTHOR>
 * @date 2023-11-16
 */
public interface ITblThreatenAlarmService extends DeviceMessageHandler<Object> {
    /**
     * 查询威胁情报
     *
     * @param id 威胁情报主键
     * @return 威胁情报
     */
    public TblThreatenAlarm selectTblThreatenAlarmById(Long id);

    /**
     * 批量查询威胁情报
     *
     * @param ids 威胁情报主键集合
     * @return 威胁情报集合
     */
    public List<TblThreatenAlarm> selectTblThreatenAlarmByIds(Long[] ids);

    /**
     * 查询威胁情报列表
     *
     * @param tblThreatenAlarm 威胁情报
     * @return 威胁情报集合
     */
    public List<TblThreatenAlarm> selectTblThreatenAlarmList(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 新增威胁情报
     *
     * @param tblThreatenAlarm 威胁情报
     * @return 结果
     */
    public int insertTblThreatenAlarm(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 导入威胁情报列表
     *
     * @param tblThreatenAlarmList 威胁情报列表
     * @return 结果
     */
    public int insertTblThreatenAlarmList(List<TblThreatenAlarm> tblThreatenAlarmList);

    /**
     * 修改威胁情报
     *
     * @param tblThreatenAlarm 威胁情报
     * @return 结果
     */
    public int updateTblThreatenAlarm(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 删除威胁情报信息
     *
     * @param id 威胁情报主键
     * @return 结果
     */
    public int deleteTblThreatenAlarmById(Long id);

    /**
     * 批量删除威胁情报
     *
     * @param ids 需要删除的威胁情报主键集合
     * @return 结果
     */
    public int deleteTblThreatenAlarmByIds(Long[] ids);

    /**
     * 威胁类型 top10
     * @return 结果
     */
    List<HashMap> getThreatenTypeTop10(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 威胁类型 top10
     * @return 结果
     */
    List<HashMap> getThreatenNameTop10(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询 威胁类型 所有
     * @return 结果
     */
    List<HashMap> getThreatenType(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询 威胁等级 数据分析
     * @return 结果
     */
    List<VulnGradeStatisticsVO> getThreatenGrade(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询 攻击源列表Top10
     */
    List<TblThreatenAlarm> getAttackIpListTop10(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询 受害源列表Top10
     */
    List<TblThreatenAlarm> getDestIpListTop10(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询 威胁列表
     */
    List<TblThreatenAlarm> getAlertInfoList(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 威胁 等级 统计分析
     */
    Map<String, List> getThreatenAnalyze(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询 威胁类型 所有,带百分比
     * @return 结果
     */
    List<HashMap> getThreatenTypeWithPercentage(TblThreatenAlarm tblThreatenAlarm);

    int getAttackNum(TblThreatenAlarm tblThreatenAlarm);
    int getHighRiskHostNum(TblThreatenAlarm tblThreatenAlarm);
    int getRiskHostNum(TblThreatenAlarm tblThreatenAlarm);
    int getThreatenAlarmNum(TblThreatenAlarm tblThreatenAlarm);
    Map<Integer, Object> getThreatenLevelNum(TblThreatenAlarm tblThreatenAlarm);

    List<TblThreatenAlarm> selectAttackIpList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectVictimIpList(TblThreatenAlarm tblThreatenAlarm);
    TblThreatenTypeSeg getAttackSegSummary(TblThreatenAlarm tblThreatenAlarm);
    TblThreatenTypeSeg getAssetAttackSegSummary(TblThreatenAlarm tblThreatenAlarm);
    TblThreatenTypeSeg selectIpAttackSegNum(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectAttackIpOverviewList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectVictimIpOverviewList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectAssetIpOverviewList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectAttackIpReportList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectVictimIpReportList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectOutConnectReportList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectAttackIpVictimStatReport(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectVictimIpAttackStatReport(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectTimeAxisList(TblThreatenAlarm tblThreatenAlarm);


    /**
     * 攻击者视角 列表 字列表查询  Map<String, Integer>
     * <AUTHOR>
     * @Description //
     * @Date 2024/9/12 14:27
     * @param tblThreatenAlarm
     * @return java.util.Map<String, Integer>
     **/
    TblThreatenTypeSeg getEventSegTypeList(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 攻击者/受害者视角 告警名称分组查询
     * <AUTHOR>
     * @Description //
     * @Date 2024/9/18 18:22
     * @param tblThreatenAlarm
     * @return java.util.List<com.ruoyi.threaten.domain.TblThreatenAlarm>
     **/
    List<TblThreatenAlarm> getGroupByName(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 攻击者/受害者视角 告警等级分组查询
     * <AUTHOR>
     * @Description //
     * @Date 2024/9/18 18:22
     * @param tblThreatenAlarm
     * @return java.util.List<com.ruoyi.threaten.domain.TblThreatenAlarm>
     **/
    List<TblThreatenAlarm> getGroupByLevel(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 攻击者 攻击者分组查询
     * <AUTHOR>
     * @Description //
     * @Date 2024/9/18 18:22
     * @param tblThreatenAlarm
     * @return java.util.List<com.ruoyi.threaten.domain.TblThreatenAlarm>
     **/
    List<TblThreatenAlarm> getGroupByAttack(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 攻击者 攻击者分组查询
     * <AUTHOR>
     * @Description //
     * @Date 2024/9/18 18:22
     * @param tblThreatenAlarm
     * @return java.util.List<com.ruoyi.threaten.domain.TblThreatenAlarm>
     **/
    List<TblThreatenAlarm> getGroupByVictim(TblThreatenAlarm tblThreatenAlarm);

    /** 攻击者 攻击阶段 查询**/
    List<TblThreatenAlarm> exportAttackStage(TblThreatenAlarm tblThreatenAlarm);

    /** 受害者 攻击阶段 查询**/
    List<TblThreatenAlarm> exportSufferStage(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectThreatenByIpList(List<String> ipList);

    List<TblThreatenAlarm> getThreatenAlarmNumByAssetId(Long assetId);

    List<FfsafeHoneypotAttackdetail> getFfsafeHoneypotAttackdetailList(TblThreatenAlarm tblThreatenAlarm);

    JSONObject getThreatTrend(ThreatTrendForm threatTrendForm);

    List<TblThreatenAlarm> getAttackIpListTop5(TblThreatenAlarm tblThreatenAlarm);

    List<TblThreatenAlarm> getDestIpListTop5(TblThreatenAlarm tblThreatenAlarm);

    void batchUpdateHandleState(List<TblThreatenAlarm> handleList);

    List<TblThreatenAlarm> selectNotSyncList();

    /**
     * 批量更新威胁告警攻击方向
     *
     * @param alarms 威胁告警列表
     */
    void batchUpdateAttackDirection(List<TblThreatenAlarm> alarms);

    /**
     * 刷新威胁告警攻击方向
     *
     * @param alarmIds 威胁告警ID列表
     * @return 刷新结果列表
     */
    List<AttackDirectionRefreshVO> refreshAttackDirection(List<Long> alarmIds);

    AjaxResult addBlockIp(JSONObject params);

    int countThreatenAlarmNum(OverviewParams params);


    List<JSONObject> groupAlarmLevelStatistics(TblThreatenAlarm tblThreatenAlarm);

int selectTblThreatenAlarmListDeputy(TblThreatenAlarm tblThreatenAlarm);

    int countAttackIpOverview(TblThreatenAlarm tblThreatenAlarm);

    List<JSONObject> selectTop10Alarm(TblThreatenAlarm tblThreatenAlarm);

    List<JSONObject> selectAlarmTypeNum(TblThreatenAlarm tblThreatenAlarm);


    List<JSONObject> selectAlarmLevelStatistics(TblThreatenAlarm tblThreatenAlarm);

    List<JSONObject> selectRecentlyBlockedList(TblThreatenAlarm tblThreatenAlarm);

    List<JSONObject> selectAttackIpOverviewTop10(TblThreatenAlarm tblThreatenAlarm);

    List<JSONObject> getRankingOfAlarmSystems(TblThreatenAlarm tblThreatenAlarm);

    JSONObject getHostOverview(TblThreatenAlarm tblThreatenAlarm);

    List<TblThreatenAlarm> selectListByGreaterThanId(Long threatenAlarmId);
}
