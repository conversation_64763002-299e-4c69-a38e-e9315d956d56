<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblPatchedMapper">
    
    <resultMap type="TblPatched" id="TblPatchedResult">
        <result property="id"    column="id"    />
        <result property="leakname"    column="leakname"    />
        <result property="cnnvd"    column="cnnvd"    />
        <result property="assetId"    column="asset_id"    />
        <result property="patchedTime"    column="patched_time"    />
        <result property="patcher"    column="patcher"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="filenames"    column="filenames"    />
    </resultMap>

    <sql id="selectTblPatchedVo">
        select id, leakname, cnnvd, asset_id, create_time, create_by, update_time, update_by,patched_time,patcher,filenames from tbl_patched
    </sql>

    <select id="selectTblPatchedList" parameterType="TblPatched" resultMap="TblPatchedResult">
        <include refid="selectTblPatchedVo"/>
        <where>  
            <if test="leakname != null  and leakname != ''"> and leakname like concat('%', #{leakname}, '%')</if>
            <if test="cnnvd != null  and cnnvd != ''"> and cnnvd = #{cnnvd}</if>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
        </where>
    </select>
    
    <select id="selectTblPatchedById" parameterType="Long" resultMap="TblPatchedResult">
        <include refid="selectTblPatchedVo"/>
        where id = #{id}
    </select>
    <select id="selectAssetIdsByCnnvd" parameterType="String" resultType="Long">
        select asset_id from tbl_patched
        where cnnvd = #{cnnvd}
    </select>
    <select id="selectTblPatchedByCnnvds" parameterType="String" resultMap="TblPatchedResult">
        <include refid="selectTblPatchedVo"/>
        where cnnvd in
        <foreach collection="list" item="cnnvd" close=")" open="(" separator=",">
            #{cnnvd}
        </foreach>
    </select>
    <select id="selectAssetIdsByCnnvdAndAssetIds" resultMap="TblPatchedResult">
        <include refid="selectTblPatchedVo" />
        <where>
            <if test="cnnvd != null and cnnvd!=''">and cnnvd = #{cnnvd}</if>
            and asset_id in
            <foreach item="assetId" collection="assetIds" open="(" separator="," close=")">
                #{assetId}
            </foreach>
        </where>
    </select>


    <insert id="insertTblPatched" parameterType="TblPatched">
        insert into tbl_patched
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="leakname != null and leakname != ''">leakname,</if>
            <if test="cnnvd != null">cnnvd,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="patcher != null">patcher,</if>
            <if test="patchedTime != null">patched_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="filenames != null">filenames,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="leakname != null and leakname != ''">#{leakname},</if>
            <if test="cnnvd != null">#{cnnvd},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="patcher != null">#{patcher},</if>
            <if test="patchedTime != null">#{patchedTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="filenames != null">#{filenames},</if>
         </trim>
    </insert>

    <update id="updateTblPatched" parameterType="TblPatched">
        update tbl_patched
        <trim prefix="SET" suffixOverrides=",">
            <if test="leakname != null and leakname != ''">leakname = #{leakname},</if>
            <if test="cnnvd != null">cnnvd = #{cnnvd},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="patcher != null">patcher=#{patcher},</if>
            <if test="patchedTime != null">patched_time=#{patchedTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="filenames != null">filenames = #{filenames},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblPatchedById" parameterType="Long">
        delete from tbl_patched where id = #{id}
    </delete>

    <delete id="deleteTblPatchedByAssetId" parameterType="Long">
        delete from tbl_patched where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblPatchedByIds" parameterType="String">
        delete from tbl_patched where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTblpatchedByPatched" parameterType="TblPatched">
        delete from tbl_patched
        <where>
            <if test="cnnvd != null  and cnnvd != ''"> and cnnvd = #{cnnvd}</if>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
        </where>

    </delete>
    <delete id="deleteTblPatchedByAssetIds" parameterType="String">
        delete from tbl_patched where asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
</mapper>