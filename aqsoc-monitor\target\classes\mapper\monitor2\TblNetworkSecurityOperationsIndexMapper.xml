<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblNetworkSecurityOperationsIndexMapper">

    <resultMap type="TblNetworkSecurityOperationsIndex" id="TblNetworkSecurityOperationsIndexResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="level"    column="level"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors" column="ancestors" />
    </resultMap>

    <sql id="selectTblNetworkSecurityOperationsIndexVo">
        select id, name, level, parent_id,ancestors,sort from tbl_network_security_operations_index
    </sql>

    <select id="selectTblNetworkSecurityOperationsIndexList" parameterType="TblNetworkSecurityOperationsIndex" resultMap="TblNetworkSecurityOperationsIndexResult">
        <include refid="selectTblNetworkSecurityOperationsIndexVo"/>
        <where>
            <if test="id != null  and id != ''"> and id = #{id}</if>
            <if test="name != null  and name != ''"> and `name` like concat('%', #{name}, '%')</if>
            <if test="level != null "> and `level` = #{level}</if>
            <if test="parentId != null  and parentId != ''"> and parent_id = #{parentId}</if>
        </where>
    </select>

    <select id="selectTblNetworkSecurityOperationsIndexById" parameterType="String" resultMap="TblNetworkSecurityOperationsIndexResult">
        <include refid="selectTblNetworkSecurityOperationsIndexVo"/>
        where id = #{id}
    </select>

    <select id="selectTblNetworkSecurityOperationsIndexByIds" parameterType="String" resultMap="TblNetworkSecurityOperationsIndexResult">
        <include refid="selectTblNetworkSecurityOperationsIndexVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectChildIndexList" resultType="com.ruoyi.monitor2.domain.TblNetworkSecurityOperationsIndex" resultMap="TblNetworkSecurityOperationsIndexResult">
        <include refid="selectTblNetworkSecurityOperationsIndexVo"/>
        where
        <foreach collection="ids" item="id" open="(" close=")" separator=" OR ">
            FIND_IN_SET(#{id},ancestors)
        </foreach>
    </select>

    <insert id="insertTblNetworkSecurityOperationsIndex" parameterType="TblNetworkSecurityOperationsIndex">
        insert into tbl_network_security_operations_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">`name`,</if>
            <if test="level != null">`level`,</if>
            <if test="parentId != null and parentId != ''">parent_id,</if>
            <if test="ancestors != null and ancestors != ''">ancestors,</if>
            <if test="sort != null">`sort`,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="level != null">#{level},</if>
            <if test="parentId != null and parentId != ''">#{parentId},</if>
            <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
            <if test="sort != null">#{sort},</if>
         </trim>
    </insert>

    <update id="updateTblNetworkSecurityOperationsIndex" parameterType="TblNetworkSecurityOperationsIndex">
        update tbl_network_security_operations_index
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="level != null">`level` = #{level},</if>
            <if test="parentId != null">
                <if test="parentId != ''">
                    parent_id = #{parentId},
                </if>
                <if test="parentId == ''">
                    parent_id = null,
                </if>
            </if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="sort != null">sort = #{sort},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblNetworkSecurityOperationsIndexById" parameterType="String">
        delete from tbl_network_security_operations_index where id = #{id}
    </delete>

    <delete id="deleteTblNetworkSecurityOperationsIndexByIds" parameterType="String">
        delete from tbl_network_security_operations_index where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
