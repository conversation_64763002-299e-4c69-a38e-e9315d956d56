<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblApplicationNetwareMapper">

    <resultMap type="TblApplicationNetware" id="TblApplicationNetwareResult">
        <result property="assetId"    column="asset_id"    />
        <result property="netwareId"    column="netware_id"    />
        <result property="ipids"    column="ipids"    />
        <result property="id"    column="id"    />
    </resultMap>

    <sql id="selectTblApplicationNetwareVo">
        select asset_id, netware_id, ipids,id from tbl_application_netware
    </sql>

    <select id="selectTblApplicationNetwareList" parameterType="TblApplicationNetware" resultMap="TblApplicationNetwareResult">
        <include refid="selectTblApplicationNetwareVo"/>
        <where>
            <if test="ipids != null  and ipids != ''"> and ipids = #{ipids}</if>
            <if test="assetId != null  and assetId != ''"> and asset_id = #{assetId}</if>
            <if test="netwareId != null  and netwareId != ''"> and netware_id = #{netwareId}</if>
            <if test="id != null  and id != ''"> and id = #{id}</if>
        </where>
    </select>

    <select id="selectTblApplicationNetwareById" parameterType="Long" resultMap="TblApplicationNetwareResult">
        <include refid="selectTblApplicationNetwareVo"/>
        where id = #{id}
    </select>

    <select id="selectTblApplicationNetwareByIds" parameterType="Long" resultMap="TblApplicationNetwareResult">
        <include refid="selectTblApplicationNetwareVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblApplicationNetware" parameterType="TblApplicationNetware">
        insert into tbl_application_netware
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="netwareId != null">netware_id,</if>
            <if test="ipids != null">ipids,</if>
            <if test="id != null">id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="netwareId != null">#{netwareId},</if>
            <if test="ipids != null">#{ipids},</if>
            <if test="id != null">#{id},</if>
         </trim>
    </insert>

    <update id="updateTblApplicationNetware" parameterType="TblApplicationNetware">
        update tbl_application_netware
        <trim prefix="SET" suffixOverrides=",">
            <if test="netwareId != null">netware_id = #{netwareId},</if>
            <if test="ipids != null">ipids = #{ipids},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblApplicationNetwareByAssetId" parameterType="Long">
        delete from tbl_application_netware where id = #{assetId}
    </delete>

    <delete id="deleteTblApplicationNetwareByAssetIds" parameterType="String">
        delete from tbl_application_netware where id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
    <delete id="deleteTblApplicationNetware" parameterType="TblApplicationNetware">
        delete from tbl_application_netware
        <where>
            <if test="ipids != null  and ipids != ''"> and ipids = #{ipids}</if>
            <if test="assetId != null  and assetId != ''"> and asset_id = #{assetId}</if>
            <if test="netwareId != null  and netwareId != ''"> and netware_id = #{netwareId}</if>
            <if test="id != null  and id != ''"> and id = #{id}</if>
        </where>
    </delete>

    <select id="selectCountNum" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(0) from tbl_application_netware
        <where>
            <if test="assetId != null  and assetId != ''"> asset_id = #{assetId}</if>
        </where>
    </select>
</mapper>
