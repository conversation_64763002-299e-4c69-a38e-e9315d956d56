<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblTempleteUserMapper">

    <resultMap type="TblTempleteUser" id="TblTempleteUserResult">
        <result property="templeteId"    column="templete_id"    />
        <result property="userId"    column="user_id"    />
    </resultMap>

    <sql id="selectTblTempleteUserVo">
        select templete_id, user_id from tbl_templete_user
    </sql>

    <select id="selectTblTempleteUserList" parameterType="TblTempleteUser" resultMap="TblTempleteUserResult">
        <include refid="selectTblTempleteUserVo"/>
        <where>
        </where>
    </select>

    <select id="selectTblTempleteUserByTempleteId" parameterType="Long" resultMap="TblTempleteUserResult">
        <include refid="selectTblTempleteUserVo"/>
        where templete_id = #{templeteId}
    </select>

    <select id="selectTblTempleteUserByTempleteIds" parameterType="Long" resultMap="TblTempleteUserResult">
        <include refid="selectTblTempleteUserVo"/>
        where templete_id in
        <foreach item="templeteId" collection="array" open="(" separator="," close=")">
            #{templeteId}
        </foreach>
    </select>

    <insert id="insertTblTempleteUser" parameterType="TblTempleteUser">
        insert into tbl_templete_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templeteId != null">templete_id,</if>
            <if test="userId != null">user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templeteId != null">#{templeteId},</if>
            <if test="userId != null">#{userId},</if>
         </trim>
    </insert>


    <insert id="insertBatchTblTempleteUser" parameterType="java.util.List">
        INSERT INTO tbl_templete_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            templete_id,
            user_id
        </trim>
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.templeteId},
            #{item.userId}
            )
        </foreach>
    </insert>


    <update id="updateTblTempleteUser" parameterType="TblTempleteUser">
        update tbl_templete_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where templete_id = #{templeteId}
    </update>

    <delete id="deleteTblTempleteUserByTempleteId" parameterType="Long">
        delete from tbl_templete_user where templete_id = #{templeteId}
    </delete>

    <delete id="deleteTblTempleteUserByTempleteIds" parameterType="String">
        delete from tbl_templete_user where templete_id in
        <foreach item="templeteId" collection="array" open="(" separator="," close=")">
            #{templeteId}
        </foreach>
    </delete>
</mapper>
