<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rule.mapper.TblRuleOrgManagementMapper">
    
    <resultMap type="TblRuleOrgManagement" id="TblRuleOrgManagementResult">
        <result property="id"    column="id"    />
        <result property="orgName"    column="org_name"    />
        <result property="linkmanName"    column="linkman_name"    />
        <result property="phone"    column="phone"    />
        <result property="workDesc"    column="work_desc"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="cilentId"    column="cilent_id"    />
        <result property="sortNum"    column="sort_num"    />
    </resultMap>

    <sql id="selectTblRuleOrgManagementVo">
        select id, org_name, linkman_name, phone, work_desc, create_time, create_by, update_time, update_by, remark, user_id, dept_id, cilent_id,sort_num from tbl_rule_org_management
    </sql>

    <select id="selectTblRuleOrgManagementList" parameterType="TblRuleOrgManagement" resultMap="TblRuleOrgManagementResult">
        <include refid="selectTblRuleOrgManagementVo"/>
        <where>  
            <if test="orgName != null  and orgName != ''"> and org_name like concat('%', #{orgName}, '%')</if>
            <if test="linkmanName != null  and linkmanName != ''"> and linkman_name like concat('%', #{linkmanName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="workDesc != null  and workDesc != ''"> and work_desc = #{workDesc}</if>
        </where>
        order by sort_num asc,update_time desc
    </select>
    
    <select id="selectTblRuleOrgManagementById" parameterType="Long" resultMap="TblRuleOrgManagementResult">
        <include refid="selectTblRuleOrgManagementVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblRuleOrgManagement" parameterType="TblRuleOrgManagement">
        insert into tbl_rule_org_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orgName != null and orgName != ''">org_name,</if>
            <if test="linkmanName != null and linkmanName != ''">linkman_name,</if>
            <if test="phone != null">phone,</if>
            <if test="workDesc != null">work_desc,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="cilentId != null">cilent_id,</if>
            <if test="sortNum != null">sort_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orgName != null and orgName != ''">#{orgName},</if>
            <if test="linkmanName != null and linkmanName != ''">#{linkmanName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="workDesc != null">#{workDesc},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="cilentId != null">#{cilentId},</if>
            <if test="sortNum != null">#{sortNum},</if>
         </trim>
    </insert>

    <update id="updateTblRuleOrgManagement" parameterType="TblRuleOrgManagement">
        update tbl_rule_org_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgName != null and orgName != ''">org_name = #{orgName},</if>
            <if test="linkmanName != null and linkmanName != ''">linkman_name = #{linkmanName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="workDesc != null">work_desc = #{workDesc},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="cilentId != null">cilent_id = #{cilentId},</if>
            <if test="sortNum != null">sort_num = #{sortNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblRuleOrgManagementById" parameterType="Long">
        delete from tbl_rule_org_management where id = #{id}
    </delete>

    <delete id="deleteTblRuleOrgManagementByIds" parameterType="String">
        delete from tbl_rule_org_management where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>