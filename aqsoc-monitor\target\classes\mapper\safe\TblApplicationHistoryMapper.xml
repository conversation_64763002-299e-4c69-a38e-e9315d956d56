<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblApplicationHistoryMapper">

    <resultMap type="TblApplicationHistory" id="TblApplicationHistoryResult">
        <result property="id"    column="id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="operator"    column="content"    />
        <result property="content"    column="content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectTblApplicationHistoryVo">
        select id, asset_id, operator, content, create_by, create_time from tbl_application_history
    </sql>

    <select id="selectTblApplicationHistoryList" parameterType="TblApplicationHistory" resultMap="TblApplicationHistoryResult">
        <include refid="selectTblApplicationHistoryVo"/>
        <where>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="operator != null "> and operator = #{operator}</if>
        </where>
        order by id desc
    </select>

    <select id="selectTblApplicationHistoryById" parameterType="Long" resultMap="TblApplicationHistoryResult">
        <include refid="selectTblApplicationHistoryVo"/>
        where id = #{id}
    </select>

    <select id="selectTblApplicationHistoryByIds" parameterType="Long" resultMap="TblApplicationHistoryResult">
        <include refid="selectTblApplicationHistoryVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblApplicationHistory" parameterType="TblApplicationHistory">
        insert into tbl_application_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="operator != null">operator,</if>
            <if test="content != null">content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="operator != null">#{operator},</if>
            <if test="content != null">#{content},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateTblApplicationHistory" parameterType="TblApplicationHistory">
        update tbl_application_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="operator != null">operator = #{operator},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblApplicationHistoryById" parameterType="Long">
        delete from tbl_application_history where id = #{id}
    </delete>

    <delete id="deleteTblApplicationHistoryByIds" parameterType="String">
        delete from tbl_application_history where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
