<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblMessageLogMapper">

    <resultMap type="TblMessageLog" id="TblMessageLogResult">
        <result property="id"    column="id"    />
        <result property="sender"    column="sender"    />
        <result property="deptId"    column="dept_id"    />
        <result property="sendTime"    column="send_time"    />
        <result property="messageContent"    column="message_content"    />
        <result property="type"    column="type"    />
        <result property="recievePhone"    column="recieve_phone"    />
        <result property="msgId"    column="msg_id"    />
        <result property="status"    column="status"    />
        <result property="result"    column="result"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="senderName"    column="user_name"    />
        <result property="deptName"    column="dept_name"    />
    </resultMap>

    <sql id="selectTblMessageLogVo">
        select tml.id, tml.sender, tml.dept_id, tml.send_time, tml.message_content, tml.type, tml.recieve_phone, tml.msg_id, tml.status, tml.result, tml.update_by, tml.update_time,
               su.user_name,sd.dept_name
        from tbl_message_log tml
        left join sys_user su on tml.sender = su.user_id
        left join sys_dept sd on tml.dept_id = sd.dept_id
    </sql>

    <select id="selectTblMessageLogList" parameterType="TblMessageLog" resultMap="TblMessageLogResult">
        <include refid="selectTblMessageLogVo"/>
        <where>
            <if test="sender != null "> and tml.sender = #{sender}</if>
            <if test="deptId != null "> and tml.dept_id = #{deptId}</if>
            <if test="sendTime != null "> and tml.send_time = #{sendTime}</if>
            <if test="messageContent != null  and messageContent != ''"> and tml.message_content like concat('%', #{messageContent}, '%')</if>
            <if test="type != null  and type != ''"> and tml.type = #{type}</if>
            <if test="recievePhone != null  and recievePhone != ''"> and tml.recieve_phone like concat('%', #{recievePhone}, '%')</if>
            <if test="msgId != null  and msgId != ''"> and tml.msg_id = #{msgId}</if>
            <if test="status != null  and status != ''"> and tml.status = #{status}</if>
            <if test="result != null  and result != ''"> and tml.result = #{result}</if>
        </where>
    </select>

    <select id="selectTblMessageLogById" parameterType="Long" resultMap="TblMessageLogResult">
        <include refid="selectTblMessageLogVo"/>
        where tml.id = #{id}
    </select>

    <select id="selectTblMessageLogByIds" parameterType="Long" resultMap="TblMessageLogResult">
        <include refid="selectTblMessageLogVo"/>
        where tml.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblMessageLog" parameterType="TblMessageLog" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_message_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sender != null">sender,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="messageContent != null">message_content,</if>
            <if test="type != null">type,</if>
            <if test="recievePhone != null">recieve_phone,</if>
            <if test="msgId != null">msg_id,</if>
            <if test="status != null">`status`,</if>
            <if test="result != null">result,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sender != null">#{sender},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="messageContent != null">#{messageContent},</if>
            <if test="type != null">#{type},</if>
            <if test="recievePhone != null">#{recievePhone},</if>
            <if test="msgId != null">#{msgId},</if>
            <if test="status != null">#{status},</if>
            <if test="result != null">#{result},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTblMessageLog" parameterType="TblMessageLog">
        update tbl_message_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="sender != null">sender = #{sender},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="messageContent != null">message_content = #{messageContent},</if>
            <if test="type != null">type = #{type},</if>
            <if test="recievePhone != null">recieve_phone = #{recievePhone},</if>
            <if test="msgId != null">msg_id = #{msgId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="result != null">result = #{result},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblMessageLogById" parameterType="Long">
        delete from tbl_message_log where id = #{id}
    </delete>

    <delete id="deleteTblMessageLogByIds" parameterType="String">
        delete from tbl_message_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
