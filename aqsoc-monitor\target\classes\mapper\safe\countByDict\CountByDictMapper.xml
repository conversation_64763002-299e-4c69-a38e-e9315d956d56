<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.CountByDictMapper">

    <resultMap id="CountByDictVOMap" type="com.ruoyi.safe.vo.countDict.CountByDictVO">
        <result property="dictLabel" column="dictLabel"/>
        <result property="dictValue" column="dictValue"/>
        <result property="count" column="count"/>
    </resultMap>

    <select id="getAppCountByDict" resultMap="CountByDictVOMap">
        <choose>
            <when test="dictType == 'appcheck_state'">
                SELECT a.check_on as dictValue, case
                    when a.check_on='new' then '登记'
                    when a.check_on='wait' then '待审核'
                    when a.check_on='fail' then '未通过'
                    when a.check_on='pass' then '通过'
                    else '其他'
                    end as dictLabel, COUNT(distinct a.asset_id) AS count
                from tbl_business_application a
                         left join sys_user m on a.manager = m.user_id
                         left join sys_user b on a.user_id = b.user_id
                         left join sys_dept c on a.dept_id = c.dept_id
                         left join tbl_vendor d on a.vendor = d.id
                         left join tbl_asset_overview e on a.asset_id = e.asset_id
                GROUP BY a.check_on
            </when>
            <otherwise>
            SELECT ${groupByField} as dictValue,
                           o.dict_label    as dictLabel,
                           COUNT(distinct a.asset_id) AS count
                         from tbl_business_application a
                         left join sys_user m on a.manager = m.user_id
                         left join sys_user b on a.user_id = b.user_id
                         left join sys_dept c on a.dept_id = c.dept_id
                         left join tbl_vendor d on a.vendor = d.id
                         left join tbl_asset_overview e on a.asset_id = e.asset_id
                        INNER JOIN sys_dict_data o ON ${groupByField} = o.dict_value
                    WHERE o.status = '0'
                      AND o.dict_type = #{dictType}
                    GROUP BY ${groupByField}
            </otherwise>
        </choose>
    </select>

    <select id="getServerCountByDict" resultMap="CountByDictVOMap">
        <!-- asset_state 做特殊处理，在线与离线判断 判断g.last_scan_state 有值以g.last_scan_state为主 无值判断 e.state-->
        SELECT
        <choose>
            <when test="dictType == 'asset_state'">
                    COALESCE(g.last_scan_state, e.state) AS dictValue,
                    COALESCE(
                                CASE
                                    WHEN g.last_scan_state = 0 THEN '离线'
                                    WHEN g.last_scan_state = 1 THEN '在线'
                                    ELSE '未知'
                                END, '未知'
                        ) AS dictLabel,
            </when>
            <otherwise>
                <choose>
                    <when test="dictType == 'asset_type'">
                        h.type_name as dictLabel
                    </when>
                    <otherwise>
                        o.dict_label as dictLabel
                    </otherwise>
                </choose>,
                ${groupByField} as dictValue,
            </otherwise>
        </choose>
        COUNT(distinct a.asset_id) AS count
        from tbl_server a
        left join  tbl_asset_overview as e on a.asset_id = e.asset_id
        left join tbl_location as l on l.location_id = e.location_id
        left join tbl_network_ip_mac as g on g.asset_id = e.asset_id and g.main_ip='1'
        LEFT JOIN tbl_application_server t2 ON t2.server_id=a.asset_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id=t2.asset_id
        <!-- 条件判断，asset_type、asset_state 做特殊处理 -->
        <choose>
            <when test="dictType == 'asset_type'">
                INNER JOIN tbl_asset_class h ON a.asset_type = h.id
                WHERE h.pid = 4
                AND h.type_graded_protection = 0
            </when>
            <when test="dictType == 'asset_state'">
                WHERE (
                        (g.last_scan_state = '1' OR (g.last_scan_state IS NULL AND e.state = '1'))
                        OR
                        (g.last_scan_state = '0' OR (g.last_scan_state IS NULL AND e.state = '0'))
                    )
            </when>
            <otherwise>
                INNER JOIN sys_dict_data o ON ${groupByField} = o.dict_value
                WHERE o.status = '0'
                AND o.dict_type = #{dictType}
            </otherwise>
        </choose>
         <!-- 分组 ，asset_state做特殊处理-->
        <choose>
            <when test="dictType == 'asset_state'">
                GROUP BY dictValue
            </when>
            <otherwise>
                GROUP BY ${groupByField}
            </otherwise>
        </choose>
    </select>

    <select id="getNetworkDevicesCountByDict" resultMap="CountByDictVOMap">
    <!-- asset_state 做特殊处理，在线与离线判断 判断g.last_scan_state 有值以g.last_scan_state为主 无值判断 e.state-->
        SELECT
             <choose>
                <when test="dictType == 'asset_state'">
                        COALESCE(g.last_scan_state, e.state) AS dictValue,
                        COALESCE(
                                CASE
                                    WHEN g.last_scan_state = 0 THEN '离线'
                                    WHEN g.last_scan_state = 1 THEN '在线'
                                    ELSE '未知'
                                END, '未知'
                        ) AS dictLabel,
                </when>
                <otherwise>
                      ${groupByField} as dictValue,
                       o.dict_label   as dictLabel,
                </otherwise>
             </choose>
               COUNT(a.asset_id) AS count
        FROM tbl_network_devices a
        left join sys_user b on b.user_id = a.user_id
        left join sys_dept c on a.dept_id = c.dept_id
        left join tbl_vendor d on a.vendor = d.id
        left join  tbl_asset_overview as e on a.asset_id = e.asset_id
        left join tbl_location as f on f.location_id = e.location_id
        left join tbl_network_ip_mac as g on g.asset_id = e.asset_id and g.main_ip='1'
        left join tbl_network_domain as n on g.domain_id is not null and g.domain_id = n.domain_id
         <!-- 条件判断，asset_state 做特殊处理 -->
        <choose>
            <when test="dictType == 'asset_state'">
                WHERE (
                        (g.last_scan_state = '1' OR (g.last_scan_state IS NULL AND e.state = '1'))
                        OR
                        (g.last_scan_state = '0' OR (g.last_scan_state IS NULL AND e.state = '0'))
                    )
            </when>
            <otherwise>
                 INNER JOIN sys_dict_data o ON ${groupByField} = o.dict_value
                  WHERE o.status = '0'
                  AND o.dict_type = #{dictType}
            </otherwise>
        </choose>

        <!-- 分组 ，asset_state做特殊处理-->
        <choose>
            <when test="dictType == 'asset_state'">
                GROUP BY dictValue
            </when>
            <otherwise>
                GROUP BY ${groupByField}
            </otherwise>
        </choose>

    </select>

    <select id="getSafetyCountByDict" resultMap="CountByDictVOMap">
        <!-- asset_state 做特殊处理，在线与离线判断 判断g.last_scan_state 有值以g.last_scan_state为主 无值判断 e.state-->
        SELECT
        <choose>
            <when test="dictType == 'asset_state'">
                    COALESCE(g.last_scan_state, e.state) AS dictValue,
                    COALESCE(
                                CASE
                                    WHEN g.last_scan_state = 0 THEN '离线'
                                    WHEN g.last_scan_state = 1 THEN '在线'
                                    ELSE '未知'
                                END, '未知'
                        ) AS dictLabel,
            </when>
            <otherwise>
                <choose>
                    <when test="dictType == 'asset_type'">
                        h.type_name as dictLabel
                    </when>
                    <otherwise>
                        o.dict_label as dictLabel
                    </otherwise>
                </choose>,
                ${groupByField} as dictValue,
            </otherwise>
        </choose>
        COUNT(a.asset_id) AS count
        from tbl_safety a
        left join sys_dept s on a.dept_id = s.dept_id
        left join tbl_vendor d on a.vendor = d.id
        left join  tbl_asset_overview as e on a.asset_id = e.asset_id
        left join tbl_location as f on f.location_id = e.location_id
        left join tbl_network_ip_mac as g on g.asset_id = e.asset_id and g.main_ip='1'
        left join tbl_network_domain as n on g.domain_id is not null and g.domain_id = n.domain_id
        <!-- 条件判断，asset_type、asset_state 做特殊处理 -->
        <choose>
            <when test="dictType == 'asset_type'">
                INNER JOIN tbl_asset_class h ON a.asset_type = h.id
                WHERE h.pid = 3
                AND h.type_graded_protection = 0
            </when>
            <when test="dictType == 'asset_state'">
                WHERE (
                        (g.last_scan_state = '1' OR (g.last_scan_state IS NULL AND e.state = '1'))
                        OR
                        (g.last_scan_state = '0' OR (g.last_scan_state IS NULL AND e.state = '0'))
                    )
            </when>
            <otherwise>
                INNER JOIN sys_dict_data o ON ${groupByField} = o.dict_value
                WHERE o.status = '0'
                AND o.dict_type = #{dictType}
            </otherwise>
        </choose>
         <!-- 分组 ，asset_state做特殊处理-->
        <choose>
            <when test="dictType == 'asset_state'">
                GROUP BY dictValue
            </when>
            <otherwise>
                GROUP BY ${groupByField}
            </otherwise>
        </choose>
    </select>

    <select id="getBoundaryCountByDict" resultMap="CountByDictVOMap">
        <!-- 公共部分 -->
        SELECT
        ${groupByField} as dictValue,
        <choose>
            <when test="dictType == 'asset_type'">
                f.type_name as dictLabel
            </when>
            <otherwise>
                d.dict_label as dictLabel
            </otherwise>
        </choose>,
        COUNT(a.asset_id) AS count
        FROM tbl_zone_boundary a
        left join tbl_business_application b on a.application = b.asset_id
        LEFT JOIN tbl_asset_overview e ON a.asset_id = e.asset_id
        <!-- 条件判断 -->
        <choose>
            <when test="dictType == 'asset_type'">
                INNER JOIN tbl_asset_class f ON a.asset_type = f.id
                WHERE f.pid = 18
                AND f.type_graded_protection = 0
            </when>
            <otherwise>
                INNER JOIN sys_dict_data d ON ${groupByField} = d.dict_value
                WHERE d.status = '0'
                AND d.dict_type = #{dictType}
            </otherwise>
        </choose>

        GROUP BY ${groupByField}
    </select>


</mapper>
