<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.MonitorBssVulnInfoMapper">

    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssVulnInfo" id="MonitorBssVulnInfoResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="poc"    column="poc"    />
        <result property="exp"    column="exp"    />
        <result property="summary"    column="summary"    />
        <result property="impact"    column="impact"    />
        <result property="solution"    column="solution"    />
        <result property="definiteness"    column="definiteness"    />
        <result property="category"    column="category"    />
        <result property="severity"    column="severity"    />
        <result property="exposures"    column="exposures"    />
        <result property="cvss"    column="cvss"    />
        <result property="publishedDateTime"    column="published_date_time"    />
        <result property="detail"    column="detail"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="syncStatus" column="sync_status" />
    </resultMap>

    <sql id="selectMonitorBssVulnInfoVo">
        select id, title, poc, exp, summary, impact, solution, definiteness, category, severity, exposures, cvss, published_date_time, detail, create_by, create_time, update_by, update_time,sync_status from monitor_bss_vuln_info
    </sql>

    <select id="selectMonitorBssVulnInfoList" parameterType="MonitorBssVulnInfo" resultMap="MonitorBssVulnInfoResult">
        <include refid="selectMonitorBssVulnInfoVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%',#{title},'%')</if>
            <if test="poc != null  and poc != ''"> and poc = #{poc}</if>
            <if test="exp != null  and exp != ''"> and exp = #{exp}</if>
            <if test="summary != null  and summary != ''"> and summary = #{summary}</if>
            <if test="impact != null  and impact != ''"> and impact = #{impact}</if>
            <if test="solution != null  and solution != ''"> and solution = #{solution}</if>
            <if test="definiteness != null "> and definiteness = #{definiteness}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="severity != null "> and severity = #{severity}</if>
            <if test="exposures != null  and exposures != ''"> and exposures = #{exposures}</if>
            <if test="cvss != null  and cvss != ''"> and cvss = #{cvss}</if>
            <if test="publishedDateTime != null "> and published_date_time = #{publishedDateTime}</if>
            <if test="detail != null  and detail != ''"> and detail = #{detail}</if>
            <if test="syncStatus != null"> and sync_status = #{syncStatus}</if>
        </where>
    </select>

    <select id="getMonitorBssVulnInfoList" parameterType="MonitorBssVulnInfo" resultMap="MonitorBssVulnInfoResult">
        <include refid="selectMonitorBssVulnInfoVo"/>
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
        </where>
    </select>

    <select id="selectMonitorBssVulnInfoById" parameterType="Long" resultMap="MonitorBssVulnInfoResult">
        <include refid="selectMonitorBssVulnInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorBssVulnInfoByTitle" parameterType="String" resultMap="MonitorBssVulnInfoResult">
        <include refid="selectMonitorBssVulnInfoVo"/>
        where title = #{title}
        limit 1
    </select>

    <select id="selectMonitorBssVulnInfoByIds" parameterType="Long" resultMap="MonitorBssVulnInfoResult">
        <include refid="selectMonitorBssVulnInfoVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMonitorBssVulnInfo" parameterType="MonitorBssVulnInfo" useGeneratedKeys="true" keyProperty="id">
        insert into monitor_bss_vuln_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="poc != null">poc,</if>
            <if test="exp != null">exp,</if>
            <if test="summary != null">summary,</if>
            <if test="impact != null">impact,</if>
            <if test="solution != null">solution,</if>
            <if test="definiteness != null">definiteness,</if>
            <if test="category != null">category,</if>
            <if test="severity != null">severity,</if>
            <if test="exposures != null">exposures,</if>
            <if test="cvss != null">cvss,</if>
            <if test="publishedDateTime != null">published_date_time,</if>
            <if test="detail != null">detail,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="syncStatus != null">sync_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="poc != null">#{poc},</if>
            <if test="exp != null">#{exp},</if>
            <if test="summary != null">#{summary},</if>
            <if test="impact != null">#{impact},</if>
            <if test="solution != null">#{solution},</if>
            <if test="definiteness != null">#{definiteness},</if>
            <if test="category != null">#{category},</if>
            <if test="severity != null">#{severity},</if>
            <if test="exposures != null">#{exposures},</if>
            <if test="cvss != null">#{cvss},</if>
            <if test="publishedDateTime != null">#{publishedDateTime},</if>
            <if test="detail != null">#{detail},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="syncStatus != null">#{syncStatus},</if>
        </trim>
    </insert>

    <update id="updateMonitorBssVulnInfo" parameterType="MonitorBssVulnInfo">
        update monitor_bss_vuln_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="poc != null">poc = #{poc},</if>
            <if test="exp != null">exp = #{exp},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="impact != null">impact = #{impact},</if>
            <if test="solution != null">solution = #{solution},</if>
            <if test="definiteness != null">definiteness = #{definiteness},</if>
            <if test="category != null">category = #{category},</if>
            <if test="severity != null">severity = #{severity},</if>
            <if test="exposures != null">exposures = #{exposures},</if>
            <if test="cvss != null">cvss = #{cvss},</if>
            <if test="publishedDateTime != null">published_date_time = #{publishedDateTime},</if>
            <if test="detail != null">detail = #{detail},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="syncStatus != null">sync_status = #{syncStatus}</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorBssVulnInfoById" parameterType="Long">
        delete from monitor_bss_vuln_info where id = #{id}
    </delete>

    <delete id="deleteMonitorBssVulnInfoByIds" parameterType="String">
        delete from monitor_bss_vuln_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getVulnTypeList" resultType="java.util.HashMap">
        select distinct category from monitor_bss_vuln_info
        group by category
    </select>
    <select id="selectNoSyncList" resultType="com.ruoyi.monitor2.domain.MonitorBssVulnInfo" resultMap="MonitorBssVulnInfoResult">
        select * from monitor_bss_vuln_info where sync_status = 0 OR sync_status is null
    </select>
</mapper>
