<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblPortMapper">
    
    <resultMap type="TblPort" id="TblPortResult">
        <result property="id"    column="id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="port"    column="port"    />
        <result property="protocol"    column="protocol"    />
        <result property="funcation"    column="funcation"    />
        <result property="state"    column="state"    />
        <result property="remark"    column="remark"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectTblPortVo">
        select id, asset_id, port, protocol, `funcation`, state,remark, update_by, update_time, create_by, create_time from tbl_port
    </sql>

    <select id="selectTblPortList" parameterType="TblPort" resultMap="TblPortResult">
        <include refid="selectTblPortVo"/>
        <where>  
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="port != null "> and port = #{port}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="funcation != null  and funcation != ''"> and `funcation` = #{funcation}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
        </where>
    </select>
    
    <select id="selectTblPortById" parameterType="Long" resultMap="TblPortResult">
        <include refid="selectTblPortVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblPort" parameterType="TblPort">
        insert into tbl_port
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="port != null">port,</if>
            <if test="protocol != null">protocol,</if>
            <if test="funcation != null">`funcation`,</if>
            <if test="state != null">state,</if>
            <if test="remark != null">remark,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="port != null">#{port},</if>
            <if test="protocol != null">#{protocol},</if>
            <if test="funcation != null">#{funcation},</if>
            <if test="state != null">#{state},</if>
            <if test="remark != null">#{remark},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateTblPort" parameterType="TblPort">
        update tbl_port
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="port != null">port = #{port},</if>
            <if test="protocol != null">protocol = #{protocol},</if>
            <if test="funcation != null">`funcation` = #{funcation},</if>
            <if test="state != null">state = #{state},</if>
            <if test="remark != null">state = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblPortById" parameterType="Long">
        delete from tbl_port where id = #{id}
    </delete>

    <delete id="deleteTblPortByIds" parameterType="String">
        delete from tbl_port where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>