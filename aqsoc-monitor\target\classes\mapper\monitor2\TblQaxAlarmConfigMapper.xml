<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblQaxAlarmConfigMapper">

    <resultMap type="TblQaxAlarmConfig" id="TblQaxAlarmConfigResult">
        <result property="currentTime"    column="current_time"    />
    </resultMap>

    <sql id="selectTblQaxAlarmConfigVo">
        select current_time from tbl_qaxalarm_config
    </sql>

    <select id="selectTblQaxAlarmConfigList" parameterType="TblQaxAlarmConfig" resultMap="TblQaxAlarmConfigResult">
        <include refid="selectTblQaxAlarmConfigVo"/>
        <where>
            <if test="currentTime != null "> and current_time = #{currentTime}</if>
        </where>
    </select>

    <select id="selectTblQaxAlarmConfigByCurrentTime" parameterType="Date" resultMap="TblQaxAlarmConfigResult">
        <include refid="selectTblQaxAlarmConfigVo"/>
        where current_time = #{currentTime}
    </select>

    <select id="selectTblQaxAlarmConfigByCurrentTimes" parameterType="Date" resultMap="TblQaxAlarmConfigResult">
        <include refid="selectTblQaxAlarmConfigVo"/>
        where current_time in
        <foreach item="currentTime" collection="array" open="(" separator="," close=")">
            #{currentTime}
        </foreach>
    </select>

    <insert id="insertTblQaxAlarmConfig" parameterType="TblQaxAlarmConfig">
        insert into tbl_qaxalarm_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="currentTime != null">current_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="currentTime != null">#{currentTime},</if>
        </trim>
    </insert>

    <update id="updateTblQaxAlarmConfig" parameterType="TblQaxAlarmConfig">
        update tbl_qaxalarm_config
        <trim prefix="SET" suffixOverrides=",">
        </trim>
        where current_time = #{currentTime}
    </update>

    <delete id="deleteTblQaxAlarmConfigByCurrentTime" parameterType="Date">
        delete from tbl_qaxalarm_config where current_time = #{currentTime}
    </delete>

    <delete id="deleteTblQaxAlarmConfigByCurrentTimes" parameterType="String">
        delete from tbl_qaxalarm_config where current_time in
        <foreach item="currentTime" collection="array" open="(" separator="," close=")">
            #{currentTime}
        </foreach>
    </delete>
</mapper>