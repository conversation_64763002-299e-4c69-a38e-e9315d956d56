<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.work.mapper.TblWorkOrderMapper">

    <resultMap type="TblWorkOrder" id="TblWorkOrderResult">
        <result property="id"    column="id"    />
        <result property="workNo" column="work_no" />
        <result property="workType"    column="work_type"    />
        <result property="eventIds" column="event_ids" typeHandler="com.ruoyi.common.handler.String2ListTypeHandler" />
        <result property="workName"    column="work_name"    />
        <result property="applicationId" column="application_id" />
        <result property="associatedIps" column="associated_ips" typeHandler="com.ruoyi.common.handler.String2ListTypeHandler" />
        <result property="eventCreateTime" column="event_create_time" />
        <result property="handleDept"    column="handle_dept"    />
        <result property="handleUser"    column="handle_user"    />
        <result property="handleUserPhone" column="handle_user_phone" />
        <result property="expectCompleteTime" column="expect_complete_time" />
        <result property="eventSource" column="event_source" />
        <result property="eventType" column="event_type" />
        <result property="eventLevel" column="event_level" />
        <result property="handleOpinion" column="handle_opinion" />
        <result property="eventNotification" column="event_notification" />
        <result property="describeFileUrl" column="describe_file_url" />
        <result property="completeTime"    column="complete_time" />
        <result property="fFlowid" column="f_flowid" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="prodId"    column="prod_id" />
        <result property="flowState"    column="flow_state" />
        <result property="applicationName" column="application_name" />
        <result property="assetIps" column="asset_ips" />
        <result property="eventDescription" column="event_description" />
        <result property="handleSituation" column="handle_situation" />
        <result property="otherSituation" column="other_situation" />
        <result property="feedbackDate" column="feedback_date" />
        <result property="feedbackFileUrl" column="feedback_file_url" />
        <result property="informSign" column="inform_sign" />
        <result property="informSeal" column="inform_seal" />
        <result property="feedbackSign" column="feedback_sign" />
        <result property="feedbackSeal" column="feedback_seal" />
        <result property="remark1" column="remark1" />
        <result property="remark2" column="remark2" />
        <result property="remark3" column="remark3" />
        <result property="remark4" column="remark4" />
        <result property="remark5" column="remark5" />
        <result property="remark6" column="remark6" />
        <result property="remark7" column="remark7" />
        <result property="remark8" column="remark8" />
        <result property="manageUser" column="manage_user" />
        <result property="manageDept" column="manage_dept" />
        <result property="issue" column="issue" />
        <result property="handleState" column="handle_state" />
        <result property="nodeProperties" column="node_properties" />
        <result property="urgency" column="urgency" />
        <result property="isPublic" column="is_public" />
        <result property="severityLevel" column="severity_level" />
        <result property="reportDate" column="report_date" />
        <result property="period" column="period" />
        <result property="signed" column="signed" />
        <result property="proofread" column="proofread" />
        <result property="editor" column="editor" />
        <association property="loginUrl" column="application_id" select="com.ruoyi.safe.mapper.TblBusinessApplicationMapper.getLoginUrlById" />
        <collection property="historyNodeProperties" column="id" select="com.ruoyi.work.mapper.TblWorkHistoryMapper.selectWorkHistoryNodePropByWorkId" />
        <collection property="currentHandleUserList" column="id" select="com.ruoyi.work.mapper.TblWorkOrderMapper.selectCurrentHandleUser" />
    </resultMap>

    <sql id="selectTblWorkOrderVo">
        select id,work_no, work_type, event_ids, work_name, application_id, login_url, associated_ips, event_create_time, handle_dept, handle_user, handle_user_phone,
               expect_complete_time, event_source, event_type, event_level, handle_opinion, event_notification, describe_file_url, complete_time,event_description,handle_situation, other_situation,
               feedback_date,feedback_file_url,inform_sign,inform_seal,feedback_sign,feedback_seal, remark1, remark2, remark3, remark4, remark5,remark6,remark7,remark8,manage_user,manage_dept,issue,
               f_flowid,create_time,update_time, prod_id, flow_state,urgency,is_public,severity_level,report_date,period,signed,proofread,editor from tbl_work_order
    </sql>

    <select id="selectTblWorkOrderList" parameterType="TblWorkOrder" resultMap="TblWorkOrderResult">
        <include refid="selectTblWorkOrderVo"/>
        <where>
            <if test="workType != null  and workType != ''"> and work_type = #{workType}</if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="workName != null  and workName != ''"> and work_name like concat('%', #{workName}, '%')</if>
            <if test="complateTime != null "> and complate_time = #{complateTime}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="handleDept != null "> and handle_dept = #{handleDept}</if>
            <if test="handleUser != null "> and handle_user = #{handleUser}</if>
            <if test="isDel != null  and isDel != ''"> and is_del = #{isDel}</if>
        </where>
    </select>

    <select id="selectTblWorkOrderById" parameterType="Long" resultMap="TblWorkOrderResult">
        SELECT
            o.id,
            o.work_no,
            o.work_type,
            o.application_id,
            o.login_url,
            o.work_name,
            o.complete_time,
            o.handle_dept,
            o.handle_user,
            o.handle_user_phone,
            o.event_create_time,
            o.expect_complete_time,
            o.associated_ips,
            o.event_ids,
            o.prod_id,
            o.flow_state,
            o.event_source,
            o.event_type,
            o.event_level,
            o.event_notification,
            o.handle_opinion,
            o.describe_file_url,
            o.event_description,
            o.handle_situation,
            o.other_situation,
            o.feedback_date,
            o.feedback_file_url,
            o.inform_sign,
            o.inform_seal,
            o.feedback_sign,
            o.feedback_seal,
            o.remark1,
            o.remark2,
            o.remark3,
            o.remark4,
            o.remark5,
            o.remark6,
            o.remark7,
            o.remark8,
            o.manage_user,
            o.manage_dept,
            o.issue,
            o.create_time,
            o.update_time,
            o.urgency,
            o.is_public,
            o.severity_level,
            o.report_date,
            o.period,
            o.signed,
            o.proofread,
            o.editor,
            hd.dept_name AS handleDeptName,
            hu.nick_name AS handleUserName,
            hu.phonenumber AS handlePhone,
            tba.asset_name AS application_name
        FROM
            tbl_work_order o
            LEFT JOIN sys_dept hd ON o.handle_dept = hd.dept_id
            LEFT JOIN sys_user hu ON o.handle_user = hu.user_id
            LEFT JOIN tbl_business_application tba ON o.application_id = tba.asset_id
        where o.id = #{id} or o.prod_id = #{id}
    </select>

    <select id="selectTblWorkOrderByIds" parameterType="Long" resultMap="TblWorkOrderResult">
        <include refid="selectTblWorkOrderVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblWorkOrder" parameterType="TblWorkOrder">
        insert into tbl_work_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="workNo != null and workNo != ''">work_no,</if>
            <if test="workType != null">work_type,</if>
            <if test="eventIds != null and eventIds.size() > 0">event_ids,</if>
            <if test="workName != null and workName != ''">work_name,</if>
            <if test="applicationId != null and applicationId != ''">application_id,</if>
            <if test="loginUrl != null and loginUrl != ''">login_url,</if>
            <if test="associatedIps != null and associatedIps.size() > 0">associated_ips,</if>
            <if test="eventCreateTime != null">event_create_time,</if>
            <if test="handleDept != null">handle_dept,</if>
            <if test="handleUser != null">handle_user,</if>
            <if test="handleUserPhone != null and handleUserPhone != ''">handle_user_phone,</if>
            <if test="expectCompleteTime != null">expect_complete_time,</if>
            <if test="eventSource != null and eventSource != ''">event_source,</if>
            <if test="eventType != null and eventType != ''">event_type,</if>
            <if test="eventLevel != null and eventLevel != ''">event_level,</if>
            <if test="handleOpinion != null and handleOpinion != ''">handle_opinion,</if>
            <if test="eventNotification != null and eventNotification != ''">event_notification,</if>
            <if test="describeFileUrl != null and describeFileUrl != ''">describe_file_url,</if>
            <if test="eventDescription != null and eventDescription != ''">event_description,</if>
            <if test="handleSituation != null and handleSituation != ''">handle_situation,</if>
            <if test="otherSituation != null and otherSituation != ''">other_situation,</if>
            <if test="feedbackFileUrl != null and feedbackFileUrl != ''">feedback_file_url,</if>
            <if test="feedbackDate != null">feedback_date,</if>
            <if test="informSign != null and informSign != ''">inform_sign,</if>
            <if test="informSeal != null and informSeal != ''">inform_seal,</if>
            <if test="feedbackSign != null and feedbackSign != ''">feedback_sign,</if>
            <if test="feedbackSeal != null and feedbackSeal != ''">feedback_seal,</if>
            <if test="remark1 != null and remark1 != ''">remark1,</if>
            <if test="remark2 != null and remark2 != ''">remark2,</if>
            <if test="remark3 != null and remark3 != ''">remark3,</if>
            <if test="remark4 != null and remark4 != ''">remark4,</if>
            <if test="remark5 != null and remark5 != ''">remark5,</if>
            <if test="remark6 != null and remark6 != ''">remark6,</if>
            <if test="remark7 != null and remark7 != ''">remark7,</if>
            <if test="remark8 != null and remark8 != ''">remark8,</if>
            <if test="manageUser != null and manageUser != ''">manage_user,</if>
            <if test="manageDept != null and manageDept != ''">manage_dept,</if>
            <if test="issue != null and issue != ''">issue,</if>
            <if test="completeTime != null">complete_time,</if>
            <if test="fFlowid != null and fFlowid != ''">f_flowid,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="prodId != null and prodId != ''">prod_id,</if>
            <if test="flowState != null">flow_state,</if>
            <if test="urgency != null">urgency,</if>
            <if test="isPublic != null">is_public,</if>
            <if test="severityLevel != null and severityLevel != ''">severity_level,</if>
            <if test="reportDate != null">report_date,</if>
            <if test="period != null">`period`,</if>
            <if test="signed != null and signed != ''">`signed`,</if>
            <if test="proofread != null and proofread != ''">proofread,</if>
            <if test="editor != null and editor != ''">editor,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="workNo != null and workNo != ''">#{workNo},</if>
            <if test="workType != null">#{workType},</if>
            <if test="eventIds != null and eventIds.size() > 0">#{eventIds},</if>
            <if test="workName != null and workName != ''">#{workName},</if>
            <if test="applicationId != null and applicationId != ''">#{applicationId},</if>
            <if test="loginUrl != null and loginUrl != ''">#{loginUrl},</if>
            <if test="associatedIps != null and associatedIps.size() > 0">#{associatedIps},</if>
            <if test="eventCreateTime != null">#{eventCreateTime},</if>
            <if test="handleDept != null">#{handleDept},</if>
            <if test="handleUser != null">#{handleUser},</if>
            <if test="handleUserPhone != null and handleUserPhone != ''">#{handleUserPhone},</if>
            <if test="expectCompleteTime != null">#{expectCompleteTime},</if>
            <if test="eventSource != null and eventSource != ''">#{eventSource},</if>
            <if test="eventType != null and eventType != ''">#{eventType},</if>
            <if test="eventLevel != null and eventLevel != ''">#{eventLevel},</if>
            <if test="handleOpinion != null and handleOpinion != ''">#{handleOpinion},</if>
            <if test="eventNotification != null and eventNotification != ''">#{eventNotification},</if>
            <if test="describeFileUrl != null and describeFileUrl != ''">#{describeFileUrl},</if>
            <if test="eventDescription != null and eventDescription != ''">#{eventDescription},</if>
            <if test="handleSituation != null and handleSituation != ''">#{handleSituation},</if>
            <if test="otherSituation != null and otherSituation != ''">#{otherSituation},</if>
            <if test="feedbackFileUrl != null and feedbackFileUrl != ''">#{feedbackFileUrl},</if>
            <if test="feedbackDate != null">#{feedbackDate},</if>
            <if test="informSign != null and informSign != ''">#{informSign},</if>
            <if test="informSeal != null and informSeal != ''">#{informSeal},</if>
            <if test="feedbackSign != null and feedbackSign != ''">#{feedbackSign},</if>
            <if test="feedbackSeal != null and feedbackSeal != ''">#{feedbackSeal},</if>
            <if test="remark1 != null and remark1 != ''">#{remark1},</if>
            <if test="remark2 != null and remark2 != ''">#{remark2},</if>
            <if test="remark3 != null and remark3 != ''">#{remark3},</if>
            <if test="remark4 != null and remark4 != ''">#{remark4},</if>
            <if test="remark5 != null and remark5 != ''">#{remark5},</if>
            <if test="remark6 != null and remark6 != ''">#{remark6},</if>
            <if test="remark7 != null and remark7 != ''">#{remark7},</if>
            <if test="remark8 != null and remark8 != ''">#{remark8},</if>
            <if test="manageUser != null and manageUser != ''">#{manageUser},</if>
            <if test="manageDept != null and manageDept != ''">#{manageDept},</if>
            <if test="issue != null and issue != ''">#{issue},</if>
            <if test="completeTime != null">#{completeTime},</if>
            <if test="fFlowid != null and fFlowid != ''">#{fFlowid},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="prodId != null and prodId != ''">#{prodId},</if>
            <if test="flowState != null">#{flowState},</if>
            <if test="urgency != null">#{urgency},</if>
            <if test="isPublic != null">#{isPublic},</if>
            <if test="severityLevel != null and severityLevel != ''">#{severityLevel},</if>
            <if test="reportDate != null">#{reportDate},</if>
            <if test="period != null">#{period},</if>
            <if test="signed != null and signed != ''">#{signed},</if>
            <if test="proofread != null and proofread != ''">#{proofread},</if>
            <if test="editor != null and editor != ''">#{editor},</if>
         </trim>
    </insert>

    <update id="updateTblWorkOrder" parameterType="TblWorkOrder">
        update tbl_work_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="workType != null">work_type = #{workType},</if>
            <if test="eventIds != null">
                <if test="workType != null and workType != '0'">
                    event_ids = #{eventIds},
                </if>
                <if test="workType == null or workType == '0'">
                    event_ids = null,
                </if>
            </if>
            <if test="workName != null and workName != ''">work_name = #{workName},</if>
            <if test="applicationId != null and applicationId != ''">application_id = #{applicationId},</if>
            <if test="loginUrl != null and loginUrl != ''">login_url = #{loginUrl},</if>
            <if test="associatedIps != null and associatedIps.size() > 0">associated_ips = #{associatedIps},</if>
            <if test="eventCreateTime != null">event_create_time = #{eventCreateTime},</if>
            <if test="handleDept != null">handle_dept = #{handleDept},</if>
            <if test="handleUser != null">handle_user = #{handleUser},</if>
            <if test="handleUserPhone != null and handleUserPhone != ''">handle_user_phone = #{handleUserPhone},</if>
            <if test="expectCompleteTime != null">expect_complete_time = #{expectCompleteTime},</if>
            <if test="eventSource != null and eventSource != ''">event_source = #{eventSource},</if>
            <if test="eventType != null and eventType != ''">event_type = #{eventType},</if>
            <if test="eventLevel != null and eventLevel != ''">event_level = #{eventLevel},</if>
            <if test="handleOpinion != null and handleOpinion != ''">handle_opinion = #{handleOpinion},</if>
            <if test="eventNotification != null and eventNotification != ''">event_notification = #{eventNotification},</if>
            <if test="describeFileUrl != null and describeFileUrl != ''">describe_file_url = #{describeFileUrl},</if>
            <if test="eventDescription != null and eventDescription != ''">event_description = #{eventDescription},</if>
            <if test="handleSituation != null and handleSituation != ''">handle_situation = #{handleSituation},</if>
            <if test="otherSituation != null and otherSituation != ''">other_situation = #{otherSituation},</if>
            <if test="feedbackDate != null">feedback_date = #{feedbackDate},</if>
            <if test="feedbackFileUrl != null and feedbackFileUrl != ''">feedback_file_url = #{feedbackFileUrl},</if>
            <if test="informSign != null and informSign != ''">inform_sign = #{informSign},</if>
            <if test="feedbackSign != null and feedbackSign != ''">feedback_sign = #{feedbackSign},</if>
            <if test="informSeal != null and informSeal != ''">inform_seal = #{informSeal},</if>
            <if test="feedbackSeal != null and feedbackSeal != ''">feedback_seal = #{feedbackSeal},</if>
            <if test="remark1 != null and remark1 != ''">remark1=#{remark1},</if>
            <if test="remark2 != null and remark2 != ''">remark2=#{remark2},</if>
            <if test="remark3 != null and remark3 != ''">remark3=#{remark3},</if>
            <if test="remark4 != null and remark4 != ''">remark4=#{remark4},</if>
            <if test="remark5 != null and remark5 != ''">remark5=#{remark5},</if>
            <if test="remark6 != null and remark6 != ''">remark6=#{remark6},</if>
            <if test="remark7 != null and remark7 != ''">remark7=#{remark7},</if>
            <if test="remark8 != null and remark8 != ''">remark8=#{remark8},</if>
            <if test="manageUser != null and manageUser != ''">manage_user=#{manageUser},</if>
            <if test="manageDept != null and manageDept != ''">manage_dept=#{manageDept},</if>
            <if test="issue != null and issue != ''">issue=#{issue},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="prodId != null and prodId != ''">prod_id = #{prodId},</if>
            <if test="flowState != null">flow_state = #{flowState},</if>
            <if test="completeUser != null and completeUser != ''">complete_user = #{completeUser},</if>
            <if test="urgency != null">urgency = #{urgency},</if>
            <if test="isPublic != null and isPublic != ''">is_public = #{isPublic},</if>
            <if test="severityLevel != null and severityLevel != ''">severity_level = #{severityLevel},</if>
            <if test="reportDate != null">report_date = #{reportDate},</if>
            <if test="period != null">`period` = #{period},</if>
            <if test="signed != null and signed != ''">`signed` = #{signed},</if>
            <if test="proofread != null and proofread != ''">proofread = #{proofread},</if>
            <if test="editor != null and editor != ''">editor = #{editor},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblWorkOrderById" parameterType="Long">
        delete from tbl_work_order where id = #{id}
    </delete>

    <delete id="deleteTblWorkOrderByIds" parameterType="String">
        delete from tbl_work_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="findBacklogList" parameterType="TblWorkOrder" resultMap="TblWorkOrderResult">
        SELECT
            t1.id,
            t1.work_no,
            t1.work_type,
            t1.event_ids,
            t1.work_name,
            t1.application_id,
            t1.login_url,
            t1.handle_dept,
            t1.handle_user,
            t1.handle_user_phone,
            t1.expect_complete_time,
            t1.event_source,
            t1.event_type,
            t1.event_level,
            t1.complete_time,
            t1.prod_Id,
            t1.complete_user,
            t1.update_time,
            t1.flow_state,
            t1.flow_state AS handle_state,
            hd.dept_name AS handleDeptName,
            hu.user_name AS handleUserName,
            tba.asset_name application_name
        FROM
            tbl_work_order t1
                INNER JOIN tbl_work_backlog t2 ON t2.work_id = t1.id AND t2.handle_user = #{flowHandleUser}
                LEFT JOIN sys_dept hd ON t1.handle_dept = hd.dept_id
                LEFT JOIN sys_user hu ON t1.handle_user = hu.user_id
                LEFT JOIN tbl_business_application tba ON tba.asset_id = t1.application_id
        <where>
            <if test="workName != null  and workName != ''"> and t1.work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null  and workType != ''"> and t1.work_type = #{workType}</if>
            <if test="flowState != null  and flowState != ''"> and t1.flow_state = #{flowState}</if>
            <if test="complateDateStart != null and complateDateEnd != null">
                and t1.complate_time BETWEEN STR_TO_DATE(#{complateDateStart}, '%Y-%m-%d %H:%i:%s')
                AND STR_TO_DATE(#{complateDateEnd}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="handleUser != null "> and t2.handle_user = #{flowHandleUser}</if>
            <if test="overTrueDate != null"> and t1.complate_time &lt; #{overTrueDate}</if>
            <if test="overFalseDate != null"> and t1.complate_time &gt;= #{overFalseDate}</if>
            <if test="handleUserName != null and handleUserName != ''">AND hu.user_name LIKE concat('%', #{handleUserName}, '%')</if>
            <if test="applicationId != null "> and t1.application_id = #{applicationId}</if>
            <if test="workNo != null and workNo != ''"> and t1.work_no like concat('%', #{workNo}, '%')</if>
            <if test="applicationName != null and applicationName != ''"> and tba.asset_name like concat('%', #{applicationName}, '%')</if>
            <if test="loginUrl != null and loginUrl != ''"> and t1.login_url like concat('%', #{loginUrl}, '%')</if>
            <if test="eventLevel != null and eventLevel != ''"> and t1.event_level = #{eventLevel}</if>
            <if test="eventType != null and eventType != ''"> and t1.event_type = #{eventType}</if>
        </where>
        ORDER BY update_time DESC
    </select>

    <select id="selectWaitList" parameterType="TblWorkOrder" resultMap="TblWorkOrderResult">
        select
        t1.id,
        t1.work_no,
        t1.work_type,
        t1.event_ids,
        t1.work_name,
        t1.application_id,
        t1.login_url,
        t1.handle_dept,
        t1.handle_user,
        t1.handle_user_phone,
        t1.expect_complete_time,
        t1.event_source,
        t1.event_type,
        t1.event_level,
        t1.complete_time,
        t1.prod_Id,
        t1.complete_user,
        t1.update_time,
        t1.flow_state,
        t1.flow_state AS handle_state,
        t1.remark2 AS node_properties,
        t1.create_time,
        t1.urgency,
        t1.is_public,
        t1.severity_level,
        t1.report_date,
        t1.period,
        t1.signed,
        t1.proofread,
        t1.editor,
        t1.remark6,
        hd.dept_name AS handleDeptName,
        hu.nick_name AS handleUserName,
        tba.asset_name application_name,
        hu2.nick_name AS create_by
        from
            tbl_work_order t1
        left join tbl_work_backlog t2 ON t2.work_id = t1.id
        left join tbl_work_history t3 on t3.work_id = t1.id
        left join tbl_work_order_target wot on wot.work_order_id = t1.id
        LEFT JOIN sys_dept hd ON wot.handle_dept = hd.dept_id
        LEFT JOIN sys_user hu ON wot.handle_user = hu.user_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id = t1.application_id
        LEFT JOIN sys_user hu2 on t1.remark5 = hu2.user_id
        <where>
            <if test="findCarbonCopyStatus == null or findCarbonCopyStatus != 1">
                (
                1=1
                <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
                <if test="handleDeptIds != null and handleDeptIds.size()>0">
                    and (hd.dept_id IN
                    <foreach item="handleDeptIdItem" collection="handleDeptIds" open="(" separator="," close=")">
                        #{handleDeptIdItem}
                    </foreach>
                    )
                </if>
                <if test="(createBy != null and createBy != '') or (flowHandleUser != null) or (queryAll != null and queryAll == true)">
                    <if test="onlySelf != null and onlySelf==true">and</if> <if test="onlySelf == null or onlySelf!=true">or</if>(
                    1=2
                    <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
                    <if test="flowHandleUser != null and findCarbonCopyStatus == null"> OR (t2.handle_user = #{flowHandleUser} OR t3.handle_user = #{flowHandleUser})</if>
                    <if test="queryAll != null and queryAll == true"> OR 1=1</if>
                    )
                </if>
                )
            </if>
            <if test="queryState != null">
                <if test="queryState == -1">
                    <!-- 待发起 -->
                    and t1.flow_state = -1
                </if>
                <if test="queryState == 1">
                    <!-- 待审核 -->
                    and (t1.flow_state = 0 OR t1.flow_state = 2)
                </if>
                <if test="queryState == 2">
                    <!-- 待处置 -->
                    and t1.flow_state = 1
                </if>
                <if test="queryState == 3">
                    <!-- 待核验 -->
                    and t1.flow_state = 3
                </if>
                <if test="queryState == 4">
                    <!-- 已完成 -->
                    and t1.flow_state = 4
                </if>
            </if>
            <if test="workName != null  and workName != ''"> and t1.work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null  and workType != ''">
                <if test="workType != '-1'">
                    and t1.work_type = #{workType}
                </if>
                <if test="workType == '-1'">
                    and t1.work_type is null or t1.work_type = ''
                </if>
            </if>
            <if test="flowState != null  and flowState != '' and flowState != 99"> and t1.flow_state = #{flowState}</if>
            <if test="flowState == 99"> and t1.flow_state is null</if>
            <if test="flowStateList != null and flowStateList.size() > 0">
                and t1.flow_state IN
                <foreach item="flowStateItem" collection="flowStateList" open="(" separator="," close=")">
                    #{flowStateItem}
                </foreach>
            </if>
            <if test="remark5 != null and remark5 != ''">
                and t1.remark5 = #{remark5}
            </if>
            <if test="complateDateStart != null and complateDateEnd != null">
                and t1.complate_time BETWEEN STR_TO_DATE(#{complateDateStart}, '%Y-%m-%d %H:%i:%s')
                AND STR_TO_DATE(#{complateDateEnd}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="findCarbonCopyStatus != null and findCarbonCopyStatus == 1">
                AND t3.type = 2 AND t3.handle_user = #{flowHandleUser}
            </if>
            <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
            <if test="handleDeptStr != null and handleDeptStr != ''">and hd.dept_name like concat('%', #{handleDeptStr}, '%')</if>
            <if test="handleUser != null and findCarbonCopyStatus == null">and t1.handle_user = #{handleUser}</if>
            <if test="handleUserStr != null and handleUserStr != ''">and (hu.nick_name like concat('%', #{handleUserStr}, '%') OR hu.user_name like concat('%', #{handleUserStr}, '%'))</if>
            <if test="overTrueDate != null"> and t1.complate_time &lt; #{overTrueDate}</if>
            <if test="overFalseDate != null"> and t1.complate_time &gt;= #{overFalseDate}</if>
            <if test="handleUserName != null and handleUserName != ''">AND hu.user_name LIKE concat('%', #{handleUserName}, '%')</if>
            <if test="applicationId != null "> and t1.application_id = #{applicationId}</if>
            <if test="workNo != null and workNo != ''"> and t1.work_no like concat('%', #{workNo}, '%')</if>
            <if test="applicationName != null and applicationName != ''"> and tba.asset_name like concat('%', #{applicationName}, '%')</if>
            <if test="loginUrl != null and loginUrl != ''"> and t1.login_url like concat('%', #{loginUrl}, '%')</if>
            <if test="eventLevel != null and eventLevel != ''"> and t1.event_level = #{eventLevel}</if>
            <if test="eventType != null and eventType != ''"> and t1.event_type = #{eventType}</if>
            <if test="startCreateTime != null">and t1.create_time &gt;= #{startCreateTime}</if>
            <if test="endCreateTime != null">and t1.create_time &lt;= #{endCreateTime}</if>
            <if test="currentUserId != null"> and t2.handle_user = #{currentUserId}</if>
            <if test="notCurrentUserId != null"> and (t2.handle_user != #{notCurrentUserId} OR t2.handle_user IS NULL)</if>
            <if test="urgency != null"> and t1.urgency = #{urgency}</if>
            <if test="isPublic != null and isPublic != ''"> and t1.is_public = #{isPublic}</if>
        </where>
        GROUP BY
        t1.id
        ORDER BY t1.update_time DESC
    </select>
    <!--<select id="selectWaitList" parameterType="TblWorkOrder" resultMap="TblWorkOrderResult">
        (SELECT
        t1.id,
        t1.work_no,
        t1.work_type,
        t1.event_ids,
        t1.work_name,
        t1.application_id,
        t1.login_url,
        t1.handle_dept,
        t1.handle_user,
        t1.handle_user_phone,
        t1.expect_complete_time,
        t1.event_source,
        t1.event_type,
        t1.event_level,
        t1.complete_time,
        t1.prod_Id,
        t1.complete_user,
        t1.update_time,
        t1.flow_state,
        t1.flow_state AS handle_state,
        t1.remark2 AS node_properties,
        t1.create_time,
        hd.dept_name AS handleDeptName,
        hu.nick_name AS handleUserName,
        tba.asset_name application_name
        FROM
        tbl_work_order t1
        INNER JOIN tbl_work_backlog t2 ON t2.work_id = t1.id
        &lt;!&ndash;<if test="flowHandleUser != null">
            AND (
            t2.handle_user = #{flowHandleUser}
            <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
            <if test="queryAll != null and queryAll == true"> OR 1=1</if>
            )
        </if>&ndash;&gt;
        LEFT JOIN sys_dept hd ON t1.handle_dept = hd.dept_id
        LEFT JOIN sys_user hu ON t1.handle_user = hu.user_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id = t1.application_id
        <where>
            (
            1=1
            <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
            <if test="handleDeptIds != null and handleDeptIds.size()>0">
                and (hd.dept_id IN
                <foreach item="handleDeptIdItem" collection="handleDeptIds" open="(" separator="," close=")">
                    #{handleDeptIdItem}
                </foreach>
                )
            </if>
            <if test="(createBy != null and createBy != '') or (flowHandleUser != null) or (queryAll != null and queryAll == true)">
                <if test="onlySelf != null and onlySelf==true">and</if> <if test="onlySelf == null or onlySelf!=true">or</if>(
                1=2
                <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
                <if test="flowHandleUser != null "> OR t2.handle_user = #{flowHandleUser}</if>
                <if test="queryAll != null and queryAll == true"> OR 1=1</if>
                )
            </if>
            )
            <if test="queryState != null">
                <if test="queryState == -1">
                    &lt;!&ndash; 待发起 &ndash;&gt;
                    and t1.flow_state = -1
                </if>
                <if test="queryState == 1">
                    &lt;!&ndash; 待审核 &ndash;&gt;
                    and (t1.flow_state = 0 OR t1.flow_state = 2)
                </if>
                <if test="queryState == 2">
                    &lt;!&ndash; 待处置 &ndash;&gt;
                    and t1.flow_state = 1
                </if>
                <if test="queryState == 3">
                    &lt;!&ndash; 待核验 &ndash;&gt;
                    and t1.flow_state = 3
                </if>
                <if test="queryState == 4">
                    &lt;!&ndash; 已完成 &ndash;&gt;
                    and 1=2
                </if>
            </if>
            <if test="workName != null  and workName != ''"> and t1.work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null  and workType != ''"> and t1.work_type = #{workType}</if>
            <if test="flowState != null  and flowState != ''"> and t1.flow_state = #{flowState}</if>
            <if test="complateDateStart != null and complateDateEnd != null">
                and t1.complate_time BETWEEN STR_TO_DATE(#{complateDateStart}, '%Y-%m-%d %H:%i:%s')
                AND STR_TO_DATE(#{complateDateEnd}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="handleDeptStr != null and handleDeptStr != ''">and hd.dept_name like concat('%', #{handleDeptStr}, '%')</if>
            <if test="handleUser != null">and t1.handle_user = #{handleUser}</if>
            <if test="handleUserStr != null and handleUserStr != ''">and (hu.nick_name like concat('%', #{handleUserStr}, '%') OR hu.user_name like concat('%', #{handleUserStr}, '%'))</if>
            &lt;!&ndash;<if test="flowHandleUser != null "> and t2.handle_user = #{flowHandleUser}</if>&ndash;&gt;
            <if test="overTrueDate != null"> and t1.complate_time &lt; #{overTrueDate}</if>
            <if test="overFalseDate != null"> and t1.complate_time &gt;= #{overFalseDate}</if>
            <if test="handleUserName != null and handleUserName != ''">AND hu.user_name LIKE concat('%', #{handleUserName}, '%')</if>
            <if test="applicationId != null "> and t1.application_id = #{applicationId}</if>
            <if test="workNo != null and workNo != ''"> and t1.work_no like concat('%', #{workNo}, '%')</if>
            <if test="applicationName != null and applicationName != ''"> and tba.asset_name like concat('%', #{applicationName}, '%')</if>
            <if test="loginUrl != null and loginUrl != ''"> and t1.login_url like concat('%', #{loginUrl}, '%')</if>
            <if test="eventLevel != null and eventLevel != ''"> and t1.event_level = #{eventLevel}</if>
            <if test="eventType != null and eventType != ''"> and t1.event_type = #{eventType}</if>
            <if test="startCreateTime != null">and t1.create_time &gt;= #{startCreateTime}</if>
            <if test="endCreateTime != null">and t1.create_time &lt;= #{endCreateTime}</if>
        </where>
        GROUP BY
            t1.id)
        UNION ALL
        (
        SELECT
        t1.id,
        t1.work_no,
        t1.work_type,
        t1.event_ids,
        t1.work_name,
        t1.application_id,
        t1.login_url,
        t1.handle_dept,
        t1.handle_user,
        t1.handle_user_phone,
        t1.expect_complete_time,
        t1.event_source,
        t1.event_type,
        t1.event_level,
        t1.complete_time,
        t1.prod_Id,
        t1.complete_user,
        t1.update_time,
        t1.flow_state AS flow_state,
        t2.handle_state,
        t2.node_properties,
        t1.create_time,
        hd.dept_name AS handleDeptName,
        hu.nick_name AS handleUserName,
        tba.asset_name application_name
        FROM
        tbl_work_order t1
        INNER JOIN ( SELECT t1.* FROM tbl_work_history t1 LEFT JOIN tbl_work_backlog t2 ON t2.work_id = t1.work_id
        &lt;!&ndash;<if test="flowHandleUser != null">
            AND (
            t2.handle_user = #{flowHandleUser}
            )
        </if>&ndash;&gt;
        WHERE t1.ID IN ( SELECT MAX( ID ) AS ID FROM tbl_work_history <where><if test="flowHandleUser != null">AND handle_user = #{flowHandleUser}</if></where> GROUP BY work_id ) AND t2.work_id IS NULL ) t2 ON t1.ID = t2.work_id
        LEFT JOIN sys_dept hd ON t1.handle_dept = hd.dept_id
        LEFT JOIN sys_user hu ON t1.handle_user = hu.user_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id = t1.application_id
        <where>
            (
            1=1
            <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
            <if test="handleDeptIds != null and handleDeptIds.size()>0">
                and (hd.dept_id IN
                <foreach item="handleDeptIdItem" collection="handleDeptIds" open="(" separator="," close=")">
                    #{handleDeptIdItem}
                </foreach>
                )
            </if>
            <if test="(createBy != null and createBy != '') or (flowHandleUser != null) or (queryAll != null and queryAll == true)">
                <if test="onlySelf != null and onlySelf==true">and</if> <if test="onlySelf == null or onlySelf!=true">or</if> (
                1=2
                <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
                <if test="flowHandleUser != null "> OR t2.handle_user = #{flowHandleUser}</if>
                <if test="queryAll != null and queryAll == true"> OR 1=1</if>
                )
            </if>
            )
            <if test="queryState != null">
                <if test="queryState == 4">
                    and (1 = 1)
                </if>
                <if test="queryState != 4">
                    and (1 = 2)
                </if>
            </if>
            <if test="workName != null  and workName != ''"> and t1.work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null  and workType != ''"> and t1.work_type = #{workType}</if>
            <if test="flowState != null  and flowState != ''"> and t1.flow_state = #{flowState}</if>
            <if test="complateDateStart != null and complateDateEnd != null">
                and t1.complate_time BETWEEN STR_TO_DATE(#{complateDateStart}, '%Y-%m-%d %H:%i:%s')
                AND STR_TO_DATE(#{complateDateEnd}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="handleDeptStr != null and handleDeptStr != ''">and hd.dept_name like concat('%', #{handleDeptStr}, '%')</if>
            <if test="handleUser != null">and t1.handle_user = #{handleUser}</if>
            <if test="handleUserStr != null and handleUserStr != ''">and (hu.nick_name like concat('%', #{handleUserStr}, '%') OR hu.user_name like concat('%', #{handleUserStr}, '%'))</if>
&lt;!&ndash;            <if test="flowHandleUser != null "> and t2.handle_user = #{flowHandleUser}</if>&ndash;&gt;
            <if test="overTrueDate != null"> and t1.complate_time &lt; #{overTrueDate}</if>
            <if test="overFalseDate != null"> and t1.complate_time &gt;= #{overFalseDate}</if>
            <if test="handleUserName != null and handleUserName != ''">AND hu.user_name LIKE concat('%', #{handleUserName}, '%')</if>
            <if test="applicationId != null "> and t1.application_id = #{applicationId}</if>
            <if test="workNo != null and workNo != ''"> and t1.work_no like concat('%', #{workNo}, '%')</if>
            <if test="applicationName != null and applicationName != ''"> and tba.asset_name like concat('%', #{applicationName}, '%')</if>
            <if test="loginUrl != null and loginUrl != ''"> and t1.login_url like concat('%', #{loginUrl}, '%')</if>
            <if test="eventLevel != null and eventLevel != ''"> and t1.event_level = #{eventLevel}</if>
            <if test="eventType != null and eventType != ''"> and t1.event_type = #{eventType}</if>
            <if test="startCreateTime != null">and t1.create_time &gt;= #{startCreateTime}</if>
            <if test="endCreateTime != null">and t1.create_time &lt;= #{endCreateTime}</if>
        </where>
        GROUP BY
        t1.id
        )
        ORDER BY update_time DESC
    </select>-->

    <select id="selectHaveDoneList" parameterType="TblWorkOrder" resultMap="TblWorkOrderResult">
        SELECT
            o.ID,
            o.work_type,
            application_id,
            work_name,
            o.event_type,
            o.complete_time,
            o.expect_complete_time,
            o.flow_state,
            hd.dept_name AS handleDeptName,
            hu.nick_name AS handleUserName,
            o.event_type,
            o.create_time,
            o.update_time,
            o.prod_id
        FROM
            tbl_work_order o
                LEFT JOIN ( SELECT * FROM tbl_work_history WHERE ID IN ( SELECT MAX( ID ) AS ID FROM tbl_work_history <where><if test="flowHandleUser != null">AND handle_user = #{flowHandleUser}</if></where> GROUP BY work_id ) ) h ON o.ID = h.work_id
                LEFT JOIN sys_dept hd ON o.handle_dept = hd.dept_id
                LEFT JOIN sys_user hu ON o.handle_user = hu.user_id
        <where>
            <if test="flowHandleUser != null">
                AND h.handle_user = #{flowHandleUser}
            </if>
            <if test="workName != null  and workName != ''"> and work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null  and workType != ''"> and o.work_type = #{workType}</if>
            <if test="overTrueDate != null"> and complate_time &lt; o.update_time</if>
            <if test="overFalseDate != null"> and complate_time &gt;= o.update_time</if>
            <if test="isOver != null">
                <if test="isOver == 1">
                    and o.complete_time &lt;= o.expect_complete_time
                </if>
                <if test="isOver == 0">
                    and (o.complete_time &gt; o.expect_complete_time OR (o.complete_time IS NULL and o.expect_complete_time &lt; NOW()))
                </if>
            </if>
            <if test="eventType != null and eventType != ''"> and o.event_type = #{eventType}</if>
            <if test="applicationId != null "> and o.application_id = #{applicationId}</if>
            <if test="handleUserName != null and handleUserName != ''">AND hu.user_name LIKE concat('%', #{handleUserName}, '%')</if>
            <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
            <if test="handleDeptStr != null and handleDeptStr != ''">and hd.dept_name like concat('%', #{handleDeptStr}, '%')</if>
            <if test="handleUser != null">and o.handle_user = #{handleUser}</if>
            <if test="handleUserStr != null and handleUserStr != ''">and (hu.nick_name like concat('%', #{handleUserStr}, '%') OR hu.user_name like concat('%', #{handleUserStr}, '%'))</if>
        </where>
            ORDER BY o.update_time desc
    </select>
    <select id="waitDoneCount" resultType="java.lang.Integer">
        select COUNT(1)
        from tbl_work_order o
        LEFT JOIN tbl_work_backlog b ON o.ID = b.work_id
        <where>
            <if test="workName != null  and workName != ''"> and work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null  and workType != ''"> and work_type = #{workType}</if>
            <if test="complateDateStart != null and complateDateEnd != null">
                and complate_time BETWEEN STR_TO_DATE(#{complateDateStart}, '%Y-%m-%d %H:%i:%s')
                AND STR_TO_DATE(#{complateDateEnd}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="handleUser != null "> and o.handle_user = #{handleUser}</if>
            <if test="waitDoneBy != null "> and b.handle_user = #{waitDoneBy}</if>
            <if test="overTrueDate != null"> and complate_time &lt; #{overTrueDate}</if>
            <if test="overFalseDate != null"> and complate_time &gt;= #{overFalseDate}</if>
            <if test="handleUserName != null and handleUserName != ''">AND hu.user_name LIKE concat('%', #{handleUserName}, '%')</if>
            <if test="applicationId != null "> and application_id = #{applicationId}</if>
        </where>
    </select>

    <select id="getCompleteCount" resultType="java.lang.Integer">
        <!--SELECT count(0) FROM tbl_work_order
        <where>
            complete_time is not null
            <if test="handleUser != null "> and handle_user = #{handleUser}</if>
        </where>-->
        SELECT
            COUNT(DISTINCT t1.id)
        FROM
            tbl_work_order t1
            LEFT JOIN tbl_work_history t2 ON t2.work_id = t1.id
        WHERE
            t2.handle_user = #{flowHandleUser} AND t2.handle_state != '0'
    </select>

    <select id="selectWorkOrderEfficiency" resultType="WorkOrderStatistics">
        SELECT
                (SELECT count(wr.id) FROM tbl_work_order wr) AS totalCount,
                (SELECT count(wr.id) FROM tbl_work_order wr WHERE wr.flow_state = 3) AS completeCount,
                (SELECT count(wr.id) FROM tbl_work_order wr WHERE wr.flow_state = 3 AND wr.update_time > wr.expect_complete_time) AS overcompleteCount,
                (SELECT count(wr.id) FROM tbl_work_order wr WHERE wr.flow_state NOT IN (3)) AS notOperateCount,
                (SELECT count(wr.id) FROM tbl_work_order wr WHERE wr.flow_state NOT IN (3) AND #{currentTime} > wr.expect_complete_time) AS overNotOperateCount
    </select>

    <select id="getStatisticsData" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
                (SELECT COUNT(distinct t1.id) FROM tbl_work_order t1 LEFT JOIN tbl_work_backlog t2 ON t2.work_id=t1.id
                LEFT JOIN tbl_work_history t3 ON t3.work_id=t1.id
                LEFT JOIN sys_dept hd ON t1.handle_dept = hd.dept_id
                WHERE
                t1.flow_state=-1
                and (
                1=1
                <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
                <if test="handleDeptIds != null and handleDeptIds.size()>0">
                    and (hd.dept_id IN
                    <foreach item="handleDeptIdItem" collection="handleDeptIds" open="(" separator="," close=")">
                        #{handleDeptIdItem}
                    </foreach>
                    )
                </if>
                <if test="(createBy != null and createBy != '') or (flowHandleUser != null) or (queryAll != null and queryAll == true)">
                    <if test="onlySelf != null and onlySelf==true">and</if> <if test="onlySelf == null or onlySelf!=true">or</if>(
                    1=2
                    <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
                    <if test="flowHandleUser != null "> OR (t2.handle_user = #{flowHandleUser} OR t3.handle_user = #{flowHandleUser})</if>
                    <if test="queryAll != null and queryAll == true"> OR 1=1</if>
                    )
                </if>
                )
                ) AS waitStartCount,
                (SELECT COUNT(distinct t1.id) FROM tbl_work_order t1 LEFT JOIN tbl_work_backlog t2 ON t2.work_id=t1.id
                LEFT JOIN tbl_work_history t3 ON t3.work_id=t1.id
                LEFT JOIN sys_dept hd ON t1.handle_dept = hd.dept_id
                WHERE
                t1.flow_state=1
                and (
                1=1
                <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
                <if test="handleDeptIds != null and handleDeptIds.size()>0">
                    and (hd.dept_id IN
                    <foreach item="handleDeptIdItem" collection="handleDeptIds" open="(" separator="," close=")">
                        #{handleDeptIdItem}
                    </foreach>
                    )
                </if>
                <if test="(createBy != null and createBy != '') or (flowHandleUser != null) or (queryAll != null and queryAll == true)">
                    <if test="onlySelf != null and onlySelf==true">and</if> <if test="onlySelf == null or onlySelf!=true">or</if>(
                    1=2
                    <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
                    <if test="flowHandleUser != null "> OR (t2.handle_user = #{flowHandleUser} OR t3.handle_user = #{flowHandleUser})</if>
                    <if test="queryAll != null and queryAll == true"> OR 1=1</if>
                    )
                </if>
                )
                ) AS waitHandleCount,
                (SELECT COUNT(distinct t1.id) FROM tbl_work_order t1 LEFT JOIN tbl_work_backlog t2 ON t2.work_id=t1.id
                LEFT JOIN tbl_work_history t3 ON t3.work_id=t1.id
                LEFT JOIN sys_dept hd ON t1.handle_dept = hd.dept_id
                WHERE
                (t1.flow_state=0 OR t1.flow_state=2)
                and (
                1=1
                <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
                <if test="handleDeptIds != null and handleDeptIds.size()>0">
                    and (hd.dept_id IN
                    <foreach item="handleDeptIdItem" collection="handleDeptIds" open="(" separator="," close=")">
                        #{handleDeptIdItem}
                    </foreach>
                    )
                </if>
                <if test="(createBy != null and createBy != '') or (flowHandleUser != null) or (queryAll != null and queryAll == true)">
                    <if test="onlySelf != null and onlySelf==true">and</if> <if test="onlySelf == null or onlySelf!=true">or</if>(
                    1=2
                    <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
                    <if test="flowHandleUser != null "> OR (t2.handle_user = #{flowHandleUser} OR t3.handle_user = #{flowHandleUser})</if>
                    <if test="queryAll != null and queryAll == true"> OR 1=1</if>
                    )
                </if>
                )
                ) AS waitAuditCount,
                (SELECT COUNT(distinct t1.id) FROM tbl_work_order t1 LEFT JOIN tbl_work_backlog t2 ON t2.work_id=t1.id
                LEFT JOIN tbl_work_history t3 ON t3.work_id=t1.id
                LEFT JOIN sys_dept hd ON t1.handle_dept = hd.dept_id
                WHERE
                t1.flow_state=3
                and (
                1=1
                <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
                <if test="handleDeptIds != null and handleDeptIds.size()>0">
                    and (hd.dept_id IN
                    <foreach item="handleDeptIdItem" collection="handleDeptIds" open="(" separator="," close=")">
                        #{handleDeptIdItem}
                    </foreach>
                    )
                </if>
                <if test="(createBy != null and createBy != '') or (flowHandleUser != null) or (queryAll != null and queryAll == true)">
                    <if test="onlySelf != null and onlySelf==true">and</if> <if test="onlySelf == null or onlySelf!=true">or</if>(
                    1=2
                    <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
                    <if test="flowHandleUser != null "> OR (t2.handle_user = #{flowHandleUser} OR t3.handle_user = #{flowHandleUser})</if>
                    <if test="queryAll != null and queryAll == true"> OR 1=1</if>
                    )
                </if>
                )
                ) AS waitCheckCount,
                (SELECT COUNT(distinct t1.id) FROM tbl_work_order t1 LEFT JOIN tbl_work_backlog t2 ON t2.work_id=t1.id
                LEFT JOIN tbl_work_history t3 ON t3.work_id=t1.id
                LEFT JOIN sys_dept hd ON t1.handle_dept = hd.dept_id
                WHERE
                t1.flow_state=4
                and (
                1=1
                <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
                <if test="handleDeptIds != null and handleDeptIds.size()>0">
                    and (hd.dept_id IN
                    <foreach item="handleDeptIdItem" collection="handleDeptIds" open="(" separator="," close=")">
                        #{handleDeptIdItem}
                    </foreach>
                    )
                </if>
                <if test="(createBy != null and createBy != '') or (flowHandleUser != null) or (queryAll != null and queryAll == true)">
                    <if test="onlySelf != null and onlySelf==true">and</if> <if test="onlySelf == null or onlySelf!=true">or</if>(
                    1=2
                    <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
                    <if test="flowHandleUser != null "> OR (t2.handle_user = #{flowHandleUser} OR t3.handle_user = #{flowHandleUser})</if>
                    <if test="queryAll != null and queryAll == true"> OR 1=1</if>
                    )
                </if>
                )
                ) AS doneCount
    </select>
    <select id="selectList" resultType="com.ruoyi.work.domain.TblWorkOrder" resultMap="TblWorkOrderResult">
        select
        t1.id,
        t1.work_no,
        t1.work_type,
        t1.event_ids,
        t1.work_name,
        t1.application_id,
        t1.login_url,
        t1.handle_dept,
        t1.handle_user,
        t1.handle_user_phone,
        t1.expect_complete_time,
        t1.event_source,
        t1.event_type,
        t1.event_level,
        t1.complete_time,
        t1.prod_Id,
        t1.complete_user,
        t1.update_time,
        t1.flow_state,
        t1.flow_state AS handle_state,
        t1.remark2 AS node_properties,
        t1.create_time,
        t1.urgency,
        t1.is_public,
        t1.severity_level,
        t1.report_date,
        t1.period,
        t1.signed,
        t1.proofread,
        t1.editor,
        t1.remark6,
        hd.dept_name AS handleDeptName,
        hu.nick_name AS handleUserName,
        tba.asset_name application_name
        from
        tbl_work_order t1
        left join tbl_work_backlog t2 ON t2.work_id = t1.id
        left join tbl_work_history t3 on t3.work_id = t1.id
        LEFT JOIN tbl_work_order_target wot ON wot.work_order_id = t1.id
        LEFT JOIN sys_dept hd ON wot.handle_dept = hd.dept_id
        LEFT JOIN sys_user hu ON wot.handle_user = hu.user_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id = wot.application_id
        <where>
            (
            1=1
            <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
            <if test="handleDeptIds != null and handleDeptIds.size()>0">
                and (hd.dept_id IN
                <foreach item="handleDeptIdItem" collection="handleDeptIds" open="(" separator="," close=")">
                    #{handleDeptIdItem}
                </foreach>
                )
            </if>
            <if test="(createBy != null and createBy != '') or (flowHandleUser != null) or (queryAll != null and queryAll == true)">
                <if test="onlySelf != null and onlySelf==true">and</if> <if test="onlySelf == null or onlySelf!=true">or</if>(
                1=2
                <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
                <if test="flowHandleUser != null "> OR (t2.handle_user = #{flowHandleUser} OR t3.handle_user = #{flowHandleUser})</if>
                <if test="queryAll != null and queryAll == true"> OR 1=1</if>
                )
            </if>
            )
            <if test="queryState != null">
                <if test="queryState == -1">
                    <!-- 待发起 -->
                    and t1.flow_state = -1
                </if>
                <if test="queryState == 1">
                    <!-- 待审核 -->
                    and (t1.flow_state = 0 OR t1.flow_state = 2)
                </if>
                <if test="queryState == 2">
                    <!-- 待处置 -->
                    and t1.flow_state = 1
                </if>
                <if test="queryState == 3">
                    <!-- 待核验 -->
                    and t1.flow_state = 3
                </if>
                <if test="queryState == 4">
                    <!-- 已完成 -->
                    and 1=2
                </if>
            </if>
            <if test="queryFlowStatus != null">
                <if test="queryFlowStatus == 0">
                    and (t1.flow_state = 0 OR t1.flow_state = 2)
                </if>
                <if test="queryFlowStatus == 1">
                    and (t1.flow_state = 1)
                </if>
                <if test="queryFlowStatus == 2">
                    and (t1.flow_state = 3)
                </if>
                <if test="queryFlowStatus == 3">
                    and (t1.flow_state = 4)
                </if>
                <if test="queryFlowStatus == -1">
                    and (t1.flow_state = -1)
                </if>
            </if>
            <if test="workName != null  and workName != ''"> and t1.work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null  and workType != ''"> and t1.work_type = #{workType}</if>
            <if test="flowState != null  and flowState != ''"> and t1.flow_state = #{flowState}</if>
            <if test="complateDateStart != null and complateDateEnd != null">
                and t1.complate_time BETWEEN STR_TO_DATE(#{complateDateStart}, '%Y-%m-%d %H:%i:%s')
                AND STR_TO_DATE(#{complateDateEnd}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
            <if test="handleDeptStr != null and handleDeptStr != ''">and hd.dept_name like concat('%', #{handleDeptStr}, '%')</if>
            <if test="handleUser != null">and wot.handle_user = #{handleUser}</if>
            <if test="handleUserStr != null and handleUserStr != ''">and (hu.nick_name like concat('%', #{handleUserStr}, '%') OR hu.user_name like concat('%', #{handleUserStr}, '%'))</if>
            <if test="overTrueDate != null"> and t1.complate_time &lt; #{overTrueDate}</if>
            <if test="overFalseDate != null"> and t1.complate_time &gt;= #{overFalseDate}</if>
            <if test="handleUserName != null and handleUserName != ''">AND hu.user_name LIKE concat('%', #{handleUserName}, '%')</if>
            <if test="applicationId != null "> and wot.application_id = #{applicationId}</if>
            <if test="workNo != null and workNo != ''"> and t1.work_no like concat('%', #{workNo}, '%')</if>
            <if test="applicationName != null and applicationName != ''"> and tba.asset_name like concat('%', #{applicationName}, '%')</if>
            <if test="loginUrl != null and loginUrl != ''"> and t1.login_url like concat('%', #{loginUrl}, '%')</if>
            <if test="eventLevel != null and eventLevel != ''"> and t1.event_level = #{eventLevel}</if>
            <if test="eventType != null and eventType != ''"> and t1.event_type = #{eventType}</if>
            <if test="startCreateTime != null">and t1.create_time &gt;= #{startCreateTime}</if>
            <if test="endCreateTime != null">and t1.create_time &lt;= #{endCreateTime}</if>
            <if test="currentUserId != null"> and t2.handle_user = #{currentUserId}</if>
            <if test="notCurrentUserId != null"> and (t2.handle_user != #{notCurrentUserId} OR t2.handle_user IS NULL)</if>
            <if test="urgency != null"> and t1.urgency = #{urgency}</if>
            <if test="isPublic != null and isPublic != ''"> and t1.is_public = #{isPublic}</if>
        </where>
        GROUP BY
        t1.id
        ORDER BY t1.update_time DESC
    </select>
    <!--<select id="selectList" resultType="com.ruoyi.work.domain.TblWorkOrder" resultMap="TblWorkOrderResult">
        (SELECT
        t1.id,
        t1.work_no,
        t1.work_type,
        t1.event_ids,
        t1.work_name,
        t1.application_id,
        t1.login_url,
        t1.handle_dept,
        t1.handle_user,
        t1.handle_user_phone,
        t1.expect_complete_time,
        t1.event_source,
        t1.event_type,
        t1.event_level,
        t1.complete_time,
        t1.prod_Id,
        t1.complete_user,
        t1.update_time,
        t1.flow_state,
        t1.flow_state AS handle_state,
        t1.remark2 AS node_properties,
        t1.create_time,
        hd.dept_name AS handleDeptName,
        hu.nick_name AS handleUserName,
        tba.asset_name application_name
        FROM
        tbl_work_order t1
        INNER JOIN tbl_work_backlog t2 ON t2.work_id = t1.id
        <if test="flowHandleUser != null">
            AND (
            t2.handle_user = #{flowHandleUser}
            <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
            )
        </if>
        LEFT JOIN sys_dept hd ON t1.handle_dept = hd.dept_id
        LEFT JOIN sys_user hu ON t1.handle_user = hu.user_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id = t1.application_id
        <where>
            (
            1=1
            <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
            <if test="handleDeptIds != null and handleDeptIds.size()>0">
                and (hd.dept_id IN
                <foreach item="handleDeptIdItem" collection="handleDeptIds" open="(" separator="," close=")">
                    #{handleDeptIdItem}
                </foreach>
                )
            </if>
            <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
            )
            <if test="queryState != null">
                <if test="queryState == -1">
                    &lt;!&ndash; 待发起 &ndash;&gt;
                    and t1.flow_state = -1
                </if>
                <if test="queryState == 1">
                    &lt;!&ndash; 待审核 &ndash;&gt;
                    and (t1.flow_state = 0 OR t1.flow_state = 2)
                </if>
                <if test="queryState == 2">
                    &lt;!&ndash; 待处置 &ndash;&gt;
                    and t1.flow_state = 1
                </if>
                <if test="queryState == 3">
                    &lt;!&ndash; 待核验 &ndash;&gt;
                    and t1.flow_state = 3
                </if>
                <if test="queryState == 4">
                    &lt;!&ndash; 已完成 &ndash;&gt;
                    and 1=2
                </if>
            </if>
            <if test="queryFlowStatus != null">
                <if test="queryFlowStatus == 0">
                    and (t1.flow_state = 0 OR t1.flow_state = 2)
                </if>
                <if test="queryFlowStatus == 1">
                    and (t1.flow_state = 1)
                </if>
                <if test="queryFlowStatus == 2">
                    and (t1.flow_state = 3)
                </if>
                <if test="queryFlowStatus == 3">
                    and (t1.flow_state = 4)
                </if>
            </if>
            <if test="workName != null  and workName != ''"> and t1.work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null  and workType != ''"> and t1.work_type = #{workType}</if>
            <if test="flowState != null  and flowState != ''"> and t1.flow_state = #{flowState}</if>
            <if test="complateDateStart != null and complateDateEnd != null">
                and t1.complate_time BETWEEN STR_TO_DATE(#{complateDateStart}, '%Y-%m-%d %H:%i:%s')
                AND STR_TO_DATE(#{complateDateEnd}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="handleDeptStr != null and handleDeptStr != ''">and hd.dept_name like concat('%', #{handleDeptStr}, '%')</if>
            <if test="handleUser != null">and t1.handle_user = #{handleUser}</if>
            <if test="handleUserStr != null and handleUserStr != ''">and (hu.nick_name like concat('%', #{handleUserStr}, '%') OR hu.user_name like concat('%', #{handleUserStr}, '%'))</if>
            <if test="flowHandleUser != null "> and t2.handle_user = #{flowHandleUser}</if>
            <if test="overTrueDate != null"> and t1.complate_time &lt; #{overTrueDate}</if>
            <if test="overFalseDate != null"> and t1.complate_time &gt;= #{overFalseDate}</if>
            <if test="handleUserName != null and handleUserName != ''">AND hu.user_name LIKE concat('%', #{handleUserName}, '%')</if>
            <if test="applicationId != null "> and t1.application_id = #{applicationId}</if>
            <if test="workNo != null and workNo != ''"> and t1.work_no like concat('%', #{workNo}, '%')</if>
            <if test="applicationName != null and applicationName != ''"> and tba.asset_name like concat('%', #{applicationName}, '%')</if>
            <if test="loginUrl != null and loginUrl != ''"> and t1.login_url like concat('%', #{loginUrl}, '%')</if>
            <if test="eventLevel != null and eventLevel != ''"> and t1.event_level = #{eventLevel}</if>
            <if test="eventType != null and eventType != ''"> and t1.event_type = #{eventType}</if>
            <if test="startCreateTime != null">and t1.create_time &gt;= #{startCreateTime}</if>
            <if test="endCreateTime != null">and t1.create_time &lt;= #{endCreateTime}</if>
            <if test="applicationId != null and applicationId != ''">and tba.asset_id=#{applicationId}</if>
        </where>
        GROUP BY
        t1.id)
        UNION ALL
        (
        SELECT
        t1.id,
        t1.work_no,
        t1.work_type,
        t1.event_ids,
        t1.work_name,
        t1.application_id,
        t1.login_url,
        t1.handle_dept,
        t1.handle_user,
        t1.handle_user_phone,
        t1.expect_complete_time,
        t1.event_source,
        t1.event_type,
        t1.event_level,
        t1.complete_time,
        t1.prod_Id,
        t1.complete_user,
        t1.update_time,
        4 AS flow_state,
        t2.handle_state,
        t2.node_properties,
        t1.create_time,
        hd.dept_name AS handleDeptName,
        hu.nick_name AS handleUserName,
        tba.asset_name application_name
        FROM
        tbl_work_order t1
        INNER JOIN ( SELECT t1.* FROM tbl_work_history t1 LEFT JOIN tbl_work_backlog t2 ON t2.work_id = t1.work_id
        <if test="flowHandleUser != null">
            AND (
            t2.handle_user = #{flowHandleUser}
            <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
            )
        </if>
        WHERE t1.ID IN ( SELECT MAX( ID ) AS ID FROM tbl_work_history <where><if test="flowHandleUser != null">AND t2.handle_user = #{flowHandleUser}</if></where> GROUP BY work_id ) AND t2.work_id IS NULL ) t2 ON t1.ID = t2.work_id
        LEFT JOIN sys_dept hd ON t1.handle_dept = hd.dept_id
        LEFT JOIN sys_user hu ON t1.handle_user = hu.user_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id = t1.application_id
        <where>
            (
            1=1
            <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
            <if test="handleDeptIds != null and handleDeptIds.size()>0">
                and (hd.dept_id IN
                <foreach item="handleDeptIdItem" collection="handleDeptIds" open="(" separator="," close=")">
                    #{handleDeptIdItem}
                </foreach>
                )
            </if>
            <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
            )
            <if test="queryState != null">
                <if test="queryState == 4">
                    and (1 = 1)
                </if>
                <if test="queryState != 4">
                    and (1 = 2)
                </if>
            </if>
            <if test="queryFlowStatus != null">
                <if test="queryFlowStatus == 0">
                    and (t1.flow_state = 2 OR t1.flow_state = 2)
                </if>
                <if test="queryFlowStatus == 1">
                    and (t1.flow_state = 1)
                </if>
                <if test="queryFlowStatus == 2">
                    and (t1.flow_state = 3)
                </if>
                <if test="queryFlowStatus == 3">
                    and (t1.flow_state = 4)
                </if>
            </if>
            <if test="workName != null  and workName != ''"> and t1.work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null  and workType != ''"> and t1.work_type = #{workType}</if>
            <if test="flowState != null  and flowState != ''"> and t1.flow_state = #{flowState}</if>
            <if test="complateDateStart != null and complateDateEnd != null">
                and t1.complate_time BETWEEN STR_TO_DATE(#{complateDateStart}, '%Y-%m-%d %H:%i:%s')
                AND STR_TO_DATE(#{complateDateEnd}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="handleDeptStr != null and handleDeptStr != ''">and hd.dept_name like concat('%', #{handleDeptStr}, '%')</if>
            <if test="handleUser != null">and t1.handle_user = #{handleUser}</if>
            <if test="handleUserStr != null and handleUserStr != ''">and (hu.nick_name like concat('%', #{handleUserStr}, '%') OR hu.user_name like concat('%', #{handleUserStr}, '%'))</if>
            <if test="flowHandleUser != null "> and t2.handle_user = #{flowHandleUser}</if>
            <if test="overTrueDate != null"> and t1.complate_time &lt; #{overTrueDate}</if>
            <if test="overFalseDate != null"> and t1.complate_time &gt;= #{overFalseDate}</if>
            <if test="handleUserName != null and handleUserName != ''">AND hu.user_name LIKE concat('%', #{handleUserName}, '%')</if>
            <if test="applicationId != null "> and t1.application_id = #{applicationId}</if>
            <if test="workNo != null and workNo != ''"> and t1.work_no like concat('%', #{workNo}, '%')</if>
            <if test="applicationName != null and applicationName != ''"> and tba.asset_name like concat('%', #{applicationName}, '%')</if>
            <if test="loginUrl != null and loginUrl != ''"> and t1.login_url like concat('%', #{loginUrl}, '%')</if>
            <if test="eventLevel != null and eventLevel != ''"> and t1.event_level = #{eventLevel}</if>
            <if test="eventType != null and eventType != ''"> and t1.event_type = #{eventType}</if>
            <if test="startCreateTime != null">and t1.create_time &gt;= #{startCreateTime}</if>
            <if test="endCreateTime != null">and t1.create_time &lt;= #{endCreateTime}</if>
            <if test="applicationId != null and applicationId != ''">and tba.asset_id=#{applicationId}</if>
        </where>
        GROUP BY
        t1.id
        )
        ORDER BY update_time DESC
    </select>-->
    <select id="selectCurrentHandleUser" resultType="com.ruoyi.common.core.domain.entity.SysUser">
        SELECT
            su.nick_name nickName,su.user_id userId
        FROM
            `tbl_work_backlog` t1
            INNER JOIN sys_user su ON su.user_id=t1.handle_user
        WHERE
            t1.work_id=#{id}
    </select>
    <select id="getDetails" resultType="com.ruoyi.work.domain.TblWorkOrder" resultMap="TblWorkOrderResult">
        select
            id,
            work_type,
            prod_Id,
            flow_state,
            flow_state AS handle_state,
            remark2 AS node_properties,
            create_time
        from
            tbl_work_order
        where
            id = #{id} OR prod_Id = #{id}
        limit 1
    </select>

    <select id="countThreatenNotificationNum" resultType="java.lang.Integer">
        SELECT
        COUNT( 1 )
        FROM
        tbl_work_order t1
        LEFT JOIN sys_dept t2 ON t1.handle_dept = t2.dept_id
        <where>
            <if test="startTime != null and endTime != null">
                and t1.create_time between #{startTime} and #{endTime}
            </if>
            <if test="deptId != 100">
                and (t2.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},t2.ancestors))
            </if>
        </where>
    </select>

    <select id="countWorkOrderNumByStatus" resultType="java.lang.Integer">
        SELECT
        COUNT(DISTINCT t1.id)
        FROM
        tbl_work_order t1
        LEFT JOIN tbl_work_backlog t2 ON t2.work_id = t1.id
        LEFT JOIN tbl_work_history t3 ON t3.work_id = t1.id
        LEFT JOIN sys_dept hd ON t1.handle_dept = hd.dept_id
        LEFT JOIN sys_user hu ON t1.handle_user = hu.user_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id = t1.application_id
        LEFT JOIN sys_user hu2 ON t1.remark5 = hu2.user_id
        <where>
            (
            1=1
            <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
            <if test="handleDeptIds != null and handleDeptIds.size()>0">
                and (hd.dept_id IN
                <foreach item="handleDeptIdItem" collection="handleDeptIds" open="(" separator="," close=")">
                    #{handleDeptIdItem}
                </foreach>
                )
            </if>
            <if test="(createBy != null and createBy != '') or (flowHandleUser != null) or (queryAll != null and queryAll == true)">
                <if test="onlySelf != null and onlySelf==true">and</if> <if test="onlySelf == null or onlySelf!=true">or</if>(
                1=2
                <if test="createBy != null and createBy != ''"> OR t1.remark5 = #{createBy}</if>
                <if test="flowHandleUser != null "> OR (t2.handle_user = #{flowHandleUser} OR t3.handle_user = #{flowHandleUser})</if>
                <if test="queryAll != null and queryAll == true"> OR 1=1</if>
                )
            </if>
            )
            <if test="queryState != null">
                <if test="queryState == -1">
                    <!-- 待发起 -->
                    and t1.flow_state = -1
                </if>
                <if test="queryState == 1">
                    <!-- 待审核 -->
                    and (t1.flow_state = 0 OR t1.flow_state = 2)
                </if>
                <if test="queryState == 2">
                    <!-- 待处置 -->
                    and t1.flow_state = 1
                </if>
                <if test="queryState == 3">
                    <!-- 待核验 -->
                    and t1.flow_state = 3
                </if>
                <if test="queryState == 4">
                    <!-- 已完成 -->
                    and t1.flow_state = 4
                </if>
            </if>
            <if test="workName != null  and workName != ''"> and t1.work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null  and workType != ''">
                <if test="workType != '-1'">
                    and t1.work_type = #{workType}
                </if>
                <if test="workType == '-1'">
                    and t1.work_type is null or t1.work_type = ''
                </if>
            </if>
            <if test="flowState != null  and flowState != '' and flowState != 99"> and t1.flow_state = #{flowState}</if>
            <if test="flowState == 99"> and t1.flow_state is null</if>
            <if test="findCarbonCopyStatus != null and findCarbonCopyStatus == 1">
                AND t3.type = 2 AND t3.handle_user = #{handleUser}
            </if>
            <if test="flowStateList != null and flowStateList.size() > 0">
                and t1.flow_state IN
                <foreach item="flowStateItem" collection="flowStateList" open="(" separator="," close=")">
                    #{flowStateItem}
                </foreach>
            </if>
            <if test="remark5 != null and remark5 != ''">
                and t1.remark5 = #{remark5}
            </if>
            <if test="complateDateStart != null and complateDateEnd != null">
                and t1.complate_time BETWEEN STR_TO_DATE(#{complateDateStart}, '%Y-%m-%d %H:%i:%s')
                AND STR_TO_DATE(#{complateDateEnd}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="handleDept != null">and (hd.dept_id = #{handleDept} OR FIND_IN_SET(#{handleDept}, hd.ancestors))</if>
            <if test="handleDeptStr != null and handleDeptStr != ''">and hd.dept_name like concat('%', #{handleDeptStr}, '%')</if>
            <!--<if test="handleUser != null and queryState == null">and t1.handle_user = #{handleUser}</if>-->
            <if test="handleUserStr != null and handleUserStr != ''">and (hu.nick_name like concat('%', #{handleUserStr}, '%') OR hu.user_name like concat('%', #{handleUserStr}, '%'))</if>
            <if test="overTrueDate != null"> and t1.complate_time &lt; #{overTrueDate}</if>
            <if test="overFalseDate != null"> and t1.complate_time &gt;= #{overFalseDate}</if>
            <if test="handleUserName != null and handleUserName != ''">AND hu.user_name LIKE concat('%', #{handleUserName}, '%')</if>
            <if test="applicationId != null "> and t1.application_id = #{applicationId}</if>
            <if test="workNo != null and workNo != ''"> and t1.work_no like concat('%', #{workNo}, '%')</if>
            <if test="applicationName != null and applicationName != ''"> and tba.asset_name like concat('%', #{applicationName}, '%')</if>
            <if test="loginUrl != null and loginUrl != ''"> and t1.login_url like concat('%', #{loginUrl}, '%')</if>
            <if test="eventLevel != null and eventLevel != ''"> and t1.event_level = #{eventLevel}</if>
            <if test="eventType != null and eventType != ''"> and t1.event_type = #{eventType}</if>
            <if test="startCreateTime != null">and t1.create_time &gt;= #{startCreateTime}</if>
            <if test="endCreateTime != null">and t1.create_time &lt;= #{endCreateTime}</if>
            <if test="currentUserId != null and currentUserId != 1"> and t2.handle_user = #{currentUserId}</if>
            <if test="notCurrentUserId != null"> and (t2.handle_user != #{notCurrentUserId} OR t2.handle_user IS NULL)</if>
            <if test="urgency != null"> and t1.urgency = #{urgency}</if>
            <if test="isPublic != null and isPublic != ''"> and t1.is_public = #{isPublic}</if>
            <!--<if test="queryState == null">
                <if test="handleUser != null">
                    AND t1.handle_user = #{handleUser}
                </if>
            </if>
            <if test="flowState != null and flowState != ''">
                AND t1.flow_state = #{flowState}
            </if>
            <if test="flowStateList != null">
                AND t1.flow_state IN
                <foreach collection="flowStateList" item="flowStateItem" open="(" separator="," close=")">
                    #{flowStateItem}
                </foreach>
            </if>
            <if test="remark5 != null and remark5 != ''">
                and t1.remark5 = #{remark5}
            </if>-->
        </where>
    </select>
</mapper>
