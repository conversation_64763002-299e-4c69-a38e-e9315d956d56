<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.work.mapper.WorkHwDayMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.work.model.WorkHwDayListVO">
        select a.* from work_hw_day a 
        <where>
                <if test="query.id!=null">
                    and a.id = #{query.id}
                </if>
                <if test="query.hwId!=null">
                    and a.hw_id = #{query.hwId}
                </if>
                <if test="query.date!=null and query.date!=''">
                    and a.date like concat('%', #{query.date}, '%')
                </if>
                <if test="query.dayWeek!=null and query.dayWeek!=''">
                    and a.day_week like concat('%', #{query.dayWeek}, '%')
                </if>
                <if test="query.isSafe!=null and query.isSafe!=''">
                    and a.is_safe like concat('%', #{query.isSafe}, '%')
                </if>
                <if test="query.remark!=null and query.remark!=''">
                    and a.remark like concat('%', #{query.remark}, '%')
                </if>
                <if test="query.createTime!=null">
                    and a.create_time = #{query.createTime}
                </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.work.model.WorkHwDayListVO">
        select a.* from work_hw_day a 
        where a.id=#{id}
    </select>
</mapper>
