<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.datainterface.mapper.DataDestNameMapper">

    <resultMap type="com.ruoyi.datainterface.domain.DataDestName" id="DataDestNameResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="cnName"    column="cn_name"    />
        <result property="status"    column="status"    />
        <result property="description"    column="description"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectDataDestNameVo">
        select id, name, cn_name, status, description, type from data_dest_name
    </sql>

    <select id="selectDataDestNameList" parameterType="DataDestName" resultMap="DataDestNameResult">
        <include refid="selectDataDestNameVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="cnName != null  and cnName != ''"> and cn_name like concat('%', #{cnName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="type != null "> and type = #{type}</if>
        </where>
    </select>

    <select id="selectDataDestNameById" parameterType="Long" resultMap="DataDestNameResult">
        <include refid="selectDataDestNameVo"/>
        where id = #{id}
    </select>

    <select id="selectDataDestNameByIds" parameterType="Long" resultMap="DataDestNameResult">
        <include refid="selectDataDestNameVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertDataDestName" parameterType="DataDestName" useGeneratedKeys="true" keyProperty="id">
        insert into data_dest_name
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="cnName != null and cnName != ''">cn_name,</if>
            <if test="status != null">status,</if>
            <if test="description != null">description,</if>
            <if test="type != null">type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="cnName != null and cnName != ''">#{cnName},</if>
            <if test="status != null">#{status},</if>
            <if test="description != null">#{description},</if>
            <if test="type != null">#{type},</if>
        </trim>
    </insert>

    <update id="updateDataDestName" parameterType="DataDestName">
        update data_dest_name
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="cnName != null and cnName != ''">cn_name = #{cnName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="description != null">description = #{description},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataDestNameById" parameterType="Long">
        delete from data_dest_name where id = #{id}
    </delete>

    <delete id="deleteDataDestNameByIds" parameterType="String">
        delete from data_dest_name where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>