{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\system\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\system\\user\\index.vue", "mtime": 1756369455997}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RVc2VyLCBnZXRVc2VyLCBkZWxVc2VyLCBhZGRVc2VyLCB1cGRhdGVVc2VyLCByZXNldFVzZXJQd2QsIGNoYW5nZVVzZXJTdGF0dXMsIGRlcHRUcmVlU2VsZWN0IH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOwppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlVzZXIiLAogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZScsICdzeXNfdXNlcl9zZXgnXSwKICBjb21wb25lbnRzOiB7IFRyZWVzZWxlY3QgfSwKICBwcm9wczp7CiAgICBkaXNhYmxlZDpmYWxzZSwKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzaG93QWxsOiBmYWxzZSwKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOeUqOaIt+ihqOagvOaVsOaNrgogICAgICB1c2VyTGlzdDogbnVsbCwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g6YOo6Zeo5qCR6YCJ6aG5CiAgICAgIGRlcHRPcHRpb25zOiB1bmRlZmluZWQsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g6YOo6Zeo5ZCN56ewCiAgICAgIGRlcHROYW1lOiB1bmRlZmluZWQsCiAgICAgIC8vIOm7mOiupOWvhueggQogICAgICBpbml0UGFzc3dvcmQ6IHVuZGVmaW5lZCwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOWyl+S9jemAiemhuQogICAgICBwb3N0T3B0aW9uczogW10sCiAgICAgIC8vIOinkuiJsumAiemhuQogICAgICByb2xlT3B0aW9uczogW10sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgZGVmYXVsdFByb3BzOiB7CiAgICAgICAgY2hpbGRyZW46ICJjaGlsZHJlbiIsCiAgICAgICAgbGFiZWw6ICJsYWJlbCIKICAgICAgfSwKICAgICAgLy8g55So5oi35a+85YWl5Y+C5pWwCiAgICAgIHVwbG9hZDogewogICAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgu+8iOeUqOaIt+WvvOWFpe+8iQogICAgICAgIG9wZW46IGZhbHNlLAogICAgICAgIC8vIOW8ueWHuuWxguagh+mimO+8iOeUqOaIt+WvvOWFpe+8iQogICAgICAgIHRpdGxlOiAiIiwKICAgICAgICAvLyDmmK/lkKbnpoHnlKjkuIrkvKAKICAgICAgICBpc1VwbG9hZGluZzogZmFsc2UsCiAgICAgICAgLy8g5piv5ZCm5pu05paw5bey57uP5a2Y5Zyo55qE55So5oi35pWw5o2uCiAgICAgICAgdXBkYXRlU3VwcG9ydDogMCwKICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gKICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkgfSwKICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYAKICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL3N5c3RlbS91c2VyL2ltcG9ydERhdGEiCiAgICAgIH0sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHVzZXJOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgcGhvbmVudW1iZXI6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZCwKICAgICAgICBkZXB0SWQ6IHVuZGVmaW5lZCwKICAgICAgICBuYW1lU2VhcmNoS2V5OiBudWxsLAogICAgICB9LAogICAgICAvLyDliJfkv6Hmga8KICAgICAgY29sdW1uczogWwogICAgICAgIHsga2V5OiAwLCBsYWJlbDogYOeUqOaIt+e8luWPt2AsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogMSwgbGFiZWw6IGDotKblj7dgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6IDIsIGxhYmVsOiBg55So5oi35aeT5ZCNYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiAzLCBsYWJlbDogYOmDqOmXqGAsIHZpc2libGU6IHRydWUgfSwKICAgICAgICB7IGtleTogNCwgbGFiZWw6IGDmiYvmnLrlj7fnoIFgLCB2aXNpYmxlOiB0cnVlIH0sCiAgICAgICAgeyBrZXk6IDUsIGxhYmVsOiBg54q25oCBYCwgdmlzaWJsZTogdHJ1ZSB9LAogICAgICAgIHsga2V5OiA2LCBsYWJlbDogYOWIm+W7uuaXtumXtGAsIHZpc2libGU6IHRydWUgfQogICAgICBdLAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICB1c2VyTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui0puWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBtaW46IDIsIG1heDogMjAsIG1lc3NhZ2U6ICfotKblj7fplb/luqblv4Xpobvku4vkuo4gMiDlkowgMjAg5LmL6Ze0JywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIGRlcHRJZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumDqOmXqOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgIF0sCiAgICAgICAgbmlja05hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlKjmiLflp5PlkI3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgcGFzc3dvcmQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlKjmiLflr4bnoIHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgbWluOiA1LCBtYXg6IDIwLCBtZXNzYWdlOiAn55So5oi35a+G56CB6ZW/5bqm5b+F6aG75LuL5LqOIDUg5ZKMIDIwIOS5i+mXtCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBlbWFpbDogWwogICAgICAgIHsKICAgICAgICAgICAgcmVxdWlyZWQ6IGZhbHNlLAogICAgICAgICAgICBtZXNzYWdlOiAi6YKu566x5Zyw5Z2A5LiN6IO95Li656m6IiwKICAgICAgICAgICAgdHJpZ2dlcjogWyJibHVyIiwgImNoYW5nZSJdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0eXBlOiAiZW1haWwiLAogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE6YKu566x5Zyw5Z2AIiwKICAgICAgICAgICAgdHJpZ2dlcjogWyJibHVyIiwgImNoYW5nZSJdCiAgICAgICAgICB9CiAgICAgICAgXSwKICAgICAgICBwaG9uZW51bWJlcjogWwogICAgICAgICAgewogICAgICAgICAgICByZXF1aXJlZDogZmFsc2UsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLmiYvmnLrlj7fnoIHkuI3og73kuLrnqboiLAogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHBhdHRlcm46IC9eMVszfDR8NXw2fDd8OHw5XVswLTldXGR7OH0kLywKICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOaJi+acuuWPt+eggSIsCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICAvLyDmoLnmja7lkI3np7DnrZvpgInpg6jpl6jmoJEKICAgIGRlcHROYW1lKHZhbCkgewogICAgICB0aGlzLiRyZWZzLnRyZWUuZmlsdGVyKHZhbCk7CiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLmdldERlcHRUcmVlKCk7CiAgICAvLyBpZighdGhpcy5kaXNhYmxlZCkKICAgICAgLy8gdGhpcy5nZXRDb25maWdLZXkoInN5cy51c2VyLmluaXRQYXNzd29yZCIpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAvLyAgIHRoaXMuaW5pdFBhc3N3b3JkID0gcmVzcG9uc2UubXNnOwogICAgICAvLyB9KTsKICB9LAoKICBtZXRob2RzOiB7CiAgICBnZXRDaGVja2VkTm9kZXMoKXsKICAgICAgaWYodGhpcy5kaXNhYmxlZCl7CiAgICAgICAgbGV0IGN1cnJlbnROb2RlID0gdGhpcy4kcmVmcy50cmVlLmdldEN1cnJlbnROb2RlKCk7CiAgICAgICAgdGhpcy4kZW1pdCgiZGVwdFNlbGVjdCIsY3VycmVudE5vZGUpOwogICAgICAgIHRoaXMuJHJlZnMudGFibGUuY2xlYXJTZWxlY3Rpb24oKTsKICAgICAgfQogICAgfSwKICAgIHNldENoZWNrZWROb2RlcyhrZXkscm93KXsKICAgICAgaWYoa2V5KSB0aGlzLiRyZWZzLnRyZWUuc2V0Q3VycmVudEtleShrZXkpOwogICAgICBpZihyb3cpIHRoaXMuJHJlZnMudGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvdyx0cnVlKTsKICAgIH0sCiAgICAvKiog5p+l6K+i55So5oi35YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0VXNlcih0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIGlmKHRoaXMuZGlzYWJsZWQpewogICAgICAgICAgICB0aGlzLiRlbWl0KCJkYXRhRG93biIsdGhpcy51c2VyTGlzdCx0aGlzLnF1ZXJ5UGFyYW1zKQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgKTsKICAgIH0sCgogICAgLyoqIOafpeivoumDqOmXqOS4i+aLieagkee7k+aehCAqLwogICAgZ2V0RGVwdFRyZWUoKSB7CiAgICAgIGRlcHRUcmVlU2VsZWN0KCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5kZXB0T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgaWYodGhpcy5kaXNhYmxlZCl7CiAgICAgICAgICB0aGlzLiRlbWl0KCJkZXB0U2VsZWN0Iix0aGlzLmRlcHRPcHRpb25zWzBdKQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g562b6YCJ6IqC54K5CiAgICBmaWx0ZXJOb2RlKHZhbHVlLCBkYXRhKSB7CiAgICAgIGlmICghdmFsdWUpIHJldHVybiB0cnVlOwogICAgICByZXR1cm4gZGF0YS5sYWJlbC5pbmRleE9mKHZhbHVlKSAhPT0gLTE7CiAgICB9LAogICAgLy8g6IqC54K55Y2V5Ye75LqL5Lu2CiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgewogICAgICBpZih0aGlzLmRpc2FibGVkKXsKICAgICAgICB0aGlzLiRlbWl0KCJkZXB0U2VsZWN0IixkYXRhKQogICAgICB9CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGVwdElkID0gZGF0YS5pZDsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwoKICAgIH0sCiAgICAvLyDnlKjmiLfnirbmgIHkv67mlLkKICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsKICAgICAgbGV0IHRleHQgPSByb3cuc3RhdHVzID09PSAiMCIgPyAi5ZCv55SoIiA6ICLlgZznlKgiOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoEiJyArIHRleHQgKyAnIiInICsgcm93LnVzZXJOYW1lICsgJyLnlKjmiLflkJfvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiBjaGFuZ2VVc2VyU3RhdHVzKHJvdy51c2VySWQsIHJvdy5zdGF0dXMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uKCkgewogICAgICAgIHJvdy5zdGF0dXMgPSByb3cuc3RhdHVzID09PSAiMCIgPyAiMSIgOiAiMCI7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICB1c2VySWQ6IHVuZGVmaW5lZCwKICAgICAgICBkZXB0SWQ6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyTmFtZTogdW5kZWZpbmVkLAogICAgICAgIG5pY2tOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgcGFzc3dvcmQ6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLAogICAgICAgIGVtYWlsOiB1bmRlZmluZWQsCiAgICAgICAgc2V4OiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiAiMCIsCiAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQsCiAgICAgICAgcG9zdElkczogW10sCiAgICAgICAgcm9sZUlkczogW10KICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXTsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIGlmKCF0aGlzLmRpc2FibGVkKXsKICAgICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLnVzZXJJZCk7CiAgICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7CiAgICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgICB9CiAgICB9LAogICAgc2VsZWN0KHNlbGVjdGlvbixyb3cpewogICAgICBpZih0aGlzLmRpc2FibGVkKXsKICAgICAgICB0aGlzLiRyZWZzLnRhYmxlLmNsZWFyU2VsZWN0aW9uKCk7CiAgICAgICAgaWYoIXNlbGVjdGlvbi5sZW5ndGgpewogICAgICAgICAgdGhpcy4kZW1pdCgidXNlclNlbGVjdCIsbnVsbCkKICAgICAgICB9ZWxzZSB7CiAgICAgICAgICB0aGlzLiRyZWZzLnRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihyb3csdHJ1ZSkKICAgICAgICAgIHRoaXMuJGVtaXQoInVzZXJTZWxlY3QiLHJvdykKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICBzZWxlY3RBbGwoKXsKICAgICAgaWYodGhpcy5kaXNhYmxlZCkKICAgICAgICB0aGlzLiRyZWZzLnRhYmxlLmNsZWFyU2VsZWN0aW9uKCk7CiAgICB9LAogICAgLy8g5pu05aSa5pON5L2c6Kem5Y+RCiAgICBoYW5kbGVDb21tYW5kKGNvbW1hbmQsIHJvdykgewogICAgICBzd2l0Y2ggKGNvbW1hbmQpIHsKICAgICAgICBjYXNlICJoYW5kbGVSZXNldFB3ZCI6CiAgICAgICAgICB0aGlzLmhhbmRsZVJlc2V0UHdkKHJvdyk7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICJoYW5kbGVBdXRoUm9sZSI6CiAgICAgICAgICB0aGlzLmhhbmRsZUF1dGhSb2xlKHJvdyk7CiAgICAgICAgICBicmVhazsKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgYnJlYWs7CiAgICAgIH0KICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgZ2V0VXNlcigpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMucG9zdE9wdGlvbnMgPSByZXNwb25zZS5wb3N0czsKICAgICAgICB0aGlzLnJvbGVPcHRpb25zID0gcmVzcG9uc2Uucm9sZXM7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOeUqOaItyI7CiAgICAgICAgdGhpcy5mb3JtLnBhc3N3b3JkID0gdGhpcy5pbml0UGFzc3dvcmQ7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCB1c2VySWQgPSByb3cudXNlcklkIHx8IHRoaXMuaWRzOwogICAgICBnZXRVc2VyKHVzZXJJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLnBvc3RPcHRpb25zID0gcmVzcG9uc2UucG9zdHM7CiAgICAgICAgdGhpcy5yb2xlT3B0aW9ucyA9IHJlc3BvbnNlLnJvbGVzOwogICAgICAgIHRoaXMuZm9ybS5wb3N0SWRzID0gcmVzcG9uc2UucG9zdElkczsKICAgICAgICB0aGlzLmZvcm0ucm9sZUlkcyA9IHJlc3BvbnNlLnJvbGVJZHM7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueeUqOaItyI7CiAgICAgICAgdGhpcy5mb3JtLnBhc3N3b3JkID0gIiI7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDph43nva7lr4bnoIHmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVJlc2V0UHdkKHJvdykgewogICAgICB0aGlzLiRwcm9tcHQoJ+ivt+i+k+WFpSInICsgcm93LnVzZXJOYW1lICsgJyLnmoTmlrDlr4bnoIEnLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICBjbG9zZU9uQ2xpY2tNb2RhbDogZmFsc2UsCiAgICAgICAgaW5wdXRQYXR0ZXJuOiAvXi57NSwyMH0kLywKICAgICAgICBpbnB1dEVycm9yTWVzc2FnZTogIueUqOaIt+WvhueggemVv+W6puW/hemhu+S7i+S6jiA1IOWSjCAyMCDkuYvpl7QiCiAgICAgIH0pLnRoZW4oKHsgdmFsdWUgfSkgPT4gewogICAgICAgICAgcmVzZXRVc2VyUHdkKHJvdy51c2VySWQsIHZhbHVlKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5Yqf77yM5paw5a+G56CB5piv77yaIiArIHZhbHVlKTsKICAgICAgICAgIH0pOwogICAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvKiog5YiG6YWN6KeS6Imy5pON5L2cICovCiAgICBoYW5kbGVBdXRoUm9sZTogZnVuY3Rpb24ocm93KSB7CiAgICAgIGNvbnN0IHVzZXJJZCA9IHJvdy51c2VySWQ7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCIvc3lzdGVtL3VzZXItYXV0aC9yb2xlLyIgKyB1c2VySWQpOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uKCkgewogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS51c2VySWQgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgIHVwZGF0ZVVzZXIodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRVc2VyKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCB1c2VySWRzID0gcm93LnVzZXJJZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk55So5oi357yW5Y+35Li6IicgKyB1c2VySWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiBkZWxVc2VyKHVzZXJJZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnc3lzdGVtL3VzZXIvZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYHVzZXJfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApCiAgICB9LAogICAgLyoqIOWvvOWFpeaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlSW1wb3J0KCkgewogICAgICB0aGlzLnVwbG9hZC50aXRsZSA9ICLnlKjmiLflr7zlhaUiOwogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5LiL6L295qih5p2/5pON5L2cICovCiAgICBpbXBvcnRUZW1wbGF0ZSgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnc3lzdGVtL3VzZXIvaW1wb3J0VGVtcGxhdGUnLCB7CiAgICAgIH0sIGB1c2VyX3RlbXBsYXRlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfSwKICAgIC8vIOaWh+S7tuS4iuS8oOS4reWkhOeQhgogICAgaGFuZGxlRmlsZVVwbG9hZFByb2dyZXNzKGV2ZW50LCBmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IHRydWU7CiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5oiQ5Yqf5aSE55CGCiAgICBoYW5kbGVGaWxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IGZhbHNlOwogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCk7CiAgICAgIHRoaXMuJGFsZXJ0KCI8ZGl2IHN0eWxlPSdvdmVyZmxvdzogYXV0bztvdmVyZmxvdy14OiBoaWRkZW47bWF4LWhlaWdodDogNzB2aDtwYWRkaW5nOiAxMHB4IDIwcHggMDsnPiIgKyByZXNwb25zZS5tc2cgKyAiPC9kaXY+IiwgIuWvvOWFpee7k+aenCIsIHsgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiB0cnVlIH0pOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDmj5DkuqTkuIrkvKDmlofku7YKICAgIHN1Ym1pdEZpbGVGb3JtKCkgewogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5zdWJtaXQoKTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgXA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user", "sourcesContent": ["<template>\n  <div :class=\"{'custom-container':!disabled}\">\n    <div class=\"custom-tree-container\">\n      <div class=\"head-container\">\n        <el-input\n          v-model=\"deptName\"\n          placeholder=\"请输入部门名称\"\n          clearable\n          size=\"medium\"\n          prefix-icon=\"el-icon-search\"\n          style=\"margin-bottom: 15px\"\n        />\n      </div>\n      <div class=\"head-container\">\n        <el-tree\n          :data=\"deptOptions\"\n          :props=\"defaultProps\"\n          :expand-on-click-node=\"false\"\n          :filter-node-method=\"filterNode\"\n          node-key=\"id\"\n          ref=\"tree\"\n          default-expand-all\n          highlight-current\n          @node-click=\"handleNodeClick\"\n        />\n      </div>\n    </div>\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          v-show=\"showSearch\"\n          label-position=\"right\"\n          label-width=\"100px\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"账号/昵称\" prop=\"nameSearchKey\">\n                <el-input\n                  v-model=\"queryParams.nameSearchKey\"\n                  placeholder=\"请输入账号或昵称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n                <el-input\n                  v-model=\"queryParams.phonenumber\"\n                  placeholder=\"请输入手机号码\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"状态\" prop=\"status\">\n                <el-select\n                  v-model=\"queryParams.status\"\n                  placeholder=\"用户状态\"\n                  clearable\n                >\n                  <el-option\n                    v-for=\"dict in dict.type.sys_normal_disable\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"创建时间\">\n                <el-date-picker\n                  v-model=\"dateRange\"\n                  value-format=\"yyyy-MM-dd\"\n                  type=\"daterange\"\n                  range-separator=\"-\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                ></el-date-picker>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">用户管理列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\" v-if=\"!disabled\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleAdd\"\n                  v-hasPermi=\"['system:user:add']\"\n                >新增</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  size=\"small\"\n                  class=\"btn1\"\n                  :disabled=\"multiple\"\n                  @click=\"handleDelete\"\n                  v-hasPermi=\"['system:user:remove']\"\n                >批量删除</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  size=\"small\"\n                  class=\"btn1\"\n                  @click=\"handleImport\"\n                  v-hasPermi=\"['system:user:import']\"\n                >导入</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                  v-hasPermi=\"['system:user:export']\"\n                >导出</el-button>\n              </el-col>\n<!--              <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\" :columns=\"columns\"></right-toolbar>-->\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          v-loading=\"loading\"\n          :data=\"userList\"\n          ref=\"table\"\n          height=\"100%\"\n          @selection-change=\"handleSelectionChange\"\n          @select=\"select\" @select-all=\"selectAll\">\n          <el-table-column type=\"selection\" width=\"50\"  />\n<!--          <el-table-column label=\"用户编号\"  key=\"userId\" prop=\"userId\" v-if=\"columns[0].visible\" />-->\n          <el-table-column label=\"账号\"  key=\"userName\" prop=\"userName\" v-if=\"columns[1].visible\" :show-overflow-tooltip=\"true\" />\n          <el-table-column label=\"用户姓名\"  key=\"nickName\" prop=\"nickName\" v-if=\"columns[2].visible\" :show-overflow-tooltip=\"true\" />\n          <el-table-column label=\"角色\"  key=\"roleNameStr\" prop=\"roleNameStr\" v-if=\"columns[2].visible\" :show-overflow-tooltip=\"true\" />\n          <el-table-column label=\"部门\"  key=\"deptNameFull\" prop=\"deptNameFull\" v-if=\"columns[3].visible\" :show-overflow-tooltip=\"true\" />\n          <el-table-column label=\"手机号码\"  key=\"phonenumber\" prop=\"phonenumber\" v-if=\"columns[4].visible\" width=\"120\" />\n          <el-table-column label=\"状态\"  key=\"status\" v-if=\"columns[5].visible\">\n            <template slot-scope=\"scope\">\n              <el-switch\n                v-if=\"!disabled\"\n                v-model=\"scope.row.status\"\n                active-value=\"0\"\n                inactive-value=\"1\"\n                @change=\"handleStatusChange(scope.row)\"\n              ></el-switch>\n              <el-tag v-else-if=\"scope.row.status\" type=\"success\">启用</el-tag>\n              <el-tag v-else type=\"danger\">停用</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"创建时间\"  prop=\"createTime\" v-if=\"columns[6].visible\" width=\"160\">\n            <template slot-scope=\"scope\">\n              <span>{{ parseTime(scope.row.createTime) }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column\n            v-if=\"!disabled\"\n            label=\"操作\"\n            fixed=\"right\"\n            :show-overflow-tooltip=\"false\"\n            width=\"160\"\n            class-name=\"small-padding fixed-width\"\n          >\n            <template slot-scope=\"scope\" v-if=\"scope.row.userId !== 1\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleUpdate(scope.row)\"\n                v-hasPermi=\"['system:user:edit']\"\n              >编辑</el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                class=\"table-delBtn\"\n                @click=\"handleDelete(scope.row)\"\n                v-hasPermi=\"['system:user:remove']\"\n              >删除</el-button>\n              <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['system:user:resetPwd', 'system:user:edit']\">\n                <span class=\"el-dropdown-link\">\n                  <i class=\"el-icon-d-arrow-right el-icon--right\"></i>更多\n                </span>\n                <el-dropdown-menu slot=\"dropdown\">\n                  <el-dropdown-item command=\"handleResetPwd\" icon=\"el-icon-key\"\n                                    v-hasPermi=\"['system:user:resetPwd']\">重置密码</el-dropdown-item>\n                  <el-dropdown-item command=\"handleAuthRole\" icon=\"el-icon-circle-check\"\n                                    v-hasPermi=\"['system:user:edit']\">分配角色</el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <!-- 添加或修改用户配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item v-if=\"form.userId !== undefined\" label=\"账号\" prop=\"userName\">\n              <el-input v-model=\"form.userName\" placeholder=\"\" maxlength=\"30\" readonly :disabled=\"true\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户姓名\" prop=\"nickName\">\n              <el-input v-model=\"form.nickName\" placeholder=\"请输入用户姓名\" maxlength=\"30\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"归属部门\" prop=\"deptId\">\n              <treeselect v-model=\"form.deptId\" :options=\"deptOptions\" :show-count=\"true\" placeholder=\"请选择归属部门\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n              <el-input v-model=\"form.phonenumber\" placeholder=\"请输入手机号码\" maxlength=\"11\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"邮箱\" prop=\"email\">\n              <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.userId == undefined\" label=\"账号\" prop=\"userName\">\n              <el-input v-model=\"form.userName\" placeholder=\"请输入账号\" maxlength=\"30\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.userId == undefined\" label=\"用户密码\" prop=\"password\">\n              <el-input v-model=\"form.password\" placeholder=\"请输入用户密码\" type=\"password\" maxlength=\"20\" show-password/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用户性别\">\n              <el-select v-model=\"form.sex\" placeholder=\"请选择性别\">\n                <el-option\n                  v-for=\"dict in dict.type.sys_user_sex\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_normal_disable\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"岗位\">\n              <el-select v-model=\"form.postIds\" multiple placeholder=\"请选择岗位\">\n                <el-option\n                  v-for=\"item in postOptions\"\n                  :key=\"item.postId\"\n                  :label=\"item.postName\"\n                  :value=\"item.postId\"\n                  :disabled=\"item.status == 1\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"角色\">\n              <el-select v-model=\"form.roleIds\" multiple placeholder=\"请选择角色\">\n                <el-option\n                  v-for=\"item in roleOptions\"\n                  :key=\"item.roleId\"\n                  :label=\"item.roleName\"\n                  :value=\"item.roleId\"\n                  :disabled=\"item.status == 1\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 用户导入对话框 -->\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-upload\n        ref=\"upload\"\n        :limit=\"1\"\n        accept=\".xlsx, .xls\"\n        :headers=\"upload.headers\"\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\n        :disabled=\"upload.isUploading\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        :auto-upload=\"false\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\n          <div class=\"el-upload__tip\" slot=\"tip\">\n            <el-checkbox v-model=\"upload.updateSupport\" /> 是否更新已经存在的用户数据\n          </div>\n          <span>仅允许导入xls、xlsx格式文件。</span>\n          <el-link type=\"primary\" :underline=\"false\" style=\"font-size:12px;vertical-align: baseline;\" @click=\"importTemplate\">下载模板</el-link>\n        </div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus, deptTreeSelect } from \"@/api/system/user\";\nimport { getToken } from \"@/utils/auth\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\n\nexport default {\n  name: \"User\",\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\n  components: { Treeselect },\n  props:{\n    disabled:false,\n  },\n  data() {\n    return {\n      showAll: false,\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 用户表格数据\n      userList: null,\n      // 弹出层标题\n      title: \"\",\n      // 部门树选项\n      deptOptions: undefined,\n      // 是否显示弹出层\n      open: false,\n      // 部门名称\n      deptName: undefined,\n      // 默认密码\n      initPassword: undefined,\n      // 日期范围\n      dateRange: [],\n      // 岗位选项\n      postOptions: [],\n      // 角色选项\n      roleOptions: [],\n      // 表单参数\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"label\"\n      },\n      // 用户导入参数\n      upload: {\n        // 是否显示弹出层（用户导入）\n        open: false,\n        // 弹出层标题（用户导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 是否更新已经存在的用户数据\n        updateSupport: 0,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/system/user/importData\"\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userName: undefined,\n        phonenumber: undefined,\n        status: undefined,\n        deptId: undefined,\n        nameSearchKey: null,\n      },\n      // 列信息\n      columns: [\n        { key: 0, label: `用户编号`, visible: true },\n        { key: 1, label: `账号`, visible: true },\n        { key: 2, label: `用户姓名`, visible: true },\n        { key: 3, label: `部门`, visible: true },\n        { key: 4, label: `手机号码`, visible: true },\n        { key: 5, label: `状态`, visible: true },\n        { key: 6, label: `创建时间`, visible: true }\n      ],\n      // 表单校验\n      rules: {\n        userName: [\n          { required: true, message: \"账号不能为空\", trigger: \"blur\" },\n          { min: 2, max: 20, message: '账号长度必须介于 2 和 20 之间', trigger: 'blur' }\n        ],\n        deptId: [\n          { required: true, message: \"部门不能为空\", trigger: \"blur\" },\n        ],\n        nickName: [\n          { required: true, message: \"用户姓名不能为空\", trigger: \"blur\" }\n        ],\n        password: [\n          { required: true, message: \"用户密码不能为空\", trigger: \"blur\" },\n          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }\n        ],\n        email: [\n        {\n            required: false,\n            message: \"邮箱地址不能为空\",\n            trigger: [\"blur\", \"change\"]\n          },\n          {\n            type: \"email\",\n            message: \"请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"]\n          }\n        ],\n        phonenumber: [\n          {\n            required: false,\n            message: \"手机号码不能为空\",\n            trigger: \"blur\"\n          },\n          {\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\"\n          }\n        ]\n      }\n    };\n  },\n  watch: {\n    // 根据名称筛选部门树\n    deptName(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    this.getList();\n    this.getDeptTree();\n    // if(!this.disabled)\n      // this.getConfigKey(\"sys.user.initPassword\").then(response => {\n      //   this.initPassword = response.msg;\n      // });\n  },\n\n  methods: {\n    getCheckedNodes(){\n      if(this.disabled){\n        let currentNode = this.$refs.tree.getCurrentNode();\n        this.$emit(\"deptSelect\",currentNode);\n        this.$refs.table.clearSelection();\n      }\n    },\n    setCheckedNodes(key,row){\n      if(key) this.$refs.tree.setCurrentKey(key);\n      if(row) this.$refs.table.toggleRowSelection(row,true);\n    },\n    /** 查询用户列表 */\n    getList() {\n      this.loading = true;\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.userList = response.rows;\n          this.total = response.total;\n          this.loading = false;\n          if(this.disabled){\n            this.$emit(\"dataDown\",this.userList,this.queryParams)\n          }\n        }\n      );\n    },\n\n    /** 查询部门下拉树结构 */\n    getDeptTree() {\n      deptTreeSelect().then(response => {\n        this.deptOptions = response.data;\n        if(this.disabled){\n          this.$emit(\"deptSelect\",this.deptOptions[0])\n        }\n      });\n    },\n    // 筛选节点\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    // 节点单击事件\n    handleNodeClick(data) {\n      if(this.disabled){\n        this.$emit(\"deptSelect\",data)\n      }\n      this.queryParams.deptId = data.id;\n      this.handleQuery();\n\n    },\n    // 用户状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.userName + '\"用户吗？').then(function() {\n        return changeUserStatus(row.userId, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function() {\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        userId: undefined,\n        deptId: undefined,\n        userName: undefined,\n        nickName: undefined,\n        password: undefined,\n        phonenumber: undefined,\n        email: undefined,\n        sex: undefined,\n        status: \"0\",\n        remark: undefined,\n        postIds: [],\n        roleIds: []\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      if(!this.disabled){\n        this.ids = selection.map(item => item.userId);\n        this.single = selection.length != 1;\n        this.multiple = !selection.length;\n      }\n    },\n    select(selection,row){\n      if(this.disabled){\n        this.$refs.table.clearSelection();\n        if(!selection.length){\n          this.$emit(\"userSelect\",null)\n        }else {\n          this.$refs.table.toggleRowSelection(row,true)\n          this.$emit(\"userSelect\",row)\n        }\n      }\n    },\n    selectAll(){\n      if(this.disabled)\n        this.$refs.table.clearSelection();\n    },\n    // 更多操作触发\n    handleCommand(command, row) {\n      switch (command) {\n        case \"handleResetPwd\":\n          this.handleResetPwd(row);\n          break;\n        case \"handleAuthRole\":\n          this.handleAuthRole(row);\n          break;\n        default:\n          break;\n      }\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      getUser().then(response => {\n        this.postOptions = response.posts;\n        this.roleOptions = response.roles;\n        this.open = true;\n        this.title = \"添加用户\";\n        this.form.password = this.initPassword;\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const userId = row.userId || this.ids;\n      getUser(userId).then(response => {\n        this.form = response.data;\n        this.postOptions = response.posts;\n        this.roleOptions = response.roles;\n        this.form.postIds = response.postIds;\n        this.form.roleIds = response.roleIds;\n        this.open = true;\n        this.title = \"修改用户\";\n        this.form.password = \"\";\n      });\n    },\n    /** 重置密码按钮操作 */\n    handleResetPwd(row) {\n      this.$prompt('请输入\"' + row.userName + '\"的新密码', \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        closeOnClickModal: false,\n        inputPattern: /^.{5,20}$/,\n        inputErrorMessage: \"用户密码长度必须介于 5 和 20 之间\"\n      }).then(({ value }) => {\n          resetUserPwd(row.userId, value).then(response => {\n            this.$modal.msgSuccess(\"修改成功，新密码是：\" + value);\n          });\n        }).catch(() => {});\n    },\n    /** 分配角色操作 */\n    handleAuthRole: function(row) {\n      const userId = row.userId;\n      this.$router.push(\"/system/user-auth/role/\" + userId);\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.userId != undefined) {\n            updateUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const userIds = row.userId || this.ids;\n      this.$modal.confirm('是否确认删除用户编号为\"' + userIds + '\"的数据项？').then(function() {\n        return delUser(userIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('system/user/export', {\n        ...this.queryParams\n      }, `user_${new Date().getTime()}.xlsx`)\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"用户导入\";\n      this.upload.open = true;\n    },\n    /** 下载模板操作 */\n    importTemplate() {\n      this.download('system/user/importTemplate', {\n      }, `user_template_${new Date().getTime()}.xlsx`)\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$alert(\"<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>\" + response.msg + \"</div>\", \"导入结果\", { dangerouslyUseHTMLString: true });\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    }\n  }\n};\n</script>\n<style lang=\"scss\" scoped>\n::v-deep .el-tree {\n  width: 100%;\n  height: calc(100vh - 265px);\n  overflow-y: auto;\n}\n::v-deep .el-tree-node__content{\n  height: 48px;\n  font-size: 14px\n}\n::v-deep .el-tree-node__content:hover{\n  background-color: #f5f5f5;\n}\n::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content{\n  line-height: 48px;\n  color: #333333;\n  font-weight: bold;\n  background-color: #f5f5f5;\n}\n</style>\n"]}]}