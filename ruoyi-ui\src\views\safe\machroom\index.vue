<template>
  <div class="custom-container">
    <div class="custom-tree-container" v-if="children&&children.length">
      <type-tree :tree-date="typeTreeData" @nodeChange="nodeChange"></type-tree>
    </div>
    <div class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-form :model="queryParams" ref="queryForm" label-position="right" :inline="true" label-width="70px">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="资产编码" prop="assetCode">
                <el-input
                  v-model="queryParams.assetCode"
                  placeholder="请输入资产编码"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="机房名称" prop="assetName">
                <el-input
                  v-model="queryParams.assetName"
                  placeholder="请输入机房名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="重要程度" prop="degreeImportance">
                <el-select v-model="queryParams.degreeImportance" placeholder="请选择重要程度" clearable>
                  <el-option
                    v-for="dict in dict.type.impt_grade"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="custom-search-btn">
                <el-button class="btn1" size="small" @click="handleQuery">查询</el-button>
                <el-button class="btn2" size="small" @click="resetQuery">重置</el-button>
                <el-button class="btn2" size="small" icon="el-icon-arrow-down" @click="showAll=true" v-if="!showAll">
                  展开
                </el-button>
                <el-button class="btn2" size="small" icon="el-icon-arrow-up" @click="showAll=false" v-else>收起
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="showAll" :gutter="10">
            <el-col :span="6">
              <el-form-item label="所属部门" prop="deptId">
                <dept-select v-model="queryParams.deptId"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="custom-content-container">
        <div class="common-header">
          <div><span class="common-head-title">物理机房列表</span></div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleAdd"
                  v-hasPermi="['safe:machroom:add']"
                >新增
                </el-button>
              </el-col>
              <!--              <el-col :span="1.5">-->
              <!--                <el-button-->
              <!--                  plain-->
              <!--                  size="small"-->
              <!--                  :disabled="single"-->
              <!--                  @click="handleUpdate"-->
              <!--                  v-hasPermi="['safe:machroom:edit']"-->
              <!--                >修改-->
              <!--                </el-button>-->
              <!--              </el-col>-->
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  :disabled="multiple"
                  @click="handleDelete"
                  v-hasPermi="['safe:machroom:remove']"
                >批量删除
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleExport"
                  v-hasPermi="['safe:machroom:export']"
                >导出
                </el-button>
              </el-col>
              <!--              <right-toolbar :showSearch.sync="showSearch" :columns="columns" @queryTable="getList"></right-toolbar>-->
            </el-row>
          </div>
        </div>
        <el-table v-loading="loading" :data="machroomList" @selection-change="handleSelectionChange"
                  @sort-change="sortChange">
          <el-table-column type="selection" width="55"/>
          <el-table-column label="资产编码" align="left" prop="assetCode" v-if="columns[0].visible"
                           show-overflow-tooltip/>
          <el-table-column label="机房名称" align="left" prop="assetName" v-if="columns[1].visible"
                           show-overflow-tooltip/>
          <el-table-column label="所属部门" prop="deptName" v-if="columns[2].visible" show-overflow-tooltip/>
          <el-table-column label="重要程度" prop="degreeImportance" v-if="columns[3].visible">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.impt_grade" :value="scope.row.degreeImportance"/>
            </template>
          </el-table-column>
          <el-table-column label="物理位置" prop="locationFullName" v-if="columns[4].visible" show-overflow-tooltip/>
          <el-table-column label="详细地址" prop="locationDetail" v-if="columns[5].visible" show-overflow-tooltip/>
          <!--<el-table-column label="责任人"  prop="mangerName" v-if="columns[4].visible" />-->
          <!--<el-table-column label="联系电话"  prop="phone" v-if="columns[5].visible" />-->
          <el-table-column label="供应商" prop="vendorName" v-if="columns[6].visible" show-overflow-tooltip/>
          <el-table-column label="备注" prop="remark" v-if="columns[7].visible" show-overflow-tooltip/>
          <el-table-column label="操作" width="200" fixed="right" :show-overflow-tooltip="false"
                           class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['safe:machroom:edit']"
              >编辑
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="handleUpdate(scope.row,false)"
                v-hasPermi="['safe:machroom:list']"
              >查看
              </el-button>
              <el-button
                size="mini"
                type="text"
                class="table-delBtn"
                @click="handleDelete(scope.row)"
                v-hasPermi="['safe:machroom:remove']"
              >删除
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="handleChart(scope.row)"
              >详情
              </el-button>
              <!--              <option-lab>
                              <template>
                                <el-button
                                  size="mini"
                                  type="text"
                                  @click="handleUpdate(scope.row,false)"
                                  v-hasPermi="['safe:machroom:list']"
                                >查看
                                </el-button>
                                <el-button
                                  size="mini"
                                  type="text"
                                  @click="handleUpdate(scope.row)"
                                  v-hasPermi="['safe:machroom:edit']"
                                >编辑
                                </el-button>
                                <el-button
                                  size="mini"
                                  type="text"
                                  @click="handleDelete(scope.row)"
                                  v-hasPermi="['safe:machroom:remove']"
                                >删除
                                </el-button>
                                <el-button
                                  size="mini"
                                  type="text"
                                  @click="handleChart(scope.row)"
                                >详情
                                </el-button>
                              </template>
                            </option-lab>-->

            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 添加或修改机房管理对话框! -->
    <el-dialog v-if="open" title="机房管理" :visible.sync="open" width="900px" append-to-body>
      <el-tabs type="card" v-model="editItem">
        <el-tab-pane :lazy="true" :label="title" name="edit">
          <el-row v-if="title != '查看机房'" :gutter="10">
            <el-form ref="form" :model="form" :rules="rules">
              <el-col :span="12">
                <el-form-item label="资产编码" prop="assetCode">
                  <el-input :disabled="!editable" v-model="form.assetCode" placeholder="请输入资产编码"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="机房名称" prop="assetName">
                  <el-input :disabled="!editable" v-model="form.assetName" placeholder="请输入机房名称"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="重要程度" prop="degreeImportance">
                  <el-select :disabled="!editable" v-model="form.degreeImportance" placeholder="请选择重要程度"
                             clearable>
                    <el-option
                      v-for="dict in dict.type.impt_grade"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="物理位置" prop="locationId">
                  <!--<el-input v-model="form.location" placeholder="请输入物理位置"/>-->
                  <!--<location-select :disabled="!editable" v-model="form.locationId"/>-->
                  <el-select :disabled="!editable" v-model="form.locationId" placeholder="请选择所属位置">
                    <el-option
                      v-for="dict in locationOptions"
                      :key="dict.locationId"
                      :label="dict.locationFullName"
                      :value="dict.locationId"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="详细地址" prop="locationDetail">
                  <el-input :disabled="!editable" v-model="form.locationDetail" placeholder="请输入详细地址"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属部门" prop="deptId">
                  <dept-select v-model="form.deptId" :is-disabled="!editable" is-current/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="children&&children.length" label="资产类型" prop="assetClass">
                  <el-select :disabled="!editable" v-model="form.assetType" placeholder="请选择资产类型"
                             @change="assetTypeChange">
                    <el-option
                      v-for="children in children"
                      :key="children.id"
                      :label="children.typeName"
                      :value="children.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="供应商" prop="vendor">
                  <el-input :disabled="!editable" :value="form.vendorName" @focus="showVendorDialog"
                            placeholder="请输入供应商"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="资产标签" prop="tags">
                  <dynamic-tag :disabled="!editable" v-model="form.tags"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input :disabled="!editable" v-model="form.remark" type="textarea" placeholder="请输入内容"/>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
          <div v-else class="customForm-container">
            <el-descriptions
              class="custom-column"
              direction="vertical"
              size="medium"
              :colon="false"
              label-class-name="custom-label-style"
              content-class-name="custom-content-style"
              :column="2">
              <el-descriptions-item label="资产编号">
                {{ form.assetCode }}
              </el-descriptions-item>
              <el-descriptions-item label="机房名称">
                {{ form.assetName }}
              </el-descriptions-item>
              <el-descriptions-item label="重要程度">
                <span
                  v-for="(item, index) in dict.type.impt_grade"
                  :key="index"
                  v-if="item.value === form.degreeImportance"
                >{{ item.label }}
          </span>
              </el-descriptions-item>
              <el-descriptions-item label="物理位置">
                {{ form.locationFullName }}
                <span
                  v-for="(item, index) in locationOptions"
                  :key="index"
                  v-if="item.locationId === form.locationId"
                >{{ item.locationFullName }}
          </span>
              </el-descriptions-item>
              <el-descriptions-item label="详细地址">
                {{ form.locationDetail }}
              </el-descriptions-item>
              <el-descriptions-item label="所属部门">
                <span
                  v-for="(item, index) in deptOptions"
                  :key="item.id"
                  v-if="item.id === form.deptId"
                >{{ item.label }}
          </span>
              </el-descriptions-item>
              <el-descriptions-item label="资产类型">
                <span
                  v-for="(item, index) in children"
                  :key="item.id"
                  v-if="item.id === form.assetType"
                >{{ item.typeName }}
          </span>
              </el-descriptions-item>
              <el-descriptions-item label="供应商">
                {{ form.vendorName }}
              </el-descriptions-item>
              <el-descriptions-item label="资产标签">
                <span
                  v-for="(item, index) in form.tags"
                  :key="index"
                >{{ item }}
          </span>
              </el-descriptions-item>
              <el-descriptions-item label="备注">
                {{ form.remark }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>
        <el-tab-pane v-if="form.assetId" :lazy="true" v-hasPermi="['safe:management:list']" label="管理信息"
                     name="manager">
          <manager :disabled="!editable" :assetId="form.assetId" @closeManagement="closeManagement"></manager>
        </el-tab-pane>
        <!--<el-tab-pane v-if="form.assetId" :lazy="true" v-hasPermi="['safe:uploadfile:list']" label="资产图片" name="file">-->
        <!--  <upload-file-table :disabled="!editable" :asset-id="form.assetId"/>-->
        <!--</el-tab-pane>-->
        <el-tab-pane v-if="form.assetId" :lazy="true" label="相关文件" name="file">
          <asset-file :disabled="!editable" :asset-id="form.assetId"/>
        </el-tab-pane>
        <el-tab-pane v-if="form.assetId" :lazy="true" label="财务信息" name="procurement">
          <procurement :disabled="!editable" :asset-id="form.assetId"></procurement>
        </el-tab-pane>
        <el-tab-pane v-if="form.assetId" :lazy="true" label="驻场信息" name="companyEmp">
          <company-emp :disabled="!editable" :asset-id="form.assetId"></company-emp>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="editable && editItem === 'edit'" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!--    <el-dialog title="选择用户" :visible.sync="userDialog" width="900px" append-to-body>-->
    <!--      <user-select @confirm="selectConfirm" @cancel="userCancel"></user-select>-->
    <!--    </el-dialog>-->
    <el-dialog title="选择供应商" :visible.sync="vendorDialog" width="900px" append-to-body>
      <vendor-select v-if="vendorDialog" @confirm="selectConfirm" @cancel="vendorCancel"></vendor-select>
    </el-dialog>
    <!--    <el-dialog title="责任人列表" width="900px" :visible.sync="managementDialog" append-to-body>-->
    <!--      <manager :assetId="assetId" @closeManagement="closeManagement"></manager>-->
    <!--    </el-dialog>-->
    <!--    <el-dialog title="资产文件" width="900px" :visible.sync="fileDialog" append-to-body>-->
    <!--      <upload-file-table :asset-id="currentAssetId"/>-->
    <!--      <div slot="footer" class="dialog-footer">-->
    <!--        <el-button type="primary" @click="fileDialog=false;">关 闭</el-button>-->
    <!--      </div>-->
    <!--    </el-dialog>-->

    <networkDeviceDetail
      :asset-id="assetId"
      asset-name="物理机房详情"
      asset-allocation-type="5"
      :device-detail-visible.sync="deviceDetailVisible"/>
  </div>
</template>

<script>
import {listMachroom, getMachroom, delMachroom, addMachroom, updateMachroom} from "@/api/safe/machroom";
import {listDept, listDeptExcludeChild} from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {getAllDeptTree, listUser} from "@/api/system/user";
import {getAssetTypeChildrenByid} from "@/api/safe/overview";
import userSelect from "@/views/components/select/userSelect";
import typeTree from "@/views/components/typeTree";
import vendorSelect from "@/views/components/select/vendorSelect";
import manager from "@/views/components/manager";
import LocationSelect from "@/views/components/select/locationSelect";
import DeptSelect from "@/views/components/select/deptSelect";
import {listLocation} from "@/api/dict/location";
import uploadFileTable from "@/views/components/table/uploadFileTable";
import procurement from "@/views/safe/procurement";
import companyEmp from "@/views/monitor2/companyEmp";
import OptionLab from "@/views/components/optionLab";
import AssetFile from "@/views/dimension/file";
import DynamicTag from '@/components/DynamicTag/index.vue';
import NetworkDeviceDetail from "@/views/safe/networkdevices/networkDeviceDetail.vue";
import {deptTreeSelect} from "@/api/system/role";

export default {
  name: "Machroom",
  components: {
    NetworkDeviceDetail,
    DynamicTag,
    Treeselect,
    userSelect,
    typeTree,
    vendorSelect,
    manager,
    LocationSelect,
    DeptSelect,
    uploadFileTable,
    procurement,
    companyEmp,
    OptionLab,
    AssetFile
  },
  dicts: ["impt_grade"],
  data() {
    return {
      showAll: false,
      classId: 1,
      className: '物理机房',
      typeClass: {},
      //分类树
      typeTreeData: [],
      typelist: [],
      children: [],
      // 表格树数据
      deptList: [],
      // 部门树选项
      deptOptions: [],
      // 位置信息树选项
      locationOptions: [],
      //用户选择
      userDialog: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      currentNames: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 机房管理表格数据
      machroomList: [],
      assetId: '',
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 产商选择
      vendorDialog: false,
      //责任人
      managementDialog: false,
      //文件
      fileDialog: false,
      //用户
      userList: [],
      userqueryParams: {
        isAsc: undefined,
        orderByColumn: undefined,
        pageNum: 1,
        pageSize: 30,
      },
      // 查询参数
      queryParams: {
        isAsc: undefined,
        orderByColumn: undefined,
        pageNum: 1,
        pageSize: 10,
        assetCode: null,
        assetName: null,
        degreeImportance: null,
        assetClass: null,
        location: null,
        userId: null,
        deptId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        assetName: [
          {required: true, message: "机房名称不能为空", trigger: "blur"},
          {min: 0, max: 64, message: '机房名称不能超过 64 个字符', trigger: 'blur'},
        ],
        locationDetail: [
          {min: 0, max: 128, message: '详细地址不能超过 128 个字符', trigger: 'blur'},
        ],
      },
      columns: [
        {key: 0, label: '资产编码', visible: true},
        {key: 1, label: '机房名称', visible: true},
        {key: 2, label: '所属部门', visible: true},
        {key: 3, label: '重要程度', visible: true},
        {key: 4, label: '物理位置', visible: true},
        {key: 5, label: '详细地址', visible: true},
        // {key: 4, label: '责任人', visible: false},
        // {key: 5, label: '联系电话', visible: false},
        {key: 6, label: '供应商', visible: true},
        {key: 7, label: '备注', visible: false},

      ],
      currentAssetId: null,
      editItem: 'edit',
      editable: true,
      deviceDetailVisible: false
    };
  },
  mounted() {
    this.init();
    this.getDeptTree();
    this.getLocationTreeselect();
  },
  watch: {
    '$route'(route) {
      if (route.name === "Machroom") {
        this.init();
      }
    }
  },
  methods: {
    init() {
      getAssetTypeChildrenByid(this.classId).then(res => {
        this.typelist = res.rows;
        let item = this.typelist.find(item => {
          return item.typeName == this.className
        })
        if (item) {
          this.typeClass.assetClass = item.id;
          this.typeClass.assetClassDesc = item.typeName;
        }
        for (let i = 0; i < this.typelist.length; i++) {
          if (this.typelist[i].typeGradedProtection == 1) {
            this.typelist.splice(Number(i), 1);
            i = i - 1;
          }
        }
        this.typeTreeData = this.handleTree(this.typelist, 'id', 'pid');
        this.children = this.typeTreeData[0].children;
      })
      this.queryParams = {...this.queryParams, ...this.$route.params};
      // console.log(this.queryParams)
      this.getList();
    },
    /** 查询所属部门 */
    getDeptTree() {
      getAllDeptTree().then(response => {
        this.deptOptions = response.data;
      });
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      };
    },
    /**
     * 树节点该变时
     */
    nodeChange(node) {
      this.queryParams.assetType = null;
      if (node.pid != 0) {
        this.queryParams.assetType = node.id;
      }
      this.getList();
    },
    /**
     * 资产类型变化
     */
    assetTypeChange(data) {
      let item = this.children.find(item => {
        return item.id == data;
      })
      if (item) {
        this.form.assetTypeDesc = item.typeName;
      }
    },
    /**
     * 点击选择用户
     */
    userSelect() {
      this.userDialog = true;
    },
    /**
     * 选择供应商
     */
    showVendorDialog() {
      if (this.editable)
        this.vendorDialog = true
    },
    /**
     * 选择供应商
     */
    selectConfirm(vendor) {

      this.form.vendor = vendor.id;
      this.form.vendorName = vendor.vendorName;
      this.vendorDialog = false;
    },
    /**
     * 取消选择
     */
    vendorCancel() {
      this.vendorDialog = false;
    },
    /**
     * 取消选择
     */
    userCancel() {
      this.userDialog = false;
    },
    /**
     * 选择责任人
     */
    showManagement(row) {
      this.assetId = row.assetId;
      this.managementDialog = true;
    },
    /**
     * 关闭选择责任人
     */
    closeManagement() {
      this.managementDialog = false;
    },
    //获取人员数据
    getTableData() {
    },
    //关闭用户窗口
    closeUserDialog() {
      this.userDialog = false;
    },
    //打开用户选择窗口
    showUserDialog(val) {
      this.dialogName = val
      this.userDialog = true
    },
    //排序
    sortChange(column, prop, order) {
      if (column.order != null) {
        this.queryParams.isAsc = 'desc';
      } else {
        this.queryParams.isAsc = 'asc';
      }
      this.queryParams.orderByColumn = column.prop;
      this.getList(this.queryParams);
    },
    /** 查询机房管理列表 */
    getList() {
      this.loading = true;
      listMachroom(this.queryParams).then(response => {
        this.machroomList = response.rows;
        this.total = Number(response.total);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        assetId: null,
        assetCode: null,
        assetName: null,
        location: null,
        degreeImportance: null,
        manger: null,
        phone: null,
        vendor: null,
        assetType: null,
        assetTypeDesc: null,
        assetClass: 1,
        assetClassDesc: "物理机房",
        locationId: null,
        tags: [],
      };
      this.editItem = 'edit';
      this.editable = true;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.assetId)
      this.currentNames = selection.map(item => item.assetName);
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.$nextTick(() => {
        Object.assign(this.form, this.queryParams);
        this.open = true;
        this.title = "添加机房";
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row, edit = true) {
      this.reset();
      this.editable = edit;
      const assetId = row.assetId || this.ids
      getMachroom(assetId).then(response => {
        this.form = response.data;
        this.form.locationId = Number(response.data.locationId);
        this.getLocationTreeselect();
        this.open = true;
        this.title = (edit ? "修改" : "查看") + "机房";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.form = {...this.form, ...this.typeClass}
      this.$refs["form"].validate(valid => {
        if (valid && this.editable) {
          if (this.form.assetId != null) {
            updateMachroom(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMachroom(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const assetIds = row.assetId || this.ids;
      let assetsName = "";
      if (!row.assetId) {
        assetsName = this.currentNames.join(",");
      } else {
        assetsName = row.assetName;
      }

      this.$modal.confirm('是否确认删除【' + assetsName + '】的数据项？').then(function () {
        return delMachroom(assetIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('safe/machroom/export', {
        ...this.queryParams
      }, `machroom_${new Date().getTime()}.xlsx`)
    },
    /** 转换位置信息数据结构 */
    normalizerLocation(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.locationId,
        label: node.locationName,
        children: node.children
      };
    },
    /** 查询位置信息下拉树结构 */
    getLocationTreeselect() {
      listLocation().then(response => {
        this.locationOptions = response.data.filter((e) => {
          return e.locationType === "1"
        });
        // this.locationOptions = this.handleTree(response.data, "locationId", "parentId");
      });
    },
    // 资产关系图
    handleChart(row) {
      // const assetId = row.assetId;
      // const assetName = row.assetName;
      // this.$tab.openPage("[" + assetName + "]资产详情", '/safe/asset/details/index/' + assetId);
      // this.$tab.openPage("[" + assetName + "]资产关系图", '/safe/asset/chart/index/' + assetId);
      this.assetId = row.assetId;
      this.deviceDetailVisible = true;
    },
    //资产文件
    handleFile(row) {
      this.currentAssetId = row.assetId;
      this.fileDialog = true;
    }
  }
};
</script>
<style scoped lang="scss">
@import "@/assets/styles/customForm";

.small-padding {
  padding-left: 0;
  padding-right: 0;
  width: 150px;
}

::v-deep .small-padding .cell {
  overflow: visible !important;
}

.operate {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
}

::v-deep .el-table {
  display: flex;
  flex-direction: column;
}

::v-deep .el-table__body-wrapper {
  overflow-y: auto;
  flex: 1;
}

::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  background: rgba(48, 111, 229, 0.8);
  color: #FFFFFF;
}

::v-deep .el-tabs__content {
  overflow-x: hidden;
}
</style>
