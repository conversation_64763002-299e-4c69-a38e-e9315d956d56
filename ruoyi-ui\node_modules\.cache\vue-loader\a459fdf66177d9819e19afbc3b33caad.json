{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\applicationDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\applicationDetails.vue", "mtime": 1756369456053}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7Z2V0VmFsRnJvbU9iamVjdH0gZnJvbSAiQC91dGlscyI7CmltcG9ydCB7Z2V0QXBwbGljYXRpb24sIGNoZWNrQXBwbGljYXRpb24sYXVkaXRDb25maWd9IGZyb20gIkAvYXBpL3NhZmUvYXBwbGljYXRpb24iOwppbXBvcnQgYXNzZXRSZWdpc3RlciBmcm9tICJAL21peGlucy9hc3NldFJlZ2lzdGVyIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiYXBwbGljYXRpb25JbmZvIiwKICBtaXhpbnM6IFthc3NldFJlZ2lzdGVyXSwKICBjb21wb25lbnRzOiB7CiAgICBTeXN0ZW1EZXRhaWxzOiAoKSA9PiBpbXBvcnQoJ0Avdmlld3MvaGhsQ29kZS9jb21wb25lbnQvc3lzdGVtRGV0YWlscycpLAogICAgRmlyZXdhbGxOYXRJbmRleDogKCkgPT4gaW1wb3J0KCdAL3ZpZXdzL3NhZmUvc2FmZXR5L2ZpcmV3YWxsTmF0SW5kZXgnKSwKICAgIEJ1c2luZXNzR2FwOiAoKSA9PiBpbXBvcnQoJy4uL2J1c2luZXNzR2FwJyksCiAgICBBcHBsaWNhdGlvbkZvcm06ICgpID0+IGltcG9ydCgnQC92aWV3cy9oaGxDb2RlL2NvbXBvbmVudC9hcHBsaWNhdGlvbi9hcHBsaWNhdGlvbkZvcm0nKSwKICAgIEFwcGxpY2F0aW9uSGFyZHdhcmU6ICgpID0+IGltcG9ydCgnQC92aWV3cy9oaGxDb2RlL2NvbXBvbmVudC9hcHBsaWNhdGlvbi9hcHBsaWNhdGlvbkhhcmR3YXJlJyksCiAgICBidXNpbmVzc0FwcGxpY2F0aW9uU3RhdHVzOiAoKSA9PiBpbXBvcnQoJ0Avdmlld3MvaGhsQ29kZS9jb21wb25lbnQvYnVzaW5lc3NBcHBsaWNhdGlvblN0YXR1cycpLAogICAgT3BlcmF0aW9uU3lzdGVtRGV0YWlsczogKCkgPT4gaW1wb3J0KCdAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L09wZXJhdGlvblN5c3RlbURldGFpbHMnKSwKICB9LAogIHByb3BzOiB7CiAgICBhcHBsaWNhdGlvblZpc2libGU6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICB0aXRsZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAogICAgcGFyYW1zOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogKCkgPT4ge30sCiAgICB9LAogIH0sCiAgcHJvdmlkZSgpIHsKICAgIHJldHVybiB7CiAgICAgICRlZGl0YWJsZTogdGhpcy5zaG93RGF0YQogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRhYk5hbWU6ICdiYXNlJywKICAgICAgc2hvd0RhdGE6IHt2YWx1ZTogdHJ1ZX0sCiAgICAgIGVkaXRhYmxlOiBmYWxzZSwKICAgICAgYXNzZXRJZDogbnVsbCwKICAgICAgYXBwbGljYXRpb246IG51bGwsCiAgICAgIG9wZW46IGZhbHNlLAogICAgICBjb250ZW50OiBudWxsLAogICAgICByZWY6IFsnYmFzZScsICdidXNpbmVzcycsICdjb21wb25lbnQnXSwvL+aPkOS6pOe7hOS7tgogICAgICBndjogZ2V0VmFsRnJvbU9iamVjdCwKICAgICAgcnVsZXM6IHt9LAogICAgICB3aGV0aGVyT3JOb3RUb0F1ZGl0OiBmYWxzZSwKICAgICAgaXNTaG93R2FwOiB0cnVlLAogICAgICBhZnRlckJ0bkxvYWQ6IGZhbHNlLAogICAgICBpc1RlbXBTYXZlOiBmYWxzZSwKICAgICAgYXNzZXRBbGxvY2F0aW9uVHlwZTogJzEnLAogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGRpYWxvZ1Zpc2libGU6IHsKICAgICAgZ2V0KCkgewogICAgICAgIHJldHVybiB0aGlzLmFwcGxpY2F0aW9uVmlzaWJsZQogICAgICB9LAogICAgICBzZXQodmFsdWUpIHsKICAgICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6YXBwbGljYXRpb25WaXNpYmxlJywgdmFsdWUpCiAgICAgIH0KICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICBhdWRpdENvbmZpZyh7CiAgICAgIHBhZ2VOdW06IDEsCiAgICAgIHBhZ2VTaXplOiAxMAogICAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgIHRoaXMud2hldGhlck9yTm90VG9BdWRpdCA9IHJlc3BvbnNlLnJvd3NbMF0uY29uZmlnVmFsdWUgIT09ICdmYWxzZSc7CiAgICB9KQogIH0sCiAgbW91bnRlZCgpIHt9LAoKICBtZXRob2RzOiB7CiAgICBjaGFuZ2VJZChpZCkgewogICAgICB0aGlzLmFzc2V0SWQgPSBpZDsKICAgIH0sCiAgICAvLyDkv53lrZgKICAgIC8vIGFzeW5jIGhhbmRsZVNhdmUoKSB7CiAgICAvLyAgIGF3YWl0IHRoaXMuJHJlZnNbdGhpcy50YWJOYW1lXS5oYW5kbGVTYXZlKCkudGhlbigoKT0+ewogICAgLy8gICAgIHRoaXMuJGVtaXQoJ2FwcGxpY2F0aW9uQ2hhbmdlJywgdHJ1ZSk7CiAgICAvLyAgIH0pOwogICAgLy8gfSwKCiAgICAvLyDkuIvkuIDmraXvvIjkv53lrZjvvIkKICAgIGFzeW5jIGhhbmRsZUFmdGVyKCkgewogICAgICAvLyBpZiAodGhpcy5pc1Nob3dHYXApIHsKICAgICAgLy8gICBpZiAodGhpcy5hc3NldElkICE9IG51bGwpIHRoaXMudGFiTmFtZSA9IHRoaXMucmVmW3RoaXMucmVmLmluZGV4T2YodGhpcy50YWJOYW1lKSArIDFdOwogICAgICAvLyAgIHJldHVybiB0cnVlOwogICAgICAvLyB9CiAgICAgIGlmICghdGhpcy5hc3NldElkKSB7CiAgICAgICAgLy8g5paw5aKe5pe277yM5qOA6aqM5Z+65pys5L+h5oGv5b+F5aGr6aG5CiAgICAgICAgaWYgKCF0aGlzLiRyZWZzWydiYXNlJ10udmFsaWRhdGVGb3JtKCkpIHJldHVybiB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+35YWI5b2V5YWl5Z+65pys5L+h5oGvISIpOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOe8lui+keaXtu+8jOajgOmqjOWfuuacrOS/oeaBr+W/heWhq+mhuQogICAgICAgIGlmICh0aGlzLnRhYk5hbWUgPT09ICdiYXNlJykgaWYgKCF0aGlzLiRyZWZzWydiYXNlJ10udmFsaWRhdGVGb3JtKCkpIHJldHVybiB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+35YWI5b2V5YWl5Z+65pys5L+h5oGvISIpOwogICAgICB9CiAgICAgIHRoaXMuYWZ0ZXJCdG5Mb2FkID0gdHJ1ZTsKICAgICAgYXdhaXQgdGhpcy4kcmVmc1t0aGlzLnRhYk5hbWVdLmhhbmRsZVNhdmUoKS50aGVuKCgpPT57CiAgICAgICAgdGhpcy4kZW1pdCgnYXBwbGljYXRpb25DaGFuZ2UnLCBmYWxzZSk7CiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gewogICAgICAgIHRoaXMuYWZ0ZXJCdG5Mb2FkID0gZmFsc2U7CiAgICAgICAgdGhpcy4kZW1pdCgnZGVwdFNlbGVjdEtleUNoYW5nZScsIHRydWUpOwogICAgICB9KTsKICAgICAgLy8gaWYgKHRoaXMuYXNzZXRJZCAhPSBudWxsKSB0aGlzLnRhYk5hbWUgPSB0aGlzLnJlZlt0aGlzLnJlZi5pbmRleE9mKHRoaXMudGFiTmFtZSkgKyAxXQogICAgfSwKICAgIGFzeW5jIGhhbmRsZUFmdGVyMigpIHsKICAgICAgLy8gaWYgKHRoaXMuaXNTaG93R2FwKSB7CiAgICAgIC8vICAgaWYgKHRoaXMuYXNzZXRJZCAhPSBudWxsKSB0aGlzLnRhYk5hbWUgPSB0aGlzLnJlZlt0aGlzLnJlZi5pbmRleE9mKHRoaXMudGFiTmFtZSkgKyAxXTsKICAgICAgLy8gICByZXR1cm4gdHJ1ZTsKICAgICAgLy8gfQogICAgICBpZiAoIXRoaXMuYXNzZXRJZCkgewogICAgICAgIC8vIOaWsOWinuaXtu+8jOajgOmqjOWfuuacrOS/oeaBr+W/heWhq+mhuQogICAgICAgIGlmICghdGhpcy4kcmVmc1snYmFzZSddLnZhbGlkYXRlRm9ybSgpKSByZXR1cm4gdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+WFiOW9leWFpeWfuuacrOS/oeaBryEiKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDnvJbovpHml7bvvIzmo4Dpqozln7rmnKzkv6Hmga/lv4XloavpobkKICAgICAgICBpZiAodGhpcy50YWJOYW1lID09PSAnYmFzZScpIGlmICghdGhpcy4kcmVmc1snYmFzZSddLnZhbGlkYXRlRm9ybSgpKSByZXR1cm4gdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+WFiOW9leWFpeWfuuacrOS/oeaBryEiKTsKICAgICAgfQogICAgICB0aGlzLmFmdGVyQnRuTG9hZCA9IHRydWU7CiAgICAgIGF3YWl0IHRoaXMuJHJlZnNbdGhpcy50YWJOYW1lXS5oYW5kbGVTYXZlKCkudGhlbigoKT0+ewogICAgICAgIHRoaXMuaXNUZW1wU2F2ZSA9IHRydWU7CiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gewogICAgICAgIHRoaXMuYWZ0ZXJCdG5Mb2FkID0gZmFsc2U7CiAgICAgIH0pOwogICAgICAvLyBpZiAodGhpcy5hc3NldElkICE9IG51bGwpIHRoaXMudGFiTmFtZSA9IHRoaXMucmVmW3RoaXMucmVmLmluZGV4T2YodGhpcy50YWJOYW1lKSArIDFdCiAgICB9LAogICAgY2hhbmdlVGFiKG5ld05hbWUsIG9sZE5hbWUpIHsKICAgICAgaWYgKHRoaXMuYXNzZXRJZCA9PSBudWxsICYmIG5ld05hbWUgIT09ICdiYXNlJykgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7flhYjlvZXlhaXns7vnu5/ln7rmnKzkv6Hmga/vvIzkv53lrZjlkI7lho3liIfmjaIhIik7CiAgICAgICAgdGhpcy50YWJOYW1lID0gJ2Jhc2UnOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBsZXQgY29tcG9uZW50cyA9IFsnYmFzZScsICdidXNpbmVzcycsICdjb21wb25lbnQnXTsKICAgICAgdGhpcy5zdGVwID0gY29tcG9uZW50cy5maW5kSW5kZXgoaXRlbSA9PiBpdGVtID09PSBuZXdOYW1lKQogICAgfSwKICAgIGNoZWNrUGFzcygpIHsKICAgICAgbGV0IGRhdGEgPSB7YXNzZXRJZDogdGhpcy5hc3NldElkLCBvcGVyYXRvcjogInBhc3MifQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTopoHlrqHmoLjpgJrov4for6XpobnloavmiqXvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBjaGVja0FwcGxpY2F0aW9uKGRhdGEpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmFwcGxpY2F0aW9uLmNoZWNrT24gPSAncGFzcyc7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5bey57uP5a6M5oiQ5a6h5qC477yBJyk7CiAgICAgIH0pOwogICAgfSwKICAgIGNoZWNrRmFpbCgpIHsKICAgICAgaWYgKHRoaXMuY29udGVudCAhPSBudWxsICYmIHRoaXMuY29udGVudCAhPT0gJycpIHsKICAgICAgICBpZiAodGhpcy5jb250ZW50Lmxlbmd0aCA+IDI1NSkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5a2X56ym5aSn5bCP6LaF6L+HMjU177yBIik7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIGxldCBkYXRhID0ge2Fzc2V0SWQ6IHRoaXMuYXNzZXRJZCwgb3BlcmF0b3I6ICJmYWlsIiwgY29udGVudDogdGhpcy5jb250ZW50fQogICAgICAgIGNoZWNrQXBwbGljYXRpb24oZGF0YSkudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy5hcHBsaWNhdGlvbi5jaGVja09uID0gJ2ZhaWwnOwogICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCflt7Lnu4/lrozmiJDlrqHmoLjvvIEnKTsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7fovpPlhaXkuI3pgJrov4flrqHmoLjmhI/op4HvvIEiKTsKCiAgICAgIH0KICAgIH0sCgogICAgLy8g5omT5byA5by556qXCiAgICBvcGVuRGlhbG9nKCkgewogICAgICAvLyBpZiAodGhpcy5wYXJhbXMgJiYgdGhpcy5wYXJhbXMuYXNzZXRJZCkgewogICAgICAvLyAgIHRoaXMuaGFuZGxlSW50b1BhZ2UoKTsKICAgICAgLy8gICByZXR1cm47CiAgICAgIC8vIH0KICAgICAgdGhpcy5pc1Nob3dHYXAgPSB0aGlzLnBhcmFtcy5pc1Nob3dHYXAgIT09IHVuZGVmaW5lZAogICAgICBpZiAodGhpcy5wYXJhbXMuYXNzZXRJZCAmJiAhdGhpcy5hc3NldElkKSB7CiAgICAgICAgdGhpcy5hc3NldElkID0gdGhpcy5wYXJhbXMuYXNzZXRJZDsKICAgICAgICB0aGlzLnNob3dEYXRhLnZhbHVlID0gdGhpcy5wYXJhbXMuc2hvd0RhdGEgPyB0aGlzLnBhcmFtcy5zaG93RGF0YSA9PT0gInRydWUiIDogdHJ1ZTsKICAgICAgICBnZXRBcHBsaWNhdGlvbih0aGlzLmFzc2V0SWQpLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMuYXBwbGljYXRpb24gPSByZXMuZGF0YS5hcHBsaWNhdGlvblZPOwogICAgICAgIH0pOwogICAgICAgIGlmICh0aGlzLnBhcmFtcy5kZXRhaWxzICYmIHRoaXMucGFyYW1zLnRhYk5hbWUpIHsKICAgICAgICAgIHRoaXMudGFiTmFtZSA9IHRoaXMucGFyYW1zLnRhYk5hbWU7CiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIC8vIOWFs+mXreW8ueeql+S6i+S7tgogICAgY2xvc2VEaWFsb2coKSB7CiAgICAgIHRoaXMuYXNzZXRJZCA9IG51bGw7CiAgICAgIHRoaXMudGFiTmFtZSA9ICdiYXNlJzsKICAgICAgdGhpcy5zaG93RGF0YS52YWx1ZSA9IHRydWU7CiAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykKICAgIH0sCgogICAgLy8g5YWz6Zet5by556qXCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5zaG93RGF0YS52YWx1ZSA9IHRydWU7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["applicationDetails.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "applicationDetails.vue", "sourceRoot": "src/views/hhlCode/component", "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"title\"\n    :visible.sync=\"dialogVisible\"\n    width=\"80%\"\n    @open=\"openDialog\"\n    @close=\"closeDialog\"\n    :before-close=\"handleClose\">\n    <div class=\"dialog-body\">\n      <div\n        v-if=\"application !== null && application.remark !== '' && application.remark !== undefined && application.checkOn === 'wait'\"\n        class=\"m_mark\">\n        <p style=\"color: red\">{{ application.remark }}</p>\n      </div>\n\n      <div style=\"float:right; margin-bottom: 15px\" v-hasPermi=\"['safe:application:check']\"\n           v-if=\"this.whetherOrNotToAudit\">\n        <el-row :gutter=\"20\" class=\"check_for\" v-if=\" gv('checkOn',application,null)=='wait'\">\n          <el-col :span=\"1.5\">\n            <el-button type=\"primary\" @click=\"checkPass()\">审核通过</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button @click=\"open = true;\">审核不通过</el-button>\n          </el-col>\n        </el-row>\n      </div>\n      <div :style=\"tabName === 'firewallNat' ? { flex: 1, overflow: 'auto' } : { flex: 1 }\" v-if=\"dialogVisible\">\n        <el-tabs v-model=\"tabName\" type=\"card\" :before-leave=\"changeTab\" v-if=\"title.includes('查看')\">\n          <el-tab-pane label=\"系统详情\" name=\"base\"/>\n          <el-tab-pane label=\"业务系统漏洞\" name=\"webBusinessGap\" v-if=\"isShowGap\"/>\n          <el-tab-pane label=\"端口暴露面\" name=\"firewallNat\" v-if=\"isShowGap\"/>\n        </el-tabs>\n        <SystemDetails\n          v-show=\"!title.includes('查看')\"\n          v-if=\"tabName === 'base'\"\n          ref=\"base\"\n          :asset-list=\"assetList\"\n          :asset-id=\"assetId\"\n          :changeId=\"changeId\"/>\n        <OperationSystemDetails\n          v-show=\"title.includes('查看') && tabName  === 'base'\"\n          :asset-list=\"assetList\"\n          :asset-id=\"assetId\"\n          :changeId=\"changeId\"/>\n        <business-gap v-if=\"tabName === 'webBusinessGap' && isShowGap\" :asset-id=\"assetId\"/>\n        <firewall-nat-index\n          v-if=\"tabName === 'firewallNat'\"\n          style=\"height: calc(100% - 10px); display: flex; flex-direction: column\"\n          :current-application-id=\"assetId\"\n          :hidden-columns=\"['serverList','businessApplicationList','tools']\"/>\n      </div>\n\n      <el-dialog title=\"业务应用审核\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n        <el-form ref=\"form\" label-width=\"130px\">\n          <div style=\"font-size: 22px;\">不通过审核意见</div>\n          <br>\n          <div>\n            <el-input v-model=\"content\" :autosize=\"{minRows: 4, maxRows: 6}\" type=\"textarea\" placeholder=\"请输入意见\"/>\n          </div>\n        </el-form>\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"checkFail()\">确定</el-button>\n          <el-button @click=\"open=false;\">取 消</el-button>\n        </div>\n      </el-dialog>\n\n    </div>\n    <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button\n          type=\"primary\"\n          @click=\"handleAfter\"\n          v-if=\"!title.includes('查看')\">\n          保存\n        </el-button>\n<!--        <el-button\n          type=\"primary\"\n          @click=\"handleSave\"\n          v-if=\"gv('checkOn',application)!='wait' && showData.value && ['component'].includes(tabName)\">\n          保存\n        </el-button>\n        <el-button\n          v-if=\"['business','base'].includes(tabName) && !isShowGap\" type=\"primary\" @click=\"handleAfter\"\n                   :loading=\"afterBtnLoad\">下一步\n        </el-button>-->\n    <el-button @click=\"handleClose\">关 闭</el-button>\n    </span>\n  </el-dialog>\n</template>\n\n<script>\nimport {getValFromObject} from \"@/utils\";\nimport {getApplication, checkApplication,auditConfig} from \"@/api/safe/application\";\nimport assetRegister from \"@/mixins/assetRegister\";\n\nexport default {\n  name: \"applicationInfo\",\n  mixins: [assetRegister],\n  components: {\n    SystemDetails: () => import('@/views/hhlCode/component/systemDetails'),\n    FirewallNatIndex: () => import('@/views/safe/safety/firewallNatIndex'),\n    BusinessGap: () => import('../businessGap'),\n    ApplicationForm: () => import('@/views/hhlCode/component/application/applicationForm'),\n    ApplicationHardware: () => import('@/views/hhlCode/component/application/applicationHardware'),\n    businessApplicationStatus: () => import('@/views/hhlCode/component/businessApplicationStatus'),\n    OperationSystemDetails: () => import('@/views/hhlCode/component/OperationSystemDetails'),\n  },\n  props: {\n    applicationVisible: {\n      type: Boolean,\n      required: true\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    params: {\n      type: Object,\n      default: () => {},\n    },\n  },\n  provide() {\n    return {\n      $editable: this.showData\n    }\n  },\n  data() {\n    return {\n      tabName: 'base',\n      showData: {value: true},\n      editable: false,\n      assetId: null,\n      application: null,\n      open: false,\n      content: null,\n      ref: ['base', 'business', 'component'],//提交组件\n      gv: getValFromObject,\n      rules: {},\n      whetherOrNotToAudit: false,\n      isShowGap: true,\n      afterBtnLoad: false,\n      isTempSave: false,\n      assetAllocationType: '1',\n    }\n  },\n  computed: {\n    dialogVisible: {\n      get() {\n        return this.applicationVisible\n      },\n      set(value) {\n        this.$emit('update:applicationVisible', value)\n      }\n    }\n  },\n  created() {\n    auditConfig({\n      pageNum: 1,\n      pageSize: 10\n    }).then(response => {\n      this.whetherOrNotToAudit = response.rows[0].configValue !== 'false';\n    })\n  },\n  mounted() {},\n\n  methods: {\n    changeId(id) {\n      this.assetId = id;\n    },\n    // 保存\n    // async handleSave() {\n    //   await this.$refs[this.tabName].handleSave().then(()=>{\n    //     this.$emit('applicationChange', true);\n    //   });\n    // },\n\n    // 下一步（保存）\n    async handleAfter() {\n      // if (this.isShowGap) {\n      //   if (this.assetId != null) this.tabName = this.ref[this.ref.indexOf(this.tabName) + 1];\n      //   return true;\n      // }\n      if (!this.assetId) {\n        // 新增时，检验基本信息必填项\n        if (!this.$refs['base'].validateForm()) return this.$modal.msgError(\"请先录入基本信息!\");\n      } else {\n        // 编辑时，检验基本信息必填项\n        if (this.tabName === 'base') if (!this.$refs['base'].validateForm()) return this.$modal.msgError(\"请先录入基本信息!\");\n      }\n      this.afterBtnLoad = true;\n      await this.$refs[this.tabName].handleSave().then(()=>{\n        this.$emit('applicationChange', false);\n      }).finally(() => {\n        this.afterBtnLoad = false;\n        this.$emit('deptSelectKeyChange', true);\n      });\n      // if (this.assetId != null) this.tabName = this.ref[this.ref.indexOf(this.tabName) + 1]\n    },\n    async handleAfter2() {\n      // if (this.isShowGap) {\n      //   if (this.assetId != null) this.tabName = this.ref[this.ref.indexOf(this.tabName) + 1];\n      //   return true;\n      // }\n      if (!this.assetId) {\n        // 新增时，检验基本信息必填项\n        if (!this.$refs['base'].validateForm()) return this.$modal.msgError(\"请先录入基本信息!\");\n      } else {\n        // 编辑时，检验基本信息必填项\n        if (this.tabName === 'base') if (!this.$refs['base'].validateForm()) return this.$modal.msgError(\"请先录入基本信息!\");\n      }\n      this.afterBtnLoad = true;\n      await this.$refs[this.tabName].handleSave().then(()=>{\n        this.isTempSave = true;\n      }).finally(() => {\n        this.afterBtnLoad = false;\n      });\n      // if (this.assetId != null) this.tabName = this.ref[this.ref.indexOf(this.tabName) + 1]\n    },\n    changeTab(newName, oldName) {\n      if (this.assetId == null && newName !== 'base') {\n        this.$modal.msgError(\"请先录入系统基本信息，保存后再切换!\");\n        this.tabName = 'base';\n        return false;\n      }\n      let components = ['base', 'business', 'component'];\n      this.step = components.findIndex(item => item === newName)\n    },\n    checkPass() {\n      let data = {assetId: this.assetId, operator: \"pass\"}\n      this.$modal.confirm('是否确认要审核通过该项填报？').then(function () {\n        checkApplication(data);\n      }).then(() => {\n        this.application.checkOn = 'pass';\n        this.$modal.msgSuccess('已经完成审核！');\n      });\n    },\n    checkFail() {\n      if (this.content != null && this.content !== '') {\n        if (this.content.length > 255) {\n          this.$message.error(\"字符大小超过255！\");\n          return;\n        }\n        let data = {assetId: this.assetId, operator: \"fail\", content: this.content}\n        checkApplication(data).then(res => {\n          this.application.checkOn = 'fail';\n          this.open = false;\n          this.$modal.msgSuccess('已经完成审核！');\n        });\n      } else {\n        this.$message.error(\"请输入不通过审核意见！\");\n\n      }\n    },\n\n    // 打开弹窗\n    openDialog() {\n      // if (this.params && this.params.assetId) {\n      //   this.handleIntoPage();\n      //   return;\n      // }\n      this.isShowGap = this.params.isShowGap !== undefined\n      if (this.params.assetId && !this.assetId) {\n        this.assetId = this.params.assetId;\n        this.showData.value = this.params.showData ? this.params.showData === \"true\" : true;\n        getApplication(this.assetId).then(res => {\n          this.application = res.data.applicationVO;\n        });\n        if (this.params.details && this.params.tabName) {\n          this.tabName = this.params.tabName;\n        }\n      }\n    },\n\n    // 关闭弹窗事件\n    closeDialog() {\n      this.assetId = null;\n      this.tabName = 'base';\n      this.showData.value = true;\n      this.$emit('close')\n    },\n\n    // 关闭弹窗\n    handleClose() {\n      this.showData.value = true;\n      this.dialogVisible = false;\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.check_for {\n  z-index: 9;\n  position: relative;\n}\n\n.down {\n  width: 100%;\n  margin-top: 10px;\n  text-align: center;\n}\n\n::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\n  background: rgba(48, 111, 229, 0.8);\n  color: #FFFFFF;\n}\n\n.m_mark {\n  width: 100%;\n  border: solid 1px red;\n  border-radius: 10px;\n  height: 80px;\n  padding: 5px;\n  margin-bottom: 10px;\n}\n\n::v-deep .el-tabs {\n  height: 100%;\n\n  .el-tabs__content {\n    height: calc(100% - 56px);\n\n    .el-tab-pane {\n      height: 100%;\n    }\n  }\n}\n\n::v-deep .el-dialog__body {\n  padding: 0 0 10px 20px;\n}\n\n.dialog-body {\n  height: 700px;\n  overflow: auto;\n}\n\n</style>\n"]}]}