<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblEmpComeMapper">
    
    <resultMap type="TblEmpCome" id="TblEmpComeResult">
        <result property="id"    column="id"    />
        <result property="eid"    column="eid"    />
        <result property="empName"    column="emp_name"    />
        <result property="dwid"    column="dwid"    />
        <result property="dwName"    column="dw_name"    />
        <result property="bgtm"    column="bgtm"    />
        <result property="addr"    column="addr"    />
        <result property="flag"    column="flag"    />
        <result property="tempe"    column="tempe"    />
        <result property="memo"    column="memo"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectTblEmpComeVo">
        select a.id, a.eid, a.bgtm, a.addr, a.flag, a.tempe, a.memo, a.create_by, a.create_time
               ,b.name as emp_name,b.dwid,c.name as dw_name
        from tbl_emp_come a
        left join tbl_company_emp b on a.eid=b.eid
        left join tbl_company c on b.dwid=c.dwid
    </sql>

    <select id="selectTblEmpComeList" parameterType="TblEmpCome" resultMap="TblEmpComeResult">
        <include refid="selectTblEmpComeVo"/>
        <where>
            <if test="dwid != null "> and c.dwid = #{dwid}</if>
            <if test="eid != null "> and a.eid = #{eid}</if>
            <if test="empName != null "> and b.name like concat('%',#{empName},'%')</if>
            <if test="bgtm != null "> and (a.bgtm >= #{bgtm} and a.bgtm &lt; DATE_ADD(#{bgtm},interval 1 DAY))</if>
            <if test="addr != null  and addr != ''"> and a.addr = #{addr}</if>
            <if test="flag != null  and flag != ''"> and a.flag = #{flag}</if>
            <if test="tempe != null  and tempe != ''"> and a.tempe = #{tempe}</if>
        </where>
    </select>
    
    <select id="selectTblEmpComeById" parameterType="Long" resultMap="TblEmpComeResult">
        <include refid="selectTblEmpComeVo"/>
        where a.id = #{id}
    </select>
        
    <insert id="insertTblEmpCome" parameterType="TblEmpCome">
        insert into tbl_emp_come
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="eid != null">eid,</if>
            <if test="bgtm != null">bgtm,</if>
            <if test="addr != null">addr,</if>
            <if test="flag != null">flag,</if>
            <if test="tempe != null">tempe,</if>
            <if test="memo != null">memo,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="eid != null">#{eid},</if>
            <if test="bgtm != null">#{bgtm},</if>
            <if test="addr != null">#{addr},</if>
            <if test="flag != null">#{flag},</if>
            <if test="tempe != null">#{tempe},</if>
            <if test="memo != null">#{memo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateTblEmpCome" parameterType="TblEmpCome">
        update tbl_emp_come
        <trim prefix="SET" suffixOverrides=",">
            <if test="eid != null">eid = #{eid},</if>
            <if test="bgtm != null">bgtm = #{bgtm},</if>
            <if test="addr != null">addr = #{addr},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="tempe != null">tempe = #{tempe},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblEmpComeById" parameterType="Long">
        delete from tbl_emp_come where id = #{id}
    </delete>

    <delete id="deleteTblEmpComeByIds" parameterType="String">
        delete from tbl_emp_come where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectEmpComeInfoByTempleteId" parameterType="Long" resultType="TblEmpComeSeat">
        SELECT
            ec.*,
            s.x AS seatX,
            s.y AS seatY,
            s.orientation,
            ce.name AS empName
        FROM
            tbl_emp_come ec
                JOIN (
                SELECT
                    eid,
                    MAX(create_time) AS max_create_time
                FROM
                    tbl_emp_come
                WHERE
                    bgtm BETWEEN DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00') AND DATE_FORMAT(NOW(), '%Y-%m-%d 23:59:59')
                GROUP BY
                    eid
                ORDER BY
                    create_time DESC
            ) AS latest ON ec.eid = latest.eid AND ec.create_time = latest.max_create_time
                LEFT JOIN tbl_company_emp ce ON ec.eid = ce.eid
                LEFT JOIN tbl_seats s ON s.id = ce.seat_id
                LEFT JOIN tbl_seat_templete st ON s.templete_id = st.id
        WHERE
            st.id = #{templeteId}
    </select>


    <!-- 运维驻场大屏相关 -->

    <resultMap type="TblEmpComeBlade" id="TblEmpComeBladeResult">
        <result property="id"    column="id"    />
        <result property="eid"    column="eid"    />
        <result property="empName"    column="emp_name"    />
        <result property="dwid"    column="dwid"    />
        <result property="dwName"    column="dw_name"    />
        <result property="bgtm"    column="bgtm"    />
        <result property="addr"    column="addr"    />
        <result property="flag"    column="flag"    />
        <result property="tempe"    column="tempe"    />
        <result property="memo"    column="memo"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="phone"    column="phone"    />
        <result property="deptName"    column="dept_name"    />
    </resultMap>

    <sql id="selectTblEmpComeBladeVo">
        select a.id, a.eid, a.bgtm, a.addr, a.flag, a.tempe, a.memo, a.create_by, a.create_time
             ,b.name as emp_name,b.dwid,c.name as dw_name,b.phone,d.dept_name
        from tbl_emp_come a
                 left join tbl_company_emp b on a.eid=b.eid
                 left join tbl_company c on b.dwid=c.dwid
                 LEFT JOIN sys_dept d on b.dept_id=d.dept_id
    </sql>

    <select id="selectTblEmpComeForBladeList" parameterType="TblEmpCome" resultMap="TblEmpComeBladeResult">
        <include refid="selectTblEmpComeBladeVo"/>
        <where>
            <if test="dwid != null "> and c.dwid = #{dwid}</if>
            <if test="eid != null "> and a.eid = #{eid}</if>
            <if test="empName != null "> and b.name like concat('%',#{empName},'%')</if>
            <if test="bgtm != null "> and (a.bgtm >= #{bgtm} and a.bgtm &lt; DATE_ADD(#{bgtm},interval 1 DAY))</if>
            <if test="addr != null  and addr != ''"> and a.addr = #{addr}</if>
            <if test="flag != null  and flag != ''"> and a.flag = #{flag}</if>
            <if test="tempe != null  and tempe != ''"> and a.tempe = #{tempe}</if>
        </where>
        order by a.id desc limit 20
    </select>

    <select id="selectEmployeeComeCount" resultType="EmpEntryAndExitRanking">
        SELECT
            ce.name AS employeeName,
            COUNT(c.id) AS comeCount
        FROM
            tbl_emp_come c
                INNER JOIN tbl_company_emp ce ON ce.eid = c.eid
        WHERE
            c.bgtm BETWEEN DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00') AND DATE_FORMAT(NOW(), '%Y-%m-%d 23:59:59')
        GROUP BY
            c.eid
        ORDER BY
            comeCount DESC
            LIMIT 20
    </select>

</mapper>