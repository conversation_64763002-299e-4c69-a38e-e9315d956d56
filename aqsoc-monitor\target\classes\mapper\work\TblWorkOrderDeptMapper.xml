<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.work.mapper.TblWorkOrderDeptMapper">

    <resultMap type="TblWorkOrderDept" id="TblWorkOrderDeptResult">
        <result property="id"    column="id"    />
        <result property="workOrderId"    column="work_order_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName" column="dept_name" />
        <result property="handleTitle" column="handle_title" />
        <result property="eventDescription" column="event_description" />
        <result property="handleSituation" column="handle_situation" />
        <result property="otherSituation" column="other_situation" />
        <result property="feedbackFileUrl" column="feedback_file_url" />
    </resultMap>

    <sql id="selectTblWorkOrderDeptVo">
        select id, work_order_id, dept_id, dept_name, handle_title, event_description, handle_situation, other_situation, feedback_file_url from tbl_work_order_dept
    </sql>

    <select id="selectTblWorkOrderDeptList" parameterType="TblWorkOrderDept" resultMap="TblWorkOrderDeptResult">
        select t1.id, t1.work_order_id, t1.dept_id,t1.handle_title,t1.event_description,t1.handle_situation,t1.other_situation,t1.feedback_file_url,sd.dept_name from tbl_work_order_dept t1
        left join sys_dept sd on t1.dept_id = sd.dept_id
        <where>
            <if test="workOrderId != null "> and t1.work_order_id = #{workOrderId}</if>
            <if test="workOrderIds != null and workOrderIds.size()>0">
                and t1.work_order_id in
                <foreach item="workOrderIdItem" collection="workOrderIds" open="(" separator="," close=")">
                    #{workOrderIdItem}
                </foreach>
            </if>
            <if test="deptId != null "> and t1.dept_id = #{deptId}</if>
        </where>
    </select>

    <select id="selectTblWorkOrderDeptById" parameterType="Long" resultMap="TblWorkOrderDeptResult">
        <include refid="selectTblWorkOrderDeptVo"/>
        where id = #{id}
    </select>

    <select id="selectTblWorkOrderDeptByIds" parameterType="Long" resultMap="TblWorkOrderDeptResult">
        <include refid="selectTblWorkOrderDeptVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblWorkOrderDept" parameterType="TblWorkOrderDept" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_work_order_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">#{workOrderId},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>
    <insert id="batchInsert">
        insert into tbl_work_order_dept (id, work_order_id, dept_id, handle_title, event_description, handle_situation, other_situation, feedback_file_url) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id}, #{item.workOrderId}, #{item.deptId}, #{item.handleTitle}, #{item.eventDescription}, #{item.handleSituation}, #{item.otherSituation}, #{item.feedbackFileUrl})
        </foreach>
    </insert>

    <update id="updateTblWorkOrderDept" parameterType="TblWorkOrderDept">
        update tbl_work_order_dept
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id = #{workOrderId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="handleTitle != null">handle_title = #{handleTitle},</if>
            <if test="eventDescription != null">event_description = #{eventDescription},</if>
            <if test="handleSituation != null">handle_situation = #{handleSituation},</if>
            <if test="otherSituation != null">other_situation = #{otherSituation},</if>
            <if test="feedbackFileUrl != null">feedback_file_url = #{feedbackFileUrl},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchUpdate">
        UPDATE tbl_work_order_dept
        SET
        handle_title = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.handleTitle != null and item.handleTitle != ''">#{item.handleTitle}</if> <if test="item.handleTitle == null or item.handleTitle == ''">handle_title</if>
        </foreach>
        ELSE handle_title
        END,
        event_description = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.eventDescription != null and item.eventDescription != ''">#{item.eventDescription}</if> <if test="item.eventDescription == null or item.eventDescription == ''">event_description</if>
        </foreach>
        ELSE event_description
        END,
        handle_situation = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.handleSituation != null and item.handleSituation != ''">#{item.handleSituation}</if> <if test="item.handleSituation == null or item.handleSituation == ''">handle_situation</if>
        </foreach>
        ELSE handle_situation
        END,
        other_situation = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.otherSituation != null and item.otherSituation != ''">#{item.otherSituation}</if> <if test="item.otherSituation == null or item.otherSituation == ''">other_situation</if>
        </foreach>
        ELSE other_situation
        END,
        feedback_file_url = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.feedbackFileUrl != null and item.feedbackFileUrl != ''">#{item.feedbackFileUrl}</if> <if test="item.feedbackFileUrl == null or item.feedbackFileUrl == ''">feedback_file_url</if>
        </foreach>
        ELSE feedback_file_url
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <delete id="deleteTblWorkOrderDeptById" parameterType="Long">
        delete from tbl_work_order_dept where id = #{id}
    </delete>

    <delete id="deleteTblWorkOrderDeptByIds" parameterType="String">
        delete from tbl_work_order_dept where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTblWorkOrderDeptByWorkId">
        delete from tbl_work_order_dept where work_order_id = #{workId}
    </delete>
</mapper>
