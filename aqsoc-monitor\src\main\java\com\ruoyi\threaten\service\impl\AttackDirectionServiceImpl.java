package com.ruoyi.threaten.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.service.INetworkDomainService;
import com.ruoyi.threaten.service.IAttackDirectionService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 攻击方向判断服务实现
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Service
public class AttackDirectionServiceImpl implements IAttackDirectionService 
{
    private static final Logger log = LoggerFactory.getLogger(AttackDirectionServiceImpl.class);
    
    @Autowired
    private INetworkDomainService networkDomainService;
    
    /**
     * 攻击方向常量
     */
    private static final String INTERNAL_TO_INTERNAL = "1"; // 内对内
    private static final String INTERNAL_TO_EXTERNAL = "2"; // 内对外
    private static final String EXTERNAL_TO_INTERNAL = "3"; // 外对内
    private static final String UNKNOWN = "4"; // 未知
    
    /**
     * 根据源IP和目标IP判断攻击方向
     * 
     * @param srcIp 源IP地址
     * @param destIp 目标IP地址
     * @return 攻击方向：1-内对内，2-内对外，3-外对内，4-未知
     */
    @Override
    public String determineAttackDirection(String srcIp, String destIp) 
    {
        try {
            // 参数校验
            if (StrUtil.isBlank(srcIp) || StrUtil.isBlank(destIp)) {
                log.debug("源IP或目标IP为空，返回未知攻击方向");
                return UNKNOWN;
            }
            
            // 获取网络区域列表
            List<NetworkDomain> domains = getNetworkDomains();
            if (CollUtil.isEmpty(domains)) {
                log.debug("网络区域列表为空，返回未知攻击方向");
                return UNKNOWN;
            }
            
            // 判断源IP和目标IP是否属于内网
            boolean srcIsInternal = getDomainIdByIp(srcIp, domains) != null;
            boolean destIsInternal = getDomainIdByIp(destIp, domains) != null;
            
            // 根据判断结果返回攻击方向
            if (srcIsInternal && destIsInternal) {
                log.debug("攻击方向判断：内对内，srcIp={}, destIp={}", srcIp, destIp);
                return INTERNAL_TO_INTERNAL;
            }
            if (srcIsInternal && !destIsInternal) {
                log.debug("攻击方向判断：内对外，srcIp={}, destIp={}", srcIp, destIp);
                return INTERNAL_TO_EXTERNAL;
            }
            if (!srcIsInternal && destIsInternal) {
                log.debug("攻击方向判断：外对内，srcIp={}, destIp={}", srcIp, destIp);
                return EXTERNAL_TO_INTERNAL;
            }
            
            log.debug("攻击方向判断：未知，srcIp={}, destIp={}", srcIp, destIp);
            return UNKNOWN;
            
        } catch (Exception e) {
            log.error("攻击方向判断异常，srcIp={}, destIp={}", srcIp, destIp, e);
            return UNKNOWN;
        }
    }
    
    /**
     * 获取网络区域列表（带缓存）
     * 
     * @return 网络区域列表
     */
    @Cacheable(value = "networkDomains", key = "'all'")
    private List<NetworkDomain> getNetworkDomains() 
    {
        try {
            NetworkDomain query = new NetworkDomain();
            return networkDomainService.selectNetworkDomainList(query);
        } catch (Exception e) {
            log.error("获取网络区域列表异常", e);
            return null;
        }
    }
    
    /**
     * 根据IP匹配网络区域（CIDR）
     * 
     * @param ip IP地址
     * @param domains 网络区域列表
     * @return 匹配的网络区域ID，未匹配返回null
     */
    private Long getDomainIdByIp(String ip, List<NetworkDomain> domains) 
    {
        if (StrUtil.isBlank(ip) || CollUtil.isEmpty(domains)) {
            return null;
        }
        
        try {
            for (NetworkDomain d : domains) {
                if (d != null && StrUtil.isNotBlank(d.getIparea())) {
                    try {
                        if (IpUtils.isInRange(ip, d.getIparea())) {
                            log.debug("IP地址 {} 匹配到网络区域：{} ({})", ip, d.getDomainName(), d.getIparea());
                            return d.getDomainId();
                        }
                    } catch (Exception ignore) {
                        // 忽略单个CIDR匹配异常，继续匹配下一个
                    }
                }
            }
            return null;
        } catch (Exception e) {
            log.error("IP地址匹配网络区域异常，ip={}", ip, e);
            return null;
        }
    }
}
