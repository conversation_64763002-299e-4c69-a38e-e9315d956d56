# 威胁告警模块添加"攻击方向"字段支持

## 任务概述

为aqsoc-main项目的威胁告警功能添加攻击方向字段的查询和展示功能，包括后端接口修改和前端页面调整。

## 项目背景

在威胁告警模块中增加"攻击方向"维度的查询和展示功能，帮助用户更好地分析和理解攻击事件的方向性特征。

## 技术分析

### 数据库层面
- `tbl_threaten_alarm`表中`attack_direction`字段已存在
- 字段类型：`varchar(10)`，默认值：`"4"`（未知）
- 字典配置完整：`attack_direction`包含4个选项

### 字典配置
- 内对内：值=1，标签=内对内
- 内对外：值=2，标签=内对外  
- 外对内：值=3，标签=外对内
- 未知：值=4，标签=未知（默认值）

### 后端接口
以下三个接口需要在Mapper.xml中添加`attackDirection`查询条件：
1. `TblThreatenAlarmController.list()` - 列表查询接口
2. `TblThreatenAlarmController.groupAlarmLevelStatistics()` - 告警等级统计接口
3. `TblThreatenAlarmController.getAttackSegSummary()` - 攻击阶段汇总接口

**重要发现**：虽然Controller接口通过`TblThreatenAlarm`对象接收参数，但Mapper.xml中缺少对应的SQL条件判断。

## 实施方案

### 方案选择
采用完全参照"同步状态"字段的实现模式，确保代码风格一致性和零破坏性风险。

### 前端修改（eventList.vue）

#### 1. 查询条件添加
**位置**：第172行后
**内容**：添加攻击方向下拉选择框
```vue
<el-col :span="6">
  <el-form-item label="攻击方向">
    <el-select v-model="queryParams.attackDirection" placeholder="请选择攻击方向" filterable clearable>
      <el-option
        v-for="dict in dict.type.attack_direction"
        :key="dict.value"
        :label="dict.label"
        :value="dict.value"
      ></el-option>
    </el-select>
  </el-form-item>
</el-col>
```

#### 2. 表格列添加
**位置**：同步状态列后
**内容**：添加攻击方向展示列
```vue
<el-table-column label="攻击方向" prop="attackDirection" width="150">
  <template slot-scope="scope">
    <dict-tag :options="dict.type.attack_direction" :value="scope.row.attackDirection"/>
  </template>
</el-table-column>
```

#### 3. 字典配置更新
**位置**：第760行
**修改**：在dicts数组中添加`'attack_direction'`

#### 4. 查询参数初始化
**位置**：resetQuery方法
**修改**：添加`attackDirection: null`

### 后端修改（TblThreatenAlarmMapper.xml）

#### 5. 添加SQL查询条件
**需要修改的三个SQL查询**：
1. `selectTblThreatenAlarmList` - list接口对应的查询
2. `groupAlarmLevelStatistics` - 告警等级统计接口对应的查询
3. `getAttackSegSummary` - 攻击阶段汇总接口对应的查询

**添加内容**：在每个查询的where条件末尾添加
```xml
<if test="attackDirection != null and attackDirection != ''">
    and ta.attack_direction = #{attackDirection}
</if>
```

## 执行记录

### 已完成步骤
1. ✅ 前端查询条件添加 - 在第172行后成功添加攻击方向下拉选择框
2. ✅ 前端表格列添加 - 在同步状态列后成功添加攻击方向展示列
3. ✅ 前端字典配置更新 - 在dicts数组中成功添加attack_direction
4. ✅ 前端查询参数初始化 - 在resetQuery方法中成功添加attackDirection字段
5. ✅ 后端Mapper.xml修复 - 在三个SQL查询中添加attackDirection条件判断
   - selectTblThreatenAlarmList (list接口)
   - groupAlarmLevelStatistics (告警等级统计接口)
   - getAttackSegSummary (攻击阶段汇总接口)

### 修改文件清单
- `ruoyi-ui/src/views/frailty/event/component/eventList.vue` - 前端页面修改
- `aqsoc-monitor/src/main/resources/mapper/threaten/TblThreatenAlarmMapper.xml` - 后端SQL查询修改

## 功能验证

### 预期结果
1. **查询功能**：用户可通过攻击方向下拉框筛选告警数据
2. **展示功能**：表格中正确显示每条告警的攻击方向信息
3. **数据一致性**：前后端字段名称完全一致
4. **用户体验**：与现有"同步状态"功能保持一致的操作方式

### 测试要点
1. 攻击方向下拉框是否正确显示4个选项
2. 选择不同攻击方向是否能正确筛选数据
3. 表格中攻击方向列是否正确显示字典标签
4. 重置查询是否正确清空攻击方向条件

## 风险评估

- **破坏性风险**：零风险（纯增量功能，不影响现有功能）
- **兼容性风险**：零风险（字段已存在于数据库和实体类）
- **性能风险**：零风险（无额外查询，复用现有机制）

## 技术特点

本实现完全符合Linus Torvalds的"好品味"编程哲学：
- **简洁性**：没有特殊情况，没有复杂逻辑
- **一致性**：完全复用现有"同步状态"的实现模式
- **实用性**：解决实际业务需求，不过度设计
- **向后兼容**：遵循"Never break userspace"原则

## 重要修复记录

### 发现的问题
在执行过程中发现，虽然Controller接口通过`TblThreatenAlarm`对象接收`attackDirection`参数，但对应的Mapper.xml文件中缺少SQL条件判断，导致前端传递的攻击方向筛选条件无法生效。

### 修复措施
在以下三个SQL查询的where条件中添加了`attackDirection`的条件判断：
1. `selectTblThreatenAlarmList` - 第259行添加
2. `groupAlarmLevelStatistics` - 第2127行添加
3. `getAttackSegSummary` - 第1200行添加

### 修复代码
```xml
<if test="attackDirection != null and attackDirection != ''">
    and ta.attack_direction = #{attackDirection}
</if>
```

## 完成状态

✅ 任务已完成，包括前端功能实现和后端SQL查询修复。
