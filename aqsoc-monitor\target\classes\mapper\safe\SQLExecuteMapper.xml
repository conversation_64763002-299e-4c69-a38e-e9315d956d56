<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.SQLExecuteMapper">
    

    <select id="executeQuery" parameterType="String" resultType="Map">
        select * from
        ${sqlString}
        <if test="where != null  and where != ''">
        where ${where}
        </if>
    </select>
    
</mapper>