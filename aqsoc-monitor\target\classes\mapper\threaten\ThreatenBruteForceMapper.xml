<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenBruteForceMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenBruteForceListVO">
        select a.* from threaten_brute_force a
        <where>
            <if test="query.alarmId!=null">
                and a.id in(select origin_id from threaten_alarm_origin where alarm_id=#{query.alarmId} and threaten_type=#{query.threatenType.name})
            </if>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.businessGroupId!=null">
                and a.business_group_id = #{query.businessGroupId}
            </if>
            <if test="query.businessGroupName!=null and query.businessGroupName!=''">
                and a.business_group_name like concat('%', #{query.businessGroupName}, '%')
            </if>
            <if test="query.eventCreateTime!=null">
                and a.event_create_time = #{query.eventCreateTime}
            </if>
            <if test="query.eventEndTime!=null">
                and a.event_end_time = #{query.eventEndTime}
            </if>
            <if test="query.eventLevel!=null and query.eventLevel!=''">
                and a.event_level like concat('%', #{query.eventLevel}, '%')
            </if>
            <if test="query.eventName!=null and query.eventName!=''">
                and a.event_name like concat('%', #{query.eventName}, '%')
            </if>
            <if test="query.eventStartTime!=null">
                and a.event_start_time = #{query.eventStartTime}
            </if>
            <if test="query.eventState!=null and query.eventState!=''">
                and a.event_state like concat('%', #{query.eventState}, '%')
            </if>
            <if test="query.hostId!=null">
                and a.host_id = #{query.hostId}
            </if>
            <if test="query.hostIp!=null and query.hostIp!=''">
                and a.host_ip like concat('%', #{query.hostIp}, '%')
            </if>
            <if test="query.hostName!=null and query.hostName!=''">
                and a.host_name like concat('%', #{query.hostName}, '%')
            </if>
            <if test="query.serviceName!=null and query.serviceName!=''">
                and a.service_name like concat('%', #{query.serviceName}, '%')
            </if>
            <if test="query.sourceIp!=null and query.sourceIp!=''">
                and a.source_ip like concat('%', #{query.sourceIp}, '%')
            </if>
            <if test="query.startTimeline!=null">
                and a.timeline >= DATE_FORMAT(#{query.startTimeline}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="query.endTimeline!=null">
                and a.timeline &lt; DATE_FORMAT(#{query.endTimeline}, '%Y-%m-%d %H:%i:%s')
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenBruteForceListVO">
        select a.* from threaten_brute_force a
        where a.id=#{id}
    </select>
</mapper>
