<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.threaten.mapper.TblAttackAlarmTagsMapper">

    <resultMap type="TblAttackAlarmTags" id="TblAttackAlarmTagsResult">
        <result property="id"    column="id"    />
        <result property="attackId"    column="attack_id"    />
        <result property="tagName"    column="tag_name"    />
    </resultMap>

    <sql id="selectTblAttackAlarmTagsVo">
        select id, attack_id, tag_name from tbl_attack_alarm_tags
    </sql>

    <select id="selectTblAttackAlarmTagsList" parameterType="TblAttackAlarmTags" resultMap="TblAttackAlarmTagsResult">
        <include refid="selectTblAttackAlarmTagsVo"/>
        <where>
            <if test="attackId != null "> and attack_id = #{attackId}</if>
            <if test="tagName != null  and tagName != ''"> and tag_name like concat('%', #{tagName}, '%')</if>
        </where>
    </select>

    <select id="selectTblAttackAlarmTagsById" parameterType="Long" resultMap="TblAttackAlarmTagsResult">
        <include refid="selectTblAttackAlarmTagsVo"/>
        where id = #{id}
    </select>

    <select id="selectTblAttackAlarmTagsByIds" parameterType="Long" resultMap="TblAttackAlarmTagsResult">
        <include refid="selectTblAttackAlarmTagsVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectTblAttackAlarmTagsByAttackId" resultType="com.ruoyi.threaten.domain.TblAttackAlarmTags" resultMap="TblAttackAlarmTagsResult">
        select id, attack_id, tag_name from tbl_attack_alarm_tags where attack_id = #{attackId}
    </select>

    <insert id="insertTblAttackAlarmTags" parameterType="TblAttackAlarmTags" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_attack_alarm_tags
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="attackId != null">attack_id,</if>
            <if test="tagName != null">tag_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="attackId != null">#{attackId},</if>
            <if test="tagName != null">#{tagName},</if>
         </trim>
    </insert>
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_attack_alarm_tags (attack_id, tag_name) values
        <foreach item="item" index="index" collection="tagsList" separator=",">
            (#{item.attackId}, #{item.tagName})
        </foreach>
        ON DUPLICATE KEY UPDATE id = VALUES(id)
    </insert>

    <select id="selectByAttackIdAndTagNames" resultType="TblAttackAlarmTags">
        SELECT id, attack_id, tag_name
        FROM tbl_attack_alarm_tags
        WHERE (attack_id, tag_name) IN
        <foreach collection="tagsList" item="item" open="(" close=")" separator=",">
            (#{item.attackId}, #{item.tagName})
        </foreach>
    </select>

    <update id="updateTblAttackAlarmTags" parameterType="TblAttackAlarmTags">
        update tbl_attack_alarm_tags
        <trim prefix="SET" suffixOverrides=",">
            <if test="attackId != null">attack_id = #{attackId},</if>
            <if test="tagName != null">tag_name = #{tagName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblAttackAlarmTagsById" parameterType="Long">
        delete from tbl_attack_alarm_tags where id = #{id}
    </delete>

    <delete id="deleteTblAttackAlarmTagsByIds" parameterType="String">
        delete from tbl_attack_alarm_tags where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
