<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeSoftwareDetailsResultMapper">

    <resultMap type="FfsafeSoftwareDetails" id="FfsafeSoftwareDetailsResult">
        <result property="id"    column="id"    />
        <result property="softwareName"    column="software_name"    />
        <result property="softwareVersion"    column="software_version"    />
        <result property="softwarePublisher"    column="software_publisher"    />
        <result property="assetId"    column="asset_id"    />
        <result property="serial"    column="serial"    />
    </resultMap>

    <sql id="selectFfsafeSoftwareDetailsVo">
        select * from ffsafe_software_details
    </sql>

    <select id="selectFfsafeSoftwareDetailsList" parameterType="FfsafeSoftwareDetails" resultMap="FfsafeSoftwareDetailsResult">
        <include refid="selectFfsafeSoftwareDetailsVo"/>
        <where>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="serial != null "> and serial = #{serial}</if>
        </where>
    </select>


    <insert id="insertFfsafeSoftwareDetails" parameterType="FfsafeSoftwareDetails" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_software_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="softwareName != null">software_name,</if>
            <if test="softwareVersion != null">software_version,</if>
            <if test="softwarePublisher != null">software_publisher,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="serial != null">serial,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="softwareName != null">#{softwareName},</if>
            <if test="softwareVersion != null">#{softwareVersion},</if>
            <if test="softwarePublisher != null">#{softwarePublisher},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="serial != null">#{serial},</if>
        </trim>
    </insert>


    <delete id="deleteFfsafeSoftwareDetailsBySerial" parameterType="String">
        delete from ffsafe_software_details where serial = #{serial}
    </delete>

</mapper>