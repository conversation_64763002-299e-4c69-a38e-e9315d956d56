<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblOperateWorkMapper">

    <resultMap type="TblOperateWork" id="TblOperateWorkResult">
        <result property="id"    column="id"    />
        <result property="workClass"    column="work_class"    />
        <result property="workName"    column="work_name"    />
        <result property="workType"    column="work_type"    />
        <result property="flowTemplateId"    column="flow_template_id"    />
        <result property="periodType"    column="period_type"    />
        <result property="periodDate"    column="period_date"    />
        <result property="periodTime"    column="period_time"    />
        <result property="personId"    column="person_id"    />
        <result property="institutionId"    column="institution_id"    />
        <result property="institutionDesc"    column="institution_desc"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="personName" column="person_name" />
        <result property="icon" column="icon" />
        <result property="iconStyle" column="icon_style" />
        <result property="deptId" column="dept_id" />
    </resultMap>

    <sql id="selectTblOperateWorkVo">
        select t1.id, t1.work_class, t1.work_name, t1.work_type, t1.flow_template_id, t1.period_type, t1.period_date, t1.period_time, t1.person_id,
               t1.institution_id, t1.institution_desc, t1.status, t1.create_by, t1.create_time, t1.update_by, t1.update_time,t1.icon,t1.icon_style,su.nick_name as person_name,
               su.dept_id
        from tbl_operate_work t1 left join sys_user su on su.user_id = t1.person_id
    </sql>

    <select id="selectTblOperateWorkList" parameterType="TblOperateWork" resultMap="TblOperateWorkResult">
        <include refid="selectTblOperateWorkVo"/>
        <where>
            <if test="workClass != null "> and t1.work_class = #{workClass}</if>
            <if test="workName != null  and workName != ''"> and t1.work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null "> and t1.work_type = #{workType}</if>
            <if test="personId != null  and personId != ''"> and t1.person_id = #{personId}</if>
            <if test="status != null"> and t1.`status` = #{status}</if>
            <if test="personName != null and personName != ''"> and (su.user_name like concat('%', #{personName}, '%') OR su.nick_name like concat('%', #{personName}, '%'))</if>
        </where>
        order by t1.create_time desc
    </select>

    <select id="selectTblOperateWorkById" parameterType="Long" resultMap="TblOperateWorkResult">
        <include refid="selectTblOperateWorkVo"/>
        where t1.id = #{id}
    </select>

    <select id="selectTblOperateWorkByIds" parameterType="Long" resultMap="TblOperateWorkResult">
        <include refid="selectTblOperateWorkVo"/>
        where t1.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectTblOperateWorkByName" parameterType="TblOperateWork" resultType="com.ruoyi.monitor2.domain.TblOperateWork">
        <include refid="selectTblOperateWorkVo"/>
        where t1.work_name = #{workName}
        <if test="id != null">
            and t1.id != #{id}
        </if>
    </select>
    <select id="count" resultType="java.lang.Integer">
        select count(1) from tbl_operate_work t1
        <where>
            <if test="workClass != null "> and t1.work_class = #{workClass}</if>
            <if test="workName != null  and workName != ''"> and t1.work_name like concat('%', #{workName}, '%')</if>
            <if test="workType != null "> and t1.work_type = #{workType}</if>
            <if test="personId != null  and personId != ''"> and t1.person_id = #{personId}</if>
            <if test="status != null"> and t1.`status` = #{status}</if>
            <if test="flowTemplateId != null and flowTemplateId != ''"> and t1.flow_template_id = #{flowTemplateId}</if>
        </where>
    </select>
    <select id="selectOperateWorkSrcColumnById" resultType="com.alibaba.fastjson2.JSONObject">
        <include refid="selectTblOperateWorkVo"/>
        where t1.id = #{id}
    </select>

    <insert id="insertTblOperateWork" parameterType="TblOperateWork" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_operate_work
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workClass != null">work_class,</if>
            <if test="workName != null and workName != ''">work_name,</if>
            <if test="workType != null">work_type,</if>
            <if test="flowTemplateId != null and flowTemplateId != ''">flow_template_id,</if>
            <if test="periodType != null">period_type,</if>
            <if test="periodDate != null">period_date,</if>
            <if test="periodTime != null">period_time,</if>
            <if test="personId != null and personId != ''">person_id,</if>
            <if test="institutionId != null and institutionId != ''">institution_id,</if>
            <if test="institutionDesc != null">institution_desc,</if>
            <if test="status != null">`status`,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="icon != null and icon != ''">icon,</if>
            <if test="iconStyle != null">icon_style,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workClass != null">#{workClass},</if>
            <if test="workName != null and workName != ''">#{workName},</if>
            <if test="workType != null">#{workType},</if>
            <if test="flowTemplateId != null and flowTemplateId != ''">#{flowTemplateId},</if>
            <if test="periodType != null">#{periodType},</if>
            <if test="periodDate != null">#{periodDate},</if>
            <if test="periodTime != null">#{periodTime},</if>
            <if test="personId != null and personId != ''">#{personId},</if>
            <if test="institutionId != null and institutionId != ''">#{institutionId},</if>
            <if test="institutionDesc != null">#{institutionDesc},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="icon != null and icon != ''">#{icon},</if>
            <if test="iconStyle != null">#{iconStyle},</if>
         </trim>
    </insert>

    <update id="updateTblOperateWork" parameterType="TblOperateWork">
        update tbl_operate_work
        <trim prefix="SET" suffixOverrides=",">
            <if test="workClass != null">work_class = #{workClass},</if>
            <if test="workName != null and workName != ''">work_name = #{workName},</if>
            <if test="workType != null">work_type = #{workType},</if>
            <if test="flowTemplateId != null and flowTemplateId != ''">flow_template_id = #{flowTemplateId},</if>
            <if test="periodType != null">period_type = #{periodType},</if>
            <if test="periodDate != null">period_date = #{periodDate},</if>
            <if test="periodTime != null">period_time = #{periodTime},</if>
            <if test="personId != null and personId != ''">person_id = #{personId},</if>
            <if test="institutionId != null and institutionId != ''">institution_id = #{institutionId},</if>
            <if test="institutionDesc != null">institution_desc = #{institutionDesc},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="iconStyle != null">icon_style = #{iconStyle},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblOperateWorkById" parameterType="Long">
        delete from tbl_operate_work where id = #{id}
    </delete>

    <delete id="deleteTblOperateWorkByIds" parameterType="String">
        delete from tbl_operate_work where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
