<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rule.mapper.TblRuleOrgManagenmentRoleMapper">

    <resultMap type="TblRuleOrgManagenmentRole" id="TblRuleOrgManagenmentRoleResult">
        <result property="roleId"    column="role_id"    />
        <result property="managementId"    column="management_id"    />
        <result property="roleName"    column="role_name"    />
        <result property="roleJob"    column="role_job"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="sortNum"    column="sort_num"    />
        <result property="phone"    column="phone"    />
    </resultMap>

    <sql id="selectTblRuleOrgManagenmentRoleVo">
        select role_id, management_id, role_name, role_job, user_id, dept_id, create_time, create_by, update_time, update_by, remark,sort_num from tbl_rule_org_managenment_role
    </sql>

    <select id="selectTblRuleOrgManagenmentRoleList" parameterType="TblRuleOrgManagenmentRole" resultMap="TblRuleOrgManagenmentRoleResult">
        select tromr.*,su.nick_name as nickName,su.phonenumber AS phone,sd.dept_name as deptName
        from tbl_rule_org_managenment_role tromr
        left join sys_user su on su.user_id = tromr.user_id
        left join sys_dept sd on sd.dept_id = tromr.dept_id
        <where>
            <if test="managementId != null "> and tromr.management_id = #{managementId}</if>
            <if test="roleName != null  and roleName != ''"> and tromr.role_name like concat('%', #{roleName}, '%')</if>
            <if test="roleJob != null  and roleJob != ''"> and tromr.role_job = #{roleJob}</if>
            <if test="userId != null "> and tromr.user_id = #{userId}</if>
            <if test="deptId != null "> and tromr.dept_id = #{deptId}</if>
        </where>
        order by sort_num asc,update_time desc
    </select>

    <select id="selectTblRuleOrgManagenmentRoleByRoleId" parameterType="Long" resultMap="TblRuleOrgManagenmentRoleResult">
        select tromr.*,su.nick_name as nickName,sd.dept_name as deptName
        from tbl_rule_org_managenment_role tromr
        left join sys_user su on su.user_id = tromr.user_id
        left join sys_dept sd on sd.dept_id = tromr.dept_id
        where tromr.role_id = #{roleId}
    </select>

    <select id="selectTblRuleOrgManagenmentRoleByRoleIds" parameterType="Long" resultMap="TblRuleOrgManagenmentRoleResult">
        <include refid="selectTblRuleOrgManagenmentRoleVo"/>
        where role_id in
        <foreach item="roleId" collection="array" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>

    <insert id="insertTblRuleOrgManagenmentRole" parameterType="TblRuleOrgManagenmentRole">
        insert into tbl_rule_org_managenment_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">role_id,</if>
            <if test="managementId != null">management_id,</if>
            <if test="roleName != null">role_name,</if>
            <if test="roleJob != null">role_job,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="sortNum != null">sort_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">#{roleId},</if>
            <if test="managementId != null">#{managementId},</if>
            <if test="roleName != null">#{roleName},</if>
            <if test="roleJob != null">#{roleJob},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="sortNum != null">#{sortNum},</if>
         </trim>
    </insert>

    <update id="updateTblRuleOrgManagenmentRole" parameterType="TblRuleOrgManagenmentRole">
        update tbl_rule_org_managenment_role
        <trim prefix="SET" suffixOverrides=",">
            <if test="managementId != null">management_id = #{managementId},</if>
            <if test="roleName != null">role_name = #{roleName},</if>
            <if test="roleJob != null">role_job = #{roleJob},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="sortNum != null">sort_num = #{sortNum},</if>
        </trim>
        where role_id = #{roleId}
    </update>

    <delete id="deleteTblRuleOrgManagenmentRoleByRoleId" parameterType="Long">
        delete from tbl_rule_org_managenment_role where role_id = #{roleId}
    </delete>

    <delete id="deleteTblRuleOrgManagenmentRoleByRoleIds" parameterType="String">
        delete from tbl_rule_org_managenment_role where role_id in
        <foreach item="roleId" collection="array" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>
</mapper>
