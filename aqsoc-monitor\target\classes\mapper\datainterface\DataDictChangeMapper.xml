<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.datainterface.mapper.DataDictChangeMapper">

    <resultMap type="DataDictChange" id="DataDictChangeResult">
        <result property="id"    column="id"    />
        <result property="dictName"    column="dict_name"    />
        <result property="dictArray"    column="dict_array"    />
        <result property="dictDesc"    column="dict_desc"    />
        <result property="dictConfig"    column="dict_config"    />
    </resultMap>

    <sql id="selectDataDictChangeVo">
        select id, dict_name, dict_array, dict_desc, dict_config from data_dict_change
    </sql>

    <select id="selectDataDictChangeList" parameterType="DataDictChange" resultMap="DataDictChangeResult">
        <include refid="selectDataDictChangeVo"/>
        <where>
            <if test="dictName != null  and dictName != ''"> and dict_name like concat('%', #{dictName}, '%')</if>
            <if test="dictArray != null  and dictArray != ''"> and dict_array like concat('%', #{dictArray}, '%')</if>
            <if test="dictDesc != null  and dictDesc != ''"> and dict_desc = #{dictDesc}</if>
            <if test="dictConfig != null  and dictConfig != ''"> and dict_config = #{dictConfig}</if>
        </where>
    </select>

    <select id="selectDataDictChangeById" parameterType="Long" resultMap="DataDictChangeResult">
        <include refid="selectDataDictChangeVo"/>
        where id = #{id}
    </select>

    <select id="selectDataDictChangeByIds" parameterType="Long" resultMap="DataDictChangeResult">
        <include refid="selectDataDictChangeVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertDataDictChange" parameterType="DataDictChange" useGeneratedKeys="true" keyProperty="id">
        insert into data_dict_change
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictName != null">dict_name,</if>
            <if test="dictArray != null">dict_array,</if>
            <if test="dictDesc != null">dict_desc,</if>
            <if test="dictConfig != null">dict_config,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dictName != null">#{dictName},</if>
            <if test="dictArray != null">#{dictArray},</if>
            <if test="dictDesc != null">#{dictDesc},</if>
            <if test="dictConfig != null">#{dictConfig},</if>
        </trim>
    </insert>

    <update id="updateDataDictChange" parameterType="DataDictChange">
        update data_dict_change
        <trim prefix="SET" suffixOverrides=",">
            <if test="dictName != null">dict_name = #{dictName},</if>
            <if test="dictArray != null">dict_array = #{dictArray},</if>
            <if test="dictDesc != null">dict_desc = #{dictDesc},</if>
            <if test="dictConfig != null">dict_config = #{dictConfig},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataDictChangeById" parameterType="Long">
        delete from data_dict_change where id = #{id}
    </delete>

    <delete id="deleteDataDictChangeByIds" parameterType="String">
        delete from data_dict_change where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>