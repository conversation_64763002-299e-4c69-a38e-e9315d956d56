{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\form.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\form.vue", "mtime": 1756369456012}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["form.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form.vue", "sourceRoot": "src/views/aqsoc/hw-work", "sourcesContent": ["<template>\n  <el-dialog :title=\"!dataForm.id ? '新增HW事务' :isCopy?'复制HW事务':'编辑HW事务'\"\n             :close-on-click-modal=\"false\" append-to-body\n             :visible.sync=\"visible\" class=\"JNPF-dialog JNPF-dialog_center\" lock-scroll\n             width=\"800px\">\n    <el-row :gutter=\"16\" class=\"\">\n      <el-form ref=\"elForm\" :model=\"dataForm\" :rules=\"rules\" size=\"small\" label-width=\"150px\" label-position=\"right\">\n        <template v-if=\"!loading\">\n          <el-col :span=\"12\">\n            <jnpf-form-tip-item label=\"年度\" prop=\"year\">\n              <el-select v-model=\"dataForm.year\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"item in yearList\"\n                  :key=\"item\"\n                  :label=\"item\"\n                  :value=\"item\">\n                </el-option>\n              </el-select>\n            </jnpf-form-tip-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <jnpf-form-tip-item label=\"所属单位\" prop=\"deptId\">\n              <dept-select\n                v-model=\"dataForm.deptId\"\n                is-current\n              />\n            </jnpf-form-tip-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <jnpf-form-tip-item label=\"联络人\" prop=\"userId\">\n              <user-select v-model=\"dataForm.userId\" placeholder=\"请选择\" multiple/>\n            </jnpf-form-tip-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <jnpf-form-tip-item label=\"HW开始时间\" prop=\"hwStart\">\n              <el-date-picker v-model=\"dataForm.hwStart\" type=\"datetime\"  style=\"width: 192px;\"value-format=\"yyyy-MM-dd HH:mm:ss\"\n                              placeholder=\"请选择HW开始时间\" clearable>\n              </el-date-picker>\n            </jnpf-form-tip-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <jnpf-form-tip-item label=\"HW结束时间\" prop=\"hwEnd\">\n              <el-date-picker v-model=\"dataForm.hwEnd\" type=\"datetime\" style=\"width: 192px;\" value-format=\"yyyy-MM-dd HH:mm:ss\"\n                              placeholder=\"请选择HW结束时间\" clearable>\n              </el-date-picker>\n            </jnpf-form-tip-item>\n          </el-col>\n        </template>\n      </el-form>\n    </el-row>\n    <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"visible = false\"> 取 消</el-button>\n          <el-button type=\"primary\" @click=\"dataFormSubmit()\" :loading=\"btnLoading\"> 确 定</el-button>\n    </span>\n  </el-dialog>\n</template>\n<script>\nimport {addData, getInfo,copyWork} from '@/api/aqsoc/work-hw/crud'\nimport {mapGetters} from \"vuex\";\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nimport DeptSelect from \"@/views/components/select/deptSelect.vue\";\n\nexport default {\n  components: {DeptSelect, UserSelect},\n  props: [],\n  data() {\n    return {\n      dataFormSubmitType: 0,\n      continueBtnLoading: false,\n      index: 0,\n      prevDis: false,\n      nextDis: false,\n      allList: [],\n      visible: false,\n      loading: false,\n      btnLoading: false,\n      currTableConf: {},\n      addTableConf: {},\n      //可选范围默认值\n      ableAll: {},\n      tableRows: {},\n      currVmodel: \"\",\n      dataForm: {\n        id: '',\n        templateId: '',\n        name: '',\n        year: '',\n        userId: '',\n        hwStart: '',\n        hwEnd: '',\n        supportOrgs: '',\n        supportUsers: '',\n        dataJson: '',\n      },\n      rules:\n        {\n          year: [{required: true, message: `年度不能为空`, trigger: 'change'}],\n          //userId: [{required: true, message: `责任人不能为空`, trigger: 'change'}],\n          hwStart: [{required: true, message: `HW开始时间不能为空`, trigger: 'change'}],\n          hwEnd: [{required: true, message: `HW结束时间不能为空`, trigger: 'change'}],\n          deptId: [{required: true, message: `所属单位不能为空`, trigger: 'change'}],\n        },\n      typeOptions: [],\n      typeProps: {\"label\": \"fullName\", \"value\": \"enCode\"},\n      childIndex: -1,\n      yearList:[],\n      isEdit: false,\n      isCopy: false,\n    }\n  },\n  computed: {\n    ...mapGetters(['userInfo']),\n  },\n  watch: {},\n  created() {\n    this.dataAll()\n    // 获取当前年份\n    const currentYear = new Date().getFullYear();\n    this.yearList= [currentYear - 1, currentYear, currentYear + 1];\n  },\n  mounted() {\n  },\n  methods: {\n    dataAll() {\n      this.gettypeOptions();\n    },\n    gettypeOptions() {\n\n    },\n    init(id, isDetail, allList,isCopy) {\n      this.isCopy = isCopy;\n      this.dataForm.id = id || 0;\n      this.visible = true;\n      this.$nextTick(() => {\n        this.$refs['elForm'].resetFields();\n        if (this.dataForm.id) {\n          this.loading = true\n          getInfo(this.dataForm.id).then(res => {\n            this.dataInfo(res.data)\n            this.loading = false\n          });\n        } else {\n          this.clearData()\n        }\n      });\n    },\n    // 表单提交\n    dataFormSubmit(type) {\n      this.dataFormSubmitType = type ? type : 0\n      this.$refs['elForm'].validate((valid) => {\n        if (valid) {\n          this.request()\n        }\n      })\n    },\n    request() {\n      this.btnLoading = true\n      let _data = this.dataList()\n      if(this.isCopy){\n        copyWork(_data).then(res => {\n          this.$message({\n            message: res.msg,\n            type: 'success',\n            duration: 1000,\n            onClose: () => {\n              this.visible = false\n              this.btnLoading = false\n              this.$emit('refresh', true)\n            }\n          })\n        })\n        return;\n      }\n      if (!this.dataForm.id) {\n        addData(_data).then((res) => {\n          this.$message({\n            message: res.msg,\n            type: 'success',\n            duration: 1000,\n            onClose: () => {\n              this.visible = false\n              this.btnLoading = false\n              this.$emit('refresh', true)\n            }\n          })\n        }).catch(() => {\n          this.btnLoading = false\n        })\n      } else {\n        addData(_data).then((res) => {\n          this.$message({\n            message: res.msg,\n            type: 'success',\n            duration: 1000,\n            onClose: () => {\n              this.visible = false\n              this.btnLoading = false\n              this.$emit('refresh', true)\n            }\n          })\n        }).catch(() => {\n          this.btnLoading = false\n          this.continueBtnLoading = false\n        })\n      }\n    },\n    clearData() {\n      this.dataForm = {\n        id: '',\n        templateId: '',\n        name: '',\n        year: '',\n        userId: '',\n        hwStart: '',\n        hwEnd: '',\n        supportOrgs: '',\n        supportUsers: '',\n        dataJson: '',\n      }\n    },\n    dataList() {\n      let userIdData = this.dataForm.userId.split(','); // 将字符串按逗号分割成数组\n      let uniqueUserIdData = [...new Set(userIdData)]; // 使用 Set 去重\n      this.dataForm.userId = uniqueUserIdData.join(','); // 将去重后的数组转换为字符串\n      var _data = JSON.parse(JSON.stringify(this.dataForm));\n      return _data;\n    },\n    dataInfo(dataAll) {\n      let _dataAll = dataAll\n      this.dataForm = _dataAll\n      this.isEdit = true\n      this.dataAll()\n      this.childIndex = -1\n    },\n  },\n}\n\n</script>\n"]}]}