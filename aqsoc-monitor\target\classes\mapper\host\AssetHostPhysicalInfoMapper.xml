<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.host.mapper.AssetHostPhysicalInfoMapper">

    <select id="queryPage" resultType="com.ruoyi.monitor2.host.model.AssetHostPhysicalInfoListVO">
        select a.* from asset_host_physical_info a 
        <where>
                <if test="query.id!=null">
                    and a.id = #{query.id}
                </if>
                <if test="query.arch!=null and query.arch!=''">
                    and a.arch like concat('%', #{query.arch}, '%')
                </if>
                <if test="query.cpu!=null and query.cpu!=''">
                    and a.cpu like concat('%', #{query.cpu}, '%')
                </if>
                <if test="query.cpuCore!=null">
                    and a.cpu_core = #{query.cpuCore}
                </if>
                <if test="query.memoryTotal!=null">
                    and a.memory_total = #{query.memoryTotal}
                </if>
                <if test="query.product!=null and query.product!=''">
                    and a.product like concat('%', #{query.product}, '%')
                </if>
                <if test="query.sn!=null and query.sn!=''">
                    and a.sn like concat('%', #{query.sn}, '%')
                </if>
                <if test="query.type!=null and query.type!=''">
                    and a.type like concat('%', #{query.type}, '%')
                </if>
                <if test="query.vendor!=null and query.vendor!=''">
                    and a.vendor like concat('%', #{query.vendor}, '%')
                </if>
        </where>
    </select>
    <select id="queryById" resultType="com.ruoyi.monitor2.host.model.AssetHostPhysicalInfoListVO">
        select a.* from asset_host_physical_info a 
        where a.id=#{id}
    </select>
</mapper>
