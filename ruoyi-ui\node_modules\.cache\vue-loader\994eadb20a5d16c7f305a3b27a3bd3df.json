{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\invadeAttack.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\invadeAttack.vue", "mtime": 1756369455984}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgYmF0Y2hEZWxldGVIb3N0SW52YXNpb24sCiAgZGVsZXRlSG9zdEludmFzaW9uLAogIGRpc3Bvc2VIb3N0SW52YXNpb24sIGdldEhvc3RJbnRydXNpb25BdHRhY2tEZXRhaWwsIGdldEhvc3RJbnZhc2lvbkRldGFpbCwKICBnZXRIb3N0SW52YXNpb25MaXN0Cn0gZnJvbSAiQC9hcGkvbW9uaXRvci9ob3N0QWdlbnQiOwppbXBvcnQge2xpc3REZXZpY2VDb25maWd9IGZyb20gIkAvYXBpL2Zmc2FmZS9kZXZpY2VDb25maWciOwppbXBvcnQge3BhcnNlVGltZX0gZnJvbSAiQC91dGlscy9ydW95aSI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImludmFkZUF0dGFjayIsCiAgZGljdHM6IFsnaGFuZGxlX3N0YXRlJ10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHNob3dBbGw6IGZhbHNlLAogICAgICByYW5nZVRpbWU6IFtdLAogICAgICBmb3JtOiB7fSwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgfSwKICAgICAgdG90YWw6IDAsCiAgICAgIGxpc3Q6IFtdLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgbXVsdGlwbGVTZWxlY3Rpb246IFtdLAogICAgICBzaG93SGFuZGxlQmF0Y2hEaWFsb2c6IGZhbHNlLAogICAgICBzaG93RGV0YWlsRGlhbG9nOiBmYWxzZSwKICAgICAgZm9ybVJ1bGVzOiB7CiAgICAgICAgaGFuZGxlU3RhdGU6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeWkhOe9rueKtuaAgScsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICBdLAogICAgICB9LAogICAgICBkZXRhaWxEYXRhOiB7fSwKICAgICAgZGV0YWlsVGl0bGVOYW1lOiAnJywKICAgICAgZGV2aWNlQ29uZmlnTGlzdDogW10KICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmluaXQoKQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGFsZXJ0SW5mb0xhYmVsKCkgewogICAgICB0cnkgewogICAgICAgIGxldCBkZXRhaWxEYXRhID0gSlNPTi5wYXJzZSh0aGlzLmRldGFpbERhdGEuZGV0YWlsRGF0YSk7CiAgICAgICAgbGV0IGFsZXJ0SW5mbyA9IGRldGFpbERhdGEuYWxlcnRfaW5mbyB8fCAnJzsKCiAgICAgICAgLy8g6L+U5Zue5YaS5Y+35LmL5YmN55qE5YaF5a65CiAgICAgICAgaWYgKGFsZXJ0SW5mbyAmJiBhbGVydEluZm8uaW5jbHVkZXMoJzonKSkgewogICAgICAgICAgcmV0dXJuIGFsZXJ0SW5mby5zdWJzdHJpbmcoMCwgYWxlcnRJbmZvLmluZGV4T2YoJzonKSkgKyAn77yaJzsKICAgICAgICB9CiAgICAgICAgcmV0dXJuIGFsZXJ0SW5mbzsKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekGRldGFpbERhdGHlpLHotKUnLCBlKTsKICAgICAgICByZXR1cm4gJyc7CiAgICAgIH0KICAgIH0sCiAgICBhbGVydEluZm9WYWx1ZSgpIHsKICAgICAgdHJ5IHsKICAgICAgICBsZXQgZGV0YWlsRGF0YSA9IEpTT04ucGFyc2UodGhpcy5kZXRhaWxEYXRhLmRldGFpbERhdGEpOwogICAgICAgIGxldCBhbGVydEluZm8gPSBkZXRhaWxEYXRhLmFsZXJ0X2luZm8gfHwgJyc7CgogICAgICAgIC8vIOi/lOWbnuWGkuWPt+S5i+WQjueahOWGheWuuQogICAgICAgIGlmIChhbGVydEluZm8gJiYgYWxlcnRJbmZvLmluY2x1ZGVzKCc6JykpIHsKICAgICAgICAgIHJldHVybiBhbGVydEluZm8uc3Vic3RyaW5nKGFsZXJ0SW5mby5pbmRleE9mKCc6JykgKyAyKTsgLy8gKzIg5piv5Li65LqG6Lez6L+H5YaS5Y+35ZKM56m65qC8CiAgICAgICAgfQogICAgICAgIHJldHVybiAnJzsKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekGRldGFpbERhdGHlpLHotKUnLCBlKTsKICAgICAgICByZXR1cm4gJyc7CiAgICAgIH0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGluaXQoKSB7CiAgICAgIHRoaXMuc2V0RGVmYXVsdERhdGVSYW5nZSgpOwogICAgICB0aGlzLmdldERldmljZUNvbmZpZ0xpc3QoKTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAoKICAgIC8vIOiOt+WPluWIl+ihqAogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICBnZXRIb3N0SW52YXNpb25MaXN0KHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsKICAgICAgICBjb25zb2xlLmxvZyhyZXMsICdyZXMnKQogICAgICAgIHRoaXMubGlzdCA9IHJlcy5yb3dzCiAgICAgICAgdGhpcy50b3RhbCA9IHJlcy50b3RhbAogICAgICB9KS5maW5hbGx5KCgpID0+IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKCiAgICBnZXREZXZpY2VDb25maWdMaXN0KCl7CiAgICAgIGxpc3REZXZpY2VDb25maWcoe3F1ZXJ5QWxsRGF0YTogdHJ1ZX0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmRldmljZUNvbmZpZ0xpc3QgPSByZXMucm93czsKICAgICAgfSkKICAgIH0sCgogICAgLyoqIOiuvue9rum7mOiupOaXpeacn+iMg+WbtCAqLwogICAgc2V0RGVmYXVsdERhdGVSYW5nZSgpIHsKICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKQogICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCkKICAgICAgc3RhcnQuc2V0VGltZShzdGFydC5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogNykKCiAgICAgIC8vIOW8gOWni+aXtumXtOS4uiAwMDowMDowMO+8jOe7k+adn+aXtumXtOS4uiAyMzo1OTo1OQogICAgICBzdGFydC5zZXRIb3VycygwLCAwLCAwLCAwKQogICAgICBlbmQuc2V0SG91cnMoMjMsIDU5LCA1OSwgOTk5KQoKICAgICAgdGhpcy5yYW5nZVRpbWUgPSBbCiAgICAgICAgdGhpcy5wYXJzZVRpbWUoc3RhcnQsICd7eX0te219LXtkfSB7aH06e2l9OntzfScpLAogICAgICAgIHRoaXMucGFyc2VUaW1lKGVuZCwgJ3t5fS17bX0te2R9IHtofTp7aX06e3N9JykKICAgICAgXQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmJlZ2luVGltZSA9IHRoaXMucmFuZ2VUaW1lWzBdCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kVGltZSA9IHRoaXMucmFuZ2VUaW1lWzFdCiAgICB9LAoKICAgIC8vIOafpeivogogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIGlmICh0aGlzLnJhbmdlVGltZSAhPSBudWxsKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5iZWdpblRpbWUgPSBwYXJzZVRpbWUodGhpcy5yYW5nZVRpbWVbMF0pOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kVGltZSA9IHBhcnNlVGltZSh0aGlzLnJhbmdlVGltZVsxXSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5iZWdpblRpbWUgPSBudWxsOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kVGltZSA9IG51bGw7CiAgICAgIH0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy4kZW1pdCgnZ2V0TGlzdCcsIHsgLi4udGhpcy5xdWVyeVBhcmFtcyB9KQogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCgogICAgLy8g5aSE572u54q25oCBCiAgICBoYW5kbGVTdGF0ZUZvcm1hdHRlcihyb3csIGNvbHVtbiwgY2VsbFZhbHVlLCBpbmRleCkgewogICAgICBpZiAoY2VsbFZhbHVlID09PSAwKSB7CiAgICAgICAgcmV0dXJuICfmnKrlpITnva4nCiAgICAgIH0gZWxzZSBpZiAoY2VsbFZhbHVlID09PSAxKSB7CiAgICAgICAgcmV0dXJuICflt7LlpITnva4nCiAgICAgIH0gZWxzZSBpZiAoY2VsbFZhbHVlID09PSAyKSB7CiAgICAgICAgcmV0dXJuICflv73nlaUnCiAgICAgIH0KICAgIH0sCgogICAgLy8g5aSE572u6LWL5YC8CiAgICBoYW5kbGVEaXNwb3NlKHJvdykgewogICAgICBpZiAocm93LmhhbmRsZVN0YXRlID09PSAyICkgewogICAgICAgIHRoaXMuZm9ybS5oYW5kbGVTdGF0ZSA9IHJvdy5oYW5kbGVTdGF0ZTsKICAgICAgICB0aGlzLmZvcm0uaGFuZGxlRGVzYyA9IHJvdy5oYW5kbGVEZXNjOwogICAgICB9ZWxzZSB7CiAgICAgICAgdGhpcy5mb3JtID0gewogICAgICAgICAgaGFuZGxlRGVzYzogJycsCiAgICAgICAgICBoYW5kbGVTdGF0ZTogJycKICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy5mb3JtLmlkID0gcm93LmlkOwogICAgICB0aGlzLnNob3dIYW5kbGVCYXRjaERpYWxvZyA9IHRydWU7CiAgICB9LAoKICAgIC8vIOWkhOe9rgogICAgYmF0Y2hEaXNwb3NlKCkgewogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgZGlzcG9zZUhvc3RJbnZhc2lvbih0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WkhOe9ruaIkOWKnycpCiAgICAgICAgICAgICAgdGhpcy5zaG93SGFuZGxlQmF0Y2hEaWFsb2cgPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLiRyZWZzWydmb3JtJ10ucmVzZXRGaWVsZHMoKQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCgogICAgLy8g6K+m5oOFCiAgICBoYW5kbGVEZXRhaWwocm93KSB7CiAgICAgIGdldEhvc3RJbnRydXNpb25BdHRhY2tEZXRhaWwocm93LmlkKS50aGVuKHJlcyA9PiB7CiAgICAgICAgY29uc29sZS5sb2cocmVzLCAncmVzJyk7CiAgICAgICAgdGhpcy5zaG93RGV0YWlsRGlhbG9nID0gdHJ1ZTsKICAgICAgICB0aGlzLmRldGFpbERhdGEgPSByZXMuZGF0YTsKICAgICAgfSkKICAgIH0sCgogICAgLy8g5Yig6ZmkCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuWIoOmZpOivpeWFpeS+teaUu+WHuz8nLCAn6K2m5ZGKJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgZGVsZXRlSG9zdEludmFzaW9uKHJvdy5pZCkudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQogICAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpCiAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICB9KQogICAgfSwKCiAgICAvLyDmibnph4/liKDpmaQKICAgIGhhbmRsZUJhdGNoRGV0YWlsKCkgewogICAgICBjb25zb2xlLmxvZyh0aGlzLm11bHRpcGxlU2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpLmpvaW4oJywnKSwgJ3RoaXMubXVsdGlwbGVTZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCknKQogICAgICBpZiAoIXRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqemcgOimgeWIoOmZpOeahOWFpeS+teaUu+WHu+aVsOaNricpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5Yig6Zmk5omA6YCJ5YWl5L615pS75Ye75pWw5o2uPycsICforablkYonLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgYmF0Y2hEZWxldGVIb3N0SW52YXNpb24odGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKS5qb2luKCcsJykpLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQogICAgICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkKICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgICAgfQogICAgfSwKICAgIC8vIOWvvOWHugogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICB0aGlzLmRvd25sb2FkKAogICAgICAgICIvZmZzYWZlL2hvc3RJbnRydXNpb25BdHRhY2svZXhwb3J0IiwKICAgICAgICB7CiAgICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zLAogICAgICAgIH0sCiAgICAgICAgYOWFpeS+teaUu+WHu18ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YAogICAgICApOwogICAgfSwKICAgIC8vIOWkmumAiQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gdmFsCiAgICB9LAogICAgLy8g6YeN572uCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gewogICAgICAgIGFsZXJ0TmFtZTogbnVsbCwKICAgICAgICBzaXA6IG51bGwsCiAgICAgICAgZGlwOiBudWxsLAogICAgICAgIGhhbmRsZVN0YXRlOiBudWxsLAogICAgICAgIGRldmljZUlkOiBudWxsLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgIH0KICAgICAgdGhpcy5zZXREZWZhdWx0RGF0ZVJhbmdlKCk7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQogICAgfSwKICB9Cn0K"}, {"version": 3, "sources": ["invadeAttack.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "invadeAttack.vue", "sourceRoot": "src/views/frailty/event/component", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label-width=\"100px\" label=\"最近告警时间\">\n                <el-date-picker\n                  v-model=\"rangeTime\"\n                  type=\"datetimerange\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                  :default-time=\"['00:00:00', '23:59:59']\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"攻击源IP\">\n                <el-input v-model=\"queryParams.sip\" placeholder=\"请输入攻击源IP\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"目标IP\">\n                <el-input v-model=\"queryParams.dip\" placeholder=\"请输入目标IP\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\"\n                >查询\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\"\n                >重置\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置状态\">\n                <el-select v-model=\"queryParams.handleState\" placeholder=\"请选择处置状态\">\n                  <el-option label=\"未处置\" :value=\"0\"></el-option>\n                  <el-option label=\"已处置\" :value=\"1\"></el-option>\n                  <el-option label=\"忽略\" :value=\"2\"></el-option>\n                  <!--                  <el-option label=\"处置中\" value=\"3\"></el-option>-->\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属探针\">\n                <el-select v-model=\"queryParams.deviceConfigId\" filterable clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in deviceConfigList\"\n                    :key=\"item.id\"\n                    :label=\"item.deviceName\"\n                    :value=\"item.id\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">入侵攻击列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"!multipleSelection.length\"\n                  @click=\"handleBatchDetail\"\n                >批量删除\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <div class=\"tableContainer\">\n          <el-table\n            :data=\"list\"\n            height=\"100%\"\n            v-loading=\"loading\"\n            @selection-change=\"handleSelectionChange\"\n          >\n            <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n            <el-table-column label=\"攻击源IP\" align=\"left\">\n              <template slot-scope=\"scope\">\n                <span class=\"table-serial-number\">{{ scope.row.sip }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"目标IP\" align=\"left\" prop=\"dip\"/>\n            <el-table-column label=\"告警名称\" align=\"left\" prop=\"alertName\">\n              <template slot-scope=\"scope\">\n                <span\n                  class=\"table-serial-number\"\n                  :style=\"scope.row.alertName.includes('web') ? {color: '#bf1a1a', backgroundColor: '#fd828233', borderColor: '#bf1a1a'} : scope.row.alertName.includes('主机') ? {color: '#1EA086', backgroundColor: '#1ea0861a', borderColor: '#1EA086'} : scope.row.alertName.includes('SSH') ? {color: '#1a2bbf', backgroundColor: '#7899e033', borderColor: '#1a2bbf'} : ''\">{{ scope.row.alertName }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"开始时间\" align=\"left\" prop=\"startTime\"/>\n            <el-table-column label=\"最近告警时间\" align=\"left\" prop=\"updateTime\"/>\n            <el-table-column label=\"处置状态\" align=\"left\" prop=\"handleState\" :formatter=\"handleStateFormatter\"/>\n            <el-table-column label=\"操作\" width=\"150\" :show-overflow-tooltip=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button\n                  size=\"mini\"\n                  type=\"text\"\n                  @click=\"handleDetail(scope.row)\"\n                >详情\n                </el-button>\n                <el-button\n                  size=\"mini\"\n                  type=\"text\"\n                  class=\"table-delBtn\"\n                  @click=\"handleDelete(scope.row)\"\n                >删除\n                </el-button>\n                <el-button\n                  size=\"mini\"\n                  type=\"text\"\n                  v-if=\"scope.row.handleState === 0 || scope.row.handleState === 2\"\n                  @click=\"handleDispose(scope.row)\"\n                >处置\n                </el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleBatchDialog\"\n      width=\"600px\"\n      append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"formRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\n          <el-select v-model=\"form.handleState\" clearable placeholder=\"请选择处置状态\">\n            <el-option v-for=\"dict in dict.type.handle_state\"\n                       :key=\"dict.value\" :label=\"dict.label\"\n                       :value=\"parseInt(dict.value)\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"form.handleDesc\" placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showHandleBatchDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"batchDispose\">确定</el-button>\n      </span>\n    </el-dialog>\n\n    <el-dialog\n      title=\"详情\"\n      :visible.sync=\"showDetailDialog\"\n      width=\"800px\"\n      class=\"detail-dialog\"\n      append-to-body>\n      <div class=\"customForm-container\" style=\"height: 100%; padding: 10px\">\n<!--        <div class=\"my-title\"><img src=\"@/assets/images/application/jbxx.png\" alt=\"\">基本信息</div>-->\n        <el-descriptions\n          class=\"custom-column\"\n          direction=\"vertical\"\n          size=\"medium\"\n          :colon=\"false\"\n          label-class-name=\"custom-label-style\"\n          content-class-name=\"custom-content-style\"\n          :column=\"3\"\n        >\n          <template v-if=\"detailData.detailType === 'vuln_scan'\">\n            <el-descriptions-item :label=\"alertInfoLabel\">{{ alertInfoValue }}</el-descriptions-item>\n          </template>\n          <template v-if=\"detailData.detailType === 'brute_force'\">\n            <el-descriptions-item label=\"源IP\">{{ JSON.parse(detailData.detailData).ip }}</el-descriptions-item>\n            <el-descriptions-item label=\"类型\">{{ JSON.parse(detailData.detailData).type }}</el-descriptions-item>\n            <el-descriptions-item label=\"登录账号\">{{ JSON.parse(detailData.detailData).login_account }}</el-descriptions-item>\n            <el-descriptions-item label=\"登录结果\">{{ JSON.parse(detailData.detailData).login_status }}</el-descriptions-item>\n            <el-descriptions-item label=\"尝试次数\">{{ JSON.parse(detailData.detailData).try_counts }}</el-descriptions-item>\n            <el-descriptions-item label=\"日志记录开始时间\">{{ JSON.parse(detailData.detailData).log_start_time }}</el-descriptions-item>\n          </template>\n          <template v-if=\"detailData.detailType === 'web_attack'\">\n            <el-descriptions-item label=\"告警名称\">{{ JSON.parse(detailData.detailData).alert_name }}</el-descriptions-item>\n            <el-descriptions-item label=\"攻击源IP\">{{ JSON.parse(detailData.detailData).sip }}</el-descriptions-item>\n            <el-descriptions-item label=\"目标IP\">{{ JSON.parse(detailData.detailData).dip }}</el-descriptions-item>\n            <el-descriptions-item label=\"web端口\">{{ JSON.parse(detailData.detailData).dport }}</el-descriptions-item>\n            <el-descriptions-item label=\"日志文件\">{{ JSON.parse(detailData.detailData).log_file }}</el-descriptions-item>\n            <el-descriptions-item label=\"攻击时长\">{{ JSON.parse(detailData.detailData).attack_duration }}</el-descriptions-item>\n            <template>\n              <el-descriptions-item\n                :span=\"3\"\n                :key=\"item.key\"\n                v-for=\"item in JSON.parse(detailData.detailData).log_alert_info\"\n                :label=\"item.log_alert_title\">{{ item.log_alert_meta }}</el-descriptions-item>\n            </template>\n          </template>\n        </el-descriptions>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  batchDeleteHostInvasion,\n  deleteHostInvasion,\n  disposeHostInvasion, getHostIntrusionAttackDetail, getHostInvasionDetail,\n  getHostInvasionList\n} from \"@/api/monitor/hostAgent\";\nimport {listDeviceConfig} from \"@/api/ffsafe/deviceConfig\";\nimport {parseTime} from \"@/utils/ruoyi\";\n\nexport default {\n  name: \"invadeAttack\",\n  dicts: ['handle_state'],\n  data() {\n    return {\n      showAll: false,\n      rangeTime: [],\n      form: {},\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      total: 0,\n      list: [],\n      loading: false,\n      multipleSelection: [],\n      showHandleBatchDialog: false,\n      showDetailDialog: false,\n      formRules: {\n        handleState: [\n          {required: true, message: '请选择处置状态', trigger: 'blur'}\n        ],\n      },\n      detailData: {},\n      detailTitleName: '',\n      deviceConfigList: []\n    }\n  },\n  created() {\n    this.init()\n  },\n  computed: {\n    alertInfoLabel() {\n      try {\n        let detailData = JSON.parse(this.detailData.detailData);\n        let alertInfo = detailData.alert_info || '';\n\n        // 返回冒号之前的内容\n        if (alertInfo && alertInfo.includes(':')) {\n          return alertInfo.substring(0, alertInfo.indexOf(':')) + '：';\n        }\n        return alertInfo;\n      } catch (e) {\n        console.error('解析detailData失败', e);\n        return '';\n      }\n    },\n    alertInfoValue() {\n      try {\n        let detailData = JSON.parse(this.detailData.detailData);\n        let alertInfo = detailData.alert_info || '';\n\n        // 返回冒号之后的内容\n        if (alertInfo && alertInfo.includes(':')) {\n          return alertInfo.substring(alertInfo.indexOf(':') + 2); // +2 是为了跳过冒号和空格\n        }\n        return '';\n      } catch (e) {\n        console.error('解析detailData失败', e);\n        return '';\n      }\n    }\n  },\n  methods: {\n    init() {\n      this.setDefaultDateRange();\n      this.getDeviceConfigList();\n      this.getList();\n    },\n\n    // 获取列表\n    getList() {\n      this.loading = true\n      getHostInvasionList(this.queryParams).then(res => {\n        console.log(res, 'res')\n        this.list = res.rows\n        this.total = res.total\n      }).finally(() => {\n        this.loading = false\n      })\n    },\n\n    getDeviceConfigList(){\n      listDeviceConfig({queryAllData: true}).then(res => {\n        this.deviceConfigList = res.rows;\n      })\n    },\n\n    /** 设置默认日期范围 */\n    setDefaultDateRange() {\n      const end = new Date()\n      const start = new Date()\n      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\n\n      // 开始时间为 00:00:00，结束时间为 23:59:59\n      start.setHours(0, 0, 0, 0)\n      end.setHours(23, 59, 59, 999)\n\n      this.rangeTime = [\n        this.parseTime(start, '{y}-{m}-{d} {h}:{i}:{s}'),\n        this.parseTime(end, '{y}-{m}-{d} {h}:{i}:{s}')\n      ]\n      this.queryParams.beginTime = this.rangeTime[0]\n      this.queryParams.endTime = this.rangeTime[1]\n    },\n\n    // 查询\n    handleQuery() {\n      if (this.rangeTime != null) {\n        this.queryParams.beginTime = parseTime(this.rangeTime[0]);\n        this.queryParams.endTime = parseTime(this.rangeTime[1]);\n      } else {\n        this.queryParams.beginTime = null;\n        this.queryParams.endTime = null;\n      }\n      this.queryParams.pageNum = 1;\n      this.$emit('getList', { ...this.queryParams })\n      this.getList();\n    },\n\n    // 处置状态\n    handleStateFormatter(row, column, cellValue, index) {\n      if (cellValue === 0) {\n        return '未处置'\n      } else if (cellValue === 1) {\n        return '已处置'\n      } else if (cellValue === 2) {\n        return '忽略'\n      }\n    },\n\n    // 处置赋值\n    handleDispose(row) {\n      if (row.handleState === 2 ) {\n        this.form.handleState = row.handleState;\n        this.form.handleDesc = row.handleDesc;\n      }else {\n        this.form = {\n          handleDesc: '',\n          handleState: ''\n        }\n      }\n      this.form.id = row.id;\n      this.showHandleBatchDialog = true;\n    },\n\n    // 处置\n    batchDispose() {\n      this.$refs['form'].validate(valid => {\n        if (valid) {\n          disposeHostInvasion(this.form).then(res => {\n            if (res.code === 200) {\n              this.$message.success('处置成功')\n              this.showHandleBatchDialog = false;\n              this.$refs['form'].resetFields()\n              this.getList();\n            }\n          })\n        }\n      })\n    },\n\n    // 详情\n    handleDetail(row) {\n      getHostIntrusionAttackDetail(row.id).then(res => {\n        console.log(res, 'res');\n        this.showDetailDialog = true;\n        this.detailData = res.data;\n      })\n    },\n\n    // 删除\n    handleDelete(row) {\n      this.$confirm('是否删除该入侵攻击?', '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteHostInvasion(row.id).then(res => {\n          this.$message.success('删除成功')\n          this.handleQuery()\n        })\n      }).catch(() => {\n      })\n    },\n\n    // 批量删除\n    handleBatchDetail() {\n      console.log(this.multipleSelection.map(item => item.id).join(','), 'this.multipleSelection.map(item => item.id)')\n      if (!this.multipleSelection.length) {\n        return this.$message.error('请选择需要删除的入侵攻击数据')\n      } else {\n        this.$confirm('是否删除所选入侵攻击数据?', '警告', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          batchDeleteHostInvasion(this.multipleSelection.map(item => item.id).join(',')).then(res => {\n            this.$message.success('删除成功')\n            this.handleQuery()\n          })\n        })\n      }\n    },\n    // 导出\n    handleExport() {\n      this.download(\n        \"/ffsafe/hostIntrusionAttack/export\",\n        {\n          ...this.queryParams,\n        },\n        `入侵攻击_${new Date().getTime()}.xlsx`\n      );\n    },\n    // 多选\n    handleSelectionChange(val) {\n      this.multipleSelection = val\n    },\n    // 重置\n    resetQuery() {\n      this.queryParams = {\n        alertName: null,\n        sip: null,\n        dip: null,\n        handleState: null,\n        deviceId: null,\n        pageNum: 1,\n        pageSize: 10\n      }\n      this.setDefaultDateRange();\n      this.handleQuery()\n    },\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n.detail-dialog {\n  ::v-deep .el-dialog__body {\n    padding: 0 20px 20px;\n  }\n}\n.table-serial-number {\n  text-align: center;\n  padding: 8px;\n  border-radius: 10px;\n}\n</style>\n"]}]}