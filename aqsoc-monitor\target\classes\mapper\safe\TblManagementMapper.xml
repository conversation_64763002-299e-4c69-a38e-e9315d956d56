<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblManagementMapper">

    <resultMap type="TblManagement" id="TblManagementResult">
        <result property="id" column="id"/>
        <result property="assetId" column="asset_id"/>
        <result property="managerName" column="manager_name"/>
        <result property="managerPhone" column="manager_phone"/>
        <result property="managerEmail" column="manager_email"/>
        <result property="managerDept" column="manager_dept"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTblManagementVo">
        select id, asset_id, manager_name, manager_phone, manager_email, manager_dept, remark, create_by, create_time, update_by, update_time from tbl_management
    </sql>

    <select id="selectTblManagementList" parameterType="TblManagement" resultMap="TblManagementResult">
        <include refid="selectTblManagementVo"/>
        <where>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="managerName != null  and managerName != ''"> and manager_name like concat('%', #{managerName}, '%')</if>
            <if test="managerPhone != null  and managerPhone != ''"> and manager_phone = #{managerPhone}</if>
            <if test="managerEmail != null  and managerEmail != ''"> and manager_email = #{managerEmail}</if>
            <if test="managerDept != null  and managerDept != ''"> and manager_dept = #{managerDept}</if>
        </where>
    </select>

    <select id="selectTblManagementById" parameterType="Long" resultMap="TblManagementResult">
        <include refid="selectTblManagementVo"/>
        where id = #{id}
    </select>
    <select id="selectTblManagementByAssetIds" parameterType="Long" resultMap="TblManagementResult">
        <include refid="selectTblManagementVo"/>
        where asset_id in
            <foreach collection="array" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
    </select>

    <insert id="insertTblManagement" parameterType="TblManagement" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="managerName != null">manager_name,</if>
            <if test="managerPhone != null">manager_phone,</if>
            <if test="managerEmail != null">manager_email,</if>
            <if test="managerDept != null">manager_dept,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="managerName != null">#{managerName},</if>
            <if test="managerPhone != null">#{managerPhone},</if>
            <if test="managerEmail != null">#{managerEmail},</if>
            <if test="managerDept != null">#{managerDept},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTblManagement" parameterType="TblManagement">
        update tbl_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="managerName != null">manager_name = #{managerName},</if>
            <if test="managerPhone != null">manager_phone = #{managerPhone},</if>
            <if test="managerEmail != null">manager_email = #{managerEmail},</if>
            <if test="managerDept != null">manager_dept = #{managerDept},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblManagementById" parameterType="Long">
        delete from tbl_management where id = #{id}
    </delete>

    <delete id="deleteTblManagementByIds" parameterType="String">
        delete from tbl_management where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
