[{"assetType": 1, "formRef": "base", "formName": "基本信息", "fieldsItems": [{"fieldName": "系统名称", "fieldKey": "assetName", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "登录地址", "fieldKey": "url", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "域名", "fieldKey": "domainUrl", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "关联服务器", "fieldKey": "associationServer", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "所属单位", "fieldKey": "deptId", "required": false, "isQuery": false, "isShow": true, "sort": 4}, {"fieldName": "责任人/电话", "fieldKey": "manager", "required": false, "isQuery": false, "isShow": true, "sort": 5}, {"fieldName": "系统类型", "fieldKey": "systemType", "required": false, "isQuery": false, "isShow": true, "sort": 6}, {"fieldName": "其它系统备注", "fieldKey": "otherSystemNotes", "required": false, "isQuery": false, "isShow": true, "sort": 7}, {"fieldName": "建设模式", "fieldKey": "construct", "required": false, "isQuery": false, "isShow": true, "sort": 8}, {"fieldName": "登录方式", "fieldKey": "loginType", "required": false, "isQuery": false, "isShow": true, "sort": 9}, {"fieldName": "技术架构", "fieldKey": "technical", "required": false, "isQuery": false, "isShow": true, "sort": 10}, {"fieldName": "主部署方式", "fieldKey": "deploy", "required": false, "isQuery": false, "isShow": true, "sort": 11}, {"fieldName": "主部署网络", "fieldKey": "domainId", "required": false, "isQuery": false, "isShow": true, "sort": 12}, {"fieldName": "上线状态", "fieldKey": "state", "required": false, "isQuery": false, "isShow": true, "sort": 13}, {"fieldName": "上线时间", "fieldKey": "uodTime", "required": false, "isQuery": false, "isShow": true, "sort": 14}, {"fieldName": "关键基础设施", "fieldKey": "iskey", "required": false, "isQuery": false, "isShow": true, "sort": 15}, {"fieldName": "开发合作企业", "fieldKey": "vendor", "required": false, "isQuery": false, "isShow": true, "sort": 16}, {"fieldName": "产品名称", "fieldKey": "productName", "required": false, "isQuery": false, "isShow": true, "sort": 17}, {"fieldName": "版本号", "fieldKey": "versionNumber", "required": false, "isQuery": false, "isShow": true, "sort": 18}, {"fieldName": "自定义标签", "fieldKey": "tags", "required": false, "isQuery": false, "isShow": true, "sort": 19}, {"fieldName": "是否开放公网", "fieldKey": "isOpenNetwork", "required": false, "isQuery": false, "isShow": true, "sort": 20}, {"fieldName": "可访问网络", "fieldKey": "accessibleNetwork", "required": false, "isQuery": false, "isShow": true, "sort": 21}, {"fieldName": "开发语言", "fieldKey": "developmentLanguage", "required": false, "isQuery": false, "isShow": true, "sort": 22}, {"fieldName": "HW时期是否可关停", "fieldKey": "hwIsTrueShutDown", "required": false, "isQuery": false, "isShow": true, "sort": 23}, {"fieldName": "安全措施防护描述", "fieldKey": "protectionDescription", "required": false, "isQuery": false, "isShow": true, "sort": 24}, {"fieldName": "备注", "fieldKey": "remark", "required": false, "isQuery": false, "isShow": true, "sort": 25}]}, {"assetType": 1, "formRef": "filing", "formName": "备案信息", "fieldsItems": [{"fieldName": "等保级别", "fieldKey": "protectGrade", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "备案编号", "fieldKey": "waitingInsuranceFilingNumber", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "备案时间", "fieldKey": "waitingInsuranceFilingTime", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "备案扫描件", "fieldKey": "waitingInsuranceFilingScan", "required": false, "isQuery": false, "isShow": true, "sort": 3}]}, {"assetType": 1, "formRef": "evaluation", "formName": "测评信息", "fieldsItems": [{"fieldName": "测评年份", "fieldKey": "evaluationYear", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "测评机构名称", "fieldKey": "evaluationAgency", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "测评报告编号", "fieldKey": "evaluationNumber", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "测评结论", "fieldKey": "evaluationResults", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "测评得分", "fieldKey": "evaluationScore", "required": false, "isQuery": false, "isShow": true, "sort": 4}, {"fieldName": "状态", "fieldKey": "evaluationStatus", "required": false, "isQuery": false, "isShow": true, "sort": 5}, {"fieldName": "测评报告", "fieldKey": "evaluationReport", "required": false, "isQuery": false, "isShow": true, "sort": 6}]}, {"assetType": 1, "formRef": "links", "formName": "外部连接信息", "fieldsItems": [{"fieldName": "连接选项", "fieldKey": "linkModel", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "连接单位与名称", "fieldKey": "linkName", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "依托边界", "fieldKey": "boundaryId", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "连接IP", "fieldKey": "linkIp", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "连接端口", "fieldKey": "linkPort", "required": false, "isQuery": false, "isShow": true, "sort": 4}, {"fieldName": "提供数据方式", "fieldKey": "linkType", "required": false, "isQuery": false, "isShow": true, "sort": 5}, {"fieldName": "主要领导是否审批", "fieldKey": "leader<PERSON><PERSON><PERSON>", "required": false, "isQuery": false, "isShow": true, "sort": 6}]}, {"assetType": 1, "formRef": "netTopo", "formName": "拓扑结构信息", "fieldsItems": [{"fieldName": "网络拓扑图", "fieldKey": "netTopo", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "拓扑图说明", "fieldKey": "netMemo", "required": false, "isQuery": false, "isShow": true, "sort": 1}]}, {"assetType": 1, "formRef": "eids", "formName": "运营维护情况", "fieldsItems": [{"fieldName": "运维人员", "fieldKey": "F_name", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "手机号码", "fieldKey": "F_mobile_phone", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "是否驻场", "fieldKey": "F_be_stationed", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "驻场地点", "fieldKey": "templateName", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "运维时间", "fieldKey": "stationTime", "required": false, "isQuery": false, "isShow": true, "sort": 4}, {"fieldName": "所属公司", "fieldKey": "orgName", "required": false, "isQuery": false, "isShow": true, "sort": 5}]}, {"assetType": 1, "formRef": "otherBase", "formName": "其他基本信息", "fieldsItems": [{"fieldName": "是否基层数据采集", "fieldKey": "isbase", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "是否日志安审平台", "fieldKey": "islog", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "是否信创适配", "fieldKey": "isadapt", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "信创适配时间", "fieldKey": "adaptDate", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "是否密码应用", "fieldKey": "iscipher", "required": false, "isQuery": false, "isShow": true, "sort": 4}, {"fieldName": "密码应用建设时间", "fieldKey": "cipherDate", "required": false, "isQuery": false, "isShow": true, "sort": 5}, {"fieldName": "是否升级改造计划", "fieldKey": "isplan", "required": false, "isQuery": false, "isShow": true, "sort": 6}, {"fieldName": "警综对接", "fieldKey": "islink", "required": false, "isQuery": false, "isShow": true, "sort": 7}]}, {"assetType": 1, "formRef": "userScale", "formName": "用户规模", "fieldsItems": [{"fieldName": "覆盖地域", "fieldKey": "coverArea", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "使用对象", "fieldKey": "serviceGroup", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "授权用户数", "fieldKey": "userNums", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "系统日均访客量", "fieldKey": "everydayVisitNums", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "系统月均活跃人数", "fieldKey": "everydayActiveNums", "required": false, "isQuery": false, "isShow": true, "sort": 4}]}, {"assetType": 1, "formRef": "businessDesc", "formName": "业务描述", "fieldsItems": [{"fieldName": "总体系统业务说明", "fieldKey": "sysBusinessState", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "上传操作手册", "fieldKey": "operateHandbook", "required": false, "isQuery": false, "isShow": true, "sort": 1}]}, {"assetType": 1, "formRef": "funcModule", "formName": "功能模块", "fieldsItems": [{"fieldName": "功能模块名称", "fieldKey": "moduleName", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "模块描述", "fieldKey": "moduleDesc", "required": false, "isQuery": false, "isShow": true, "sort": 1}]}, {"assetType": 1, "formRef": "serverInfo", "formName": "所安装服务器环境", "fieldsItems": [{"fieldName": "服务器名称", "fieldKey": "assetName", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "操作系统类型及版本", "fieldKey": "optsystem", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "是否虚拟化", "fieldKey": "isVirtual", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "IP", "fieldKey": "ip", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "端口", "fieldKey": "port", "required": false, "isQuery": false, "isShow": true, "sort": 4}, {"fieldName": "部署功能", "fieldKey": "deployFunc", "required": false, "isQuery": false, "isShow": true, "sort": 5}, {"fieldName": "部署组件", "fieldKey": "deployComponent", "required": false, "isQuery": false, "isShow": true, "sort": 6}]}, {"assetType": 1, "formRef": "dbInfo", "formName": "所安装数据库环境", "fieldsItems": [{"fieldName": "服务器名称", "fieldKey": "assetName", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "数据库类型及版本", "fieldKey": "dbsystem", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "是否虚拟化", "fieldKey": "isVirtual", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "IP", "fieldKey": "ip", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "端口", "fieldKey": "port", "required": false, "isQuery": false, "isShow": true, "sort": 4}, {"fieldName": "部署功能", "fieldKey": "deployFunc", "required": false, "isQuery": false, "isShow": true, "sort": 5}, {"fieldName": "部署组件", "fieldKey": "deployComponent", "required": false, "isQuery": false, "isShow": true, "sort": 6}]}, {"assetType": 1, "formRef": "netWorkDeviceInfo", "formName": "关联网络设备", "fieldsItems": [{"fieldName": "设备名称", "fieldKey": "assetName", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "设备类型", "fieldKey": "assetClassDesc", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "设备厂家", "fieldKey": "brandModel", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "设备型号", "fieldKey": "ver", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "设备IP", "fieldKey": "ip", "required": false, "isQuery": false, "isShow": true, "sort": 4}]}, {"assetType": 1, "formRef": "safeDeviceInfo", "formName": "关联安全设备", "fieldsItems": [{"fieldName": "设备名称", "fieldKey": "assetName", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "设备类型", "fieldKey": "assetClassDesc", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "设备厂家", "fieldKey": "brandModel", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "设备型号", "fieldKey": "ver", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "设备IP", "fieldKey": "ip", "required": false, "isQuery": false, "isShow": true, "sort": 4}]}, {"assetType": 2, "formRef": "base", "formName": "基本信息", "fieldsItems": [{"fieldName": "资产编码", "fieldKey": "assetCode", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "资产名称", "fieldKey": "assetName", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "资产类型", "fieldKey": "assetType", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "重要程度", "fieldKey": "degreeImportance", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "所属部门", "fieldKey": "deptId", "required": false, "isQuery": false, "isShow": true, "sort": 4}, {"fieldName": "供应商", "fieldKey": "vendor", "required": false, "isQuery": false, "isShow": true, "sort": 5}, {"fieldName": "设备厂商", "fieldKey": "facilityManufacturer", "required": false, "isQuery": false, "isShow": true, "sort": 6}, {"fieldName": "设备型号", "fieldKey": "facilityType", "required": false, "isQuery": false, "isShow": true, "sort": 7}, {"fieldName": "维保单位", "fieldKey": "maintainUnit", "required": false, "isQuery": false, "isShow": true, "sort": 8}, {"fieldName": "虚拟设备", "fieldKey": "isVirtual", "required": false, "isQuery": false, "isShow": true, "sort": 9}, {"fieldName": "承载设备", "fieldKey": "baseAsset", "required": false, "isQuery": false, "isShow": true, "sort": 10}, {"fieldName": "主机名称", "fieldKey": "hostName", "required": false, "isQuery": false, "isShow": true, "sort": 11}, {"fieldName": "是否热设备", "fieldKey": "isSparing", "required": false, "isQuery": false, "isShow": true, "sort": 12}, {"fieldName": "资产标签", "fieldKey": "tags", "required": false, "isQuery": false, "isShow": true, "sort": 13}]}, {"assetType": 2, "formRef": "network", "formName": "网络信息", "fieldsItems": [{"fieldName": "外网IP", "fieldKey": "exposedIp", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "主IP", "fieldKey": "isMainIp", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "所属网络", "fieldKey": "domainId", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "IP", "fieldKey": "ipv4", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "MAC地址", "fieldKey": "mac", "required": false, "isQuery": false, "isShow": true, "sort": 4}]}, {"assetType": 2, "formRef": "hardwareAndSoftware", "formName": "硬件/软件概况信息", "fieldsItems": [{"fieldName": "操作系统", "fieldKey": "optId", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "CPU架构", "fieldKey": "cpuFrame", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "中间件", "fieldKey": "mdId", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "数据库", "fieldKey": "dbId", "required": false, "isQuery": false, "isShow": true, "sort": 3}]}, {"assetType": 2, "formRef": "location", "formName": "位置信息", "fieldsItems": [{"fieldName": "所在机房", "fieldKey": "locationId", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "详细位置", "fieldKey": "locationDetail", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "详细信息", "fieldKey": "details", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "备注", "fieldKey": "remark", "required": false, "isQuery": false, "isShow": true, "sort": 3}]}, {"assetType": 3, "formRef": "base", "formName": "基本信息", "fieldsItems": [{"fieldName": "资产编码", "fieldKey": "assetCode", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "资产类型", "fieldKey": "assetType", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "ipv4", "fieldKey": "ipv4", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "MAC", "fieldKey": "mac", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "操作系统", "fieldKey": "optId", "required": false, "isQuery": false, "isShow": true, "sort": 4}, {"fieldName": "所属部门", "fieldKey": "deptId", "required": false, "isQuery": false, "isShow": true, "sort": 5}, {"fieldName": "使用人", "fieldKey": "userId", "required": false, "isQuery": false, "isShow": true, "sort": 6}, {"fieldName": "所属网络", "fieldKey": "domainId", "required": false, "isQuery": false, "isShow": true, "sort": 7}, {"fieldName": "是否国产化", "fieldKey": "isLocalization", "required": false, "isQuery": false, "isShow": true, "sort": 8}]}, {"assetType": 4, "formRef": "base", "formName": "基本信息", "fieldsItems": [{"fieldName": "资产名称", "fieldKey": "assetName", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "资产编码", "fieldKey": "assetCode", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "重要程度", "fieldKey": "degreeImportance", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "系统及版本", "fieldKey": "systemVersion", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "设备厂家", "fieldKey": "brandModel", "required": false, "isQuery": false, "isShow": true, "sort": 4}, {"fieldName": "设备型号", "fieldKey": "ver", "required": false, "isQuery": false, "isShow": true, "sort": 5}, {"fieldName": "所属部门", "fieldKey": "deptId", "required": false, "isQuery": false, "isShow": true, "sort": 6}, {"fieldName": "供应商", "fieldKey": "vendor", "required": false, "isQuery": false, "isShow": true, "sort": 7}, {"fieldName": "网络区域", "fieldKey": "domainId", "required": false, "isQuery": false, "isShow": true, "sort": 8}, {"fieldName": "主IP", "fieldKey": "ip", "required": false, "isQuery": false, "isShow": true, "sort": 9}, {"fieldName": "管理部门", "fieldKey": "manageDeptId", "required": false, "isQuery": false, "isShow": true, "sort": 10}, {"fieldName": "管理地址", "fieldKey": "manageAddress", "required": false, "isQuery": false, "isShow": true, "sort": 11}, {"fieldName": "互联vlan", "fieldKey": "interconnectVlan", "required": false, "isQuery": false, "isShow": true, "sort": 12}, {"fieldName": "互联地址", "fieldKey": "interconnectManage", "required": false, "isQuery": false, "isShow": true, "sort": 13}, {"fieldName": "上联核心交换机互联地址", "fieldKey": "switchInterconnectManage", "required": false, "isQuery": false, "isShow": true, "sort": 14}, {"fieldName": "是否热设备", "fieldKey": "isSparing", "required": false, "isQuery": false, "isShow": true, "sort": 15}, {"fieldName": "预计投入使用时间", "fieldKey": "usageTime", "required": false, "isQuery": false, "isShow": true, "sort": 16}, {"fieldName": "资产类型", "fieldKey": "assetType", "required": false, "isQuery": false, "isShow": true, "sort": 17}, {"fieldName": "所在位置", "fieldKey": "locationId", "required": false, "isQuery": false, "isShow": true, "sort": 18}, {"fieldName": "详细地址", "fieldKey": "locationDetail", "required": false, "isQuery": false, "isShow": true, "sort": 19}, {"fieldName": "虚拟设备", "fieldKey": "isVirtual", "required": false, "isQuery": false, "isShow": true, "sort": 20}, {"fieldName": "用途", "fieldKey": "purpose", "required": false, "isQuery": false, "isShow": true, "sort": 21}, {"fieldName": "自定义资产标签", "fieldKey": "tags", "required": false, "isQuery": false, "isShow": true, "sort": 22}, {"fieldName": "备注", "fieldKey": "remark", "required": false, "isQuery": false, "isShow": true, "sort": 23}]}, {"assetType": 5, "formRef": "base", "formName": "基本信息", "fieldsItems": [{"fieldName": "资产编码", "fieldKey": "assetCode", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "资产名称", "fieldKey": "assetName", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "资产类型", "fieldKey": "assetType", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "重要程度", "fieldKey": "degreeImportance", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "系统及版本", "fieldKey": "systemVersion", "required": false, "isQuery": false, "isShow": true, "sort": 4}, {"fieldName": "设备厂家", "fieldKey": "brandModel", "required": false, "isQuery": false, "isShow": true, "sort": 5}, {"fieldName": "设备型号", "fieldKey": "ver", "required": false, "isQuery": false, "isShow": true, "sort": 6}, {"fieldName": "所属部门", "fieldKey": "deptId", "required": false, "isQuery": false, "isShow": true, "sort": 7}, {"fieldName": "合作商", "fieldKey": "vendorName", "required": false, "isQuery": false, "isShow": true, "sort": 8}, {"fieldName": "网络区域", "fieldKey": "domainId", "required": false, "isQuery": false, "isShow": true, "sort": 9}, {"fieldName": "主IP", "fieldKey": "ip", "required": false, "isQuery": false, "isShow": true, "sort": 10}, {"fieldName": "购买时间", "fieldKey": "buyTime", "required": false, "isQuery": false, "isShow": true, "sort": 11}, {"fieldName": "维保到期时间", "fieldKey": "mExpirationDate", "required": false, "isQuery": false, "isShow": true, "sort": 12}, {"fieldName": "是否热设备", "fieldKey": "isSparing", "required": false, "isQuery": false, "isShow": true, "sort": 13}, {"fieldName": "管理地址", "fieldKey": "man<PERSON><PERSON><PERSON><PERSON>", "required": false, "isQuery": false, "isShow": true, "sort": 24}, {"fieldName": "所在位置", "fieldKey": "locationId", "required": false, "isQuery": false, "isShow": true, "sort": 15}, {"fieldName": "详细地址", "fieldKey": "locationDetail", "required": false, "isQuery": false, "isShow": true, "sort": 16}, {"fieldName": "虚拟设备", "fieldKey": "isVirtual", "required": false, "isQuery": false, "isShow": true, "sort": 17}, {"fieldName": "用途", "fieldKey": "purpose", "required": false, "isQuery": false, "isShow": true, "sort": 18}, {"fieldName": "资产标签", "fieldKey": "tags", "required": false, "isQuery": false, "isShow": true, "sort": 19}, {"fieldName": "备注", "fieldKey": "remark", "required": false, "isQuery": false, "isShow": true, "sort": 20}]}, {"assetType": 6, "formRef": "base", "formName": "基本信息", "fieldsItems": [{"fieldName": "所属部门", "fieldKey": "deptId", "required": false, "isQuery": false, "isShow": true, "sort": 0}, {"fieldName": "边界编码", "fieldKey": "assetCode", "required": false, "isQuery": false, "isShow": true, "sort": 1}, {"fieldName": "边界名称", "fieldKey": "assetName", "required": false, "isQuery": false, "isShow": true, "sort": 2}, {"fieldName": "接入方式", "fieldKey": "accessWay", "required": false, "isQuery": false, "isShow": true, "sort": 3}, {"fieldName": "重要程度", "fieldKey": "degreeImportance", "required": false, "isQuery": false, "isShow": true, "sort": 4}, {"fieldName": "边界类型", "fieldKey": "assetType", "required": false, "isQuery": false, "isShow": true, "sort": 5}, {"fieldName": "备注", "fieldKey": "remark", "required": false, "isQuery": false, "isShow": true, "sort": 6}]}]