{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\alertEvent.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\alertEvent.vue", "mtime": 1756369455960}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_eventList", "_interopRequireDefault", "require", "_attackViewList", "_sufferViewList", "_index", "_index2", "_history", "_threat", "_AttackAlarm", "_honeypotAlarmList", "_honeypotAttackViewList", "_apiAlarmList", "_hostEvent", "_invadeAttack", "_ffsafeIpFilterblocking", "_flowRiskAssets", "_ruoyi", "_hostAgent", "name", "components", "SufferViewList", "AttackViewList", "EventList", "Asset<PERSON>iew", "IpfilterLog", "IpFilterLogHistory", "HoneypotAlarmList", "HoneypotAttackViewList", "ApiAlarmList", "HostEvent", "InvadeAttack", "data", "_this", "activeName", "propActiveName", "srcQueryParams", "queryParams", "currentQueryParams", "currentCard", "currentBtn", "headCardOptions", "title", "total", "getStatisticsValue", "getGroupStatisticsData", "key", "click", "headBtnClick", "alarmLevel", "riskLevel", "_objectSpread2", "default", "btnArr", "icon", "value", "label", "honeypotAlarmStatisticsData", "apiAlarmStatisticsData", "riskType", "hostAgentEventStatisticsData", "invadeAttackStatisticsData", "filterLogStatisticData", "$forceUpdate", "threatenAlarmStatisticsData", "attackAlarmStatisticsData", "hostEventStatisticsData", "watch", "$route", "handler", "newVal", "_this2", "query", "type", "$nextTick", "immediate", "mounted", "tabs", "params", "startTime", "parseTime", "Date", "setHours", "endTime", "handleState", "getAlarmLevelStatistics", "getHoneypotAlarmLevelStatistics", "getFilterLogStatistic", "getFlowRiskAssetsStatistics", "beginTime", "getInvadeAttackStatistics", "computed", "methods", "_this3", "groupAlarmLevelStatistics", "then", "res", "getAttackAlarmLevelStatistics", "_this4", "groupAttackAlarmLevelStatistics", "_this5", "groupHoneypotAlarmLevelStatistics", "_this6", "_this7", "countHostInvasion", "console", "log", "_this8", "handleClick", "$router", "push", "card<PERSON><PERSON>", "btnKey", "srcData", "handleResetButton", "handleApiQueryChange", "query<PERSON>hange", "handleGetEventList", "handleGetAttackEventList", "handleGetHoneyEventList", "handleGetApiEventList", "statisticsParams", "undefined", "deviceConfigId", "riskAssets", "handleGetIpFilterList"], "sources": ["src/views/frailty/event/alertEvent.vue"], "sourcesContent": ["<template>\n  <div class=\"alert-event-box\">\n    <div class=\"head-card-box\">\n      <div class=\"head-card-item\" v-for=\"(headItem,index) in headCardOptions\" :key=\"'head-'+index\">\n        <div :class=\"currentCard === headItem.key ? 'head-card active' : 'head-card'\" @click=\"headItem.click\">\n          <div class=\"head-card-title\">\n            {{ headItem.title }}\n            <span style=\"margin-left: 5px\">\n              <el-tag type=\"primary\" effect=\"dark\" size=\"mini\">{{ headItem.total() }}</el-tag>\n            </span>\n          </div>\n          <div class=\"head-card-btn-box\">\n            <el-row :gutter=\"20\" style=\"height: 100%;display: flex;align-items: center;margin-left: 0;margin-right: 0\">\n              <el-col\n                v-for=\"(btnItem,btnIndex) in headItem.btnArr\"\n                :key=\"'btn-'+btnIndex\"\n                :span=\"24/headItem.btnArr.length\"\n                :class=\"currentCard === headItem.key && currentBtn === btnItem.key ? 'head-btn-active' : ''\"\n                style=\"padding-top: 10px;padding-bottom: 10px;\"\n              >\n                <div class=\"head-card-btn\" @click.stop=\"btnItem.click\">\n                  <div :class=\"[ btnItem.label === '弱口令账号' ? 'btn-icon1' : 'btn-icon']\">\n                    <el-image :src=\"btnItem.icon\" />\n                  </div>\n                  <div class=\"btn-content\">\n                    <div class=\"btn-content-value\">\n                      {{ btnItem.value() }}\n                    </div>\n                    <div class=\"btn-content-label\">\n                      {{ btnItem.label }}\n                    </div>\n                  </div>\n                </div>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div v-if=\"currentCard === 1\" class=\"box-content\">\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"告警列表\" name=\"first\" />\n        <el-tab-pane label=\"攻击者视角\" name=\"second\" />\n        <el-tab-pane label=\"受害者视角\" name=\"third\" />\n        <el-tab-pane label=\"资产视角\" name=\"four\" />\n        <!--        <el-tab-pane label=\"阻断IP\" name=\"five\"></el-tab-pane>-->\n      </el-tabs>\n      <div style=\"height: calc(100% - 41px); margin-top: 3px\">\n        <event-list v-if=\"propActiveName === 'first'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetEventList\" />\n        <attack-view-list v-if=\"propActiveName === 'second'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetAttackEventList\" />\n        <suffer-view-list v-if=\"propActiveName === 'third'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetEventList\" />\n        <asset-view v-if=\"propActiveName === 'four'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetEventList\" />\n      </div>\n    </div>\n    <div v-if=\"currentCard === 2\" class=\"box-content\">\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"告警列表\" name=\"first\" />\n        <el-tab-pane label=\"攻击者视角\" name=\"second\" />\n      </el-tabs>\n      <div style=\"height: calc(100% - 41px); margin-top: 3px\">\n        <honeypotAlarmList v-if=\"propActiveName === 'first'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetHoneyEventList\" />\n        <honeypot-attack-view-list v-if=\"propActiveName === 'second'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetHoneyEventList\" />\n      </div>\n    </div>\n    <div v-if=\"currentCard === 3\" class=\"box-content\">\n      <api-alarm-list v-if=\"currentCard === 3\" :props-active-name=\"'apiAlarm'\" :props-query-params=\"queryParams\" @reset-button=\"handleResetButton\" @query-change=\"handleApiQueryChange\" @getList=\"handleGetApiEventList\" />\n    </div>\n    <div v-if=\"currentCard === 4\" class=\"box-content\">\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"阻断中列表\" name=\"first\" />\n        <el-tab-pane label=\"阻断历史记录\" name=\"second\" />\n      </el-tabs>\n      <div style=\"height: calc(100% - 47px); margin-top: 8px\">\n        <IpfilterLog v-if=\"propActiveName === 'first'\" ref=\"ipFilterLog\" :props-active-name=\"propActiveName\" @getList=\"handleGetIpFilterList\" />\n        <IpFilterLogHistory v-if=\"propActiveName === 'second'\" ref=\"ipFilterLogHistory\" :props-active-name=\"propActiveName\" @getList=\"handleGetIpFilterList\" />\n      </div>\n    </div>\n    <div v-if=\"currentCard === 5\" class=\"box-content\">\n      <invade-attack ref=\"invadeAttack\" v-if=\"propActiveName === 'first'\" @getList=\"getInvadeAttackStatistics\"/>\n      <host-event ref=\"hostEvent\" v-if=\"propActiveName === 'second'\"/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport EventList from './component/eventList'\nimport AttackViewList from './component/attackViewList'\nimport SufferViewList from './component/sufferViewList'\nimport AssetView from '@/views/threat/asset/index'\nimport IpfilterLog from '@/views/aqsoc/ffsafe-ipfilter-log/index'\nimport IpFilterLogHistory from '@/views/aqsoc/ffsafe-ipfilter-log/history'\nimport { groupAlarmLevelStatistics, groupHoneypotAlarmLevelStatistics } from '@/api/threat/threat'\nimport { groupAlarmLevelStatistics as groupAttackAlarmLevelStatistics } from '@/api/threaten/AttackAlarm';\nimport HoneypotAlarmList from './component/honeypotAlarmList'\nimport HoneypotAttackViewList from './component/honeypotAttackViewList'\nimport ApiAlarmList from './component/apiAlarmList'\nimport HostEvent from './component/hostEvent.vue'\nimport InvadeAttack from './component/invadeAttack.vue'\nimport { getFilterLogStatistic } from '@/api/safe/ffsafeIpFilterblocking'\nimport { getFlowRiskAssetsStatistics } from '@/api/ffsafe/flowRiskAssets'\nimport { parseTime } from '@/utils/ruoyi'\nimport {countHostInvasion} from \"@/api/monitor/hostAgent\";\n\nexport default {\n  name: 'AlertEvent',\n  components: {\n    SufferViewList,\n    AttackViewList,\n    EventList,\n    AssetView,\n    IpfilterLog,\n    IpFilterLogHistory,\n    HoneypotAlarmList,\n    HoneypotAttackViewList,\n    ApiAlarmList,\n    HostEvent,\n    InvadeAttack\n  },\n  data() {\n    return {\n      activeName: 'first',\n      propActiveName: 'first',\n      srcQueryParams: {},\n      queryParams: {},\n      currentQueryParams: {},\n      currentCard: 1,\n      currentBtn: null,\n      headCardOptions: [\n        {\n          title: '流量威胁告警',\n          total: () => this.getStatisticsValue(this.getGroupStatisticsData, 'total'),\n          key: 1,\n          click: () => {\n            this.headBtnClick(1, null)\n            this.currentQueryParams.alarmLevel = null;\n            this.currentQueryParams.riskLevel = null;\n            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n          },\n          btnArr: [\n            /*{\n              icon: require('@/assets/icons/event/level5.png'),\n              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel5'),\n              label: '严重',\n              key: 5,\n              click: () => {\n                this.headBtnClick(1, 5)\n                this.currentQueryParams.alarmLevel = '5'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },*/\n            {\n              icon: require('@/assets/icons/event/level4.png'),\n              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel4'),\n              label: '高危',\n              key: 4,\n              click: () => {\n                this.headBtnClick(1, 4)\n                this.currentQueryParams.alarmLevel = '4'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level3.png'),\n              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel3'),\n              label: '中危',\n              key: 3,\n              click: () => {\n                this.headBtnClick(1, 3)\n                this.currentQueryParams.alarmLevel = '3'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level2.png'),\n              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel2'),\n              label: '低危',\n              key: 2,\n              click: () => {\n                this.headBtnClick(1, 2)\n                this.currentQueryParams.alarmLevel = '2'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            }\n          ]\n        },\n        {\n          title: '蜜罐诱捕告警',\n          total: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'total'),\n          key: 2,\n          click: () => this.headBtnClick(2, null),\n          btnArr: [\n            {\n              icon: require('@/assets/icons/event/level5.png'),\n              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel5'),\n              label: '严重',\n              key: 5,\n              click: () => {\n                this.headBtnClick(2, 5)\n                this.currentQueryParams.alarmLevel = '5'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level4.png'),\n              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel4'),\n              label: '高危',\n              key: 4,\n              click: () => {\n                this.headBtnClick(2, 4)\n                this.currentQueryParams.alarmLevel = '4'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level3.png'),\n              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel3'),\n              label: '中危',\n              key: 3,\n              click: () => {\n                this.headBtnClick(2, 3)\n                this.currentQueryParams.alarmLevel = '3'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level2.png'),\n              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel2'),\n              label: '低危',\n              key: 2,\n              click: () => {\n                this.headBtnClick(2, 2)\n                this.currentQueryParams.alarmLevel = '2'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            }\n          ]\n        },\n        {\n          title: 'API告警',\n          total: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'total'),\n          key: 3,\n          click: () => {\n            this.headBtnClick(3, null)\n            this.currentQueryParams.riskType = null\n            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n          },\n          btnArr: [\n            {\n              icon: require('@/assets/icons/event/weakPassword.png'),\n              value: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'weakPassword'),\n              label: '弱口令账号',\n              key: 1,\n              click: () => {\n                this.headBtnClick(3, 1)\n                this.currentQueryParams.riskType = 'weak_password'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/敏感信息.png'),\n              value: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'sensitiveInfo'),\n              label: '敏感信息',\n              key: 2,\n              click: () => {\n                this.headBtnClick(3, 2)\n                this.currentQueryParams.riskType = 'sensitive_info'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/高危资产.png'),\n              value: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'highRiskAssets'),\n              label: '高危资产',\n              key: 3,\n              click: () => {\n                this.headBtnClick(3, 3)\n                this.currentQueryParams.riskType = 'high_risk_assets'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            }\n          ]\n        },\n        {\n          title: '主机Agent事件',\n          total: () => this.getStatisticsValue(this.hostAgentEventStatisticsData, 'total'),\n          key: 5,\n          click: () => {\n            this.headBtnClick(5, null)\n            // this.currentQueryParams.riskType = 'host_agent_event'\n            this.currentQueryParams.riskType = null\n            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n          },\n          btnArr: [\n            {\n              icon: require('@/assets/icons/event/invade.png'),\n              value: () => this.getStatisticsValue(this.invadeAttackStatisticsData, 'total'),\n              label: '入侵攻击',\n              key: 1,\n              click: () => {\n                this.headBtnClick(5, 1)\n                this.propActiveName = 'first'\n                // this.currentQueryParams.riskType = 'invade'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/host-event.png'),\n              value: () => this.getStatisticsValue(this.hostAgentEventStatisticsData, 'hostEvent'),\n              label: '主机事件',\n              key: 2,\n              click: () => {\n                this.headBtnClick(5, 2)\n                this.propActiveName = 'second'\n                // this.currentQueryParams.riskType = 'malware'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            }\n          ]\n        },\n        {\n          title: '实时阻断',\n          total: () => this.getStatisticsValue(this.filterLogStatisticData, 'total'),\n          key: 4,\n          click: () => {\n            this.headBtnClick(4, null)\n            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n          },\n          btnArr: [\n            {\n              icon: require('@/assets/icons/event/正在阻断.png'),\n              value: () => this.getStatisticsValue(this.filterLogStatisticData, 'blockingCount'),\n              label: '正在阻断',\n              key: 1,\n              click: () => {\n                this.propActiveName = 'first'\n                this.activeName = 'first'\n                this.headBtnClick(4, 1)\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/阻断历史.png'),\n              value: () => this.getStatisticsValue(this.filterLogStatisticData, 'blockLogCount'),\n              label: '历史记录',\n              key: 2,\n              click: () => {\n                this.propActiveName = 'second'\n                this.activeName = 'second'\n                this.$forceUpdate()\n                this.headBtnClick(4, 2)\n              }\n            }\n          ]\n        }\n      ],\n      threatenAlarmStatisticsData: {},\n      attackAlarmStatisticsData: {},\n      honeypotAlarmStatisticsData: {},\n      filterLogStatisticData: {},\n      apiAlarmStatisticsData: {},\n      hostAgentEventStatisticsData: {},\n      hostEventStatisticsData: {},\n      invadeAttackStatisticsData: {},\n    }\n  },\n  watch: {\n    $route: {\n      handler(newVal) {\n        if (newVal.query.type === '4') {\n          // 设置当前选中卡片和按钮\n          this.currentCard = 4 // 对应实时阻断卡片\n          this.currentBtn = 1 // 对应正在阻断按钮\n\n          // 如果需要触发按钮点击逻辑\n          this.$nextTick(() => {\n            // 调用按钮点击方法\n            this.headCardOptions[3].btnArr[0].click()\n          })\n        }\n        if (newVal.query.type === '2') {\n          this.currentCard = 2\n          this.currentBtn = 2\n        }\n      },\n      immediate: true\n    },\n    currentCard: {\n      handler(newVal) {\n        // 当切换到API告警卡片时，刷新数据\n        // 注释掉这个调用，避免重复调用统计接口，统计数据由子组件的getList事件触发\n        // if (newVal === 3) {\n        //   this.refreshApiAlarmStatistics()\n        // }\n      }\n    },\n    activeName: {\n      handler(newVal) {\n        if (this.currentCard === 4) {\n          if (newVal === 'first') {\n            this.currentBtn = 1\n          }\n          if (newVal === 'second') {\n            this.currentBtn = 2\n          }\n        }\n      }\n    }\n  },\n  mounted() {\n    const query = this.$route.query\n    if (query) {\n      this.srcQueryParams = query\n      this.queryParams = { ...this.srcQueryParams }\n      if (query.tabs) {\n        this.propActiveName = query.tabs\n        this.activeName = query.tabs\n      }\n    }\n    const params = {\n      startTime: parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'),\n      endTime: parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'),\n      handleState: '0'\n    }\n    this.getAlarmLevelStatistics(params)\n    this.getHoneypotAlarmLevelStatistics(params)\n    this.getFilterLogStatistic({})\n    this.getFlowRiskAssetsStatistics({\n      params: {\n        beginTime: params.startTime,\n        endTime: params.endTime\n      },\n      handleState: params.handleState\n    })\n    this.getInvadeAttackStatistics({\n      beginTime: params.startTime,\n      endTime: params.endTime\n    })\n    // this.getHostEventStatistics()\n  },\n  computed: {\n    getGroupStatisticsData() {\n      return this.activeName === 'second' ? this.attackAlarmStatisticsData : this.threatenAlarmStatisticsData;\n    }\n  },\n  methods: {\n    getAlarmLevelStatistics(params) {\n      groupAlarmLevelStatistics(params).then(res => {\n        this.threatenAlarmStatisticsData = res.data\n      })\n    },\n    getAttackAlarmLevelStatistics(params) {\n      groupAttackAlarmLevelStatistics(params).then(res => {\n        this.attackAlarmStatisticsData = res.data\n      })\n    },\n    getHoneypotAlarmLevelStatistics(params) {\n      groupHoneypotAlarmLevelStatistics(params).then(res => {\n        this.honeypotAlarmStatisticsData = res.data\n      })\n    },\n    getFilterLogStatistic(params) {\n      getFilterLogStatistic(params).then(res => {\n        this.filterLogStatisticData = res.data\n      })\n    },\n    getInvadeAttackStatistics(params) {\n      countHostInvasion(params).then(res => {\n        console.log(res.data, 'getInvadeAttackStatistics')\n        this.invadeAttackStatisticsData = res.data\n      })\n    },\n    getFlowRiskAssetsStatistics(params) {\n      getFlowRiskAssetsStatistics(params).then(res => {\n        this.apiAlarmStatisticsData = res.data\n      })\n    },\n    handleClick() {\n      this.propActiveName = this.activeName\n      this.$router.push({ query: {}})\n    },\n    headBtnClick(cardKey, btnKey) {\n      if (this.currentCard !== cardKey) {\n        this.currentBtn = null\n      }\n      this.currentQueryParams = {}\n\n      // 根据卡片类型设置对应的默认标签页\n      if (cardKey === 1) {\n        // 流量威胁告警：重置为告警列表标签页\n        if (this.currentCard !== cardKey) {\n          this.activeName = 'first'\n          this.propActiveName = 'first'\n        }\n      } else if (cardKey === 2) {\n        // 蜜罐诱捕告警：重置为告警列表标签页\n        if (this.currentCard !== cardKey) {\n          this.activeName = 'first'\n          this.propActiveName = 'first'\n        }\n      }\n      this.currentCard = cardKey\n      this.currentBtn = btnKey\n      // cardKey === 3(API告警) 不需要设置标签页，因为没有子标签页\n      // currentCard === 4 (实时阻断) 不需要设置标签页，因为没有子标签页\n    },\n    getStatisticsValue(srcData, key) {\n      if (!srcData) {\n        return 0\n      }\n      return srcData[key] || 0\n    },\n    handleResetButton() {\n      // 重置API告警按钮选中状态\n      if (this.currentCard === 3) {\n        this.currentBtn = null\n        // 重置查询参数中的风险类型\n        this.currentQueryParams.riskType = null\n        this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n      }\n    },\n    handleApiQueryChange(queryChange) {\n      // 处理API告警查询参数变化，同步按钮选中状态\n      if (queryChange.riskType) {\n        // 根据风险类型设置对应的按钮选中状态\n        switch (queryChange.riskType) {\n          case 'weak_password':\n            this.currentBtn = 1\n            break\n          case 'sensitive_info':\n            this.currentBtn = 2\n            break\n          case 'high_risk_assets':\n            this.currentBtn = 3\n            break\n          default:\n            this.currentBtn = null\n        }\n      } else {\n        this.currentBtn = null\n      }\n    },\n    handleGetEventList(params) {\n      if (!params.alarmLevel) {\n        this.currentBtn = null\n      }\n      params.alarmLevel = null\n      this.getAlarmLevelStatistics(params)\n    },\n    handleGetAttackEventList(params) {\n      if (!params.riskLevel) {\n        this.currentBtn = null\n      }\n      params.riskLevel = null\n      this.getAttackAlarmLevelStatistics(params)\n    },\n    handleGetHoneyEventList(params) {\n      if (!params.alarmLevel) {\n        this.currentBtn = null\n      }\n      params.alarmLevel = null\n      this.getHoneypotAlarmLevelStatistics(params)\n    },\n    handleGetApiEventList(params) {\n      // 构建统计接口的查询参数，包含所有查询条件\n      const statisticsParams = {\n        params: {\n          beginTime: params.params ? params.params.beginTime : undefined,\n          endTime: params.params ? params.params.endTime : undefined\n        },\n        deviceConfigId: params.deviceConfigId\n      }\n      // 传递所有查询条件给统计接口\n      if (params.riskAssets) {\n        statisticsParams.riskAssets = params.riskAssets\n      }\n\n      if (params.handleState !== undefined && params.handleState !== null) {\n        statisticsParams.handleState = params.handleState\n      }\n\n      params.riskType = null\n      this.getFlowRiskAssetsStatistics(statisticsParams)\n    },\n    handleGetIpFilterList(params) {\n      this.getFilterLogStatistic(params)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../../assets/styles/tabs.scss\";\n@font-face {\n  font-family: electronicFont;\n  src: url(../../../assets/fonts/DS-DIGI.ttf);\n}\n\n.alert-event-box {\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  .head-card-box {\n    width: 100%;\n    height: 109px;\n    display: flex;\n    flex-direction: row;\n    gap: 8px;\n    margin-bottom: 8px;\n    .head-card-item {\n      flex: 1;\n      &:nth-last-child(-n+2) {\n        width: 17%;\n        flex: none;\n      }\n    }\n  }\n  .box-content {\n    height: calc(100% - 115px);\n  }\n}\n\n.head-card{\n  //height: 119px;\n  background-color: #FFFFFF;\n  padding: 8px 10px;\n  font-size: 14px;\n  display: flex;\n  flex-direction: column;\n  cursor: pointer;\n  .head-card-title{\n    color: #242424;\n    font-size: 14px;\n    font-weight: 700;\n  }\n  .head-card-btn-box{\n    flex: 1;\n    margin-top: 5px;\n    margin-bottom: 5px;\n    .head-card-btn{\n      display: flex;\n      align-items: center;\n      cursor: pointer;\n      .btn-icon{\n        width: 35%;\n        text-align: center;\n        .el-image{\n          width: 24px;\n          height: 24px;\n        }\n      }\n      .btn-icon1{\n        width: 35px;\n        text-align: center;\n        .el-image{\n          width: 24px;\n          height: 24px;\n        }\n      }\n      .btn-content{\n        height: 40px;\n        padding-left: 5px;\n        display: flex;\n        flex-direction: column;\n        position: relative;\n        flex: 1;\n        .btn-content-value{\n          font-size: 18px;\n          font-weight: 700;\n          font-family: electronicFont;\n        }\n        .btn-content-label{\n          position: absolute;\n          bottom: 0;\n          font-weight: 400;\n          white-space: nowrap;\n        }\n      }\n    }\n\n    .head-btn-active{\n      background-color: #f3f8fe;\n    }\n  }\n}\n.active{\n  border: 1px solid #4382FD;\n}\n</style>\n"], "mappings": ";;;;;;;;AAqFA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,eAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,eAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,OAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,QAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,OAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAP,OAAA;AACA,IAAAQ,kBAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,uBAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,aAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,UAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,aAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,uBAAA,GAAAb,OAAA;AACA,IAAAc,eAAA,GAAAd,OAAA;AACA,IAAAe,MAAA,GAAAf,OAAA;AACA,IAAAgB,UAAA,GAAAhB,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAiB,IAAA;EACAC,UAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,SAAA,EAAAA,cAAA;IACAC,WAAA,EAAAA,eAAA;IACAC,kBAAA,EAAAA,gBAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,sBAAA,EAAAA,+BAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,YAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,UAAA;MACAC,cAAA;MACAC,cAAA;MACAC,WAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,UAAA;MACAC,eAAA,GACA;QACAC,KAAA;QACAC,KAAA,WAAAA,MAAA;UAAA,OAAAV,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAY,sBAAA;QAAA;QACAC,GAAA;QACAC,KAAA,WAAAA,MAAA;UACAd,KAAA,CAAAe,YAAA;UACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;UACAhB,KAAA,CAAAK,kBAAA,CAAAY,SAAA;UACAjB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;QACA;QACAe,MAAA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA;UACAC,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAY,sBAAA;UAAA;UACAW,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAY,sBAAA;UAAA;UACAW,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAY,sBAAA;UAAA;UACAW,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA;MAEA,GACA;QACAI,KAAA;QACAC,KAAA,WAAAA,MAAA;UAAA,OAAAV,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAwB,2BAAA;QAAA;QACAX,GAAA;QACAC,KAAA,WAAAA,MAAA;UAAA,OAAAd,KAAA,CAAAe,YAAA;QAAA;QACAK,MAAA,GACA;UACAC,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAwB,2BAAA;UAAA;UACAD,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAwB,2BAAA;UAAA;UACAD,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAwB,2BAAA;UAAA;UACAD,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAwB,2BAAA;UAAA;UACAD,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA;MAEA,GACA;QACAI,KAAA;QACAC,KAAA,WAAAA,MAAA;UAAA,OAAAV,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAyB,sBAAA;QAAA;QACAZ,GAAA;QACAC,KAAA,WAAAA,MAAA;UACAd,KAAA,CAAAe,YAAA;UACAf,KAAA,CAAAK,kBAAA,CAAAqB,QAAA;UACA1B,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;QACA;QACAe,MAAA,GACA;UACAC,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAyB,sBAAA;UAAA;UACAF,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAqB,QAAA;YACA1B,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAyB,sBAAA;UAAA;UACAF,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAqB,QAAA;YACA1B,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAyB,sBAAA;UAAA;UACAF,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAqB,QAAA;YACA1B,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA;MAEA,GACA;QACAI,KAAA;QACAC,KAAA,WAAAA,MAAA;UAAA,OAAAV,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAA2B,4BAAA;QAAA;QACAd,GAAA;QACAC,KAAA,WAAAA,MAAA;UACAd,KAAA,CAAAe,YAAA;UACA;UACAf,KAAA,CAAAK,kBAAA,CAAAqB,QAAA;UACA1B,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;QACA;QACAe,MAAA,GACA;UACAC,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAA4B,0BAAA;UAAA;UACAL,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAE,cAAA;YACA;YACAF,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAA2B,4BAAA;UAAA;UACAJ,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAE,cAAA;YACA;YACAF,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA;MAEA,GACA;QACAI,KAAA;QACAC,KAAA,WAAAA,MAAA;UAAA,OAAAV,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAA6B,sBAAA;QAAA;QACAhB,GAAA;QACAC,KAAA,WAAAA,MAAA;UACAd,KAAA,CAAAe,YAAA;UACAf,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;QACA;QACAe,MAAA,GACA;UACAC,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAA6B,sBAAA;UAAA;UACAN,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAE,cAAA;YACAF,KAAA,CAAAC,UAAA;YACAD,KAAA,CAAAe,YAAA;UACA;QACA,GACA;UACAM,IAAA,EAAApD,OAAA;UACAqD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAA6B,sBAAA;UAAA;UACAN,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAE,cAAA;YACAF,KAAA,CAAAC,UAAA;YACAD,KAAA,CAAA8B,YAAA;YACA9B,KAAA,CAAAe,YAAA;UACA;QACA;MAEA,EACA;MACAgB,2BAAA;MACAC,yBAAA;MACAR,2BAAA;MACAK,sBAAA;MACAJ,sBAAA;MACAE,4BAAA;MACAM,uBAAA;MACAL,0BAAA;IACA;EACA;EACAM,KAAA;IACAC,MAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAC,MAAA;QACA,IAAAD,MAAA,CAAAE,KAAA,CAAAC,IAAA;UACA;UACA,KAAAlC,WAAA;UACA,KAAAC,UAAA;;UAEA;UACA,KAAAkC,SAAA;YACA;YACAH,MAAA,CAAA9B,eAAA,IAAAY,MAAA,IAAAN,KAAA;UACA;QACA;QACA,IAAAuB,MAAA,CAAAE,KAAA,CAAAC,IAAA;UACA,KAAAlC,WAAA;UACA,KAAAC,UAAA;QACA;MACA;MACAmC,SAAA;IACA;IACApC,WAAA;MACA8B,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA;QACA;QACA;QACA;MAAA;IAEA;IACApC,UAAA;MACAmC,OAAA,WAAAA,QAAAC,MAAA;QACA,SAAA/B,WAAA;UACA,IAAA+B,MAAA;YACA,KAAA9B,UAAA;UACA;UACA,IAAA8B,MAAA;YACA,KAAA9B,UAAA;UACA;QACA;MACA;IACA;EACA;EACAoC,OAAA,WAAAA,QAAA;IACA,IAAAJ,KAAA,QAAAJ,MAAA,CAAAI,KAAA;IACA,IAAAA,KAAA;MACA,KAAApC,cAAA,GAAAoC,KAAA;MACA,KAAAnC,WAAA,OAAAc,cAAA,CAAAC,OAAA,WAAAhB,cAAA;MACA,IAAAoC,KAAA,CAAAK,IAAA;QACA,KAAA1C,cAAA,GAAAqC,KAAA,CAAAK,IAAA;QACA,KAAA3C,UAAA,GAAAsC,KAAA,CAAAK,IAAA;MACA;IACA;IACA,IAAAC,MAAA;MACAC,SAAA,MAAAC,gBAAA,MAAAC,IAAA,GAAAC,QAAA;MACAC,OAAA,MAAAH,gBAAA,MAAAC,IAAA,GAAAC,QAAA;MACAE,WAAA;IACA;IACA,KAAAC,uBAAA,CAAAP,MAAA;IACA,KAAAQ,+BAAA,CAAAR,MAAA;IACA,KAAAS,qBAAA;IACA,KAAAC,2BAAA;MACAV,MAAA;QACAW,SAAA,EAAAX,MAAA,CAAAC,SAAA;QACAI,OAAA,EAAAL,MAAA,CAAAK;MACA;MACAC,WAAA,EAAAN,MAAA,CAAAM;IACA;IACA,KAAAM,yBAAA;MACAD,SAAA,EAAAX,MAAA,CAAAC,SAAA;MACAI,OAAA,EAAAL,MAAA,CAAAK;IACA;IACA;EACA;EACAQ,QAAA;IACA9C,sBAAA,WAAAA,uBAAA;MACA,YAAAX,UAAA,qBAAA+B,yBAAA,QAAAD,2BAAA;IACA;EACA;EACA4B,OAAA;IACAP,uBAAA,WAAAA,wBAAAP,MAAA;MAAA,IAAAe,MAAA;MACA,IAAAC,iCAAA,EAAAhB,MAAA,EAAAiB,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAA7B,2BAAA,GAAAgC,GAAA,CAAAhE,IAAA;MACA;IACA;IACAiE,6BAAA,WAAAA,8BAAAnB,MAAA;MAAA,IAAAoB,MAAA;MACA,IAAAC,sCAAA,EAAArB,MAAA,EAAAiB,IAAA,WAAAC,GAAA;QACAE,MAAA,CAAAjC,yBAAA,GAAA+B,GAAA,CAAAhE,IAAA;MACA;IACA;IACAsD,+BAAA,WAAAA,gCAAAR,MAAA;MAAA,IAAAsB,MAAA;MACA,IAAAC,yCAAA,EAAAvB,MAAA,EAAAiB,IAAA,WAAAC,GAAA;QACAI,MAAA,CAAA3C,2BAAA,GAAAuC,GAAA,CAAAhE,IAAA;MACA;IACA;IACAuD,qBAAA,WAAAA,sBAAAT,MAAA;MAAA,IAAAwB,MAAA;MACA,IAAAf,6CAAA,EAAAT,MAAA,EAAAiB,IAAA,WAAAC,GAAA;QACAM,MAAA,CAAAxC,sBAAA,GAAAkC,GAAA,CAAAhE,IAAA;MACA;IACA;IACA0D,yBAAA,WAAAA,0BAAAZ,MAAA;MAAA,IAAAyB,MAAA;MACA,IAAAC,4BAAA,EAAA1B,MAAA,EAAAiB,IAAA,WAAAC,GAAA;QACAS,OAAA,CAAAC,GAAA,CAAAV,GAAA,CAAAhE,IAAA;QACAuE,MAAA,CAAA1C,0BAAA,GAAAmC,GAAA,CAAAhE,IAAA;MACA;IACA;IACAwD,2BAAA,WAAAA,4BAAAV,MAAA;MAAA,IAAA6B,MAAA;MACA,IAAAnB,2CAAA,EAAAV,MAAA,EAAAiB,IAAA,WAAAC,GAAA;QACAW,MAAA,CAAAjD,sBAAA,GAAAsC,GAAA,CAAAhE,IAAA;MACA;IACA;IACA4E,WAAA,WAAAA,YAAA;MACA,KAAAzE,cAAA,QAAAD,UAAA;MACA,KAAA2E,OAAA,CAAAC,IAAA;QAAAtC,KAAA;MAAA;IACA;IACAxB,YAAA,WAAAA,aAAA+D,OAAA,EAAAC,MAAA;MACA,SAAAzE,WAAA,KAAAwE,OAAA;QACA,KAAAvE,UAAA;MACA;MACA,KAAAF,kBAAA;;MAEA;MACA,IAAAyE,OAAA;QACA;QACA,SAAAxE,WAAA,KAAAwE,OAAA;UACA,KAAA7E,UAAA;UACA,KAAAC,cAAA;QACA;MACA,WAAA4E,OAAA;QACA;QACA,SAAAxE,WAAA,KAAAwE,OAAA;UACA,KAAA7E,UAAA;UACA,KAAAC,cAAA;QACA;MACA;MACA,KAAAI,WAAA,GAAAwE,OAAA;MACA,KAAAvE,UAAA,GAAAwE,MAAA;MACA;MACA;IACA;IACApE,kBAAA,WAAAA,mBAAAqE,OAAA,EAAAnE,GAAA;MACA,KAAAmE,OAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAnE,GAAA;IACA;IACAoE,iBAAA,WAAAA,kBAAA;MACA;MACA,SAAA3E,WAAA;QACA,KAAAC,UAAA;QACA;QACA,KAAAF,kBAAA,CAAAqB,QAAA;QACA,KAAAtB,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,WAAAhB,cAAA,QAAAE,kBAAA;MACA;IACA;IACA6E,oBAAA,WAAAA,qBAAAC,WAAA;MACA;MACA,IAAAA,WAAA,CAAAzD,QAAA;QACA;QACA,QAAAyD,WAAA,CAAAzD,QAAA;UACA;YACA,KAAAnB,UAAA;YACA;UACA;YACA,KAAAA,UAAA;YACA;UACA;YACA,KAAAA,UAAA;YACA;UACA;YACA,KAAAA,UAAA;QACA;MACA;QACA,KAAAA,UAAA;MACA;IACA;IACA6E,kBAAA,WAAAA,mBAAAvC,MAAA;MACA,KAAAA,MAAA,CAAA7B,UAAA;QACA,KAAAT,UAAA;MACA;MACAsC,MAAA,CAAA7B,UAAA;MACA,KAAAoC,uBAAA,CAAAP,MAAA;IACA;IACAwC,wBAAA,WAAAA,yBAAAxC,MAAA;MACA,KAAAA,MAAA,CAAA5B,SAAA;QACA,KAAAV,UAAA;MACA;MACAsC,MAAA,CAAA5B,SAAA;MACA,KAAA+C,6BAAA,CAAAnB,MAAA;IACA;IACAyC,uBAAA,WAAAA,wBAAAzC,MAAA;MACA,KAAAA,MAAA,CAAA7B,UAAA;QACA,KAAAT,UAAA;MACA;MACAsC,MAAA,CAAA7B,UAAA;MACA,KAAAqC,+BAAA,CAAAR,MAAA;IACA;IACA0C,qBAAA,WAAAA,sBAAA1C,MAAA;MACA;MACA,IAAA2C,gBAAA;QACA3C,MAAA;UACAW,SAAA,EAAAX,MAAA,CAAAA,MAAA,GAAAA,MAAA,CAAAA,MAAA,CAAAW,SAAA,GAAAiC,SAAA;UACAvC,OAAA,EAAAL,MAAA,CAAAA,MAAA,GAAAA,MAAA,CAAAA,MAAA,CAAAK,OAAA,GAAAuC;QACA;QACAC,cAAA,EAAA7C,MAAA,CAAA6C;MACA;MACA;MACA,IAAA7C,MAAA,CAAA8C,UAAA;QACAH,gBAAA,CAAAG,UAAA,GAAA9C,MAAA,CAAA8C,UAAA;MACA;MAEA,IAAA9C,MAAA,CAAAM,WAAA,KAAAsC,SAAA,IAAA5C,MAAA,CAAAM,WAAA;QACAqC,gBAAA,CAAArC,WAAA,GAAAN,MAAA,CAAAM,WAAA;MACA;MAEAN,MAAA,CAAAnB,QAAA;MACA,KAAA6B,2BAAA,CAAAiC,gBAAA;IACA;IACAI,qBAAA,WAAAA,sBAAA/C,MAAA;MACA,KAAAS,qBAAA,CAAAT,MAAA;IACA;EACA;AACA", "ignoreList": []}]}