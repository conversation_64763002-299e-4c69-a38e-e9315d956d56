<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenDataAlarmMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenDataAlarmListVO">
        select a.* from threaten_data_alarm a left join threaten_alarm_origin b on a.id = b.origin_id and (b.threaten_type = 'threaten_data_alarm' or b.threaten_type = '数据安全')
        <where>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.baseId!=null">
                and a.base_id = #{query.baseId}
            </if>
            <if test="query.tamperType!=null">
                and a.tamper_type = #{query.tamperType}
            </if>
            <if test="query.leakageType!=null and query.leakageType!=''">
                and a.leakage_type like concat('%', #{query.leakageType}, '%')
            </if>
            <if test="query.tamperPath!=null and query.tamperPath!=''">
                and a.tamper_path like concat('%', #{query.tamperPath}, '%')
            </if>
            <if test="query.keyword!=null and query.keyword!=''">
                and a.keyword like concat('%', #{query.keyword}, '%')
            </if>
            <if test="query.tamperContent!=null and query.tamperContent!=''">
                and a.tamper_content like concat('%', #{query.tamperContent}, '%')
            </if>
            <if test="query.createTime!=null">
                and a.create_time = #{query.createTime}
            </if>
            <if test="query.alarmId!=null">
                and b.alarm_id = #{query.alarmId}
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenDataAlarmListVO">
        select a.* from threaten_data_alarm a 
        where a.id=#{id}
    </select>
</mapper>
