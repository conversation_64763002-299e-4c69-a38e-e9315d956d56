{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\api\\monitor\\hostAgent.js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\api\\monitor\\hostAgent.js", "mtime": 1756369455928}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1751956515056}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi93c2gvYXVnbWVudF93b3Jrc3BhY2UvYXFzb2MtbWFpbi9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYmF0Y2hEZWxldGVIb3N0SW52YXNpb24gPSBiYXRjaERlbGV0ZUhvc3RJbnZhc2lvbjsKZXhwb3J0cy5jb3VudEhvc3RJbnZhc2lvbiA9IGNvdW50SG9zdEludmFzaW9uOwpleHBvcnRzLmRlbGV0ZUhvc3RJbnZhc2lvbiA9IGRlbGV0ZUhvc3RJbnZhc2lvbjsKZXhwb3J0cy5kaXNwb3NlSG9zdEludmFzaW9uID0gZGlzcG9zZUhvc3RJbnZhc2lvbjsKZXhwb3J0cy5nZXRIb3N0SW50cnVzaW9uQXR0YWNrRGV0YWlsID0gZ2V0SG9zdEludHJ1c2lvbkF0dGFja0RldGFpbDsKZXhwb3J0cy5nZXRIb3N0SW52YXNpb25EZXRhaWwgPSBnZXRIb3N0SW52YXNpb25EZXRhaWw7CmV4cG9ydHMuZ2V0SG9zdEludmFzaW9uTGlzdCA9IGdldEhvc3RJbnZhc2lvbkxpc3Q7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovKiDlqIHog4Hnm5HmtYvkuLvmnLphZ2VudOS6i+S7tiAqLwoKLyoqCiAqIOS4u+acuuWFpeS+teaUu+WHu+WIl+ihqAogKi8KZnVuY3Rpb24gZ2V0SG9zdEludmFzaW9uTGlzdChwYXJhbXMpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9mZnNhZmUvaG9zdEludHJ1c2lvbkF0dGFjay9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHBhcmFtcwogIH0pOwp9CgovKioKICog5Li75py65YWl5L615pS75Ye76K+m5oOFCiAqLwpmdW5jdGlvbiBnZXRIb3N0SW52YXNpb25EZXRhaWwoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9mZnNhZmUvaG9zdEludHJ1c2lvbkF0dGFjay8nICsgaWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8qKgogKiDmn6Xor6LkuLvmnLrlhaXkvrXmlLvlh7vlr7nlupTor6bmg4UKICovCmZ1bmN0aW9uIGdldEhvc3RJbnRydXNpb25BdHRhY2tEZXRhaWwoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9mZnNhZmUvaG9zdEludHJ1c2lvbkF0dGFjay9kZXRhaWwvJyArIGlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovKioKICog5Li75py65YWl5L615pS75Ye75Yig6ZmkCiAqLwpmdW5jdGlvbiBkZWxldGVIb3N0SW52YXNpb24oaWRzKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvZmZzYWZlL2hvc3RJbnRydXNpb25BdHRhY2svJyArIGlkcywKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLyoqCiAqIOaJuemHj+WIoOmZpOS4u+acuuWFpeS+teaUu+WHuwogKi8KZnVuY3Rpb24gYmF0Y2hEZWxldGVIb3N0SW52YXNpb24oaWRzKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvZmZzYWZlL2hvc3RJbnRydXNpb25BdHRhY2svYmF0Y2gvJyArIGlkcywKICAgIG1ldGhvZDogJ0RFTEVURScKICB9KTsKfQoKLyoqCiAqIOe7n+iuoeS4u+acuuWFpeS+teaUu+WHu+aVsOmHjwogKi8KZnVuY3Rpb24gY291bnRIb3N0SW52YXNpb24ocGFyYW1zKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvZmZzYWZlL2hvc3RJbnRydXNpb25BdHRhY2svc3RhdGlzdGljcycsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBwYXJhbXMKICB9KTsKfQoKLyoqCiAqIOS4u+acuuWFpeS+teaUu+WHu+WkhOe9rgogKi8KZnVuY3Rpb24gZGlzcG9zZUhvc3RJbnZhc2lvbihwYXJhbXMpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9mZnNhZmUvaG9zdEludHJ1c2lvbkF0dGFjay9oYW5kbGUnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBwYXJhbXMKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getHostInvasionList", "params", "request", "url", "method", "getHostInvasionDetail", "id", "getHostIntrusionAttackDetail", "deleteHostInvasion", "ids", "batchDeleteHostInvasion", "countHostInvasion", "disposeHostInvasion", "data"], "sources": ["E:/wsh/augment_workspace/aqsoc-main/ruoyi-ui/src/api/monitor/hostAgent.js"], "sourcesContent": ["/* 威胁监测主机agent事件 */\nimport request from '@/utils/request';\n\n/**\n * 主机入侵攻击列表\n */\nexport function getHostInvasionList(params) {\n  return request({\n    url: '/ffsafe/hostIntrusionAttack/list',\n    method: 'get',\n    params\n  })\n}\n\n/**\n * 主机入侵攻击详情\n */\nexport function getHostInvasionDetail(id) {\n  return request({\n    url: '/ffsafe/hostIntrusionAttack/' + id,\n    method: 'get'\n  })\n}\n\n/**\n * 查询主机入侵攻击对应详情\n */\nexport function getHostIntrusionAttackDetail(id) {\n  return request({\n    url: '/ffsafe/hostIntrusionAttack/detail/' + id,\n    method: 'get'\n  })\n}\n\n/**\n * 主机入侵攻击删除\n */\nexport function deleteHostInvasion(ids) {\n  return request({\n    url: '/ffsafe/hostIntrusionAttack/' + ids,\n    method: 'delete'\n  })\n}\n\n/**\n * 批量删除主机入侵攻击\n */\nexport function batchDeleteHostInvasion(ids) {\n  return request({\n    url: '/ffsafe/hostIntrusionAttack/batch/' + ids,\n    method: 'DELETE',\n  })\n}\n\n/**\n * 统计主机入侵攻击数量\n */\nexport function countHostInvasion(params) {\n  return request({\n    url: '/ffsafe/hostIntrusionAttack/statistics',\n    method: 'get',\n    params\n  })\n}\n\n/**\n * 主机入侵攻击处置\n */\nexport function disposeHostInvasion(params) {\n  return request({\n    url: '/ffsafe/hostIntrusionAttack/handle',\n    method: 'post',\n    data: params\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;AACA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AADA;;AAGA;AACA;AACA;AACO,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACO,SAASI,qBAAqBA,CAACC,EAAE,EAAE;EACxC,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B,GAAGG,EAAE;IACxCF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACO,SAASG,4BAA4BA,CAACD,EAAE,EAAE;EAC/C,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC,GAAGG,EAAE;IAC/CF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACO,SAASI,kBAAkBA,CAACC,GAAG,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B,GAAGM,GAAG;IACzCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACO,SAASM,uBAAuBA,CAACD,GAAG,EAAE;EAC3C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC,GAAGM,GAAG;IAC/CL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACO,SAASO,iBAAiBA,CAACV,MAAM,EAAE;EACxC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACO,SAASW,mBAAmBA,CAACX,MAAM,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,MAAM;IACdS,IAAI,EAAEZ;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}