<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.strategy.config.mapper.ThreatenStrategyRelationMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.strategy.config.model.ThreatenStrategyRelationListVO">
        select a.* from threaten_strategy_relation a 
        <where>
                <if test="query.id!=null">
                    and a.id = #{query.id}
                </if>
                <if test="query.strategyId!=null">
                    and a.strategy_id = #{query.strategyId}
                </if>
                <if test="query.threatenType!=null and query.threatenType!=''">
                    and a.threaten_type like concat('%', #{query.threatenType}, '%')
                </if>
                <if test="query.createTime!=null">
                    and a.create_time = #{query.createTime}
                </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.strategy.config.model.ThreatenStrategyRelationListVO">
        select a.* from threaten_strategy_relation a 
        where a.id=#{id}
    </select>
</mapper>
