{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\editServer.vue?vue&type=style&index=0&id=29ef855e&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\editServer.vue", "mtime": 1756381475338}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAiQC9hc3NldHMvc3R5bGVzL2N1c3RvbUZvcm0iOwouY3VzdG9tRm9ybSB7CiAgOjp2LWRlZXAgLmVsLWRpYWxvZ19fYm9keSB7CiAgICBoZWlnaHQ6IDc1dmg7CiAgICBvdmVyZmxvdy15OiBhdXRvOwogICAgcGFkZGluZzogMCAhaW1wb3J0YW50OwogIH0KfQo="}, {"version": 3, "sources": ["editServer.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi4BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "editServer.vue", "sourceRoot": "src/views/safe/server", "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"title\"\n    :visible.sync=\"visible\"\n    width=\"1200px\"\n    @open=\"openDialog\"\n    class=\"customForm\"\n    append-to-body>\n    <div class=\"customForm-container\" style=\"height: 100%; padding: 0 20px;\">\n      <el-form\n        ref=\"form\"\n        :model=\"form\"\n        :rules=\"rules\"\n        label-position=\"top\">\n        <el-row>\n          <el-col :span=\"24\">\n            <template v-for=\"ele in visibleAssetFields\">\n              <el-row\n                :gutter=\"20\"\n                :key=\"ele.id\"\n                type=\"flex\"\n                v-if=\"ele.isShow\"\n                style=\"flex-wrap: wrap;margin-bottom: 10px;\">\n                <!-- 基本信息、硬件/软件概况信息、位置信息等模块 -->\n                <el-col\n                  :span=\"24\"\n                  style=\"display: flex; flex-wrap: wrap;\"\n                  v-if=\"ele.formName === '基本信息' || ele.formName === '硬件/软件概况信息' || ele.formName === '位置信息'\">\n                  <el-col class=\"my-title\">\n                    <img v-if=\"ele.formName === '基本信息' && ele.isShow\" src=\"@/assets/images/application/jbxx.png\" alt=\"\"/>\n                    <i v-else-if=\"ele.formName === '硬件/软件概况信息' && ele.isShow\" class=\"el-icon-cpu icon\" />\n                    <i v-else-if=\"ele.formName === '位置信息' && ele.isShow\" class=\"el-icon-location-information icon\" />\n                    {{ ele.formName }}\n                  </el-col>\n                  <template v-for=\"item in ele.fieldsItems\">\n                    <el-col :key=\"item.fieldKey\" :span=\"getSpan(item.fieldKey)\" v-if=\"shouldShowField(item)\">\n                      <el-form-item\n                        :label=\"item.fieldName\"\n                        :prop=\"item.fieldKey\"\n                        :rules=\"getRulesForField(item)\">\n                        <!-- 资产类型字段 -->\n                        <template v-if=\"item.fieldKey === 'assetType'\">\n                          <el-select v-model=\"form.assetType\" placeholder=\"请选择资产类型\" @change=\"assetTypeChange\">\n                            <el-option v-for=\"children in children\" :key=\"children.id\" :label=\"children.typeName\"\n                                       :value=\"children.id\"/>\n                          </el-select>\n                        </template>\n\n                        <!-- 重要程度字段 -->\n                        <template v-else-if=\"item.fieldKey === 'degreeImportance'\">\n                          <el-select v-model=\"form.degreeImportance\" :placeholder=\"'请选择' + item.fieldName\">\n                            <el-option\n                              v-for=\"dict in dict.type.impt_grade\"\n                              :key=\"dict.value\"\n                              :label=\"dict.label\"\n                              :value=\"dict.value\"\n                            ></el-option>\n                          </el-select>\n                        </template>\n\n                        <!-- 所属部门字段 -->\n                        <template v-else-if=\"item.fieldKey === 'deptId'\">\n                          <dept-select v-model=\"form.deptId\" is-current/>\n                        </template>\n\n                        <!-- 供应商字段 -->\n                        <template v-else-if=\"item.fieldKey === 'vendor'\">\n                          <el-input\n                            v-model=\"form.vendorName\"\n                            @focus=\"showVendorDialog('vendor')\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n\n                        <!-- 设备厂商字段 -->\n                        <template v-else-if=\"item.fieldKey === 'facilityManufacturer'\">\n                          <el-input\n                            v-model=\"form.facilityManufacturerName\"\n                            @focus=\"showVendorDialog('facilityManufacturer')\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n\n                        <!-- 维保单位字段 -->\n                        <template v-else-if=\"item.fieldKey === 'maintainUnit'\">\n                          <el-input\n                            v-model=\"form.maintainUnitName\"\n                            @focus=\"showVendorDialog('maintainUnit')\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n\n                        <!-- 虚拟设备字段 -->\n                        <template v-else-if=\"['isVirtual', 'isSparing'].includes(item.fieldKey)\">\n                          <el-radio-group\n                            v-model=\"form[item.fieldKey]\"\n                            @input=\"item.fieldKey === 'isVirtual' ? changeIsVirtual : undefined\"\n                          >\n                            <el-radio\n                              v-for=\"dict in getDictDataInfo(item.fieldKey)\"\n                              :key=\"dict.value\"\n                              :label=\"dict.value\"\n                            >\n                              {{ dict.label }}\n                            </el-radio>\n                          </el-radio-group>\n                        </template>\n\n                        <!-- 承载设备字段 -->\n                        <template v-else-if=\"item.fieldKey === 'baseAsset'\">\n                          <el-input\n                            v-model=\"form.baseAssetName\"\n                            @focus=\"baseAssetSelectHandle\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n\n                        <template v-else-if=\"item.fieldKey === 'optId'\">\n                          <el-input\n                            v-model=\"form.optId\"\n                            @focus=\"setupOptSystem('optId')\"\n                            placeholder=\"请输入操作系统\"/>\n                        </template>\n\n                        <template v-else-if=\"item.fieldKey === 'mdId'\">\n                          <el-input\n                          v-model=\"form.mdId\"\n                          @focus=\"setupOptSystem('mdId')\"\n                          placeholder=\"请输入中间件\"/>\n                        </template>\n\n                        <template v-else-if=\"item.fieldKey === 'dbId'\">\n                          <el-input\n                            v-model=\"form.dbId\"\n                            @focus=\"setupOptSystem('dbId')\"\n                            placeholder=\"请输入数据库\"/>\n                        </template>\n\n                        <!-- 资产标签字段 -->\n                        <template v-else-if=\"item.fieldKey === 'tags'\">\n                          <dynamic-tag v-model=\"form.tags\"/>\n                        </template>\n\n                        <!-- 所在机房 -->\n                        <template v-else-if=\"item.fieldKey === 'locationId'\">\n                          <location-select v-model=\"form.locationId\"/>\n                        </template>\n\n                        <!-- 默认文本输入框 -->\n                        <template v-else>\n                          <el-input\n                            v-model=\"form[item.fieldKey]\"\n                            :type=\"item.fieldKey === 'remark' ? 'textarea' : 'text'\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n                      </el-form-item>\n                    </el-col>\n                  </template>\n                </el-col>\n\n                <!-- 网络信息模块单独处理 -->\n                <el-col :span=\"24\" v-if=\"ele.formName === '网络信息' && ele.isShow\">\n                  <div class=\"my-title\"><img src=\"@/assets/images/application/netinfo.png\" alt=\"\"/>网络信息</div>\n                  <template v-for=\"item in ele.fieldsItems\">\n                    <el-form-item\n                      :key=\"item.fieldKey\"\n                      :label=\"item.fieldName\"\n                      :prop=\"item.fieldKey\"\n                      v-if=\"item.fieldKey === 'exposedIp'\"\n                      :rules=\"getRulesForField(item)\">\n                      <el-input v-model=\"form.exposedIp\" placeholder=\"请输入外网IP\"/>\n                    </el-form-item>\n                  </template>\n                  <el-table :data=\"form.ipMacArr\" v-if=\"netinfoTable.length\" style=\"width: 100%\">\n                    <el-table-column\n                      v-for=\"(colum, index) in netinfoTable\"\n                      :key=\"index\"\n                      :label=\"colum.fieldName\"\n                      :width=\"colum.fieldKey === 'isMainIp' ? '60px' : ''\">\n                      <template slot-scope=\"scope\">\n                        <!-- 主IP复选框 -->\n                        <el-checkbox\n                          v-if=\"colum.fieldKey === 'isMainIp'\"\n                          v-model=\"scope.row.isMainIp\"/>\n\n                        <!-- 网络区域选择器 -->\n                        <NetworkSelect\n                          v-else-if=\"colum.fieldKey === 'domainId'\"\n                          v-model=\"scope.row.domainId\"/>\n\n                        <!-- IP地址输入框 -->\n                        <el-input\n                          v-else-if=\"colum.fieldKey === 'ipv4'\"\n                          v-model=\"scope.row.ipv4\"\n                          placeholder=\"请输入IP\"/>\n\n                        <!-- MAC地址输入框 -->\n                        <el-input\n                          v-else-if=\"colum.fieldKey === 'mac'\"\n                          v-model=\"scope.row.mac\"\n                          placeholder=\"请输入MAC地址\"/>\n                      </template>\n                    </el-table-column>\n                    <el-table-column align=\"center\">\n                      <template slot=\"header\">\n                        <div style=\"display: inline;\">操作</div>\n                        <div style=\"display: inline;float: right;font-size: 18px;margin-left: 10px;\"><i\n                          class=\"el-icon-circle-plus-outline\" @click=\"addIpMac\"/></div>\n                      </template>\n                      <template slot-scope=\"scope\">\n                        <span style=\"font-size: 18px;\">\n                          <i class=\"el-icon-remove-outline\" @click=\"removeIpMac(scope.$index)\"/>\n                        </span>\n                      </template>\n                    </el-table-column>\n                  </el-table>\n                </el-col>\n              </el-row>\n            </template>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <el-dialog title=\"选择供应商\" :visible.sync=\"vendorDialog\" width=\"900px\" append-to-body>\n        <vendor-select v-if=\"vendorDialog\" :value=\"getSelectedVendor()\" @confirm=\"selectConfirm2\" @cancel=\"userCancel2\"/>\n      </el-dialog>\n      <el-dialog title=\"选择系统软件\" :visible.sync=\"softDialog\" width=\"800px\" append-to-body>\n        <product-select v-if=\"softDialog\" :type=\"softType\" :multipleMode=\"multiple\" :value=\"getSelectedProduct()\"\n                        @confirm=\"softConfirm\"\n                        @cancel=\"softCancel\" @change=\"softDialog= false\" ref=\"softForm\"/>\n      </el-dialog>\n      <el-dialog title=\"选择服务器\" :visible.sync=\"baseAssetDialog\" width=\"800px\" append-to-body>\n        <over-view-select v-if=\"baseAssetDialog\" :asset-class=\"4\" :type=\"softType\" :value=\"valueInit()\"\n                          @confirm=\"baseAssetConfirm\" @cancel=\"baseAssetCancel\"></over-view-select>\n      </el-dialog>\n      <el-dialog title=\"选择绑定主机\" :visible.sync=\"assetHostDialog\" width=\"800px\" append-to-body :show-close=\"false\">\n        <el-select v-model=\"host\">\n          <el-option v-for=\"host in assetHosts\"\n                     :key=\"host.id\"\n                     :label=\"host.hostName\"\n                     :value=\"host.id\"></el-option>\n        </el-select>\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"chooseHost\">确定</el-button>\n        </div>\n      </el-dialog>\n    </div>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button v-if=\"editItem === 'edit'\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\n      <el-button @click=\"cancel\">取 消</el-button>\n    </div>\n  </el-dialog>\n\n</template>\n\n<script>\nimport {addServer2, updateServer2, getServer} from \"@/api/safe/server\";\nimport {getVendor} from \"@/api/safe/vendor\";\nimport {getAssetTypeChildrenByid} from \"@/api/safe/overview\";\nimport vendorSelect from \"@/views/components/select/vendorSelect\";\nimport productSelect from \"@/views/components/select/productSelect\";\nimport DeptSelect from \"@/views/components/select/deptSelect\";\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport LocationSelect from \"@/views/components/select/locationSelect\";\nimport DynamicTag from \"@/components/DynamicTag\";\nimport OverViewSelect from \"@/views/components/select/overViewSelect\";\nimport {getHostInfo, getHostList} from \"@/api/asset/host\";\nimport {listProduct} from \"@/api/monitor2/product\";\nimport {listDomain} from \"@/api/dict/domain\";\nimport assetRegister from \"@/mixins/assetRegister\";\n\nexport default {\n  name: 'EditServer',\n  mixins: [assetRegister],\n  dicts: ['impt_grade', 'sys_yes_no', 'proc_type','is_sparing'],\n  components: {\n    vendorSelect,\n    productSelect,\n    DeptSelect,\n    NetworkSelect,\n    LocationSelect,\n    DynamicTag,\n    OverViewSelect,\n  },\n  props: {\n    assetId: {\n      type: [Number, String],\n      default: null,\n      required: false\n    },\n    handleData: {\n      type: Object,\n      default: null,\n      required: false\n    },\n    title: {\n      type: String,\n      default: '新增服务器'\n    },\n    editFlagVisible: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      host: undefined,\n      assetHosts: [],\n      assetHostDialog: false,\n      myTags: ['国产化', '安可'],\n      classId: 4,\n      children: [],\n      typeTreeData: [],\n      typelist: [],\n      vendorDialog: false,\n      softDialog: false,\n      softType: 'system',\n      softField: '',\n      editItem: 'edit',\n      addEvent: '',\n      multiple: false,\n      form: {\n        ipMacArr: [],\n        assetName: '',\n        exposedIp: '',\n        handleId: undefined,\n        dbsystemArr: [],\n        mdsystemArr: [],\n      },\n      rules: {\n        assetCode: [\n          /*{required: true, message: \"资产编码不能为空\", trigger: \"blur\"},*/\n          {min: 0, max: 64, message: '资产编码不能超过 64 个字符', trigger: 'blur'},\n        ],\n        assetName: [\n          {required: true, message: \"资产名称不能为空\", trigger: \"blur\"},\n          {min: 0, max: 64, message: '资产名称不能超过 64 个字符', trigger: 'blur'},\n        ],\n        assetType: [\n          {required: true, message: \"资产类型不能为空\", trigger: \"blur\"},\n        ],\n        ip: [\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: \"IP地址不能为空或格式不正确\",\n            trigger: \"blur\"\n          },\n        ],\n        mac: [\n          {pattern: '^([0-9a-fA-F]{2}(:|-)){5}[0-9a-fA-F]{2}$', message: \"Mac地址格式不正确\", trigger: \"blur\"},\n        ],\n        domainId: [\n          {required: true, message: \"网络区域不能为空！\", trigger: 'blur'}\n        ],\n        locationDetail: [\n          {min: 0, max: 128, message: '详细地址不能超过 128 个字符', trigger: 'blur'},\n        ],\n        deptId: [\n          {required: true, message: \"所属部门不能为空\", trigger: \"blur\"},\n        ]\n      },\n      baseAssetDialog: false,\n      assetAllocationType: '2',\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.editFlagVisible\n      },\n      set(val) {\n        this.$emit('update:editFlagVisible', val)\n      }\n    },\n    // 动态字段\n    visibleAssetFields() {\n      return this.assetList.map(group => {\n        const newGroup = { ...group };\n        newGroup.fieldsItems = group.fieldsItems.filter(item => item.isShow);\n        return newGroup;\n      });\n    },\n    // 动态规则\n    dynamicRules() {\n      const rules = {};\n      this.visibleAssetFields.forEach(group => {\n        group.fieldsItems.forEach(item => {\n          const fieldKey = item.fieldKey;\n\n          if (!rules[fieldKey]) {\n            rules[fieldKey] = [];\n          }\n\n          if (this.rules[fieldKey]) {\n            const filteredRules = this.rules[fieldKey].filter(rule => !rule.required);\n            rules[fieldKey].push(...filteredRules);\n          }\n\n          if (item.required) {\n            const hasRequiredRule = rules[fieldKey].some(rule => rule.required);\n            if (!hasRequiredRule) {\n              rules[fieldKey].push({\n                required: true,\n                message: `${item.fieldName}不能为空`,\n                trigger: 'blur'\n              });\n            }\n          }\n        });\n      });\n      return rules;\n    },\n\n    // 网络信息列表字段\n    netinfoTable() {\n      let assetFields = this.assetList.filter(group => group.formName === '网络信息')\n      return assetFields[0].fieldsItems.filter(item => item.fieldKey !== 'exposedIp' && item.isShow)\n    }\n  },\n  mounted() {\n\n  },\n  methods: {\n    getDictDataInfo(fieldKey) {\n      switch (fieldKey) {\n        case 'isVirtual':\n          return this.dict.type.sys_yes_no;\n        case 'isSparing':\n          return this.dict.type.is_sparing;\n        default:\n          return [];\n      }\n    },\n\n    openDialog() {\n      this.init();\n      this.initData();\n      this.$nextTick(() => {\n        if (!this.form.assetCode){\n          this.form.assetCode = 'ZJ' + this.getFormattedDate()\n        }\n      })\n    },\n    chooseHost() {\n      if(!this.host){\n        this.$message('请选择一台主机！')\n      }else{\n        this.gethost(this.host)\n        this.assetHostDialog = false\n      }\n    },\n    init() {\n      getAssetTypeChildrenByid(this.classId).then(res => {\n        this.typelist = res.rows;\n        for (let i = 0; i < this.typelist.length; i++) {\n          if (this.typelist[i].typeGradedProtection == 1) {\n            this.typelist.splice(Number(i), 1);\n            i = i - 1;\n          }\n        }\n        this.typeTreeData = this.handleTree(this.typelist, 'id', 'pid');\n        this.children = this.typeTreeData[0].children;\n        if (this.children && this.children.length) this.rules.assetType = [\n          {required: true, message: \"资产类型不能为空\", trigger: \"blur\"},\n        ]\n      });\n    },\n    async initData() {\n      if (this.assetId) {\n        await getServer(this.assetId).then(response => {\n          if (response.data.ipMacArr) {\n            response.data.ipMacArr.forEach(e => {\n              if (e.mainIp === '1') {\n                e.isMainIp = true\n              } else if (e.mainIp === '0') {\n                e.isMainIp = false\n              }\n            })\n          }\n          this.form = response.data;\n          if (this.form.optsystem != null) this.form.optId = this.form.optsystem.procName;\n          if (this.form.dbsystemArr !== null && this.form.dbsystemArr !== undefined && this.form.dbsystemArr.length) {\n            this.form.dbId = ''\n            this.form.dbsystemArr.forEach(e => {\n              this.form.dbId += '【' + e.procName + '】'\n            })\n          }\n          if (this.form.mdsystemArr !== null && this.form.mdsystemArr !== undefined && this.form.mdsystemArr.length) {\n            this.form.mdId = ''\n            this.form.mdsystemArr.forEach(e => {\n              this.form.mdId += '【' + e.procName + '】'\n            })\n          }\n          this.myTags = this.form.tags ? this.form.tags.split(',') : [];\n\n          if (this.form.facilityManufacturer) {\n            getVendor(this.form.facilityManufacturer).then(res => {\n              this.form.facilityManufacturerName = res.data.vendorName;\n            });\n          }\n\n          if (this.form.maintainUnit) {\n            getVendor(this.form.maintainUnit).then(res => {\n              this.form.maintainUnitName = res.data.vendorName;\n            });\n          }\n        })\n      }\n      if (this.handleData) {\n        if(this.handleData.deptId){\n          this.form.deptId = this.handleData.deptId;\n        }\n        this.form.handleId = this.handleData.handleId\n        if(this.handleData.ip){\n          let ipMacItem = {\n            isMainIp: true,\n            ipv4: this.handleData.ip\n          }\n          await listDomain({iparea: this.handleData.ip}).then(domainRes => {\n            if(domainRes.data && domainRes.data.length>0){\n              ipMacItem.domainId = domainRes.data[0].domainId;\n            }\n          })\n          this.form.ipMacArr.push(ipMacItem);\n        }\n        if(this.handleData.name){\n          await listProduct({procName: this.handleData.name}).then(productRes => {\n            if(productRes.rows){\n              let matchArr = productRes.rows.filter(item => this.handleData.name === item.procName);\n              if(matchArr && matchArr.length > 0){\n                this.$set(this.form,'optId',matchArr[0].procName);\n                let optsystem = this.form.optsystem || {};\n                optsystem.prdid = matchArr[0].prdid;\n                optsystem.procName = matchArr[0].procName;\n                this.$set(this.form,'optsystem',optsystem);\n              }\n            }\n          })\n        }\n        if (this.handleData.hostId) {\n          if (this.assetId) {\n            if (this.handleData.hostId.indexOf(',') > 0) {\n              this.assetHostDialog = true\n\n              await getHostList({pageSize: -1, hostIds: this.handleData.hostId.split(',')}).then(res => {\n                console.log('res', res)\n                this.assetHosts = res.data.list\n              })\n            } else {\n              this.$confirm('是否使用探测数据替换本地数据?').then(() => {\n                this.gethost(this.handleData.hostId)\n              })\n            }\n          } else {\n            this.gethost(this.handleData.hostId)\n          }\n        }\n      }\n    },\n    gethost(hostId) {\n      getHostInfo(hostId).then(res => {\n        const host = res.data\n        this.form.assetName = host.hostName\n        this.form.cpuFrame = host.hostPhysical.arch\n        let ipMacArr = host.hostNicList.filter(row => row.ipv4.indexOf(host.hostIp) > -1)\n        const oldIpMacArr = this.form.ipMacArr.filter(row => row.ipv4.indexOf(host.hostIp) > -1)\n        console.log('oldIpMacArr', oldIpMacArr)\n        ipMacArr.forEach(row => {\n          row.ipv4 = row.ipv4.replace(/[\\[\\]]/g, '').split('/')[0]\n          row.ipv6 = ''\n          row.id = ''\n          if (oldIpMacArr.length > 0) {\n            row.domainId = oldIpMacArr[0].domainId\n            row.isMainIp = oldIpMacArr[0].isMainIp\n          }\n        })\n        this.form.exposedIp = host.exposedIp\n        this.form.ipMacArr = ipMacArr\n        this.form.cwHostId = host.id\n        this.form.optsystem = {prdid: host.prdid, procName: host.osReleaseName}\n        this.form.optId = this.form.optsystem.procName;\n        this.form.dbsystemArr = []\n        this.form.mdsystemArr = []\n        for (let row of host.hostApplicationList) {\n          if (row.category === 'database') {\n            this.form.dbsystemArr.push({prdid: row.prdid, procName: row.app})\n          } else if (row.category === 'web_server') {\n            this.form.mdsystemArr.push({prdid: row.prdid, procName: row.app})\n          }\n        }\n        this.form.dbId = ''\n        this.form.dbsystemArr.forEach(e => {\n          this.form.dbId += '【' + e.procName + '】'\n        })\n        this.form.mdId = ''\n        this.form.mdsystemArr.forEach(e => {\n          this.form.mdId += '【' + e.procName + '】'\n        })\n      })\n    },\n    /**\n     * 选择供应商\n     */\n    showVendorDialog(vendor) {\n      if (vendor == 'vendor') {\n        this.vendorType = 'vendor';\n      }\n      if (vendor == 'facilityManufacturer') {\n        this.vendorType = 'facilityManufacturer';\n      }\n      if (vendor == 'maintainUnit') {\n        this.vendorType = 'maintainUnit';\n      }\n      this.vendorDialog = true\n    },\n    getSelectedVendor() {\n      if (this.vendorType == 'vendor' && this.form.vendor != undefined) {\n        return this.form.vendor;\n      } else if (this.vendorType == 'facilityManufacturer' && this.form.facilityManufacturer != undefined) {\n        return this.form.facilityManufacturer;\n      } else if (this.vendorType == 'maintainUnit' && this.form.maintainUnit != undefined) {\n        return this.form.maintainUnit;\n      }\n    },\n    /**\n     * 选择产商\n     */\n    selectConfirm2(vendor) {\n      if (this.vendorType == 'vendor') {\n        this.form.vendor = vendor.id;\n        this.form.vendorName = vendor.vendorName;\n      }\n      if (this.vendorType == 'facilityManufacturer') {\n        this.form.facilityManufacturer = vendor.id;\n        this.form.facilityManufacturerName = vendor.vendorName;\n      }\n      if (this.vendorType == 'maintainUnit') {\n        this.form.maintainUnit = vendor.id;\n        this.form.maintainUnitName = vendor.vendorName;\n      }\n      this.vendorDialog = false;\n    },\n    userCancel2() {\n      this.vendorDialog = false;\n    },\n    //选择操作系统\n    setupOptSystem(field) {\n      this.softField = field;\n      if (field == 'optId') {\n        this.multiple = false\n        this.softType = 'system'\n      }\n      if (field == 'dbId') {\n        this.multiple = true\n        this.softType = 'database';\n      }\n      if (field == 'mdId') {\n        this.multiple = true\n        this.softType = 'middleware';\n      }\n      this.softDialog = true;\n    },\n    getSelectedProduct() {\n      if (this.softField == 'optId' && this.form.optsystem != undefined) {\n        return this.form.optsystem.prdid;\n      } else if (this.softField == 'dbId' && this.form.dbsystemArr != null && this.form.dbsystemArr != undefined && this.form.dbsystemArr.length > 0) {\n        return this.form.dbsystemArr.map(e => e.prdid)\n      } else if (this.softField == 'mdId' && this.form.mdsystemArr != null && this.form.mdsystemArr != undefined && this.form.mdsystemArr.length > 0) {\n        return this.form.mdsystemArr.map(e => e.prdid)\n      }\n    },\n    softConfirm(row) {\n      let value = ''\n      if (this.softField == 'optId') {\n        if (row == null) {\n          this.form.optsystem = null;\n        } else {\n          if (this.form.optsystem == null) this.form.optsystem = {};\n          this.form.optsystem.prdid = row.prdid;\n          this.form.optsystem.procName = row.procName;\n        }\n        value = row.procName\n      }\n      if (this.softField == 'dbId') {\n        if (row == null || row.length === 0) {\n          this.form.dbsystemArr = [];\n        } else {\n          this.form.dbsystemArr = [];\n          row.forEach(e => {\n            this.form.dbsystemArr.push({prdid: e.prdid, procName: e.procName})\n            value += '【' + e.procName + '】'\n          })\n        }\n      }\n      if (this.softField == 'mdId') {\n        if (row == null || row.length === 0) {\n          this.form.mdsystemArr = [];\n        } else {\n          this.form.mdsystemArr = [];\n          row.forEach(e => {\n            this.form.mdsystemArr.push({prdid: e.prdid, procName: e.procName})\n            value += '【' + e.procName + '】'\n          })\n        }\n      }\n      this.form[this.softField] = value\n      this.softDialog = false;\n    },\n    softCancel() {\n      this.softDialog = false;\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.form = {...this.form}\n      this.form.assetClass = this.classId\n      this.form.assetClassDesc = this.className\n      this.$refs['form'].validate(valid => {\n        if (valid && this.checkIpMacArr()) {\n          const flag = (this.children && this.children.length > 0)\n          if (flag == undefined) {\n            this.form.assetType = this.classId\n            this.form.assetTypeDesc = this.className\n          }\n          this.form.ipMacArr.forEach(e => {\n            if (e.isMainIp) {\n              e.mainIp = '1'\n            } else {\n              e.mainIp = '0'\n            }\n          })\n          if (this.form.assetId != null) {\n            updateServer2(this.form).then(response => {\n              this.$modal.msgSuccess('修改成功')\n              this.form.ipMacArr = [];\n              this.$refs.form.resetFields()\n              this.$emit('confirm', response.data)\n              this.visible = false;\n              if(this.$parent.$parent){\n                // this.$parent.$parent.editFlag = false;\n                this.$parent.$parent.getList && this.$parent.$parent.getList();\n                this.$parent.$parent.getServerCountByDictData && this.$parent.$parent.getServerCountByDictData();\n              }\n            });\n          } else {\n            addServer2(this.form).then(response => {\n              this.$modal.msgSuccess('新增成功')\n              this.$refs.form.resetFields()\n              this.form.ipMacArr = [];\n              this.$emit('confirm', response.data)\n              this.open = false\n              if (this.$route.params.add) {\n                this.$router.back()\n              }\n              this.backAdd(this.form)\n              this.visible = false;\n              if(this.$parent.$parent){\n                // this.$parent.$parent.editFlag = false;\n                this.$parent.$parent.getList && this.$parent.$parent.getList();\n                this.$parent.$parent.getServerCountByDictData && this.$parent.$parent.getServerCountByDictData();\n              }\n            })\n          }\n        }\n      });\n    },\n    cancel() {\n      this.$refs['form'].resetFields()\n      this.$emit('cancel')\n    },\n    /**\n     * 其他页面增加的回调\n     */\n    backAdd(addrow) {\n      this.$root.$emit(this.addEvent, addrow)\n    },\n    addIpMac() {\n      this.form.ipMacArr.push({isMainIp: false})\n    },\n    removeIpMac(index) {\n      this.form.ipMacArr.splice(index, 1)\n    },\n    checkIpMacArr() {\n      if (this.form.ipMacArr === null || this.form.ipMacArr.length <= 0) {\n        this.$modal.msgError('网络信息不可为空')\n        return false\n      }\n\n      let mainIpCount = 0\n      for (let i = 0; i < this.form.ipMacArr.length; i++) {\n        const e = this.form.ipMacArr[i]\n        if (e.domainId === null || e.domainId === '' || e.domainId === undefined) {\n          this.$modal.msgError('所属网络不可为空')\n          return false\n        }\n        if (e.ipv4 === null || e.ipv4 === '' || e.ipv4 === undefined) {\n          this.$modal.msgError('IP不可为空')\n          return false\n        } else {\n          if (!this.isValidIP(e.ipv4)) {\n            this.$modal.msgError('请输入正确格式的IP')\n            return false\n          }\n        }\n        if (e.isMainIp) {\n          mainIpCount++\n        }\n      }\n      if (mainIpCount === 0) {\n        this.$modal.msgError('必须选择一个主IP')\n        return false\n      } else if (mainIpCount > 1) {\n        this.$modal.msgError('只能选择一个主IP')\n        return false\n      }\n\n      return true\n    },\n    inputLossOfFocus(val, index){\n      if (this.form.assetCode == '' || !this.form.assetCode){\n        this.form.assetCode = 'ZJ' + this.getFormattedDate()\n      }\n    },\n    /**\n     * 资产类型变化\n     */\n    assetTypeChange(data) {\n      let item = this.children.find(item => {\n        return item.id == data;\n      })\n      if (item) {\n        this.form.assetTypeDesc = item.typeName;\n      }\n    },\n\n    getRulesForField(item) {\n      return this.dynamicRules[item.fieldKey] || [];\n    },\n\n    // 获取字段所占列数\n    getSpan(fieldKey) {\n      return fieldKey === 'remark' ? 16 : 8;\n    },\n\n    // 判断字段是否应该显示\n    shouldShowField(item) {\n      // 承载设备只在虚拟设备为Y时显示\n      if (item.fieldKey === 'baseAsset') {\n        return this.form.isVirtual === 'Y';\n      }\n      // 资产类型只在有子类型时显示\n      if (item.fieldKey === 'assetType') {\n        return this.children && this.children.length;\n      }\n      return true;\n    },\n\n    getFormattedDate() {\n      const now = new Date();\n      const year = now.getFullYear();\n      const month = String(now.getMonth() + 1).padStart(2, '0');\n      const day = String(now.getDate()).padStart(2, '0');\n      const hours = String(now.getHours()).padStart(2, '0');\n      const minutes = String(now.getMinutes()).padStart(2, '0');\n      const seconds = String(now.getSeconds()).padStart(2, '0');\n\n      return `${year}${month}${day}${hours}${minutes}${seconds}`;\n    },\n    isValidIP(ip) {\n      const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/\n      const flag = ipRegex.test(ip)\n      console.log(flag)\n      return flag\n    },\n    baseAssetSelectHandle() {\n      this.baseAssetDialog = true;\n    },\n    baseAssetCancel() {\n      this.baseAssetDialog = false;\n    },\n    baseAssetConfirm(row) {\n      this.form.baseAsset = row.assetId;\n      this.form.baseAssetName = row.assetName;\n      this.baseAssetCancel();\n    },\n    valueInit() {\n      if (this.form.baseAsset != null && this.form.baseAsset != undefined) {\n        return [{assetId: this.form.baseAsset}];\n      }\n    },\n    changeIsVirtual(value) {\n      if (value === 'N') {\n        this.form.baseAsset = ''\n        this.form.baseAssetName = ''\n      }\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"@/assets/styles/customForm\";\n.customForm {\n  ::v-deep .el-dialog__body {\n    height: 75vh;\n    overflow-y: auto;\n    padding: 0 !important;\n  }\n}\n</style>\n"]}]}