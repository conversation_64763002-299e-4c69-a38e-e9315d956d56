<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenHoneypotTrapMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenHoneypotTrapListVO">
        select a.* from threaten_honeypot_trap a
        <where>
            <if test="query.alarmId!=null">
                and a.id in(select origin_id from threaten_alarm_origin where alarm_id=#{query.alarmId} and threaten_type=#{query.threatenType.name})
            </if>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.businessGroupId!=null">
                and a.business_group_id = #{query.businessGroupId}
            </if>
            <if test="query.businessGroupName!=null and query.businessGroupName!=''">
                and a.business_group_name like concat('%', #{query.businessGroupName}, '%')
            </if>
            <if test="query.eventCreateTime!=null">
                and a.event_create_time = #{query.eventCreateTime}
            </if>
            <if test="query.eventDescription!=null and query.eventDescription!=''">
                and a.event_description like concat('%', #{query.eventDescription}, '%')
            </if>
            <if test="query.eventEndTime!=null">
                and a.event_end_time = #{query.eventEndTime}
            </if>
            <if test="query.eventLevel!=null and query.eventLevel!=''">
                and a.event_level like concat('%', #{query.eventLevel}, '%')
            </if>
            <if test="query.eventName!=null and query.eventName!=''">
                and a.event_name like concat('%', #{query.eventName}, '%')
            </if>
            <if test="query.eventSolution!=null and query.eventSolution!=''">
                and a.event_solution like concat('%', #{query.eventSolution}, '%')
            </if>
            <if test="query.eventStartTime!=null">
                and a.event_start_time = #{query.eventStartTime}
            </if>
            <if test="query.eventState!=null and query.eventState!=''">
                and a.event_state like concat('%', #{query.eventState}, '%')
            </if>
            <if test="query.eventUpdateTime!=null">
                and a.event_update_time = #{query.eventUpdateTime}
            </if>
            <if test="query.hostId!=null">
                and a.host_id = #{query.hostId}
            </if>
            <if test="query.hostIp!=null and query.hostIp!=''">
                and a.host_ip like concat('%', #{query.hostIp}, '%')
            </if>
            <if test="query.hostName!=null and query.hostName!=''">
                and a.host_name like concat('%', #{query.hostName}, '%')
            </if>
            <if test="query.sourceIp!=null and query.sourceIp!=''">
                and a.source_ip like concat('%', #{query.sourceIp}, '%')
            </if>
            <if test="query.targetIp!=null and query.targetIp!=''">
                and a.target_ip like concat('%', #{query.targetIp}, '%')
            </if>
            <if test="query.targetPort!=null">
                and a.target_port = #{query.targetPort}
            </if>
            <if test="query.startTimeline!=null">
                and a.timeline >= DATE_FORMAT(#{query.startTimeline}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="query.endTimeline!=null">
                and a.timeline &lt; DATE_FORMAT(#{query.endTimeline}, '%Y-%m-%d %H:%i:%s')
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenHoneypotTrapListVO">
        select a.* from threaten_honeypot_trap a
        where a.id=#{id}
    </select>
</mapper>
