<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenExceptNetMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenExceptNetListVO">
        select a.* from threaten_except_net a
        <where>
            <if test="query.alarmId!=null">
                and a.id in(select origin_id from threaten_alarm_origin where alarm_id=#{query.alarmId} and threaten_type=#{query.threatenType.name})
            </if>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.businessGroupId!=null">
                and a.business_group_id = #{query.businessGroupId}
            </if>
            <if test="query.businessGroupName!=null and query.businessGroupName!=''">
                and a.business_group_name like concat('%', #{query.businessGroupName}, '%')
            </if>
            <if test="query.environment!=null and query.environment!=''">
                and a.environment like concat('%', #{query.environment}, '%')
            </if>
            <if test="query.eventCreateTime!=null">
                and a.event_create_time = #{query.eventCreateTime}
            </if>
            <if test="query.eventDescription!=null and query.eventDescription!=''">
                and a.event_description like concat('%', #{query.eventDescription}, '%')
            </if>
            <if test="query.eventLevel!=null and query.eventLevel!=''">
                and a.event_level like concat('%', #{query.eventLevel}, '%')
            </if>
            <if test="query.eventName!=null and query.eventName!=''">
                and a.event_name like concat('%', #{query.eventName}, '%')
            </if>
            <if test="query.eventState!=null and query.eventState!=''">
                and a.event_state like concat('%', #{query.eventState}, '%')
            </if>
            <if test="query.eventType!=null and query.eventType!=''">
                and a.event_type like concat('%', #{query.eventType}, '%')
            </if>
            <if test="query.hostId!=null">
                and a.host_id = #{query.hostId}
            </if>
            <if test="query.hostIp!=null and query.hostIp!=''">
                and a.host_ip like concat('%', #{query.hostIp}, '%')
            </if>
            <if test="query.hostName!=null and query.hostName!=''">
                and a.host_name like concat('%', #{query.hostName}, '%')
            </if>
            <if test="query.ipCityCode!=null and query.ipCityCode!=''">
                and a.ip_city_code like concat('%', #{query.ipCityCode}, '%')
            </if>
            <if test="query.ipCountryCode!=null and query.ipCountryCode!=''">
                and a.ip_country_code like concat('%', #{query.ipCountryCode}, '%')
            </if>
            <if test="query.ipIsp!=null and query.ipIsp!=''">
                and a.ip_isp like concat('%', #{query.ipIsp}, '%')
            </if>
            <if test="query.ipOwner!=null and query.ipOwner!=''">
                and a.ip_owner like concat('%', #{query.ipOwner}, '%')
            </if>
            <if test="query.ipProvinceCode!=null and query.ipProvinceCode!=''">
                and a.ip_province_code like concat('%', #{query.ipProvinceCode}, '%')
            </if>
            <if test="query.processCommandLine!=null and query.processCommandLine!=''">
                and a.process_command_line like concat('%', #{query.processCommandLine}, '%')
            </if>
            <if test="query.processId!=null">
                and a.process_id = #{query.processId}
            </if>
            <if test="query.processName!=null and query.processName!=''">
                and a.process_name like concat('%', #{query.processName}, '%')
            </if>
            <if test="query.processPath!=null and query.processPath!=''">
                and a.process_path like concat('%', #{query.processPath}, '%')
            </if>
            <if test="query.userGid!=null">
                and a.user_gid = #{query.userGid}
            </if>
            <if test="query.userGname!=null and query.userGname!=''">
                and a.user_gname like concat('%', #{query.userGname}, '%')
            </if>
            <if test="query.userId!=null">
                and a.user_id = #{query.userId}
            </if>
            <if test="query.userName!=null and query.userName!=''">
                and a.user_name like concat('%', #{query.userName}, '%')
            </if>
            <if test="query.startTimeline!=null">
                and a.timeline >= DATE_FORMAT(#{query.startTimeline}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="query.endTimeline!=null">
                and a.timeline &lt; DATE_FORMAT(#{query.endTimeline}, '%Y-%m-%d %H:%i:%s')
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenExceptNetListVO">
        select a.* from threaten_except_net a
        where a.id=#{id}
    </select>
</mapper>
