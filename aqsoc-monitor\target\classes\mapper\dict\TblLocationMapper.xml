<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dict.mapper.TblLocationMapper">

    <resultMap type="TblLocation" id="TblLocationResult">
        <result property="locationId" column="location_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="locationName" column="location_name"/>
        <result property="locationFullName" column="location_full_name"/>
        <result property="locationType" column="location_type"/>
        <result property="orderNum" column="order_num"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>

    <sql id="selectTblLocationVo">
        select location_id, parent_id, ancestors, location_name, location_full_name, location_type, order_num, status, remark, create_by, create_time, update_by, update_time, user_id, dept_id from tbl_location as l
    </sql>

    <select id="selectTblLocationList" parameterType="TblLocation" resultMap="TblLocationResult">
        <include refid="selectTblLocationVo"/>
        <where>
            <if test="locationName != null  and locationName != ''"> and location_name like concat('%', #{locationName}, '%')</if>
            <if test="locationFullName != null  and locationFullName != ''"> and location_full_name like concat('%', #{locationFullName}, '%')</if>
            <if test="locationType != null  and locationType != ''"> and location_type = #{locationType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="parentId != null and parentId != ''">and parent_id = #{parentId}</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
        order by parent_id, order_num
    </select>

    <select id="selectTblLocationByLocationId" parameterType="Long" resultMap="TblLocationResult">
        <include refid="selectTblLocationVo"/>
        where location_id = #{locationId}
    </select>
    <select id="selectTblLocationByLocationIds" parameterType="string" resultMap="TblLocationResult">
        <include refid="selectTblLocationVo"/>
        where location_id in
        <foreach collection="list" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </select>

    <select id="selectChildrenLocationById" parameterType="Long" resultMap="TblLocationResult">
        <include refid="selectTblLocationVo"/>
        where find_in_set(#{locationId}, ancestors)
	</select>

    <select id="selectNormalChildrenLocationById" parameterType="Long" resultType="int">
		select count(*) from tbl_location where status = '0' and find_in_set(#{locationId}, ancestors)
	</select>

    <select id="hasChildByLocationId" parameterType="Long" resultType="int">
		select count(1) from tbl_location
		where parent_id = #{locationId} limit 1
	</select>

    <select id="checkLocationNameUnique" resultMap="TblLocationResult">
        <include refid="selectTblLocationVo"/>
        where location_name = #{locationName} and parent_id = #{parentId} limit 1
    </select>

    <select id="getTblLocationByLocationId" resultMap="TblLocationResult">
        SELECT
        location_id,
        parent_id,
        ancestors,
        location_name,
        location_full_name,
        location_type,
        order_num,
        STATUS,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        user_id,
        dept_id
        FROM
        tbl_location
        WHERE
        ancestors LIKE concat('%',#{locationId},'%') AND location_type = #{type}
    </select>

    <select id="selectTblLocationByLocationIdsAndType" resultMap="TblLocationResult">
        SELECT
            location_id,
            parent_id,
            ancestors,
            location_name,
            location_full_name,
            location_type,
            order_num,
            STATUS,
            remark,
            create_by,
            create_time,
            update_by,
            update_time,
            user_id,
            dept_id
        FROM
            tbl_location
        WHERE
        location_type = #{type} and location_id in
        <foreach collection="strings" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </select>

    <select id="selectParentLocation" resultType="com.ruoyi.dict.domain.TblLocation" resultMap="TblLocationResult">
        SELECT
            *
        FROM
            `tbl_location` t1
            INNER JOIN (SELECT ancestors FROM tbl_location WHERE location_id = #{locationId}) t2 ON FIND_IN_SET(t1.location_id,t2.ancestors)
        WHERE
            location_type=#{locationType}
    </select>

    <update id="updateLocationChildren" parameterType="java.util.List">
        update tbl_location set ancestors =
        <foreach collection="tblLocations" item="item" index="index"
                 separator=" " open="case location_id" close="end">
            when #{item.locationId} then #{item.ancestors}
        </foreach>
        , location_full_name =
        <foreach collection="tblLocations" item="item" index="index"
                 separator=" " open="case location_id" close="end">
            when #{item.locationId} then #{item.locationFullName}
        </foreach>
        where location_id in
        <foreach collection="tblLocations" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.locationId}
        </foreach>
    </update>

    <update id="updateLocationStatusNormal" parameterType="Long">
        update tbl_location set status = '0' where location_id in
        <foreach collection="array" item="locationId" open="(" separator="," close=")">
            #{locationId}
        </foreach>
    </update>

    <insert id="insertTblLocation" parameterType="TblLocation" useGeneratedKeys="true" keyProperty="locationId">
        insert into tbl_location
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="locationName != null">location_name,</if>
            <if test="locationFullName != null">location_full_name,</if>
            <if test="locationType != null">location_type,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="locationName != null">#{locationName},</if>
            <if test="locationFullName != null">#{locationFullName},</if>
            <if test="locationType != null">#{locationType},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
        </trim>
    </insert>

    <update id="updateTblLocation" parameterType="TblLocation">
        update tbl_location
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="locationName != null">location_name = #{locationName},</if>
            <if test="locationFullName != null">location_full_name = #{locationFullName},</if>
            <if test="locationType != null">location_type = #{locationType},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where location_id = #{locationId}
    </update>

    <delete id="deleteTblLocationByLocationId" parameterType="Long">
        delete from tbl_location where location_id = #{locationId}
    </delete>

    <delete id="deleteTblLocationByLocationIds" parameterType="String">
        delete from tbl_location where location_id in
        <foreach item="locationId" collection="array" open="(" separator="," close=")">
            #{locationId}
        </foreach>
    </delete>
</mapper>
