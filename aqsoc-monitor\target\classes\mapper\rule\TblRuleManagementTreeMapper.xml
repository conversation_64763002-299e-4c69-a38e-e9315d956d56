<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rule.mapper.TblRuleManagementTreeMapper">
    
    <resultMap type="TblRuleManagementTree" id="TblRuleManagementTreeResult">
        <result property="tid"    column="tid"    />
        <result property="pid"    column="pid"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="name"    column="name"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="cilentId"    column="cilent_id"    />
    </resultMap>

    <sql id="selectTblRuleManagementTreeVo">
        select tid, pid, ancestors, name, create_time, create_by, update_time, update_by, remark, user_id, dept_id, cilent_id from tbl_rule_management_tree
    </sql>

    <select id="selectTblRuleManagementTreeList" parameterType="TblRuleManagementTree" resultMap="TblRuleManagementTreeResult">
        <include refid="selectTblRuleManagementTreeVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
        </where>
    </select>
    
    <select id="selectTblRuleManagementTreeByTid" parameterType="Long" resultMap="TblRuleManagementTreeResult">
        <include refid="selectTblRuleManagementTreeVo"/>
        where tid = #{tid}
    </select>
    <select id="selectTblRuleManagementsChildren" resultMap="TblRuleManagementTreeResult" parameterType="TblRuleManagementTree">
        <include refid="selectTblRuleManagementTreeVo"/>
        where  find_in_set(#{tid},ancestors)
    </select>
        
    <insert id="insertTblRuleManagementTree" parameterType="TblRuleManagementTree" useGeneratedKeys="true" keyProperty="tid">
        insert into tbl_rule_management_tree
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pid != null">pid,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="cilentId != null">cilent_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pid != null">#{pid},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="cilentId != null">#{cilentId},</if>
         </trim>
    </insert>

    <update id="updateTblRuleManagementTree" parameterType="TblRuleManagementTree">
        update tbl_rule_management_tree
        <trim prefix="SET" suffixOverrides=",">
            <if test="pid != null">pid = #{pid},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="cilentId != null">cilent_id = #{cilentId},</if>
        </trim>
        where tid = #{tid}
    </update>

    <delete id="deleteTblRuleManagementTreeByTid" parameterType="Long">
        delete from tbl_rule_management_tree where tid = #{tid}
    </delete>

    <delete id="deleteTblRuleManagementTreeByTids" parameterType="String">
        delete from tbl_rule_management_tree where tid in 
        <foreach item="tid" collection="array" open="(" separator="," close=")">
            #{tid}
        </foreach>
    </delete>
</mapper>