<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblAccessLogMapper">
    
    <resultMap type="TblAccessLog" id="TblAccessLogResult">
        <result property="id"    column="id"    />
        <result property="eid"    column="eid"    />
        <result property="empName"    column="emp_name"    />
        <result property="dwid"    column="dwid"    />
        <result property="dwName"    column="dw_name"    />
        <result property="sysname"    column="sysname"    />
        <result property="url"    column="url"    />
        <result property="optime"    column="optime"    />
        <result property="method"    column="method"    />
        <result property="requestMethod"    column="request_method"    />
        <result property="ip"    column="ip"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="errorMsg"    column="error_msg"    />
    </resultMap>

    <sql id="selectTblAccessLogVo">
        select a.id, a.eid, a.sysname, a.url, a.optime, a.method, a.request_method, a.ip, a.content, a.status,
               a.error_msg,b.name as emp_name,b.dwid,c.name as dw_name
        from tbl_access_log a
         left join tbl_company_emp b on a.eid=b.eid
         left join tbl_company c on b.dwid=c.dwid
    </sql>

    <select id="selectTblAccessLogList" parameterType="TblAccessLog" resultMap="TblAccessLogResult">
        <include refid="selectTblAccessLogVo"/>
        <where>
            <if test="dwid != null "> and c.dwid = #{dwid}</if>
            <if test="eid != null "> and a.eid = #{eid}</if>
            <if test="empName != null "> and b.name like concat('%', #{empName}, '%')</if>
            <if test="sysname != null"> and a.sysname like concat('%', #{sysname}, '%')</if>
            <if test="url != null"> and a.url = #{url}</if>
            <if test="optime != null "> and (a.optime >= #{optime} and a.optime &lt; DATE_ADD(#{optime},interval 1 DAY))</if>
            <if test="method != null"> and a.method = #{method}</if>
            <if test="requestMethod != null"> and a.request_method = #{requestMethod}</if>
            <if test="ip != null  and ip != ''"> and a.ip = #{ip}</if>
            <if test="status != null "> and a.status = #{status}</if>
        </where>
    </select>
    
    <select id="selectTblAccessLogById" parameterType="Long" resultMap="TblAccessLogResult">
        <include refid="selectTblAccessLogVo"/>
        where a.id = #{id}
    </select>
        
    <insert id="insertTblAccessLog" parameterType="TblAccessLog">
        insert into tbl_access_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="eid != null">eid,</if>
            <if test="sysname != null">sysname,</if>
            <if test="url != null">url,</if>
            <if test="optime != null">optime,</if>
            <if test="method != null">method,</if>
            <if test="requestMethod != null">request_method,</if>
            <if test="ip != null">ip,</if>
            <if test="content != null">content,</if>
            <if test="status != null">status,</if>
            <if test="errorMsg != null">error_msg,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="eid != null">#{eid},</if>
            <if test="sysname != null">#{sysname},</if>
            <if test="url != null">#{url},</if>
            <if test="optime != null">#{optime},</if>
            <if test="method != null">#{method},</if>
            <if test="requestMethod != null">#{requestMethod},</if>
            <if test="ip != null">#{ip},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
         </trim>
    </insert>

    <update id="updateTblAccessLog" parameterType="TblAccessLog">
        update tbl_access_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="eid != null">eid = #{eid},</if>
            <if test="sysname != null">sysname = #{sysname},</if>
            <if test="url != null">url = #{url},</if>
            <if test="optime != null">optime = #{optime},</if>
            <if test="method != null">method = #{method},</if>
            <if test="requestMethod != null">request_method = #{requestMethod},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="content != null">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblAccessLogById" parameterType="Long">
        delete from tbl_access_log where id = #{id}
    </delete>

    <delete id="deleteTblAccessLogByIds" parameterType="String">
        delete from tbl_access_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>