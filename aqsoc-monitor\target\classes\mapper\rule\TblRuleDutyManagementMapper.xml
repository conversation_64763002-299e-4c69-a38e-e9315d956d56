<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rule.mapper.TblRuleDutyManagementMapper">
    
    <resultMap type="TblRuleDutyManagement" id="TblRuleDutyManagementResult">
        <result property="id"    column="id"    />
        <result property="dName"    column="d_name"    />
        <result property="dPhone"    column="d_phone"    />
        <result property="dDesc"    column="d_desc"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="cilentId"    column="cilent_id"    />
    </resultMap>

    <sql id="selectTblRuleDutyManagementVo">
        select id, d_name, d_phone, d_desc, create_time, create_by, update_time, update_by, remark, user_id, dept_id, cilent_id from tbl_rule_duty_management
    </sql>

    <select id="selectTblRuleDutyManagementList" parameterType="TblRuleDutyManagement" resultMap="TblRuleDutyManagementResult">
        <include refid="selectTblRuleDutyManagementVo"/>
        <where>  
            <if test="dName != null  and dName != ''"> and d_name like concat('%', #{dName}, '%')</if>
            <if test="dPhone != null  and dPhone != ''"> and d_phone = #{dPhone}</if>
            <if test="dDesc != null  and dDesc != ''"> and d_desc like concat('%',#{dDesc},'%')</if>
        </where>
    </select>
    
    <select id="selectTblRuleDutyManagementById" parameterType="Long" resultMap="TblRuleDutyManagementResult">
        <include refid="selectTblRuleDutyManagementVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblRuleDutyManagement" parameterType="TblRuleDutyManagement">
        insert into tbl_rule_duty_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="dName != null">d_name,</if>
            <if test="dPhone != null">d_phone,</if>
            <if test="dDesc != null">d_desc,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="cilentId != null">cilent_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="dName != null">#{dName},</if>
            <if test="dPhone != null">#{dPhone},</if>
            <if test="dDesc != null">#{dDesc},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="cilentId != null">#{cilentId},</if>
         </trim>
    </insert>

    <update id="updateTblRuleDutyManagement" parameterType="TblRuleDutyManagement">
        update tbl_rule_duty_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="dName != null">d_name = #{dName},</if>
            <if test="dPhone != null">d_phone = #{dPhone},</if>
            <if test="dDesc != null">d_desc = #{dDesc},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="cilentId != null">cilent_id = #{cilentId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblRuleDutyManagementById" parameterType="Long">
        delete from tbl_rule_duty_management where id = #{id}
    </delete>

    <delete id="deleteTblRuleDutyManagementByIds" parameterType="String">
        delete from tbl_rule_duty_management where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>