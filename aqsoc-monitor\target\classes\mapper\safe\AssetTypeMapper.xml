<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.AssetTypeMapper">
    <resultMap id="AssetTypeMap" type="com.ruoyi.safe.domain.AssetClass">
        <id column="id" property="id"></id>
        <result column="type_name"  property="typeName"></result>
        <result column="pid"  property="pid"></result>
        <result column="type_code"  property="typeCode"></result>
        <result column="type_desc"  property="typeDesc"></result>
        <result column="sequence"  property="sequence"></result>
        <result column="type_graded_protection"  property="typeGradedProtection"></result>
    </resultMap>

    <sql id="assetTypeVo">
        select id,type_name,pid,type_desc,sequence,type_graded_protection,type_code
        from tbl_asset_class
    </sql>
    <select id="selectedAssetTypeChildrenByIdAPid" parameterType="Long" resultMap="AssetTypeMap">
        <include refid="assetTypeVo"></include>
        where id = #{id} or pid = #{id}
    </select>
    <select id="selectAssetTypeList" resultMap="AssetTypeMap">
        <include refid="assetTypeVo"></include>
        order by type_graded_protection , id
    </select>

    <select id="selectAssetClassList" resultMap="AssetTypeMap">
        <include refid="assetTypeVo"></include>
        where pid=0 order by id
    </select>

    <select id="selectAssetTypeById" parameterType="Long" resultMap="AssetTypeMap">
        <include refid="assetTypeVo"></include>
        where id = #{id}
    </select>
    <insert id="insertAssetType" parameterType="AssetClass">
        insert into tbl_asset_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">type_name,</if>
            <if test="pid != null">pid,</if>
            <if test="typeCode != null and typeCode != ''">type_code,</if>
            <if test="typeDesc != null and typeDesc != ''">type_desc,</if>
            <if test="sequence != null and sequence != ''">sequence,</if>
            <if test="typeGradedProtection != null and typeGradedProtection != ''">type_graded_protection,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">#{typeName},</if>
            <if test="pid != null">#{pid},</if>
            <if test="typeCode !=null and typeCode != ''">#{typeDesc},</if>
            <if test="typeDesc !=null and typeDesc != ''">#{typeDesc},</if>
            <if test="sequence != null and sequence != ''">#{sequence},</if>
            <if test="typeGradedProtection != null and typeGradedProtection != ''">#{typeGradedProtection},</if>

        </trim>
    </insert>
    <update id="updataAssetType" parameterType="AssetClass">
        update tbl_asset_class
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeName!=null and typeName!=''">type_name = #{typeName},</if>
            <if test="pid!=null ">pid = #{pid},</if>
            <if test="typeCode!=null and typeCode!=''">type_code = #{typeCode},</if>
            <if test="typeDesc!=null and typeDesc!=''">type_desc = #{typeDesc},</if>
            <if test="sequence!=null ">sequence = #{sequence},</if>
            <if test="typeGradedProtection!=null ">type_graded_protection = #{typeGradedProtection},</if>
        </trim>
        where id = #{id}
    </update>
    <delete id="deleteAssetType" parameterType="Long">
        delete from tbl_asset_class where id = #{id}
    </delete>
</mapper>