<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.work.mapper.TblWorkOrderCommonWordsMapper">

    <resultMap type="TblWorkOrderCommonWords" id="TblWorkOrderCommonWordsResult">
        <result property="id"    column="id"    />
        <result property="content"    column="content"    />
        <result property="userId"    column="user_id"    />
        <result property="num"    column="num"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectTblWorkOrderCommonWordsVo">
        select id, content, user_id, num, type, update_time from tbl_work_order_common_words
    </sql>

    <select id="selectTblWorkOrderCommonWordsList" parameterType="TblWorkOrderCommonWords" resultMap="TblWorkOrderCommonWordsResult">
        <include refid="selectTblWorkOrderCommonWordsVo"/>
        <where>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="num != null "> and num = #{num}</if>
            <if test="type != null "> and type = #{type}</if>
        </where>
        order by update_time desc
        limit 5
    </select>

    <select id="selectTblWorkOrderCommonWordsById" parameterType="Long" resultMap="TblWorkOrderCommonWordsResult">
        <include refid="selectTblWorkOrderCommonWordsVo"/>
        where id = #{id}
    </select>

    <select id="selectTblWorkOrderCommonWordsByIds" parameterType="Long" resultMap="TblWorkOrderCommonWordsResult">
        <include refid="selectTblWorkOrderCommonWordsVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblWorkOrderCommonWords" parameterType="TblWorkOrderCommonWords">
        insert into tbl_work_order_common_words
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="userId != null and userId != ''">`user_id`,</if>
            <if test="num != null">num,</if>
            <if test="type != null">type,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="num != null">#{num},</if>
            <if test="type != null">#{type},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblWorkOrderCommonWords" parameterType="TblWorkOrderCommonWords">
        update tbl_work_order_common_words
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="num != null">num = #{num},</if>
            <if test="type != null">type = #{type},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblWorkOrderCommonWordsById" parameterType="Long">
        delete from tbl_work_order_common_words where id = #{id}
    </delete>

    <delete id="deleteTblWorkOrderCommonWordsByIds" parameterType="String">
        delete from tbl_work_order_common_words where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
