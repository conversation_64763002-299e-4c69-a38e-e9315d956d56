<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.work.mapper.WorkHwMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.work.model.WorkHwListVO">
        select a.* from work_hw a
        <where>
                <if test="query.id!=null">
                    and a.id = #{query.id}
                </if>
                <if test="query.templateId!=null">
                    and a.template_id = #{query.templateId}
                </if>
                <if test="query.name!=null and query.name!=''">
                    and a.name like concat('%', #{query.name}, '%')
                </if>
                <if test="query.year!=null and query.year!=''">
                    and a.year like concat('%', #{query.year}, '%')
                </if>
                <if test="query.userId!=null">
                    and a.user_id = #{query.userId}
                </if>
                <if test="query.hwStart!=null">
                    and a.hw_start = #{query.hwStart}
                </if>
                <if test="query.hwEnd!=null">
                    and a.hw_end = #{query.hwEnd}
                </if>
                <if test="query.supportOrgs!=null and query.supportOrgs!=''">
                    and a.support_orgs like concat('%', #{query.supportOrgs}, '%')
                </if>
                <if test="query.supportUsers!=null and query.supportUsers!=''">
                    and a.support_users like concat('%', #{query.supportUsers}, '%')
                </if>
                <if test="query.dataJson!=null and query.dataJson!=''">
                    and a.data_json like concat('%', #{query.dataJson}, '%')
                </if>
        </where>
        order by a.create_time desc
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.work.model.WorkHwListVO">
        select a.* from work_hw a
        where a.id=#{id}
    </select>
</mapper>
