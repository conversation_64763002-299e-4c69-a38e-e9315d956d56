<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.audit.mapper.TblAssetAuditLogsMapper">
    
    <resultMap type="TblAssetAuditLogs" id="TblAssetAuditLogsResult">
        <result property="id"    column="id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="logStartTime"    column="log_start_time"    />
        <result property="logEndTime"    column="log_end_time"    />
        <result property="logTitle"    column="log_title"    />
        <result property="logPath"    column="log_path"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <sql id="selectTblAssetAuditLogsVo">
        select id, asset_id, log_start_time, log_end_time, log_title, log_path, remark, create_time, create_by, update_time, update_by, user_id, dept_id from tbl_asset_audit_logs
    </sql>

    <select id="selectTblAssetAuditLogsList" parameterType="TblAssetAuditLogs" resultMap="TblAssetAuditLogsResult">
        <include refid="selectTblAssetAuditLogsVo"/>
        <where>  
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="logStartTime != null "> and log_start_time = #{logStartTime}</if>
            <if test="logEndTime != null "> and log_end_time = #{logEndTime}</if>
            <if test="logTitle != null  and logTitle != ''"> and log_title = #{logTitle}</if>
            <if test="logPath != null  and logPath != ''"> and log_path = #{logPath}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
        </where>
    </select>
    
    <select id="selectTblAssetAuditLogsById" parameterType="Long" resultMap="TblAssetAuditLogsResult">
        <include refid="selectTblAssetAuditLogsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblAssetAuditLogs" parameterType="TblAssetAuditLogs">
        insert into tbl_asset_audit_logs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="logStartTime != null">log_start_time,</if>
            <if test="logEndTime != null">log_end_time,</if>
            <if test="logTitle != null">log_title,</if>
            <if test="logPath != null">log_path,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="logStartTime != null">#{logStartTime},</if>
            <if test="logEndTime != null">#{logEndTime},</if>
            <if test="logTitle != null">#{logTitle},</if>
            <if test="logPath != null">#{logPath},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>

    <update id="updateTblAssetAuditLogs" parameterType="TblAssetAuditLogs">
        update tbl_asset_audit_logs
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="logStartTime != null">log_start_time = #{logStartTime},</if>
            <if test="logEndTime != null">log_end_time = #{logEndTime},</if>
            <if test="logTitle != null">log_title = #{logTitle},</if>
            <if test="logPath != null">log_path = #{logPath},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblAssetAuditLogsById" parameterType="Long">
        delete from tbl_asset_audit_logs where id = #{id}
    </delete>

    <delete id="deleteTblAssetAuditLogsByIds" parameterType="String">
        delete from tbl_asset_audit_logs where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>