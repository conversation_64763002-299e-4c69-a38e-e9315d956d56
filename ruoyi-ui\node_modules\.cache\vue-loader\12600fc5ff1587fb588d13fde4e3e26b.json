{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\machroom\\index.vue?vue&type=style&index=0&id=0bcdb722&scoped=true&lang=scss", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\machroom\\index.vue", "mtime": 1756381475352}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAiQC9hc3NldHMvc3R5bGVzL2N1c3RvbUZvcm0iOwoKLnNtYWxsLXBhZGRpbmcgewogIHBhZGRpbmctbGVmdDogMDsKICBwYWRkaW5nLXJpZ2h0OiAwOwogIHdpZHRoOiAxNTBweDsKfQoKOjp2LWRlZXAgLnNtYWxsLXBhZGRpbmcgLmNlbGwgewogIG92ZXJmbG93OiB2aXNpYmxlICFpbXBvcnRhbnQ7Cn0KCi5vcGVyYXRlIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgbWFyZ2luLXRvcDogMzBweDsKfQoKOjp2LWRlZXAgLmVsLXRhYmxlIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47Cn0KCjo6di1kZWVwIC5lbC10YWJsZV9fYm9keS13cmFwcGVyIHsKICBvdmVyZmxvdy15OiBhdXRvOwogIGZsZXg6IDE7Cn0KCjo6di1kZWVwIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19faXRlbS5pcy1hY3RpdmUgewogIGJhY2tncm91bmQ6IHJnYmEoNDgsIDExMSwgMjI5LCAwLjgpOwogIGNvbG9yOiAjRkZGRkZGOwp9Cgo6OnYtZGVlcCAuZWwtdGFic19fY29udGVudCB7CiAgb3ZlcmZsb3cteDogaGlkZGVuOwp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw0BA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/safe/machroom", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-tree-container\" v-if=\"children&&children.length\">\n      <type-tree :tree-date=\"typeTreeData\" @nodeChange=\"nodeChange\"></type-tree>\n    </div>\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form :model=\"queryParams\" ref=\"queryForm\" label-position=\"right\" :inline=\"true\" label-width=\"70px\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"资产编码\" prop=\"assetCode\">\n                <el-input\n                  v-model=\"queryParams.assetCode\"\n                  placeholder=\"请输入资产编码\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"机房名称\" prop=\"assetName\">\n                <el-input\n                  v-model=\"queryParams.assetName\"\n                  placeholder=\"请输入机房名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"重要程度\" prop=\"degreeImportance\">\n                <el-select v-model=\"queryParams.degreeImportance\" placeholder=\"请选择重要程度\" clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.impt_grade\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-if=\"showAll\" :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属部门\" prop=\"deptId\">\n                <dept-select v-model=\"queryParams.deptId\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">物理机房列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleAdd\"\n                  v-hasPermi=\"['safe:machroom:add']\"\n                >新增\n                </el-button>\n              </el-col>\n              <!--              <el-col :span=\"1.5\">-->\n              <!--                <el-button-->\n              <!--                  plain-->\n              <!--                  size=\"small\"-->\n              <!--                  :disabled=\"single\"-->\n              <!--                  @click=\"handleUpdate\"-->\n              <!--                  v-hasPermi=\"['safe:machroom:edit']\"-->\n              <!--                >修改-->\n              <!--                </el-button>-->\n              <!--              </el-col>-->\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"handleDelete\"\n                  v-hasPermi=\"['safe:machroom:remove']\"\n                >批量删除\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                  v-hasPermi=\"['safe:machroom:export']\"\n                >导出\n                </el-button>\n              </el-col>\n              <!--              <right-toolbar :showSearch.sync=\"showSearch\" :columns=\"columns\" @queryTable=\"getList\"></right-toolbar>-->\n            </el-row>\n          </div>\n        </div>\n        <el-table v-loading=\"loading\" :data=\"machroomList\" @selection-change=\"handleSelectionChange\"\n                  @sort-change=\"sortChange\">\n          <el-table-column type=\"selection\" width=\"55\"/>\n          <el-table-column label=\"资产编码\" align=\"left\" prop=\"assetCode\" v-if=\"columns[0].visible\"\n                           show-overflow-tooltip/>\n          <el-table-column label=\"机房名称\" align=\"left\" prop=\"assetName\" v-if=\"columns[1].visible\"\n                           show-overflow-tooltip/>\n          <el-table-column label=\"所属部门\" prop=\"deptName\" v-if=\"columns[2].visible\" show-overflow-tooltip/>\n          <el-table-column label=\"重要程度\" prop=\"degreeImportance\" v-if=\"columns[3].visible\">\n            <template slot-scope=\"scope\">\n              <dict-tag :options=\"dict.type.impt_grade\" :value=\"scope.row.degreeImportance\"/>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"物理位置\" prop=\"locationFullName\" v-if=\"columns[4].visible\" show-overflow-tooltip/>\n          <el-table-column label=\"详细地址\" prop=\"locationDetail\" v-if=\"columns[5].visible\" show-overflow-tooltip/>\n          <!--<el-table-column label=\"责任人\"  prop=\"mangerName\" v-if=\"columns[4].visible\" />-->\n          <!--<el-table-column label=\"联系电话\"  prop=\"phone\" v-if=\"columns[5].visible\" />-->\n          <el-table-column label=\"供应商\" prop=\"vendorName\" v-if=\"columns[6].visible\" show-overflow-tooltip/>\n          <el-table-column label=\"备注\" prop=\"remark\" v-if=\"columns[7].visible\" show-overflow-tooltip/>\n          <el-table-column label=\"操作\" width=\"200\" fixed=\"right\" :show-overflow-tooltip=\"false\"\n                           class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleUpdate(scope.row)\"\n                v-hasPermi=\"['safe:machroom:edit']\"\n              >编辑\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleUpdate(scope.row,false)\"\n                v-hasPermi=\"['safe:machroom:list']\"\n              >查看\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                class=\"table-delBtn\"\n                @click=\"handleDelete(scope.row)\"\n                v-hasPermi=\"['safe:machroom:remove']\"\n              >删除\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleChart(scope.row)\"\n              >详情\n              </el-button>\n              <!--              <option-lab>\n                              <template>\n                                <el-button\n                                  size=\"mini\"\n                                  type=\"text\"\n                                  @click=\"handleUpdate(scope.row,false)\"\n                                  v-hasPermi=\"['safe:machroom:list']\"\n                                >查看\n                                </el-button>\n                                <el-button\n                                  size=\"mini\"\n                                  type=\"text\"\n                                  @click=\"handleUpdate(scope.row)\"\n                                  v-hasPermi=\"['safe:machroom:edit']\"\n                                >编辑\n                                </el-button>\n                                <el-button\n                                  size=\"mini\"\n                                  type=\"text\"\n                                  @click=\"handleDelete(scope.row)\"\n                                  v-hasPermi=\"['safe:machroom:remove']\"\n                                >删除\n                                </el-button>\n                                <el-button\n                                  size=\"mini\"\n                                  type=\"text\"\n                                  @click=\"handleChart(scope.row)\"\n                                >详情\n                                </el-button>\n                              </template>\n                            </option-lab>-->\n\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <!-- 添加或修改机房管理对话框! -->\n    <el-dialog v-if=\"open\" title=\"机房管理\" :visible.sync=\"open\" width=\"900px\" append-to-body>\n      <el-tabs type=\"card\" v-model=\"editItem\">\n        <el-tab-pane :lazy=\"true\" :label=\"title\" name=\"edit\">\n          <el-row v-if=\"title != '查看机房'\" :gutter=\"10\">\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"资产编码\" prop=\"assetCode\">\n                  <el-input :disabled=\"!editable\" v-model=\"form.assetCode\" placeholder=\"请输入资产编码\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"机房名称\" prop=\"assetName\">\n                  <el-input :disabled=\"!editable\" v-model=\"form.assetName\" placeholder=\"请输入机房名称\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"重要程度\" prop=\"degreeImportance\">\n                  <el-select :disabled=\"!editable\" v-model=\"form.degreeImportance\" placeholder=\"请选择重要程度\"\n                             clearable>\n                    <el-option\n                      v-for=\"dict in dict.type.impt_grade\"\n                      :key=\"dict.value\"\n                      :label=\"dict.label\"\n                      :value=\"dict.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"物理位置\" prop=\"locationId\">\n                  <!--<el-input v-model=\"form.location\" placeholder=\"请输入物理位置\"/>-->\n                  <!--<location-select :disabled=\"!editable\" v-model=\"form.locationId\"/>-->\n                  <el-select :disabled=\"!editable\" v-model=\"form.locationId\" placeholder=\"请选择所属位置\">\n                    <el-option\n                      v-for=\"dict in locationOptions\"\n                      :key=\"dict.locationId\"\n                      :label=\"dict.locationFullName\"\n                      :value=\"dict.locationId\"\n                    ></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"24\">\n                <el-form-item label=\"详细地址\" prop=\"locationDetail\">\n                  <el-input :disabled=\"!editable\" v-model=\"form.locationDetail\" placeholder=\"请输入详细地址\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"所属部门\" prop=\"deptId\">\n                  <dept-select v-model=\"form.deptId\" :is-disabled=\"!editable\" is-current/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item v-if=\"children&&children.length\" label=\"资产类型\" prop=\"assetClass\">\n                  <el-select :disabled=\"!editable\" v-model=\"form.assetType\" placeholder=\"请选择资产类型\"\n                             @change=\"assetTypeChange\">\n                    <el-option\n                      v-for=\"children in children\"\n                      :key=\"children.id\"\n                      :label=\"children.typeName\"\n                      :value=\"children.id\"\n                    ></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"供应商\" prop=\"vendor\">\n                  <el-input :disabled=\"!editable\" :value=\"form.vendorName\" @focus=\"showVendorDialog\"\n                            placeholder=\"请输入供应商\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"24\">\n                <el-form-item label=\"资产标签\" prop=\"tags\">\n                  <dynamic-tag :disabled=\"!editable\" v-model=\"form.tags\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"remark\">\n                  <el-input :disabled=\"!editable\" v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"/>\n                </el-form-item>\n              </el-col>\n            </el-form>\n          </el-row>\n          <div v-else class=\"customForm-container\">\n            <el-descriptions\n              class=\"custom-column\"\n              direction=\"vertical\"\n              size=\"medium\"\n              :colon=\"false\"\n              label-class-name=\"custom-label-style\"\n              content-class-name=\"custom-content-style\"\n              :column=\"2\">\n              <el-descriptions-item label=\"资产编号\">\n                {{ form.assetCode }}\n              </el-descriptions-item>\n              <el-descriptions-item label=\"机房名称\">\n                {{ form.assetName }}\n              </el-descriptions-item>\n              <el-descriptions-item label=\"重要程度\">\n                <span\n                  v-for=\"(item, index) in dict.type.impt_grade\"\n                  :key=\"index\"\n                  v-if=\"item.value === form.degreeImportance\"\n                >{{ item.label }}\n          </span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"物理位置\">\n                {{ form.locationFullName }}\n                <span\n                  v-for=\"(item, index) in locationOptions\"\n                  :key=\"index\"\n                  v-if=\"item.locationId === form.locationId\"\n                >{{ item.locationFullName }}\n          </span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"详细地址\">\n                {{ form.locationDetail }}\n              </el-descriptions-item>\n              <el-descriptions-item label=\"所属部门\">\n                <span\n                  v-for=\"(item, index) in deptOptions\"\n                  :key=\"item.id\"\n                  v-if=\"item.id === form.deptId\"\n                >{{ item.label }}\n          </span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"资产类型\">\n                <span\n                  v-for=\"(item, index) in children\"\n                  :key=\"item.id\"\n                  v-if=\"item.id === form.assetType\"\n                >{{ item.typeName }}\n          </span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"供应商\">\n                {{ form.vendorName }}\n              </el-descriptions-item>\n              <el-descriptions-item label=\"资产标签\">\n                <span\n                  v-for=\"(item, index) in form.tags\"\n                  :key=\"index\"\n                >{{ item }}\n          </span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"备注\">\n                {{ form.remark }}\n              </el-descriptions-item>\n            </el-descriptions>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane v-if=\"form.assetId\" :lazy=\"true\" v-hasPermi=\"['safe:management:list']\" label=\"管理信息\"\n                     name=\"manager\">\n          <manager :disabled=\"!editable\" :assetId=\"form.assetId\" @closeManagement=\"closeManagement\"></manager>\n        </el-tab-pane>\n        <!--<el-tab-pane v-if=\"form.assetId\" :lazy=\"true\" v-hasPermi=\"['safe:uploadfile:list']\" label=\"资产图片\" name=\"file\">-->\n        <!--  <upload-file-table :disabled=\"!editable\" :asset-id=\"form.assetId\"/>-->\n        <!--</el-tab-pane>-->\n        <el-tab-pane v-if=\"form.assetId\" :lazy=\"true\" label=\"相关文件\" name=\"file\">\n          <asset-file :disabled=\"!editable\" :asset-id=\"form.assetId\"/>\n        </el-tab-pane>\n        <el-tab-pane v-if=\"form.assetId\" :lazy=\"true\" label=\"财务信息\" name=\"procurement\">\n          <procurement :disabled=\"!editable\" :asset-id=\"form.assetId\"></procurement>\n        </el-tab-pane>\n        <el-tab-pane v-if=\"form.assetId\" :lazy=\"true\" label=\"驻场信息\" name=\"companyEmp\">\n          <company-emp :disabled=\"!editable\" :asset-id=\"form.assetId\"></company-emp>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button v-if=\"editable && editItem === 'edit'\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <!--    <el-dialog title=\"选择用户\" :visible.sync=\"userDialog\" width=\"900px\" append-to-body>-->\n    <!--      <user-select @confirm=\"selectConfirm\" @cancel=\"userCancel\"></user-select>-->\n    <!--    </el-dialog>-->\n    <el-dialog title=\"选择供应商\" :visible.sync=\"vendorDialog\" width=\"900px\" append-to-body>\n      <vendor-select v-if=\"vendorDialog\" @confirm=\"selectConfirm\" @cancel=\"vendorCancel\"></vendor-select>\n    </el-dialog>\n    <!--    <el-dialog title=\"责任人列表\" width=\"900px\" :visible.sync=\"managementDialog\" append-to-body>-->\n    <!--      <manager :assetId=\"assetId\" @closeManagement=\"closeManagement\"></manager>-->\n    <!--    </el-dialog>-->\n    <!--    <el-dialog title=\"资产文件\" width=\"900px\" :visible.sync=\"fileDialog\" append-to-body>-->\n    <!--      <upload-file-table :asset-id=\"currentAssetId\"/>-->\n    <!--      <div slot=\"footer\" class=\"dialog-footer\">-->\n    <!--        <el-button type=\"primary\" @click=\"fileDialog=false;\">关 闭</el-button>-->\n    <!--      </div>-->\n    <!--    </el-dialog>-->\n\n    <networkDeviceDetail\n      :asset-id=\"assetId\"\n      asset-name=\"物理机房详情\"\n      asset-allocation-type=\"5\"\n      :device-detail-visible.sync=\"deviceDetailVisible\"/>\n  </div>\n</template>\n\n<script>\nimport {listMachroom, getMachroom, delMachroom, addMachroom, updateMachroom} from \"@/api/safe/machroom\";\nimport {listDept, listDeptExcludeChild} from \"@/api/system/dept\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport {getAllDeptTree, listUser} from \"@/api/system/user\";\nimport {getAssetTypeChildrenByid} from \"@/api/safe/overview\";\nimport userSelect from \"@/views/components/select/userSelect\";\nimport typeTree from \"@/views/components/typeTree\";\nimport vendorSelect from \"@/views/components/select/vendorSelect\";\nimport manager from \"@/views/components/manager\";\nimport LocationSelect from \"@/views/components/select/locationSelect\";\nimport DeptSelect from \"@/views/components/select/deptSelect\";\nimport {listLocation} from \"@/api/dict/location\";\nimport uploadFileTable from \"@/views/components/table/uploadFileTable\";\nimport procurement from \"@/views/safe/procurement\";\nimport companyEmp from \"@/views/monitor2/companyEmp\";\nimport OptionLab from \"@/views/components/optionLab\";\nimport AssetFile from \"@/views/dimension/file\";\nimport DynamicTag from '@/components/DynamicTag/index.vue';\nimport NetworkDeviceDetail from \"@/views/safe/networkdevices/networkDeviceDetail.vue\";\nimport {deptTreeSelect} from \"@/api/system/role\";\n\nexport default {\n  name: \"Machroom\",\n  components: {\n    NetworkDeviceDetail,\n    DynamicTag,\n    Treeselect,\n    userSelect,\n    typeTree,\n    vendorSelect,\n    manager,\n    LocationSelect,\n    DeptSelect,\n    uploadFileTable,\n    procurement,\n    companyEmp,\n    OptionLab,\n    AssetFile\n  },\n  dicts: [\"impt_grade\"],\n  data() {\n    return {\n      showAll: false,\n      classId: 1,\n      className: '物理机房',\n      typeClass: {},\n      //分类树\n      typeTreeData: [],\n      typelist: [],\n      children: [],\n      // 表格树数据\n      deptList: [],\n      // 部门树选项\n      deptOptions: [],\n      // 位置信息树选项\n      locationOptions: [],\n      //用户选择\n      userDialog: false,\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      currentNames: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 机房管理表格数据\n      machroomList: [],\n      assetId: '',\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 产商选择\n      vendorDialog: false,\n      //责任人\n      managementDialog: false,\n      //文件\n      fileDialog: false,\n      //用户\n      userList: [],\n      userqueryParams: {\n        isAsc: undefined,\n        orderByColumn: undefined,\n        pageNum: 1,\n        pageSize: 30,\n      },\n      // 查询参数\n      queryParams: {\n        isAsc: undefined,\n        orderByColumn: undefined,\n        pageNum: 1,\n        pageSize: 10,\n        assetCode: null,\n        assetName: null,\n        degreeImportance: null,\n        assetClass: null,\n        location: null,\n        userId: null,\n        deptId: null,\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        assetName: [\n          {required: true, message: \"机房名称不能为空\", trigger: \"blur\"},\n          {min: 0, max: 64, message: '机房名称不能超过 64 个字符', trigger: 'blur'},\n        ],\n        locationDetail: [\n          {min: 0, max: 128, message: '详细地址不能超过 128 个字符', trigger: 'blur'},\n        ],\n      },\n      columns: [\n        {key: 0, label: '资产编码', visible: true},\n        {key: 1, label: '机房名称', visible: true},\n        {key: 2, label: '所属部门', visible: true},\n        {key: 3, label: '重要程度', visible: true},\n        {key: 4, label: '物理位置', visible: true},\n        {key: 5, label: '详细地址', visible: true},\n        // {key: 4, label: '责任人', visible: false},\n        // {key: 5, label: '联系电话', visible: false},\n        {key: 6, label: '供应商', visible: true},\n        {key: 7, label: '备注', visible: false},\n\n      ],\n      currentAssetId: null,\n      editItem: 'edit',\n      editable: true,\n      deviceDetailVisible: false\n    };\n  },\n  mounted() {\n    this.init();\n    this.getDeptTree();\n    this.getLocationTreeselect();\n  },\n  watch: {\n    '$route'(route) {\n      if (route.name === \"Machroom\") {\n        this.init();\n      }\n    }\n  },\n  methods: {\n    init() {\n      getAssetTypeChildrenByid(this.classId).then(res => {\n        this.typelist = res.rows;\n        let item = this.typelist.find(item => {\n          return item.typeName == this.className\n        })\n        if (item) {\n          this.typeClass.assetClass = item.id;\n          this.typeClass.assetClassDesc = item.typeName;\n        }\n        for (let i = 0; i < this.typelist.length; i++) {\n          if (this.typelist[i].typeGradedProtection == 1) {\n            this.typelist.splice(Number(i), 1);\n            i = i - 1;\n          }\n        }\n        this.typeTreeData = this.handleTree(this.typelist, 'id', 'pid');\n        this.children = this.typeTreeData[0].children;\n      })\n      this.queryParams = {...this.queryParams, ...this.$route.params};\n      // console.log(this.queryParams)\n      this.getList();\n    },\n    /** 查询所属部门 */\n    getDeptTree() {\n      getAllDeptTree().then(response => {\n        this.deptOptions = response.data;\n      });\n    },\n    /** 转换部门数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.deptId,\n        label: node.deptName,\n        children: node.children\n      };\n    },\n    /**\n     * 树节点该变时\n     */\n    nodeChange(node) {\n      this.queryParams.assetType = null;\n      if (node.pid != 0) {\n        this.queryParams.assetType = node.id;\n      }\n      this.getList();\n    },\n    /**\n     * 资产类型变化\n     */\n    assetTypeChange(data) {\n      let item = this.children.find(item => {\n        return item.id == data;\n      })\n      if (item) {\n        this.form.assetTypeDesc = item.typeName;\n      }\n    },\n    /**\n     * 点击选择用户\n     */\n    userSelect() {\n      this.userDialog = true;\n    },\n    /**\n     * 选择供应商\n     */\n    showVendorDialog() {\n      if (this.editable)\n        this.vendorDialog = true\n    },\n    /**\n     * 选择供应商\n     */\n    selectConfirm(vendor) {\n\n      this.form.vendor = vendor.id;\n      this.form.vendorName = vendor.vendorName;\n      this.vendorDialog = false;\n    },\n    /**\n     * 取消选择\n     */\n    vendorCancel() {\n      this.vendorDialog = false;\n    },\n    /**\n     * 取消选择\n     */\n    userCancel() {\n      this.userDialog = false;\n    },\n    /**\n     * 选择责任人\n     */\n    showManagement(row) {\n      this.assetId = row.assetId;\n      this.managementDialog = true;\n    },\n    /**\n     * 关闭选择责任人\n     */\n    closeManagement() {\n      this.managementDialog = false;\n    },\n    //获取人员数据\n    getTableData() {\n    },\n    //关闭用户窗口\n    closeUserDialog() {\n      this.userDialog = false;\n    },\n    //打开用户选择窗口\n    showUserDialog(val) {\n      this.dialogName = val\n      this.userDialog = true\n    },\n    //排序\n    sortChange(column, prop, order) {\n      if (column.order != null) {\n        this.queryParams.isAsc = 'desc';\n      } else {\n        this.queryParams.isAsc = 'asc';\n      }\n      this.queryParams.orderByColumn = column.prop;\n      this.getList(this.queryParams);\n    },\n    /** 查询机房管理列表 */\n    getList() {\n      this.loading = true;\n      listMachroom(this.queryParams).then(response => {\n        this.machroomList = response.rows;\n        this.total = Number(response.total);\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        assetId: null,\n        assetCode: null,\n        assetName: null,\n        location: null,\n        degreeImportance: null,\n        manger: null,\n        phone: null,\n        vendor: null,\n        assetType: null,\n        assetTypeDesc: null,\n        assetClass: 1,\n        assetClassDesc: \"物理机房\",\n        locationId: null,\n        tags: [],\n      };\n      this.editItem = 'edit';\n      this.editable = true;\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.assetId)\n      this.currentNames = selection.map(item => item.assetName);\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.$nextTick(() => {\n        Object.assign(this.form, this.queryParams);\n        this.open = true;\n        this.title = \"添加机房\";\n      })\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row, edit = true) {\n      this.reset();\n      this.editable = edit;\n      const assetId = row.assetId || this.ids\n      getMachroom(assetId).then(response => {\n        this.form = response.data;\n        this.form.locationId = Number(response.data.locationId);\n        this.getLocationTreeselect();\n        this.open = true;\n        this.title = (edit ? \"修改\" : \"查看\") + \"机房\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.form = {...this.form, ...this.typeClass}\n      this.$refs[\"form\"].validate(valid => {\n        if (valid && this.editable) {\n          if (this.form.assetId != null) {\n            updateMachroom(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addMachroom(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const assetIds = row.assetId || this.ids;\n      let assetsName = \"\";\n      if (!row.assetId) {\n        assetsName = this.currentNames.join(\",\");\n      } else {\n        assetsName = row.assetName;\n      }\n\n      this.$modal.confirm('是否确认删除【' + assetsName + '】的数据项？').then(function () {\n        return delMachroom(assetIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {\n      });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('safe/machroom/export', {\n        ...this.queryParams\n      }, `machroom_${new Date().getTime()}.xlsx`)\n    },\n    /** 转换位置信息数据结构 */\n    normalizerLocation(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.locationId,\n        label: node.locationName,\n        children: node.children\n      };\n    },\n    /** 查询位置信息下拉树结构 */\n    getLocationTreeselect() {\n      listLocation().then(response => {\n        this.locationOptions = response.data.filter((e) => {\n          return e.locationType === \"1\"\n        });\n        // this.locationOptions = this.handleTree(response.data, \"locationId\", \"parentId\");\n      });\n    },\n    // 资产关系图\n    handleChart(row) {\n      // const assetId = row.assetId;\n      // const assetName = row.assetName;\n      // this.$tab.openPage(\"[\" + assetName + \"]资产详情\", '/safe/asset/details/index/' + assetId);\n      // this.$tab.openPage(\"[\" + assetName + \"]资产关系图\", '/safe/asset/chart/index/' + assetId);\n      this.assetId = row.assetId;\n      this.deviceDetailVisible = true;\n    },\n    //资产文件\n    handleFile(row) {\n      this.currentAssetId = row.assetId;\n      this.fileDialog = true;\n    }\n  }\n};\n</script>\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n\n.small-padding {\n  padding-left: 0;\n  padding-right: 0;\n  width: 150px;\n}\n\n::v-deep .small-padding .cell {\n  overflow: visible !important;\n}\n\n.operate {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 30px;\n}\n\n::v-deep .el-table {\n  display: flex;\n  flex-direction: column;\n}\n\n::v-deep .el-table__body-wrapper {\n  overflow-y: auto;\n  flex: 1;\n}\n\n::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\n  background: rgba(48, 111, 229, 0.8);\n  color: #FFFFFF;\n}\n\n::v-deep .el-tabs__content {\n  overflow-x: hidden;\n}\n</style>\n"]}]}