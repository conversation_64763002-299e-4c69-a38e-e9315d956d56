{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\userSelect.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\userSelect.vue", "mtime": 1756369456052}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_user", "require", "_deptSelect", "_interopRequireDefault", "name", "dicts", "components", "DeptSelect", "props", "userdata", "type", "String", "default", "value", "required", "separator", "multiple", "Boolean", "clearable", "data", "idName", "labelName", "valueName", "moduleName", "businessName", "functionName", "selected", "options", "visible", "loading", "ids", "single", "many", "showSearch", "total", "list", "title", "open", "queryParams", "pageNum", "pageSize", "userName", "nick<PERSON><PERSON>", "phonenumber", "form", "rules", "message", "trigger", "min", "max", "deptId", "password", "email", "pattern", "initPassword", "watch", "handler", "newVal", "oldVal", "handleValue", "deep", "immediate", "mounted", "_this", "$nextTick", "methods", "Array", "isArray", "split", "length", "map", "item", "parseInt", "handleSelected", "_this2", "getUsers", "then", "response", "_this2$options", "_this2$ids", "newOptions", "userId", "uniqueOptions", "filter", "option", "some", "existingOption", "push", "apply", "_toConsumableArray2", "newIds", "getUser", "handleClear", "$emit", "handleRemoveTag", "_this3", "index", "splice", "join", "openDialog", "getList", "$refs", "elSelect", "blur", "closeDialog", "handleBeforeClose", "done", "_this4", "handleOpen", "handleClose", "handleConfirm", "_this5", "id", "user", "find", "handleCancel", "_this6", "noIds", "listUser", "rows", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "rowdata", "_iterator2", "_step2", "resdata", "table", "toggleRowSelection", "err", "e", "f", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelect", "selection", "row", "_this7", "clearSelection", "flag", "tempOption", "for<PERSON>ach", "handleSelectAll", "_this8", "optionItem", "handleSelectionChange", "_this9", "handleClick", "column", "event", "undefined", "handleAdd", "reset", "concat", "$store", "state", "handleUpdate", "_this10", "handleDelete", "_this11", "$modal", "confirm", "<PERSON><PERSON><PERSON>", "msgSuccess", "catch", "handleExport", "download", "_objectSpread2", "Date", "getTime", "submitForm", "_this12", "validate", "valid", "updateUser", "addUser", "cancel", "sex", "status", "remark", "postIds", "roleIds"], "sources": ["src/views/hhlCode/component/userSelect.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-select\n      ref=\"elSelect\"\n      v-model=\"selected\"\n      :placeholder=\"$attrs['placeholder']||'请选择'+this.functionName\"\n      :multiple=\"multiple\"\n      :clearable=\"clearable\"\n      class=\"tags-select-input\"\n      collapse-tags\n      @focus=\"openDialog\"\n      @clear=\"handleClear\"\n      @remove-tag=\"handleRemoveTag\"\n      v-bind=\"$attrs\">\n      <el-option\n        v-for=\"item in options\"\n        :key=\"item[valueName]\"\n        :label=\"item[labelName]+'-'+item.phonenumber\"\n        :value=\"item[valueName]\">\n      </el-option>\n    </el-select>\n    <el-dialog\n      :title=\"'选择'+this.functionName\"\n      :visible.sync=\"visible\"\n      :before-close=\"handleBeforeClose\"\n      @open=\"handleOpen\"\n      @close=\"handleClose\"\n      width=\"800px\"\n      top=\"5vh\"\n      append-to-body>\n      <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n        <!--<el-form-item label=\"账号\" prop=\"userName\">-->\n        <!--  <el-input-->\n        <!--    v-model=\"queryParams.userName\"-->\n        <!--    placeholder=\"请输入账号\"-->\n        <!--    clearable-->\n        <!--    @keyup.enter.native=\"handleQuery\"-->\n        <!--  />-->\n        <!--</el-form-item>-->\n        <el-form-item label=\"姓名\" prop=\"nickName\">\n          <el-input\n            v-model=\"queryParams.nickName\"\n            placeholder=\"请输入姓名\"\n            clearable\n            @keyup.enter.native=\"handleQuery\"\n          />\n        </el-form-item>\n        <el-form-item label=\"联系方式\" prop=\"phonenumber\">\n          <el-input\n            v-model=\"queryParams.phonenumber\"\n            placeholder=\"请输入联系方式\"\n            clearable\n            @keyup.enter.native=\"handleQuery\"\n          />\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <!--<el-row :gutter=\"10\" class=\"mb8\">-->\n      <!--  <el-col :span=\"1.5\">-->\n      <!--    <el-button-->\n      <!--      type=\"primary\"-->\n      <!--      plain-->\n      <!--      icon=\"el-icon-plus\"-->\n      <!--      size=\"mini\"-->\n      <!--      @click=\"handleAdd\"-->\n      <!--      v-hasPermi=\"['system:user:add']\"-->\n      <!--    >新增</el-button>-->\n      <!--  </el-col>-->\n      <!--  <el-col :span=\"1.5\">-->\n      <!--    <el-button-->\n      <!--      type=\"success\"-->\n      <!--      plain-->\n      <!--      icon=\"el-icon-edit\"-->\n      <!--      size=\"mini\"-->\n      <!--      :disabled=\"single\"-->\n      <!--      @click=\"handleUpdate\"-->\n      <!--      v-hasPermi=\"['system:user:edit']\"-->\n      <!--    >修改</el-button>-->\n      <!--  </el-col>-->\n      <!--  <el-col :span=\"1.5\">-->\n      <!--    <el-button-->\n      <!--      type=\"danger\"-->\n      <!--      plain-->\n      <!--      icon=\"el-icon-delete\"-->\n      <!--      size=\"mini\"-->\n      <!--      :disabled=\"many\"-->\n      <!--      @click=\"handleDelete\"-->\n      <!--      v-hasPermi=\"['system:user:remove']\"-->\n      <!--    >删除</el-button>-->\n      <!--  </el-col>-->\n      <!--  <el-col :span=\"1.5\">-->\n      <!--    <el-button-->\n      <!--      type=\"warning\"-->\n      <!--      plain-->\n      <!--      icon=\"el-icon-download\"-->\n      <!--      size=\"mini\"-->\n      <!--      @click=\"handleExport\"-->\n      <!--      v-hasPermi=\"['system:user:export']\"-->\n      <!--    >导出</el-button>-->\n      <!--  </el-col>-->\n      <!--  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>-->\n      <!--</el-row>-->\n\n      <el-table ref=\"table\" v-loading=\"loading\" :data=\"list\" @select=\"handleSelect\" @select-all=\"handleSelectAll\"\n                @selection-change=\"handleSelectionChange\" @row-click=\"handleClick\" height=\"260px\">\n        <el-table-column type=\"selection\" width=\"55\" ></el-table-column>\n        <el-table-column label=\"账号\" prop=\"userName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"姓名\" prop=\"nickName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"邮箱\" prop=\"email\" :show-overflow-tooltip=\"true\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.email ? scope.row.email : '--' }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"联系方式\" prop=\"phonenumber\" :show-overflow-tooltip=\"true\"/>\n        <!--<el-table-column label=\"状态\"  prop=\"status\">-->\n        <!--  <template slot-scope=\"scope\">-->\n        <!--    <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>-->\n        <!--  </template>-->\n        <!--</el-table-column>-->\n        <!--<el-table-column label=\"创建时间\"  prop=\"createTime\" width=\"180\">-->\n        <!--  <template slot-scope=\"scope\">-->\n        <!--    <span>{{ parseTime(scope.row.createTime) }}</span>-->\n        <!--  </template>-->\n        <!--</el-table-column>-->\n        <!--        <el-table-column label=\"操作\"  class-name=\"small-padding fixed-width\">-->\n        <!--          <template slot-scope=\"scope\">-->\n        <!--            <el-button-->\n        <!--              size=\"mini\"-->\n        <!--              type=\"text\"-->\n        <!--              icon=\"el-icon-edit\"-->\n        <!--              @click=\"handleUpdate(scope.row)\"-->\n        <!--              v-hasPermi=\"['system:user:edit']\"-->\n        <!--            >修改-->\n        <!--            </el-button>-->\n        <!--            <el-button-->\n        <!--              size=\"mini\"-->\n        <!--              type=\"text\"-->\n        <!--              icon=\"el-icon-delete\"-->\n        <!--              @click=\"handleDelete(scope.row)\"-->\n        <!--              v-hasPermi=\"['system:user:remove']\"-->\n        <!--            >删除-->\n        <!--            </el-button>-->\n        <!--          </template>\n                </el-table-column>-->\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n\n      <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"700px\" append-to-body>\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n          <el-row type=\"flex\" style=\"flex-wrap: wrap;\">\n            <el-col :span=\"12\">\n              <el-form-item label=\"姓名\" prop=\"nickName\">\n                <el-input v-model=\"form.nickName\" placeholder=\"请输入姓名\" maxlength=\"30\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"归属部门\" prop=\"deptId\">\n                <dept-select v-model=\"form.deptId\" is-current/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"联系方式\" prop=\"phonenumber\">\n                <el-input v-model=\"form.phonenumber\" placeholder=\"请输入联系方式\" maxlength=\"11\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"邮箱\" prop=\"email\">\n                <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"备注\">\n                <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n          <el-button @click=\"cancel\">取 消</el-button>\n        </div>\n      </el-dialog>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n        >添加{{ functionName }}\n        </el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">确定</el-button>\n        <el-button @click=\"handleCancel\">取消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {addUser, delUser, getUser, getUsers, listUser, updateUser} from \"@/api/system/user\";\nimport DeptSelect from '@/views/components/select/deptSelect';\n\nexport default {\n  name: \"UserSelect\",\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\n  components: {DeptSelect},\n  props: {\n    userdata: {type: String, default: null,},\n    // 值\n    value: {required: true},\n    // 分隔符\n    separator: {type: String, required: false, default: ','},\n    // 是否多选\n    multiple: {type: Boolean, required: false, default: false},\n    // 是否可以清空选项\n    clearable: {type: Boolean, required: false, default: true},\n  },\n  data() {\n    return {\n      // 主键字段名\n      idName: 'userId',\n      // 选项标签字段名\n      labelName: 'nickName',\n      // 选项值字段名\n      valueName: 'userId',\n      // 模块名\n      moduleName: 'system',\n      // 业务名\n      businessName: 'user',\n      // 功能名\n      functionName: '人员',\n      // 选中项\n      selected: null,\n      // 所有选中项的id和name\n      options: [],\n      // 是否显示弹出层\n      visible: false,\n      // 遮罩层\n      loading: true,\n      // 所有选中项的id\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      many: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        userName: null,\n        nickName: null,\n        phonenumber: null,\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        userName: [\n          {required: true, message: \"账号不能为空\", trigger: \"blur\"},\n          {min: 2, max: 20, message: '账号长度必须介于 2 和 20 之间', trigger: 'blur'},\n        ],\n        deptId: [\n          {required: true, message: \"部门不能为空\", trigger: \"blur\"},\n        ],\n        nickName: [\n          {required: true, message: \"姓名不能为空\", trigger: \"blur\"},\n        ],\n        password: [\n          {required: true, message: \"密码不能为空\", trigger: \"blur\"},\n          {min: 5, max: 20, message: '密码长度必须介于 5 和 20 之间', trigger: 'blur'},\n        ],\n        email: [\n          {type: \"email\", message: \"请输入正确的邮箱地址\", trigger: [\"blur\", \"change\"]},\n        ],\n        phonenumber: [\n          {required: true, message: \"联系方式不能为空\", trigger: \"blur\"},\n          {pattern: /^((0\\d{2,3}\\d{5,8})|(1[3-9]\\d{9}))$/, message: \"请输入正确的联系方式\", trigger: \"blur\"},\n        ],\n      },\n      // 默认密码\n      initPassword: null,\n    };\n  },\n  watch: {\n    value: {\n      handler(newVal, oldVal) {\n        this.handleValue(newVal);\n      },\n      deep: true,\n      immediate: true,\n    },\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.selected = this.multiple ? [] : '';\n      this.handleValue(this.value);\n    });\n  },\n  methods: {\n    // 处理值\n    handleValue(value) {\n      if (typeof value !== 'undefined' && value !== null && value !== '') {\n        if (this.multiple) {\n          let selected = Array.isArray(value) ? value : value.split(this.separator);\n          if (selected.length > 0) this.selected = selected.map(item => parseInt(item));\n        } else {\n          this.selected = parseInt(value);\n        }\n        this.handleSelected(this.selected);\n      } else {\n        this.selected = this.multiple ? [] : '';\n      }\n    },\n    // 处理选中项\n    handleSelected(value) {\n      if (typeof value !== 'undefined' || value !== null) {\n        if (this.multiple) {\n          // this.options = [];\n          if (value.length > 0) {\n            //只有在初始化时进行查询\n            if (this.options.length <= 0) {\n              getUsers(value).then(response => {\n                  // 处理数据\n                  const newOptions = response.data.map(item => ({\n                    userId: item.userId,\n                    nickName: item.nickName,\n                    phonenumber: item.phonenumber\n                  }));\n                  // 避免重复添加数据\n                  const uniqueOptions = newOptions.filter(option => !this.options.some(existingOption => existingOption.userId === option.userId));\n                  // 更新 options\n                  this.options.push(...uniqueOptions);\n                  // 更新 ids\n                  const newIds = uniqueOptions.map(option => option.userId);\n                  this.ids.push(...newIds);\n                })\n            }\n\n            // value.forEach(item => {\n            //   getUser(item).then(response => {\n            //     this.options.push(response.data);\n            //   });\n            // });\n          }\n        } else {\n          if (!this.options.some(item => item[this.idName] === value)) {\n            getUser(value).then(response => {\n              this.options = [response.data];\n            });\n          }\n        }\n      } else {\n        this.ids = [];\n        this.options = [];\n      }\n    },\n    // 处理清空事件\n    handleClear() {\n      this.ids = [];\n      this.options = [];\n      if (Array.isArray(this.value)) {\n        this.$emit('input', []);\n      } else {\n        this.$emit('input', '');\n      }\n    },\n    // 处理多选模式下移除tag事件\n    handleRemoveTag(value) {\n      //删除id\n      this.ids.some((item, index) => {\n        if (item === value) {\n          this.ids.splice(index, 1);\n        }\n      });\n      //删除option\n      this.options.some((item, index) => {\n        if (item.userId === value) this.options.splice(index, 1);\n      });\n      if (Array.isArray(this.value)) {\n        this.$emit('input', this.selected);\n      } else {\n        this.$emit('input', this.selected.join(this.separator));\n      }\n    },\n    /** 打开对话框 */\n    openDialog() {\n      this.queryParams.nickName = null;\n      this.queryParams.phonenumber = null;\n      this.queryParams.pageNum = 1;\n      this.queryParams.pageSize = 10;\n      this.getList();\n      // this.getConfigKey(\"sys.user.initPassword\").then(response => {\n      //   this.initPassword = response.msg;\n      // });\n      this.visible = true;\n      this.$refs.elSelect.blur();\n    },\n    /** 关闭对话框 */\n    closeDialog() {\n      this.visible = false;\n    },\n    /** 关闭前的回调，会暂停对话框的关闭 */\n    handleBeforeClose(done) {\n      this.$nextTick(()=>{\n        //关闭后，将选择框全部重置\n        this.ids = [];\n        this.options = [];\n        this.handleValue(this.value);\n        done();\n      })\n    },\n    /** 对话框打开的回调 */\n    handleOpen() {\n    },\n    /** 对话框关闭的回调 */\n    handleClose() {\n    },\n    /** 确认按钮 */\n    handleConfirm() {\n      if (this.multiple) {\n        if (Array.isArray(this.value)) {\n          this.$emit('input', this.ids);\n        } else {\n          this.$emit('input', this.ids.join(this.separator));\n        }\n      } else {\n        let id = this.ids[0];\n        let user = this.list.find(item => item[this.idName] === id);\n        this.options = [user];\n        this.selected = id;\n        this.$emit('setPhone', user.phonenumber);\n        this.$emit('input', id);\n      }\n      this.visible = false;\n      // }\n      // if (this.ids.length == 0){\n      //   this.$message.warning(\"请选择人员！\");\n      // }\n    },\n    /** 取消按钮 */\n    handleCancel() {\n      this.ids = [];\n      this.options = [];\n      this.handleValue(this.value);\n      this.visible = false;\n    },\n    /** 查询列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.noIds = [999999];\n      listUser(this.queryParams).then(response => {\n        this.list = response.rows;\n        this.total = response.total;\n        this.$nextTick(() => {\n          for (let rowdata of this.list) {\n            for (let resdata of this.options) {\n              // if (this.userdata==null){\n              //   this.options=[];\n              // }\n              if (rowdata.userId == resdata.userId) {\n                this.$refs.table.toggleRowSelection(rowdata, true);\n              }\n            }\n          }\n        });\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 处理选择框事件\n    handleSelect(selection, row) {\n      this.$nextTick(() => {\n        if (!this.multiple) {\n          if (selection.length > 1) {\n            this.$refs.table.clearSelection();\n            this.$refs.table.toggleRowSelection(row, true);\n          }\n        } else {\n          var flag = this.options.some(item => {\n            return item.userId == row.userId\n          })\n          if (!flag) {\n            // 回显数据里没有本条，把这条加进来(选中)\n            var tempOption = {\n              userId: row.userId,\n              nickName: row.nickName,\n              phonenumber: row.phonenumber\n            }\n            this.options.push(tempOption);\n            this.ids.push(row.userId);\n          } else {\n            // 回显数据里有本条，把这条删除(取消选中)\n            this.options.forEach((item, index) => {\n              if (item.userId == row.userId) {\n                this.options.splice(index, 1);\n                this.ids.forEach((userId, index) => {\n                  if (userId == item.userId) {\n                    this.ids.splice(index, 1);\n                  }\n                })\n              }\n            });\n          }\n        }\n      });\n    },\n    // 处理全选框事件\n    handleSelectAll(selection) {\n      if (!this.multiple) {\n        this.$refs.table.clearSelection()\n      } else {\n        //全选事件\n        if (selection.length > 0) {\n          this.list.forEach(item => {\n            //将本页未勾选的进行勾选\n            if (!this.options.some(optionItem => {\n              return item.userId === optionItem.userId\n            })) {\n              this.handleSelect(selection, item);\n            }\n          })\n          //取消全选事件\n        } else {\n          this.list.forEach(item => {\n            //将本页勾选的进行取消\n            if (this.options.some(optionItem => {\n              return item.userId === optionItem.userId\n            })) {\n              this.handleSelect(selection, item);\n            }\n          })\n        }\n      }\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item[this.idName]);\n      this.single = selection.length !== 1;\n      this.many = !selection.length;\n    },\n    // 处理行点击事件\n    handleClick(row, column, event) {\n      if (row) {\n        let rows = this.$refs.table.selection;\n        // let selected = rows.find(item => item[this.idName] === row[this.idName]);\n        // if (!this.multiple) {\n        //   this.$refs.table.clearSelection();\n        // }\n        // this.$refs.table.toggleRowSelection(row, selected ? undefined : true);\n        //勾选或者取消勾选\n        this.$refs.table.toggleRowSelection(row, this.options.some(item => {\n          return item.userId == row.userId\n        }) ? undefined : true);\n        this.handleSelect(rows, row);\n      }\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = `添加${this.functionName}信息`;\n      this.form.password = this.initPassword;\n      this.form.deptId = this.$store.state.user.deptId;\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row[this.idName] || this.ids\n      getUser(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = `修改${this.functionName}信息`;\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row[this.idName] || this.ids;\n      this.$modal.confirm(`是否确认删除${this.functionName}编号为\"${ids}\"的数据项？`).then(function () {\n        return delUser(ids);\n      }).then(() => {\n        this.$modal.msgSuccess(\"删除成功\");\n        this.getList();\n      }).catch(() => {\n      });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download(`${this.moduleName}/${this.businessName}/export`, {\n        ...this.queryParams,\n      }, `${this.functionName}_${new Date().getTime()}.xlsx`)\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form[this.idName] != null) {\n            updateUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            // 特殊需求不输入账号和密码就可以添加用户\n            this.form.userName = this.form.phonenumber;\n            addUser(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 取消按钮 */\n    cancel() {\n      this.ids = [];\n      this.options = [];\n      this.handleValue(this.value);\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        userId: undefined,\n        deptId: undefined,\n        userName: undefined,\n        nickName: undefined,\n        password: undefined,\n        phonenumber: undefined,\n        email: undefined,\n        sex: undefined,\n        status: \"0\",\n        remark: undefined,\n        postIds: [],\n        roleIds: []\n      };\n      this.resetForm(\"form\");\n    },\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.tags-select-input ::v-deep .el-select__tags {\n  white-space: nowrap;\n  overflow: hidden;\n  flex-flow: nowrap;\n  display:flex;\n  flex-wrap:nowrap;\n}\n.tags-select-input ::v-deep .el-select__tags-text {\n  display: inline-block;\n  max-width: 90px;//设置最大宽度 超出显示...\n  white-space: nowrap;\n  overflow: hidden;\n  flex-flow: nowrap;\n  vertical-align:bottom;\n  text-overflow:ellipsis;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAmNA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,KAAA;IACAC,QAAA;MAAAC,IAAA,EAAAC,MAAA;MAAAC,OAAA;IAAA;IACA;IACAC,KAAA;MAAAC,QAAA;IAAA;IACA;IACAC,SAAA;MAAAL,IAAA,EAAAC,MAAA;MAAAG,QAAA;MAAAF,OAAA;IAAA;IACA;IACAI,QAAA;MAAAN,IAAA,EAAAO,OAAA;MAAAH,QAAA;MAAAF,OAAA;IAAA;IACA;IACAM,SAAA;MAAAR,IAAA,EAAAO,OAAA;MAAAH,QAAA;MAAAF,OAAA;IAAA;EACA;EACAO,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,MAAA;MACA;MACAC,SAAA;MACA;MACAC,SAAA;MACA;MACAC,UAAA;MACA;MACAC,YAAA;MACA;MACAC,YAAA;MACA;MACAC,QAAA;MACA;MACAC,OAAA;MACA;MACAC,OAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,IAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAJ,QAAA,GACA;UAAA3B,QAAA;UAAAgC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,MAAA,GACA;UAAApC,QAAA;UAAAgC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,QAAA,GACA;UAAA5B,QAAA;UAAAgC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,QAAA,GACA;UAAArC,QAAA;UAAAgC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,KAAA,GACA;UAAA1C,IAAA;UAAAoC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,WAAA,GACA;UAAA7B,QAAA;UAAAgC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAM,OAAA;UAAAP,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAO,YAAA;IACA;EACA;EACAC,KAAA;IACA1C,KAAA;MACA2C,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,KAAAC,WAAA,CAAAF,MAAA;MACA;MACAG,IAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAArC,QAAA,GAAAqC,KAAA,CAAA/C,QAAA;MACA+C,KAAA,CAAAJ,WAAA,CAAAI,KAAA,CAAAlD,KAAA;IACA;EACA;EACAoD,OAAA;IACA;IACAN,WAAA,WAAAA,YAAA9C,KAAA;MACA,WAAAA,KAAA,oBAAAA,KAAA,aAAAA,KAAA;QACA,SAAAG,QAAA;UACA,IAAAU,QAAA,GAAAwC,KAAA,CAAAC,OAAA,CAAAtD,KAAA,IAAAA,KAAA,GAAAA,KAAA,CAAAuD,KAAA,MAAArD,SAAA;UACA,IAAAW,QAAA,CAAA2C,MAAA,WAAA3C,QAAA,GAAAA,QAAA,CAAA4C,GAAA,WAAAC,IAAA;YAAA,OAAAC,QAAA,CAAAD,IAAA;UAAA;QACA;UACA,KAAA7C,QAAA,GAAA8C,QAAA,CAAA3D,KAAA;QACA;QACA,KAAA4D,cAAA,MAAA/C,QAAA;MACA;QACA,KAAAA,QAAA,QAAAV,QAAA;MACA;IACA;IACA;IACAyD,cAAA,WAAAA,eAAA5D,KAAA;MAAA,IAAA6D,MAAA;MACA,WAAA7D,KAAA,oBAAAA,KAAA;QACA,SAAAG,QAAA;UACA;UACA,IAAAH,KAAA,CAAAwD,MAAA;YACA;YACA,SAAA1C,OAAA,CAAA0C,MAAA;cACA,IAAAM,cAAA,EAAA9D,KAAA,EAAA+D,IAAA,WAAAC,QAAA;gBAAA,IAAAC,cAAA,EAAAC,UAAA;gBACA;gBACA,IAAAC,UAAA,GAAAH,QAAA,CAAA1D,IAAA,CAAAmD,GAAA,WAAAC,IAAA;kBAAA;oBACAU,MAAA,EAAAV,IAAA,CAAAU,MAAA;oBACAvC,QAAA,EAAA6B,IAAA,CAAA7B,QAAA;oBACAC,WAAA,EAAA4B,IAAA,CAAA5B;kBACA;gBAAA;gBACA;gBACA,IAAAuC,aAAA,GAAAF,UAAA,CAAAG,MAAA,WAAAC,MAAA;kBAAA,QAAAV,MAAA,CAAA/C,OAAA,CAAA0D,IAAA,WAAAC,cAAA;oBAAA,OAAAA,cAAA,CAAAL,MAAA,KAAAG,MAAA,CAAAH,MAAA;kBAAA;gBAAA;gBACA;gBACA,CAAAH,cAAA,GAAAJ,MAAA,CAAA/C,OAAA,EAAA4D,IAAA,CAAAC,KAAA,CAAAV,cAAA,MAAAW,mBAAA,CAAA7E,OAAA,EAAAsE,aAAA;gBACA;gBACA,IAAAQ,MAAA,GAAAR,aAAA,CAAAZ,GAAA,WAAAc,MAAA;kBAAA,OAAAA,MAAA,CAAAH,MAAA;gBAAA;gBACA,CAAAF,UAAA,GAAAL,MAAA,CAAA5C,GAAA,EAAAyD,IAAA,CAAAC,KAAA,CAAAT,UAAA,MAAAU,mBAAA,CAAA7E,OAAA,EAAA8E,MAAA;cACA;YACA;;YAEA;YACA;YACA;YACA;YACA;UACA;QACA;UACA,UAAA/D,OAAA,CAAA0D,IAAA,WAAAd,IAAA;YAAA,OAAAA,IAAA,CAAAG,MAAA,CAAAtD,MAAA,MAAAP,KAAA;UAAA;YACA,IAAA8E,aAAA,EAAA9E,KAAA,EAAA+D,IAAA,WAAAC,QAAA;cACAH,MAAA,CAAA/C,OAAA,IAAAkD,QAAA,CAAA1D,IAAA;YACA;UACA;QACA;MACA;QACA,KAAAW,GAAA;QACA,KAAAH,OAAA;MACA;IACA;IACA;IACAiE,WAAA,WAAAA,YAAA;MACA,KAAA9D,GAAA;MACA,KAAAH,OAAA;MACA,IAAAuC,KAAA,CAAAC,OAAA,MAAAtD,KAAA;QACA,KAAAgF,KAAA;MACA;QACA,KAAAA,KAAA;MACA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAAjF,KAAA;MAAA,IAAAkF,MAAA;MACA;MACA,KAAAjE,GAAA,CAAAuD,IAAA,WAAAd,IAAA,EAAAyB,KAAA;QACA,IAAAzB,IAAA,KAAA1D,KAAA;UACAkF,MAAA,CAAAjE,GAAA,CAAAmE,MAAA,CAAAD,KAAA;QACA;MACA;MACA;MACA,KAAArE,OAAA,CAAA0D,IAAA,WAAAd,IAAA,EAAAyB,KAAA;QACA,IAAAzB,IAAA,CAAAU,MAAA,KAAApE,KAAA,EAAAkF,MAAA,CAAApE,OAAA,CAAAsE,MAAA,CAAAD,KAAA;MACA;MACA,IAAA9B,KAAA,CAAAC,OAAA,MAAAtD,KAAA;QACA,KAAAgF,KAAA,eAAAnE,QAAA;MACA;QACA,KAAAmE,KAAA,eAAAnE,QAAA,CAAAwE,IAAA,MAAAnF,SAAA;MACA;IACA;IACA,YACAoF,UAAA,WAAAA,WAAA;MACA,KAAA7D,WAAA,CAAAI,QAAA;MACA,KAAAJ,WAAA,CAAAK,WAAA;MACA,KAAAL,WAAA,CAAAC,OAAA;MACA,KAAAD,WAAA,CAAAE,QAAA;MACA,KAAA4D,OAAA;MACA;MACA;MACA;MACA,KAAAxE,OAAA;MACA,KAAAyE,KAAA,CAAAC,QAAA,CAAAC,IAAA;IACA;IACA,YACAC,WAAA,WAAAA,YAAA;MACA,KAAA5E,OAAA;IACA;IACA,uBACA6E,iBAAA,WAAAA,kBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAA3C,SAAA;QACA;QACA2C,MAAA,CAAA7E,GAAA;QACA6E,MAAA,CAAAhF,OAAA;QACAgF,MAAA,CAAAhD,WAAA,CAAAgD,MAAA,CAAA9F,KAAA;QACA6F,IAAA;MACA;IACA;IACA,eACAE,UAAA,WAAAA,WAAA,GACA;IACA,eACAC,WAAA,WAAAA,YAAA,GACA;IACA,WACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,SAAA/F,QAAA;QACA,IAAAkD,KAAA,CAAAC,OAAA,MAAAtD,KAAA;UACA,KAAAgF,KAAA,eAAA/D,GAAA;QACA;UACA,KAAA+D,KAAA,eAAA/D,GAAA,CAAAoE,IAAA,MAAAnF,SAAA;QACA;MACA;QACA,IAAAiG,EAAA,QAAAlF,GAAA;QACA,IAAAmF,IAAA,QAAA9E,IAAA,CAAA+E,IAAA,WAAA3C,IAAA;UAAA,OAAAA,IAAA,CAAAwC,MAAA,CAAA3F,MAAA,MAAA4F,EAAA;QAAA;QACA,KAAArF,OAAA,IAAAsF,IAAA;QACA,KAAAvF,QAAA,GAAAsF,EAAA;QACA,KAAAnB,KAAA,aAAAoB,IAAA,CAAAtE,WAAA;QACA,KAAAkD,KAAA,UAAAmB,EAAA;MACA;MACA,KAAApF,OAAA;MACA;MACA;MACA;MACA;IACA;IACA,WACAuF,YAAA,WAAAA,aAAA;MACA,KAAArF,GAAA;MACA,KAAAH,OAAA;MACA,KAAAgC,WAAA,MAAA9C,KAAA;MACA,KAAAe,OAAA;IACA;IACA,WACAwE,OAAA,WAAAA,QAAA;MAAA,IAAAgB,MAAA;MACA,KAAAvF,OAAA;MACA,KAAAS,WAAA,CAAA+E,KAAA;MACA,IAAAC,cAAA,OAAAhF,WAAA,EAAAsC,IAAA,WAAAC,QAAA;QACAuC,MAAA,CAAAjF,IAAA,GAAA0C,QAAA,CAAA0C,IAAA;QACAH,MAAA,CAAAlF,KAAA,GAAA2C,QAAA,CAAA3C,KAAA;QACAkF,MAAA,CAAApD,SAAA;UAAA,IAAAwD,SAAA,OAAAC,2BAAA,CAAA7G,OAAA,EACAwG,MAAA,CAAAjF,IAAA;YAAAuF,KAAA;UAAA;YAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAlB,IAAA;cAAA,IAAAmB,OAAA,GAAAH,KAAA,CAAA7G,KAAA;cAAA,IAAAiH,UAAA,OAAAL,2BAAA,CAAA7G,OAAA,EACAwG,MAAA,CAAAzF,OAAA;gBAAAoG,MAAA;cAAA;gBAAA,KAAAD,UAAA,CAAAH,CAAA,MAAAI,MAAA,GAAAD,UAAA,CAAAF,CAAA,IAAAlB,IAAA;kBAAA,IAAAsB,OAAA,GAAAD,MAAA,CAAAlH,KAAA;kBACA;kBACA;kBACA;kBACA,IAAAgH,OAAA,CAAA5C,MAAA,IAAA+C,OAAA,CAAA/C,MAAA;oBACAmC,MAAA,CAAAf,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAL,OAAA;kBACA;gBACA;cAAA,SAAAM,GAAA;gBAAAL,UAAA,CAAAM,CAAA,CAAAD,GAAA;cAAA;gBAAAL,UAAA,CAAAO,CAAA;cAAA;YACA;UAAA,SAAAF,GAAA;YAAAX,SAAA,CAAAY,CAAA,CAAAD,GAAA;UAAA;YAAAX,SAAA,CAAAa,CAAA;UAAA;QACA;QACAjB,MAAA,CAAAvF,OAAA;MACA;IACA;IACA,aACAyG,WAAA,WAAAA,YAAA;MACA,KAAAhG,WAAA,CAAAC,OAAA;MACA,KAAA6D,OAAA;IACA;IACA,aACAmC,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,YAAA,WAAAA,aAAAC,SAAA,EAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA5E,SAAA;QACA,KAAA4E,MAAA,CAAA5H,QAAA;UACA,IAAA0H,SAAA,CAAArE,MAAA;YACAuE,MAAA,CAAAvC,KAAA,CAAA4B,KAAA,CAAAY,cAAA;YACAD,MAAA,CAAAvC,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAS,GAAA;UACA;QACA;UACA,IAAAG,IAAA,GAAAF,MAAA,CAAAjH,OAAA,CAAA0D,IAAA,WAAAd,IAAA;YACA,OAAAA,IAAA,CAAAU,MAAA,IAAA0D,GAAA,CAAA1D,MAAA;UACA;UACA,KAAA6D,IAAA;YACA;YACA,IAAAC,UAAA;cACA9D,MAAA,EAAA0D,GAAA,CAAA1D,MAAA;cACAvC,QAAA,EAAAiG,GAAA,CAAAjG,QAAA;cACAC,WAAA,EAAAgG,GAAA,CAAAhG;YACA;YACAiG,MAAA,CAAAjH,OAAA,CAAA4D,IAAA,CAAAwD,UAAA;YACAH,MAAA,CAAA9G,GAAA,CAAAyD,IAAA,CAAAoD,GAAA,CAAA1D,MAAA;UACA;YACA;YACA2D,MAAA,CAAAjH,OAAA,CAAAqH,OAAA,WAAAzE,IAAA,EAAAyB,KAAA;cACA,IAAAzB,IAAA,CAAAU,MAAA,IAAA0D,GAAA,CAAA1D,MAAA;gBACA2D,MAAA,CAAAjH,OAAA,CAAAsE,MAAA,CAAAD,KAAA;gBACA4C,MAAA,CAAA9G,GAAA,CAAAkH,OAAA,WAAA/D,MAAA,EAAAe,KAAA;kBACA,IAAAf,MAAA,IAAAV,IAAA,CAAAU,MAAA;oBACA2D,MAAA,CAAA9G,GAAA,CAAAmE,MAAA,CAAAD,KAAA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAiD,eAAA,WAAAA,gBAAAP,SAAA;MAAA,IAAAQ,MAAA;MACA,UAAAlI,QAAA;QACA,KAAAqF,KAAA,CAAA4B,KAAA,CAAAY,cAAA;MACA;QACA;QACA,IAAAH,SAAA,CAAArE,MAAA;UACA,KAAAlC,IAAA,CAAA6G,OAAA,WAAAzE,IAAA;YACA;YACA,KAAA2E,MAAA,CAAAvH,OAAA,CAAA0D,IAAA,WAAA8D,UAAA;cACA,OAAA5E,IAAA,CAAAU,MAAA,KAAAkE,UAAA,CAAAlE,MAAA;YACA;cACAiE,MAAA,CAAAT,YAAA,CAAAC,SAAA,EAAAnE,IAAA;YACA;UACA;UACA;QACA;UACA,KAAApC,IAAA,CAAA6G,OAAA,WAAAzE,IAAA;YACA;YACA,IAAA2E,MAAA,CAAAvH,OAAA,CAAA0D,IAAA,WAAA8D,UAAA;cACA,OAAA5E,IAAA,CAAAU,MAAA,KAAAkE,UAAA,CAAAlE,MAAA;YACA;cACAiE,MAAA,CAAAT,YAAA,CAAAC,SAAA,EAAAnE,IAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACA6E,qBAAA,WAAAA,sBAAAV,SAAA;MAAA,IAAAW,MAAA;MACA,KAAAvH,GAAA,GAAA4G,SAAA,CAAApE,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA8E,MAAA,CAAAjI,MAAA;MAAA;MACA,KAAAW,MAAA,GAAA2G,SAAA,CAAArE,MAAA;MACA,KAAArC,IAAA,IAAA0G,SAAA,CAAArE,MAAA;IACA;IACA;IACAiF,WAAA,WAAAA,YAAAX,GAAA,EAAAY,MAAA,EAAAC,KAAA;MACA,IAAAb,GAAA;QACA,IAAApB,IAAA,QAAAlB,KAAA,CAAA4B,KAAA,CAAAS,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,KAAArC,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAS,GAAA,OAAAhH,OAAA,CAAA0D,IAAA,WAAAd,IAAA;UACA,OAAAA,IAAA,CAAAU,MAAA,IAAA0D,GAAA,CAAA1D,MAAA;QACA,KAAAwE,SAAA;QACA,KAAAhB,YAAA,CAAAlB,IAAA,EAAAoB,GAAA;MACA;IACA;IACA,aACAe,SAAA,WAAAA,UAAA;MACA,KAAAC,KAAA;MACA,KAAAtH,IAAA;MACA,KAAAD,KAAA,kBAAAwH,MAAA,MAAAnI,YAAA;MACA,KAAAmB,IAAA,CAAAO,QAAA,QAAAG,YAAA;MACA,KAAAV,IAAA,CAAAM,MAAA,QAAA2G,MAAA,CAAAC,KAAA,CAAA7C,IAAA,CAAA/D,MAAA;IACA;IACA,aACA6G,YAAA,WAAAA,aAAApB,GAAA;MAAA,IAAAqB,OAAA;MACA,KAAAL,KAAA;MACA,IAAA3C,EAAA,GAAA2B,GAAA,MAAAvH,MAAA,UAAAU,GAAA;MACA,IAAA6D,aAAA,EAAAqB,EAAA,EAAApC,IAAA,WAAAC,QAAA;QACAmF,OAAA,CAAApH,IAAA,GAAAiC,QAAA,CAAA1D,IAAA;QACA6I,OAAA,CAAA3H,IAAA;QACA2H,OAAA,CAAA5H,KAAA,kBAAAwH,MAAA,CAAAI,OAAA,CAAAvI,YAAA;MACA;IACA;IACA,aACAwI,YAAA,WAAAA,aAAAtB,GAAA;MAAA,IAAAuB,OAAA;MACA,IAAApI,GAAA,GAAA6G,GAAA,MAAAvH,MAAA,UAAAU,GAAA;MACA,KAAAqI,MAAA,CAAAC,OAAA,wCAAAR,MAAA,MAAAnI,YAAA,0BAAAmI,MAAA,CAAA9H,GAAA,uCAAA8C,IAAA;QACA,WAAAyF,aAAA,EAAAvI,GAAA;MACA,GAAA8C,IAAA;QACAsF,OAAA,CAAAC,MAAA,CAAAG,UAAA;QACAJ,OAAA,CAAA9D,OAAA;MACA,GAAAmE,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,IAAAb,MAAA,MAAArI,UAAA,OAAAqI,MAAA,MAAApI,YAAA,kBAAAkJ,cAAA,CAAA9J,OAAA,MACA,KAAA0B,WAAA,MAAAsH,MAAA,CACA,KAAAnI,YAAA,OAAAmI,MAAA,KAAAe,IAAA,GAAAC,OAAA;IACA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAAzE,KAAA,SAAA0E,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,OAAA,CAAAlI,IAAA,CAAAkI,OAAA,CAAA1J,MAAA;YACA,IAAA6J,gBAAA,EAAAH,OAAA,CAAAlI,IAAA,EAAAgC,IAAA,WAAAC,QAAA;cACAiG,OAAA,CAAAX,MAAA,CAAAG,UAAA;cACAQ,OAAA,CAAAzI,IAAA;cACAyI,OAAA,CAAA1E,OAAA;YACA;UACA;YACA;YACA0E,OAAA,CAAAlI,IAAA,CAAAH,QAAA,GAAAqI,OAAA,CAAAlI,IAAA,CAAAD,WAAA;YACA,IAAAuI,aAAA,EAAAJ,OAAA,CAAAlI,IAAA,EAAAgC,IAAA,WAAAC,QAAA;cACAiG,OAAA,CAAAX,MAAA,CAAAG,UAAA;cACAQ,OAAA,CAAAzI,IAAA;cACAyI,OAAA,CAAA1E,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,WACA+E,MAAA,WAAAA,OAAA;MACA,KAAArJ,GAAA;MACA,KAAAH,OAAA;MACA,KAAAgC,WAAA,MAAA9C,KAAA;MACA,KAAAwB,IAAA;MACA,KAAAsH,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA/G,IAAA;QACAqC,MAAA,EAAAwE,SAAA;QACAvG,MAAA,EAAAuG,SAAA;QACAhH,QAAA,EAAAgH,SAAA;QACA/G,QAAA,EAAA+G,SAAA;QACAtG,QAAA,EAAAsG,SAAA;QACA9G,WAAA,EAAA8G,SAAA;QACArG,KAAA,EAAAqG,SAAA;QACA2B,GAAA,EAAA3B,SAAA;QACA4B,MAAA;QACAC,MAAA,EAAA7B,SAAA;QACA8B,OAAA;QACAC,OAAA;MACA;MACA,KAAAhD,SAAA;IACA;EACA;AACA", "ignoreList": []}]}