<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenMiningFileMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenMiningFileListVO">
        select a.* from threaten_mining_file a left join threaten_alarm_origin b on a.id = b.origin_id and (b.threaten_type = 'threaten_mining_file' or b.threaten_type = '挖矿软件')
        <where>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.baseId!=null">
                and a.base_id = #{query.baseId}
            </if>
            <if test="query.fileName!=null and query.fileName!=''">
                and a.file_name like concat('%', #{query.fileName}, '%')
            </if>
            <if test="query.processName!=null and query.processName!=''">
                and a.process_name like concat('%', #{query.processName}, '%')
            </if>
            <if test="query.fileMd5!=null and query.fileMd5!=''">
                and a.file_md5 like concat('%', #{query.fileMd5}, '%')
            </if>
            <if test="query.fileSha1!=null and query.fileSha1!=''">
                and a.file_sha1 like concat('%', #{query.fileSha1}, '%')
            </if>
            <if test="query.fileSha256!=null and query.fileSha256!=''">
                and a.file_sha256 like concat('%', #{query.fileSha256}, '%')
            </if>
            <if test="query.fileSm3!=null and query.fileSm3!=''">
                and a.file_sm3 like concat('%', #{query.fileSm3}, '%')
            </if>
            <if test="query.family!=null and query.family!=''">
                and a.family like concat('%', #{query.family}, '%')
            </if>
            <if test="query.organization!=null and query.organization!=''">
                and a.organization like concat('%', #{query.organization}, '%')
            </if>
            <if test="query.filePath!=null and query.filePath!=''">
                and a.file_path like concat('%', #{query.filePath}, '%')
            </if>
            <if test="query.fileSize!=null">
                and a.file_size = #{query.fileSize}
            </if>
            <if test="query.fileAuthority!=null and query.fileAuthority!=''">
                and a.file_authority like concat('%', #{query.fileAuthority}, '%')
            </if>
            <if test="query.fileCreateTime!=null">
                and a.file_create_time = #{query.fileCreateTime}
            </if>
            <if test="query.fileActionTime!=null">
                and a.file_action_time = #{query.fileActionTime}
            </if>
            <if test="query.authorityModifyTime!=null">
                and a.authority_modify_time = #{query.authorityModifyTime}
            </if>
            <if test="query.contentModifyTime!=null">
                and a.content_modify_time = #{query.contentModifyTime}
            </if>
            <if test="query.miningAddr!=null and query.miningAddr!=''">
                and a.mining_addr like concat('%', #{query.miningAddr}, '%')
            </if>
            <if test="query.subDomainList!=null and query.subDomainList!=''">
                and a.sub_domain_list like concat('%', #{query.subDomainList}, '%')
            </if>
            <if test="query.createTime!=null">
                and a.create_time = #{query.createTime}
            </if>
            <if test="query.alarmId!=null">
                and b.alarm_id = #{query.alarmId}
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenMiningFileListVO">
        select a.* from threaten_mining_file a 
        where a.id=#{id}
    </select>
</mapper>
