<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblManageMapper">

    <resultMap type="TblManage" id="TblManageResult">
        <result property="assetId" column="asset_id"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="functionDesc" column="function_desc"/>
        <result property="manageType" column="manage_type"/>
        <result property="degreeImportance" column="degree_importance"/>
        <result property="assetType" column="asset_type"/>
        <result property="assetTypeDesc" column="asset_type_desc"/>
        <result property="assetClass" column="asset_class"/>
        <result property="assetClassDesc" column="asset_class_desc"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="orgnId" column="orgn_id"/>
    </resultMap>

    <sql id="selectTblManageVo">
        select asset_id,
               asset_code,
               asset_name,
               function_desc,
               manage_type,
               degree_importance,
               asset_type,
               asset_type_desc,
               asset_class,
               asset_class_desc,
               remark,
               create_by,
               create_time,
               update_by,
               update_time,
               user_id,
               dept_id,
               orgn_id
        from tbl_manage a
    </sql>

    <select id="selectTblManageList" parameterType="TblManage" resultMap="TblManageResult">
        <include refid="selectTblManageVo"/>
        <where>
            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>
            <if test="assetCode != null  and assetCode != ''"> and asset_code = #{assetCode}</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="functionDesc != null  and functionDesc != ''"> and function_desc = #{functionDesc}</if>
            <if test="manageType != null  and manageType != ''"> and manage_type = #{manageType}</if>
            <if test="degreeImportance != null  and degreeImportance != ''"> and degree_importance = #{degreeImportance}</if>
            <if test="assetType != null  and assetType != ''"> and asset_type = #{assetType}</if>
            <if test="assetTypeDesc != null  and assetTypeDesc != ''"> and asset_type_desc = #{assetTypeDesc}</if>
            <if test="assetClass != null  and assetClass != ''"> and asset_class = #{assetClass}</if>
            <if test="assetClassDesc != null  and assetClassDesc != ''"> and asset_class_desc = #{assetClassDesc}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="orgnId != null "> and orgn_id = #{orgnId}</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
    </select>

    <select id="selectTblManageByAssetId" parameterType="Long" resultMap="TblManageResult">
        <include refid="selectTblManageVo"/>
        where asset_id = #{assetId}
    </select>

    <select id="selectTblManageByAssetIds" parameterType="Long" resultMap="TblManageResult">
        <include refid="selectTblManageVo"/>
        where asset_id in
        <foreach item="assetId" collection="array" open="(" close=")" separator=",">
            #{assetId}
        </foreach>
    </select>

    <insert id="insertTblManage" parameterType="TblManage">
        insert into tbl_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="assetCode != null">asset_code,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="functionDesc != null">function_desc,</if>
            <if test="manageType != null">manage_type,</if>
            <if test="degreeImportance != null">degree_importance,</if>
            <if test="assetType != null">asset_type,</if>
            <if test="assetTypeDesc != null">asset_type_desc,</if>
            <if test="assetClass != null and assetClass != ''">asset_class,</if>
            <if test="assetClassDesc != null">asset_class_desc,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="orgnId != null">orgn_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="assetCode != null">#{assetCode},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="functionDesc != null">#{functionDesc},</if>
            <if test="manageType != null">#{manageType},</if>
            <if test="degreeImportance != null">#{degreeImportance},</if>
            <if test="assetType != null">#{assetType},</if>
            <if test="assetTypeDesc != null">#{assetTypeDesc},</if>
            <if test="assetClass != null and assetClass != ''">#{assetClass},</if>
            <if test="assetClassDesc != null">#{assetClassDesc},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="orgnId != null">#{orgnId},</if>
         </trim>
    </insert>

    <update id="updateTblManage" parameterType="TblManage">
        update tbl_manage
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="functionDesc != null">function_desc = #{functionDesc},</if>
            <if test="manageType != null">manage_type = #{manageType},</if>
            <if test="degreeImportance != null">degree_importance = #{degreeImportance},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="assetTypeDesc != null">asset_type_desc = #{assetTypeDesc},</if>
            <if test="assetClass != null and assetClass != ''">asset_class = #{assetClass},</if>
            <if test="assetClassDesc != null">asset_class_desc = #{assetClassDesc},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="orgnId != null">orgn_id = #{orgnId},</if>
        </trim>
        where asset_id = #{assetId}
    </update>

    <delete id="deleteTblManageByAssetId" parameterType="Long">
        delete from tbl_manage where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblManageByAssetIds" parameterType="String">
        delete from tbl_manage where asset_id in 
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
</mapper>