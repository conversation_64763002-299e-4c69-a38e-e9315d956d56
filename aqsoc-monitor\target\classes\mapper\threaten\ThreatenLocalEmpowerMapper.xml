<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenLocalEmpowerMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenLocalEmpowerListVO">
        select a.* from threaten_local_empower a
        <where>
            <if test="query.alarmId!=null">
                and a.id in(select origin_id from threaten_alarm_origin where alarm_id=#{query.alarmId} and threaten_type=#{query.threatenType.name})
            </if>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.businessGroupId!=null">
                and a.business_group_id = #{query.businessGroupId}
            </if>
            <if test="query.businessGroupName!=null and query.businessGroupName!=''">
                and a.business_group_name like concat('%', #{query.businessGroupName}, '%')
            </if>
            <if test="query.eventDescription!=null and query.eventDescription!=''">
                and a.event_description like concat('%', #{query.eventDescription}, '%')
            </if>
            <if test="query.eventLevel!=null and query.eventLevel!=''">
                and a.event_level like concat('%', #{query.eventLevel}, '%')
            </if>
            <if test="query.eventName!=null and query.eventName!=''">
                and a.event_name like concat('%', #{query.eventName}, '%')
            </if>
            <if test="query.eventSolution!=null and query.eventSolution!=''">
                and a.event_solution like concat('%', #{query.eventSolution}, '%')
            </if>
            <if test="query.eventState!=null and query.eventState!=''">
                and a.event_state like concat('%', #{query.eventState}, '%')
            </if>
            <if test="query.filePermission!=null and query.filePermission!=''">
                and a.file_permission like concat('%', #{query.filePermission}, '%')
            </if>
            <if test="query.hostId!=null">
                and a.host_id = #{query.hostId}
            </if>
            <if test="query.hostIp!=null and query.hostIp!=''">
                and a.host_ip like concat('%', #{query.hostIp}, '%')
            </if>
            <if test="query.hostName!=null and query.hostName!=''">
                and a.host_name like concat('%', #{query.hostName}, '%')
            </if>
            <if test="query.processCommandLine!=null and query.processCommandLine!=''">
                and a.process_command_line like concat('%', #{query.processCommandLine}, '%')
            </if>
            <if test="query.processId!=null">
                and a.process_id = #{query.processId}
            </if>
            <if test="query.processName!=null and query.processName!=''">
                and a.process_name like concat('%', #{query.processName}, '%')
            </if>
            <if test="query.processPath!=null and query.processPath!=''">
                and a.process_path like concat('%', #{query.processPath}, '%')
            </if>
            <if test="query.processTree!=null and query.processTree!=''">
                and a.process_tree like concat('%', #{query.processTree}, '%')
            </if>
            <if test="query.userGname!=null and query.userGname!=''">
                and a.user_gname like concat('%', #{query.userGname}, '%')
            </if>
            <if test="query.userName!=null and query.userName!=''">
                and a.user_name like concat('%', #{query.userName}, '%')
            </if>
            <if test="query.userTargetUser!=null and query.userTargetUser!=''">
                and a.user_target_user like concat('%', #{query.userTargetUser}, '%')
            </if>
            <if test="query.userTargetUserid!=null and query.userTargetUserid!=''">
                and a.user_target_userid like concat('%', #{query.userTargetUserid}, '%')
            </if>
            <if test="query.eventCreateTime!=null">
                and a.event_create_time = #{query.eventCreateTime}
            </if>
            <if test="query.startTimeline!=null">
                and a.timeline >= DATE_FORMAT(#{query.startTimeline}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="query.endTimeline!=null">
                and a.timeline &lt; DATE_FORMAT(#{query.endTimeline}, '%Y-%m-%d %H:%i:%s')
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenLocalEmpowerListVO">
        select a.* from threaten_local_empower a
        where a.id=#{id}
    </select>
</mapper>
