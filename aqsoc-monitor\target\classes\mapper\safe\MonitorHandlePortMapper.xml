<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.MonitorHandlePortMapper">

    <resultMap type="com.ruoyi.safe.domain.MonitorHandlePort" id="MonitorHandlePortResult">
        <result property="id"    column="id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="domainId"    column="domain_id"    />
        <result property="jobId"    column="job_id"    />
        <result property="deployId"    column="deploy_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="assetType"    column="asset_type"    />
        <result property="businessSystem"    column="business_system"    />
        <result property="port"    column="port"    />
        <result property="enterAppName"    column="enter_app_name"    />
        <result property="appName"    column="app_name"    />
        <result property="appVer"    column="app_ver"    />
        <result property="ip"    column="ip"    />
        <result property="state"    column="state"    />
        <result property="exceptionState"    column="exception_state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectMonitorHandlePortVo">
        select id, asset_id, domain_id, job_id, deploy_id, dept_id, port, app_name, app_ver, ip, state, exception_state, create_by, create_time, update_by, update_time, remark, is_del from monitor_handle_port
    </sql>
    <sql id="selectMonitorHandlePortVo2">
        select mhp.id, mhp.asset_id, mhp.domain_id, mhp.job_id, mhp.deploy_id, mhp.dept_id, mhp.port, mhp.app_name, mhp.app_ver, mhp.ip, mhp.state, mhp.exception_state,mhp.update_time,
        tao.asset_type_desc as asset_type, sd.dept_name, td.proc_name as enter_app_name
        from monitor_handle_port mhp
        left join tbl_asset_overview tao on tao.asset_id = mhp.asset_id
        left join sys_dept sd on mhp.dept_id = sd.dept_id
        left join tbl_deploy td on td.deploy_id = mhp.deploy_id
    </sql>

    <select id="selectMonitorHandlePortList" parameterType="com.ruoyi.safe.domain.MonitorHandlePort" resultMap="MonitorHandlePortResult">
        <include refid="selectMonitorHandlePortVo2"/>
        <where>
            mhp.is_del = 0
            <if test="assetId != null "> and mhp.asset_id = #{assetId}</if>
            <if test="domainId != null "> and mhp.domain_id = #{domainId}</if>
            <if test="jobId != null "> and mhp.job_id = #{jobId}</if>
            <if test="deployId != null "> and mhp.deploy_id = #{deployId}</if>
            <if test="deptId != null "> and mhp.dept_id = #{deptId}</if>
            <if test="port != null "> and mhp.port = #{port}</if>
            <if test="appName != null  and appName != ''"> and mhp.app_name like concat('%', #{appName}, '%')</if>
            <if test="appVer != null  and appVer != ''"> and mhp.app_ver = #{appVer}</if>
            <if test="ip != null  and ip != ''"> and mhp.ip = #{ip}</if>
            <if test="assetTypeCode != null">and tao.asset_type = #{assetTypeCode}</if>
            <if test="businessSystem != null and businessSystem != ''">and EXISTS(SELECT 1 FROM tbl_application_server tas INNER JOIN tbl_business_application tba on tba.asset_id = tas.asset_id where mhp.asset_id = tas.server_id and instr(tba.asset_name, #{businessSystem}) > 0)</if>
            <if test="state != null  and state != ''"> and mhp.state = #{state}</if>
            <if test="exceptionState != null "> and mhp.exception_state = #{exceptionState}</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
        order by mhp.update_time desc
    </select>

    <select id="selectMonitorHandlePortById" parameterType="java.lang.Long" resultMap="MonitorHandlePortResult">
        <include refid="selectMonitorHandlePortVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorHandlePortByIds" parameterType="java.lang.Long" resultMap="MonitorHandlePortResult">
        <include refid="selectMonitorHandlePortVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMonitorHandlePort" parameterType="com.ruoyi.safe.domain.MonitorHandlePort">
        insert into monitor_handle_port
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="domainId != null">domain_id,</if>
            <if test="jobId != null">job_id,</if>
            <if test="deployId != null">deploy_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="port != null">port,</if>
            <if test="appName != null">app_name,</if>
            <if test="appVer != null">app_ver,</if>
            <if test="ip != null">ip,</if>
            <if test="state != null">state,</if>
            <if test="exceptionState != null">exception_state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="domainId != null">#{domainId},</if>
            <if test="jobId != null">#{jobId},</if>
            <if test="deployId != null">#{deployId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="port != null">#{port},</if>
            <if test="appName != null">#{appName},</if>
            <if test="appVer != null">#{appVer},</if>
            <if test="ip != null">#{ip},</if>
            <if test="state != null">#{state},</if>
            <if test="exceptionState != null">#{exceptionState},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateMonitorHandlePort" parameterType="com.ruoyi.safe.domain.MonitorHandlePort">
        update monitor_handle_port
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="domainId != null">domain_id = #{domainId},</if>
            <if test="jobId != null">job_id = #{jobId},</if>
            <if test="deployId != null">deploy_id = #{deployId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="port != null">port = #{port},</if>
            <if test="appName != null">app_name = #{appName},</if>
            <if test="appVer != null">app_ver = #{appVer},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="state != null">state = #{state},</if>
            <if test="exceptionState != null">exception_state = #{exceptionState},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="insertOrUpdateHandlePort" parameterType="com.ruoyi.safe.domain.MonitorHandlePort">
        <selectKey keyProperty="countData" resultType="java.lang.Integer" order="BEFORE">
            select count(1) from monitor_handle_port where port = #{port} and asset_id = #{assetId}
        </selectKey>
        <if test="countData > 0">
            update monitor_handle_port
            <trim prefix="SET" suffixOverrides=",">
                <if test="domainId != null">domain_id = #{domainId},</if>
                <if test="jobId != null">job_id = #{jobId},</if>
                <if test="deployId != null">deploy_id = #{deployId},</if>
                <if test="deptId != null">dept_id = #{deptId},</if>
                <if test="appName != null">app_name = #{appName},</if>
                <if test="appVer != null">app_ver = #{appVer},</if>
                <if test="ip != null">ip = #{ip},</if>
                <if test="state != null">state = #{state},</if>
                <if test="exceptionState != null">exception_state = #{exceptionState},</if>
                <if test="createBy != null">create_by = #{createBy},</if>
                <if test="createTime != null">create_time = #{createTime},</if>
                <if test="updateBy != null">update_by = #{updateBy},</if>
                <if test="updateTime != null">update_time = #{updateTime},</if>
                <if test="remark != null">remark = #{remark},</if>
                <if test="isDel != null">is_del = #{isDel},</if>
            </trim>
            where port = #{port} and asset_id = #{assetId}
        </if>
        <if test="countData == 0">
            insert into monitor_handle_port
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="assetId != null">asset_id,</if>
                <if test="domainId != null">domain_id,</if>
                <if test="jobId != null">job_id,</if>
                <if test="deployId != null">deploy_id,</if>
                <if test="deptId != null">dept_id,</if>
                <if test="port != null">port,</if>
                <if test="appName != null">app_name,</if>
                <if test="appVer != null">app_ver,</if>
                <if test="ip != null">ip,</if>
                <if test="state != null">state,</if>
                <if test="exceptionState != null">exception_state,</if>
                <if test="createBy != null">create_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="remark != null">remark,</if>
                <if test="isDel != null">is_del,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id},</if>
                <if test="assetId != null">#{assetId},</if>
                <if test="domainId != null">#{domainId},</if>
                <if test="jobId != null">#{jobId},</if>
                <if test="deployId != null">#{deployId},</if>
                <if test="deptId != null">#{deptId},</if>
                <if test="port != null">#{port},</if>
                <if test="appName != null">#{appName},</if>
                <if test="appVer != null">#{appVer},</if>
                <if test="ip != null">#{ip},</if>
                <if test="state != null">#{state},</if>
                <if test="exceptionState != null">#{exceptionState},</if>
                <if test="createBy != null">#{createBy},</if>
                <if test="createTime != null">#{createTime},</if>
                <if test="updateBy != null">#{updateBy},</if>
                <if test="updateTime != null">#{updateTime},</if>
                <if test="remark != null">#{remark},</if>
                <if test="isDel != null">#{isDel},</if>
            </trim>
        </if>
    </update>

    <delete id="deleteMonitorHandlePortById" parameterType="java.lang.Long">
        delete from monitor_handle_port where id = #{id}
    </delete>

    <delete id="deleteMonitorHandlePortByIds" parameterType="java.lang.String">
        delete from monitor_handle_port where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectNeedDel" resultType="java.lang.Long">
        select mhp.id from monitor_handle_port mhp
        inner join tbl_deploy td on mhp.asset_id = td.asset_id and mhp.port = td.port
        inner join tbl_product_finger tpf on tpf.prdid = td.prdid and mhp.app_name = tpf.os_name
        union all
        select mhp.id from monitor_handle_port mhp
        inner join tbl_deploy td on mhp.asset_id = td.asset_id and mhp.port = td.port
        inner join tbl_product tp on tp.prdid = td.prdid and mhp.app_name = tp.proc_name
    </select>

    <select id="getSystemName" resultType="com.alibaba.fastjson2.JSONObject">
        select DISTINCT tba.asset_name as assetName,tba.asset_id as assetId from tbl_business_application tba inner join tbl_application_server tas on tba.asset_id = tas.asset_id
        where tas.server_id = #{serverId}
    </select>
</mapper>
