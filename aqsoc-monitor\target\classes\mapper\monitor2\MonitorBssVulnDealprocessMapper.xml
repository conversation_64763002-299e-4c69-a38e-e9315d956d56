<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.MonitorBssVulnDealprocessMapper">

    <resultMap type="MonitorBssVulnDealprocess" id="MonitorBssVulnDealprocessResult">
        <result property="id"    column="id"    />
        <result property="dealId"    column="deal_id"    />
        <result property="dealStatus"    column="deal_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectMonitorBssVulnDealprocessVo">
        select id, deal_id, deal_status, create_by, create_time from monitor_bss_vuln_dealprocess
    </sql>

    <select id="selectMonitorBssVulnDealprocessList" parameterType="MonitorBssVulnDealprocess" resultMap="MonitorBssVulnDealprocessResult">
        <include refid="selectMonitorBssVulnDealprocessVo"/>
        <where>
            <if test="dealId != null "> and deal_id = #{dealId}</if>
            <if test="dealStatus != null "> and deal_status = #{dealStatus}</if>
        </where>
    </select>

    <select id="selectMonitorBssVulnDealprocessById" parameterType="Long" resultMap="MonitorBssVulnDealprocessResult">
        <include refid="selectMonitorBssVulnDealprocessVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorBssVulnDealprocessByIds" parameterType="Long" resultMap="MonitorBssVulnDealprocessResult">
        <include refid="selectMonitorBssVulnDealprocessVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMonitorBssVulnDealprocess" parameterType="MonitorBssVulnDealprocess">
        insert into monitor_bss_vuln_dealprocess
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="dealId != null">deal_id,</if>
            <if test="dealStatus != null">deal_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="dealId != null">#{dealId},</if>
            <if test="dealStatus != null">#{dealStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateMonitorBssVulnDealprocess" parameterType="MonitorBssVulnDealprocess">
        update monitor_bss_vuln_dealprocess
        <trim prefix="SET" suffixOverrides=",">
            <if test="dealId != null">deal_id = #{dealId},</if>
            <if test="dealStatus != null">deal_status = #{dealStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorBssVulnDealprocessById" parameterType="Long">
        delete from monitor_bss_vuln_dealprocess where id = #{id}
    </delete>

    <delete id="deleteMonitorBssVulnDealprocessByIds" parameterType="String">
        delete from monitor_bss_vuln_dealprocess where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
