<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dimension.mapper.TblAssetCompanyEmpMapper">

    <resultMap type="TblAssetCompanyEmp" id="TblAssetCompanyEmpResult">
        <result property="assetId" column="asset_id"/>
        <result property="eid" column="eid"/>
    </resultMap>

    <sql id="selectTblAssetCompanyEmpVo">
        select asset_id, eid from tbl_asset_company_emp
    </sql>

    <select id="selectTblAssetCompanyEmpList" parameterType="TblAssetCompanyEmp" resultMap="TblAssetCompanyEmpResult">
        <include refid="selectTblAssetCompanyEmpVo"/>
        <where>
        </where>
    </select>

    <select id="selectTblAssetCompanyEmpByAssetId" parameterType="Long" resultMap="TblAssetCompanyEmpResult">
        <include refid="selectTblAssetCompanyEmpVo"/>
        where asset_id = #{assetId}
    </select>

    <select id="selectTblAssetCompanyEmpByAssetIds" parameterType="Long" resultMap="TblAssetCompanyEmpResult">
        <include refid="selectTblAssetCompanyEmpVo"/>
        where asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </select>

    <insert id="insertTblAssetCompanyEmp" parameterType="TblAssetCompanyEmp">
        insert into tbl_asset_company_emp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="eid != null">eid,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="eid != null">#{eid},</if>
        </trim>
    </insert>

    <insert id="batchInsertTblAssetCompanyEmp">
        insert into tbl_asset_company_emp(asset_id, eid) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.assetId},#{item.eid})
        </foreach>
    </insert>

    <update id="updateTblAssetCompanyEmp" parameterType="TblAssetCompanyEmp">
        update tbl_asset_company_emp
        <trim prefix="SET" suffixOverrides=",">
            <if test="eid != null">eid = #{eid},</if>
        </trim>
        where asset_id = #{assetId}
    </update>

    <delete id="deleteTblAssetCompanyEmpByAssetId" parameterType="Long">
        delete from tbl_asset_company_emp where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblAssetCompanyEmpByAssetIds" parameterType="String">
        delete from tbl_asset_company_emp where asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>

    <select id="selectCountNum" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(0) from tbl_asset_company_emp
        <where>
            <if test="assetId != null">
                asset_id = #{assetId}
            </if>
        </where>
    </select>
</mapper>
