<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenExceptCmdMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenExceptCmdListVO">
        select a.* from threaten_except_cmd a
        <where>
            <if test="query.alarmId!=null">
                and a.id in(select origin_id from threaten_alarm_origin where alarm_id=#{query.alarmId} and threaten_type=#{query.threatenType.name})
            </if>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.filePath!=null and query.filePath!=''">
                and a.file_path like concat('%', #{query.filePath}, '%')
            </if>
            <if test="query.processTree!=null and query.processTree!=''">
                and a.process_tree like concat('%', #{query.processTree}, '%')
            </if>
            <if test="query.processName!=null and query.processName!=''">
                and a.process_name like concat('%', #{query.processName}, '%')
            </if>
            <if test="query.cmdLine!=null and query.cmdLine!=''">
                and a.cmd_line like concat('%', #{query.cmdLine}, '%')
            </if>
            <if test="query.environment!=null and query.environment!=''">
                and a.environment like concat('%', #{query.environment}, '%')
            </if>
            <if test="query.userName!=null and query.userName!=''">
                and a.user_name like concat('%', #{query.userName}, '%')
            </if>
            <if test="query.userGroup!=null and query.userGroup!=''">
                and a.user_group like concat('%', #{query.userGroup}, '%')
            </if>
            <if test="query.osName!=null and query.osName!=''">
                and a.os_name like concat('%', #{query.osName}, '%')
            </if>
            <if test="query.hostIp!=null and query.hostIp!=''">
                and a.host_ip like concat('%', #{query.hostIp}, '%')
            </if>
            <if test="query.businessGroupId!=null">
                and a.business_group_id = #{query.businessGroupId}
            </if>
            <if test="query.businessGroupName!=null and query.businessGroupName!=''">
                and a.business_group_name like concat('%', #{query.businessGroupName}, '%')
            </if>
            <if test="query.hostId!=null">
                and a.host_id = #{query.hostId}
            </if>
            <if test="query.eventName!=null and query.eventName!=''">
                and a.event_name like concat('%', #{query.eventName}, '%')
            </if>
            <if test="query.ruleName!=null and query.ruleName!=''">
                and a.rule_name like concat('%', #{query.ruleName}, '%')
            </if>
            <if test="query.signature!=null and query.signature!=''">
                and a.signature like concat('%', #{query.signature}, '%')
            </if>
            <if test="query.eventLevel!=null and query.eventLevel!=''">
                and a.event_level like concat('%', #{query.eventLevel}, '%')
            </if>
            <if test="query.eventState!=null and query.eventState!=''">
                and a.event_state like concat('%', #{query.eventState}, '%')
            </if>
            <if test="query.eventCreateTime!=null">
                and a.event_create_time >= #{query.eventCreateTime}
            </if>
            <if test="query.eventUpdateTime!=null">
                and a.event_update_time = #{query.eventUpdateTime}
            </if>
            <if test="query.userId!=null">
                and a.user_id = #{query.userId}
            </if>
            <if test="query.userGid!=null">
                and a.user_gid = #{query.userGid}
            </if>
            <if test="query.eventStartTime!=null">
                and a.event_start_time = #{query.eventStartTime}
            </if>
            <if test="query.eventEndTime!=null">
                and a.event_end_time = #{query.eventEndTime}
            </if>
            <if test="query.eventDescription!=null and query.eventDescription!=''">
                and a.event_description like concat('%', #{query.eventDescription}, '%')
            </if>
            <if test="query.eventSolution!=null and query.eventSolution!=''">
                and a.event_solution like concat('%', #{query.eventSolution}, '%')
            </if>
            <if test="query.startTimeline!=null">
                and a.timeline >= DATE_FORMAT(#{query.startTimeline}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="query.endTimeline!=null">
                and a.timeline &lt; DATE_FORMAT(#{query.endTimeline}, '%Y-%m-%d %H:%i:%s')
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenExceptCmdListVO">
        select a.*
        from threaten_except_cmd a
        where a.id = #{id}
    </select>
</mapper>
