<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblZoneBoundaryMapper">

    <resultMap type="TblZoneBoundary" id="TblZoneBoundaryResult">
        <result property="assetId" column="asset_id"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="zone1" column="zone1"/>
        <result property="zone2" column="zone2"/>
        <result property="accessWay" column="access_way"/>
        <result property="application" column="application"/>
        <result property="securityLevel" column="security_level"/>
        <result property="manger" column="manger"/>
        <result property="vendor" column="vendor"/>
        <result property="degreeImportance" column="degree_importance"/>
        <result property="assetType" column="asset_type"/>
        <result property="assetTypeDesc" column="asset_type_desc"/>
        <result property="assetClass" column="asset_class"/>
        <result property="assetClassDesc" column="asset_class_desc"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="orgnId" column="orgn_id"/>
        <result property="applicationName" column="application_name"/>
    </resultMap>

    <sql id="selectTblZoneBoundaryVo">
        select a.asset_id,
               a.asset_code,
               a.asset_name,
               a.zone1,
               a.zone2,
               a.access_way,
               a.application,
               b.asset_name as application_name,
               a.security_level,
               a.manger,
               a.vendor,
               a.degree_importance,
               a.asset_type,
               a.asset_type_desc,
               a.asset_class,
               a.asset_class_desc,
               a.remark,
               a.create_by,
               a.create_time,
               a.update_by,
               a.update_time,
               a.user_id,
               a.dept_id,
               a.orgn_id
        from tbl_zone_boundary a
                 left join tbl_business_application b on a.application = b.asset_id
    </sql>

    <select id="selectTblZoneBoundaryList" parameterType="TblZoneBoundary" resultMap="TblZoneBoundaryResult">
        <include refid="selectTblZoneBoundaryVo"/>
        <where>
            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>
            <if test="assetCode != null  and assetCode != ''"> and a.asset_code = #{assetCode}</if>
            <if test="assetName != null  and assetName != ''"> and a.asset_name like concat('%', #{assetName}, '%')</if>
            <if test="accessWay != null  and accessWay != ''"> and a.access_way like concat('%', #{accessWay}, '%')</if>
            <if test="applicationName != null  and applicationName != ''"> and b.asset_name like concat('%', #{applicationName}, '%')</if>
            <if test="assetType != null "> and a.asset_type = #{assetType}</if>
            <if test="assetClass != null "> and a.asset_class = #{assetClass}</if>
            <if test="deptId != null and deptId != 0">
                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
            <if test="degreeImportance != null and degreeImportance != ''">and a.degree_importance=#{degreeImportance}</if>
        </where>
    </select>
    
    <select id="selectTblZoneBoundaryByAssetId" parameterType="Long" resultMap="TblZoneBoundaryResult">
        <include refid="selectTblZoneBoundaryVo"/>
        where a.asset_id = #{assetId}
    </select>

    <select id="selectTblZoneBoundaryByAssetIds" parameterType="Long" resultMap="TblZoneBoundaryResult">
        <include refid="selectTblZoneBoundaryVo"/>
        where a.asset_id in
        <foreach item="assetId" collection="array" open="(" close=")" separator=",">
            #{assetId}
        </foreach>
    </select>

    <insert id="insertTblZoneBoundary" parameterType="TblZoneBoundary">
        insert into tbl_zone_boundary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="assetCode != null">asset_code,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="zone1 != null">zone1,</if>
            <if test="zone2 != null">zone2,</if>
            <if test="accessWay != null">access_way,</if>
            <if test="application != null">application,</if>
            <if test="securityLevel != null">security_level,</if>
            <if test="manger != null">manger,</if>
            <if test="vendor != null">vendor,</if>
            <if test="degreeImportance != null">degree_importance,</if>
            <if test="assetType != null">asset_type,</if>
            <if test="assetTypeDesc != null">asset_type_desc,</if>
            <if test="assetClass != null">asset_class,</if>
            <if test="assetClassDesc != null">asset_class_desc,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="orgnId != null">orgn_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="assetCode != null">#{assetCode},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="zone1 != null">#{zone1},</if>
            <if test="zone2 != null">#{zone2},</if>
            <if test="accessWay != null">#{accessWay},</if>
            <if test="application != null">#{application},</if>
            <if test="securityLevel != null">#{securityLevel},</if>
            <if test="manger != null">#{manger},</if>
            <if test="vendor != null">#{vendor},</if>
            <if test="degreeImportance != null">#{degreeImportance},</if>
            <if test="assetType != null">#{assetType},</if>
            <if test="assetTypeDesc != null">#{assetTypeDesc},</if>
            <if test="assetClass != null">#{assetClass},</if>
            <if test="assetClassDesc != null">#{assetClassDesc},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="orgnId != null">#{orgnId},</if>
         </trim>
    </insert>

    <update id="updateTblZoneBoundary" parameterType="TblZoneBoundary">
        update tbl_zone_boundary
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="zone1 != null">zone1 = #{zone1},</if>
            <if test="zone2 != null">zone2 = #{zone2},</if>
            <if test="accessWay != null">access_way = #{accessWay},</if>
            <if test="application != null">application = #{application},</if>
            <if test="securityLevel != null">security_level = #{securityLevel},</if>
            <if test="manger != null">manger = #{manger},</if>
            <if test="vendor != null">vendor = #{vendor},</if>
            <if test="degreeImportance != null">degree_importance = #{degreeImportance},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="assetTypeDesc != null">asset_type_desc = #{assetTypeDesc},</if>
            <if test="assetClass != null">asset_class = #{assetClass},</if>
            <if test="assetClassDesc != null">asset_class_desc = #{assetClassDesc},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="orgnId != null">orgn_id = #{orgnId},</if>
        </trim>
        where asset_id = #{assetId}
    </update>

    <delete id="deleteTblZoneBoundaryByAssetId" parameterType="Long">
        delete from tbl_zone_boundary where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblZoneBoundaryByAssetIds" parameterType="String">
        delete from tbl_zone_boundary where asset_id in 
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
</mapper>