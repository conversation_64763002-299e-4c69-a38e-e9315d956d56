<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblFirewallPolicyMapper">
    <resultMap type="TblFirewallPolicy" id="TblFirewallPolicyResult">
        <result property="id"    column="id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="assetName" column="asset_name" />
        <result property="outDataId"    column="out_data_id"    />
        <result property="outFirewallId"    column="out_firewall_id"    />
        <result property="outType"    column="out_type"    />
        <result property="policyName"    column="policy_name"    />
        <result property="policyName0"    column="policy_name0"    />
        <result property="num"    column="num"    />
        <result property="fromZone"    column="from_zone"    />
        <result property="toZone"    column="to_zone"    />
        <result property="clientIpStr"    column="client_ip_str"    />
        <result property="clientPortStr"    column="client_port_str"    />
        <result property="clientValue"    column="client_value"    />
        <result property="serverIpStr"    column="server_ip_str"    />
        <result property="serverPortStr"    column="server_port_str"    />
        <result property="serverValue"    column="server_value"    />
        <result property="serviceName"    column="service_name"    />
        <result property="action"    column="action"    />
        <result property="enabled"    column="enabled"    />
        <result property="expirationDate"    column="expiration_date"    />
        <result property="overdue"    column="overdue"    />
        <result property="outCreTime"    column="out_cre_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="refStatus"    column="ref_status"    />
        <result property="sourceType"    column="source_type"    />
        <result property="sourceData"    column="source_data"    />
        <result property="longCon"    column="long_con"    />
        <result property="ipVersion"    column="ip_version"    />
        <result property="emptyRule"    column="empty_rule"    />
        <result property="times"    column="times"    />
        <result property="ruleHitPercent"    column="rule_hit_percent"    />
        <result property="serverHitPercent"    column="server_hit_percent"    />
        <result property="serverIpHitPercent"    column="server_ip_hit_percent"    />
        <result property="serverPortHitPercent"    column="server_port_hit_percent"    />
        <result property="clientHitPercent"    column="client_hit_percent"    />
        <result property="firstHitTime"    column="first_hit_time"    />
        <result property="lastHitTime"    column="last_hit_time"    />
        <result property="refAppPolicyId"    column="ref_app_policy_id"    />
        <result property="policyGrp"    column="policy_grp"    />
        <result property="appCompareType"    column="app_compare_type"    />
        <result property="virtualSystem"    column="virtual_system"    />
    </resultMap>

    <sql id="selectTblFirewallPolicyVo">
        select t1.*,t2.asset_name from tbl_firewall_policy t1
        left join tbl_safety t2 on t1.asset_id = t2.asset_id
    </sql>

    <select id="selectTblFirewallPolicyList" parameterType="TblFirewallPolicy" resultMap="TblFirewallPolicyResult">
        <include refid="selectTblFirewallPolicyVo"></include>
        <where>
            <if test="assetId != null "> and t1.asset_id = #{assetId}</if>
            <if test="outDataId != null  and outDataId != ''"> and t1.out_data_id = #{outDataId}</if>
            <if test="outFirewallId != null  and outFirewallId != ''"> and t1.out_firewall_id = #{outFirewallId}</if>
            <if test="outType != null "> and t1.out_type = #{outType}</if>
            <if test="policyName != null  and policyName != ''"> and t1.policy_name like concat('%', #{policyName}, '%')</if>
            <if test="policyName0 != null  and policyName0 != ''"> and t1.policy_name0 = #{policyName0}</if>
            <if test="num != null "> and t1.num = #{num}</if>
            <if test="fromZone != null  and fromZone != ''"> and t1.from_zone = #{fromZone}</if>
            <if test="toZone != null  and toZone != ''"> and t1.to_zone = #{toZone}</if>
            <if test="clientIpStr != null  and clientIpStr != ''"> and t1.client_ip_str = #{clientIpStr}</if>
            <if test="clientPortStr != null  and clientPortStr != ''"> and t1.client_port_str = #{clientPortStr}</if>
            <if test="clientValue != null  and clientValue != ''"> and t1.client_value = #{clientValue}</if>
            <if test="serverIpStr != null  and serverIpStr != ''"> and t1.server_ip_str = #{serverIpStr}</if>
            <if test="serverPortStr != null  and serverPortStr != ''"> and t1.server_port_str = #{serverPortStr}</if>
            <if test="serverValue != null  and serverValue != ''"> and t1.server_value = #{serverValue}</if>
            <if test="serviceName != null  and serviceName != ''"> and t1.service_name like concat('%', #{serviceName}, '%')</if>
            <if test="action != null "> and t1.action = #{action}</if>
            <if test="enabled != null "> and t1.enabled = #{enabled}</if>
            <if test="expirationDate != null  and expirationDate != ''"> and t1.expiration_date = #{expirationDate}</if>
            <if test="overdue != null "> and t1.overdue = #{overdue}</if>
            <if test="outCreTime != null "> and t1.out_cre_time = #{outCreTime}</if>
            <if test="refStatus != null  and refStatus != ''"> and t1.ref_status = #{refStatus}</if>
            <if test="sourceType != null  and sourceType != ''"> and t1.source_type = #{sourceType}</if>
            <if test="sourceData != null  and sourceData != ''"> and t1.source_data = #{sourceData}</if>
            <if test="longCon != null "> and t1.long_con = #{longCon}</if>
            <if test="ipVersion != null  and ipVersion != ''"> and t1.ip_version = #{ipVersion}</if>
            <if test="emptyRule != null "> and t1.empty_rule = #{emptyRule}</if>
            <if test="times != null  and times != ''"> and t1.times = #{times}</if>
            <if test="ruleHitPercent != null  and ruleHitPercent != ''"> and t1.rule_hit_percent = #{ruleHitPercent}</if>
            <if test="serverHitPercent != null  and serverHitPercent != ''"> and t1.server_hit_percent = #{serverHitPercent}</if>
            <if test="serverIpHitPercent != null  and serverIpHitPercent != ''"> and t1.server_ip_hit_percent = #{serverIpHitPercent}</if>
            <if test="serverPortHitPercent != null  and serverPortHitPercent != ''"> and t1.server_port_hit_percent = #{serverPortHitPercent}</if>
            <if test="clientHitPercent != null  and clientHitPercent != ''"> and t1.client_hit_percent = #{clientHitPercent}</if>
            <if test="firstHitTime != null  and firstHitTime != ''"> and t1.first_hit_time = #{firstHitTime}</if>
            <if test="lastHitTime != null  and lastHitTime != ''"> and t1.last_hit_time = #{lastHitTime}</if>
            <if test="refAppPolicyId != null  and refAppPolicyId != ''"> and t1.ref_app_policy_id = #{refAppPolicyId}</if>
            <if test="policyGrp != null  and policyGrp != ''"> and t1.policy_grp = #{policyGrp}</if>
            <if test="appCompareType != null  and appCompareType != ''"> and t1.app_compare_type = #{appCompareType}</if>
            <if test="virtualSystem != null  and virtualSystem != ''"> and t1.virtual_system = #{virtualSystem}</if>
        </where>
    </select>

    <select id="selectTblFirewallPolicyById" parameterType="Long" resultMap="TblFirewallPolicyResult">
        <include refid="selectTblFirewallPolicyVo"/>
        where t1.id = #{id}
    </select>

    <select id="selectTblFirewallPolicyByIds" parameterType="Long" resultMap="TblFirewallPolicyResult">
        <include refid="selectTblFirewallPolicyVo"/>
        where t1.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectFirewallPolicyByFirewallIds" resultType="com.ruoyi.safe.domain.TblFirewallPolicy" resultMap="TblFirewallPolicyResult">
        <include refid="selectTblFirewallPolicyVo"/>
        where t1.out_firewall_id in
        <foreach item="firewallId" collection="firewallIds" open="(" separator="," close=")">
            #{firewallId}
        </foreach>
        <!-- group by t1.out_firewall_id -->
    </select>

    <insert id="insertTblFirewallPolicy" parameterType="TblFirewallPolicy" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_firewall_policy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="outDataId != null">out_data_id,</if>
            <if test="outFirewallId != null">out_firewall_id,</if>
            <if test="outType != null">out_type,</if>
            <if test="policyName != null">policy_name,</if>
            <if test="policyName0 != null">policy_name0,</if>
            <if test="num != null">num,</if>
            <if test="fromZone != null">from_zone,</if>
            <if test="toZone != null">to_zone,</if>
            <if test="clientIpStr != null">client_ip_str,</if>
            <if test="clientPortStr != null">client_port_str,</if>
            <if test="clientValue != null">client_value,</if>
            <if test="serverIpStr != null">server_ip_str,</if>
            <if test="serverPortStr != null">server_port_str,</if>
            <if test="serverValue != null">server_value,</if>
            <if test="serviceName != null">service_name,</if>
            <if test="action != null">action,</if>
            <if test="enabled != null">enabled,</if>
            <if test="expirationDate != null">expiration_date,</if>
            <if test="overdue != null">overdue,</if>
            <if test="outCreTime != null">out_cre_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="refStatus != null">ref_status,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="sourceData != null">source_data,</if>
            <if test="longCon != null">long_con,</if>
            <if test="ipVersion != null">ip_version,</if>
            <if test="emptyRule != null">empty_rule,</if>
            <if test="times != null">times,</if>
            <if test="ruleHitPercent != null">rule_hit_percent,</if>
            <if test="serverHitPercent != null">server_hit_percent,</if>
            <if test="serverIpHitPercent != null">server_ip_hit_percent,</if>
            <if test="serverPortHitPercent != null">server_port_hit_percent,</if>
            <if test="clientHitPercent != null">client_hit_percent,</if>
            <if test="firstHitTime != null">first_hit_time,</if>
            <if test="lastHitTime != null">last_hit_time,</if>
            <if test="refAppPolicyId != null">ref_app_policy_id,</if>
            <if test="policyGrp != null">policy_grp,</if>
            <if test="appCompareType != null">app_compare_type,</if>
            <if test="virtualSystem != null">virtual_system,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="outDataId != null">#{outDataId},</if>
            <if test="outFirewallId != null">#{outFirewallId},</if>
            <if test="outType != null">#{outType},</if>
            <if test="policyName != null">#{policyName},</if>
            <if test="policyName0 != null">#{policyName0},</if>
            <if test="num != null">#{num},</if>
            <if test="fromZone != null">#{fromZone},</if>
            <if test="toZone != null">#{toZone},</if>
            <if test="clientIpStr != null">#{clientIpStr},</if>
            <if test="clientPortStr != null">#{clientPortStr},</if>
            <if test="clientValue != null">#{clientValue},</if>
            <if test="serverIpStr != null">#{serverIpStr},</if>
            <if test="serverPortStr != null">#{serverPortStr},</if>
            <if test="serverValue != null">#{serverValue},</if>
            <if test="serviceName != null">#{serviceName},</if>
            <if test="action != null">#{action},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="overdue != null">#{overdue},</if>
            <if test="outCreTime != null">#{outCreTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="refStatus != null">#{refStatus},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="sourceData != null">#{sourceData},</if>
            <if test="longCon != null">#{longCon},</if>
            <if test="ipVersion != null">#{ipVersion},</if>
            <if test="emptyRule != null">#{emptyRule},</if>
            <if test="times != null">#{times},</if>
            <if test="ruleHitPercent != null">#{ruleHitPercent},</if>
            <if test="serverHitPercent != null">#{serverHitPercent},</if>
            <if test="serverIpHitPercent != null">#{serverIpHitPercent},</if>
            <if test="serverPortHitPercent != null">#{serverPortHitPercent},</if>
            <if test="clientHitPercent != null">#{clientHitPercent},</if>
            <if test="firstHitTime != null">#{firstHitTime},</if>
            <if test="lastHitTime != null">#{lastHitTime},</if>
            <if test="refAppPolicyId != null">#{refAppPolicyId},</if>
            <if test="policyGrp != null">#{policyGrp},</if>
            <if test="appCompareType != null">#{appCompareType},</if>
            <if test="virtualSystem != null">#{virtualSystem},</if>
        </trim>
    </insert>

    <insert id="insertBatch">
        insert into tbl_firewall_policy
        (asset_id, out_data_id, out_firewall_id, out_type, policy_name, policy_name0, num, from_zone, to_zone, client_ip_str, client_port_str, client_value, server_ip_str, server_port_str, server_value, service_name, action, enabled, expiration_date, overdue, out_cre_time, create_time, update_time, ref_status, source_type, source_data, long_con, ip_version, empty_rule, times, rule_hit_percent, server_hit_percent, server_ip_hit_percent, server_port_hit_percent, client_hit_percent, first_hit_time, last_hit_time, ref_app_policy_id, policy_grp, app_compare_type, virtual_system)
        <foreach open="values " close="" collection="list" item="item" separator=",">
            (#{item.assetId},#{item.outDataId},#{item.outFirewallId},#{item.outType},#{item.policyName},#{item.policyName0},#{item.num},#{item.fromZone},#{item.toZone},#{item.clientIpStr},#{item.clientPortStr},#{item.clientValue},#{item.serverIpStr},#{item.serverPortStr},#{item.serverValue},#{item.serviceName},#{item.action},#{item.enabled},#{item.expirationDate},#{item.overdue},#{item.outCreTime},#{item.createTime},#{item.updateTime},#{item.refStatus},#{item.sourceType},#{item.sourceData},#{item.longCon},#{item.ipVersion},#{item.emptyRule},#{item.times},#{item.ruleHitPercent},#{item.serverHitPercent},#{item.serverIpHitPercent},#{item.serverPortHitPercent},#{item.clientHitPercent},#{item.firstHitTime},#{item.lastHitTime},#{item.refAppPolicyId},#{item.policyGrp},#{item.appCompareType},#{item.virtualSystem})
        </foreach>
    </insert>

    <update id="updateTblFirewallPolicy" parameterType="TblFirewallPolicy">
        update tbl_firewall_policy
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="outDataId != null">out_data_id = #{outDataId},</if>
            <if test="outFirewallId != null">out_firewall_id = #{outFirewallId},</if>
            <if test="outType != null">out_type = #{outType},</if>
            <if test="policyName != null">policy_name = #{policyName},</if>
            <if test="policyName0 != null">policy_name0 = #{policyName0},</if>
            <if test="num != null">num = #{num},</if>
            <if test="fromZone != null">from_zone = #{fromZone},</if>
            <if test="toZone != null">to_zone = #{toZone},</if>
            <if test="clientIpStr != null">client_ip_str = #{clientIpStr},</if>
            <if test="clientPortStr != null">client_port_str = #{clientPortStr},</if>
            <if test="clientValue != null">client_value = #{clientValue},</if>
            <if test="serverIpStr != null">server_ip_str = #{serverIpStr},</if>
            <if test="serverPortStr != null">server_port_str = #{serverPortStr},</if>
            <if test="serverValue != null">server_value = #{serverValue},</if>
            <if test="serviceName != null">service_name = #{serviceName},</if>
            <if test="action != null">action = #{action},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="overdue != null">overdue = #{overdue},</if>
            <if test="outCreTime != null">out_cre_time = #{outCreTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="refStatus != null">ref_status = #{refStatus},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="sourceData != null">source_data = #{sourceData},</if>
            <if test="longCon != null">long_con = #{longCon},</if>
            <if test="ipVersion != null">ip_version = #{ipVersion},</if>
            <if test="emptyRule != null">empty_rule = #{emptyRule},</if>
            <if test="times != null">times = #{times},</if>
            <if test="ruleHitPercent != null">rule_hit_percent = #{ruleHitPercent},</if>
            <if test="serverHitPercent != null">server_hit_percent = #{serverHitPercent},</if>
            <if test="serverIpHitPercent != null">server_ip_hit_percent = #{serverIpHitPercent},</if>
            <if test="serverPortHitPercent != null">server_port_hit_percent = #{serverPortHitPercent},</if>
            <if test="clientHitPercent != null">client_hit_percent = #{clientHitPercent},</if>
            <if test="firstHitTime != null">first_hit_time = #{firstHitTime},</if>
            <if test="lastHitTime != null">last_hit_time = #{lastHitTime},</if>
            <if test="refAppPolicyId != null">ref_app_policy_id = #{refAppPolicyId},</if>
            <if test="policyGrp != null">policy_grp = #{policyGrp},</if>
            <if test="appCompareType != null">app_compare_type = #{appCompareType},</if>
            <if test="virtualSystem != null">virtual_system = #{virtualSystem},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblFirewallPolicyById" parameterType="Long">
        delete from tbl_firewall_policy where id = #{id}
    </delete>

    <delete id="deleteTblFirewallPolicyByIds" parameterType="String">
        delete from tbl_firewall_policy where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByFirewallId">
        delete from tbl_firewall_policy where out_firewall_id = #{firewallId}
    </delete>
    <delete id="deleteByAssetIds">
        delete from tbl_firewall_policy where asset_id in
        <foreach item="assetId" collection="assetIds" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
    <delete id="deleteByFirewallIds">
        delete from tbl_firewall_policy where out_firewall_id in
        <foreach item="firewallId" collection="firewallIds" open="(" separator="," close=")">
            #{firewallId}
        </foreach>
    </delete>
    <delete id="deleteAll">
        delete from tbl_firewall_policy
    </delete>
    <delete id="deleteTblFirewallPolicyByAssetIds">
        delete from tbl_firewall_policy where asset_id in
        <foreach item="assetId" collection="assetIds" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
</mapper>
