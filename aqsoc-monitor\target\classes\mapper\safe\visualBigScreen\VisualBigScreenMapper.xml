<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.VisualBigScreenMapper">

   <!-- <resultMap id="CountByDomainMap" type="com.example.dto.ApplicationAttackDTO">
        <result column="asset_id" property="assetId"/>
        <result column="asset_name" property="assetName"/>
        <result column="event_count" property="eventCount"/>
        <result column="ip_count" property="ipCount"/>
    </resultMap>-->

    <select id="getApplicationAttackSituation" resultType="com.ruoyi.safe.vo.visualBigScreen.ApplicationAttackVO">
        SELECT
            tba.asset_id,
            tba.asset_name,
            COUNT(DISTINCT vd.id) AS event_count,
            COUNT(DISTINCT vd.dest_ip) AS ip_count
        FROM
            tbl_threaten_alarm vd
                LEFT JOIN tbl_network_ip_mac im ON (vd.src_ip = im.ipv4 OR vd.dest_ip = im.ipv4)
                LEFT JOIN tbl_application_server t3 ON t3.server_id = im.asset_id
                LEFT JOIN tbl_business_application tba ON tba.asset_id = t3.asset_id
        <where>
            tba.asset_id IS NOT NULL
            <if test="params.startTime != null and params.endTime != null">
                AND vd.update_time  BETWEEN #{params.startTime} AND #{params.endTime}
            </if>
        </where>
        GROUP BY
            tba.asset_id,
            tba.asset_name
        ORDER BY
            event_count DESC
        LIMIT 10
    </select>

    <select id="getRealTimeAttackAlert" resultType="com.ruoyi.safe.vo.visualBigScreen.RealTimeAttackAlertVO">
        select id,
               threaten_name,
               handle_state,
               update_time,
               src_ip,
               threaten_type,
               dest_ip,
               data_source
        FROM tbl_threaten_alarm
        order by update_time desc
        limit 10
    </select>

    <select id="getVulnCount" resultType="int">
        SELECT
            COALESCE(SUM(a.vuln_count), 0) AS vuln_count
        FROM (
                 SELECT COUNT(vd.id) AS vuln_count
                 FROM monitor_bss_vuln_deal vd
                 UNION ALL
                 SELECT COUNT(wvd.id) AS vuln_count
                 FROM monitor_bss_webvuln_deal wvd
             ) a
    </select>

    <select id="getAutoHandleNum" resultType="int">
        select count(distinct ip) ipfilterCount from ffsafe_ipfilter_log
    </select>


    <select id="getAttackerIPsTop5" resultType="com.ruoyi.safe.vo.visualBigScreen.AttackerIPsTop5VO">
        SELECT
            SUM(t1.alarm_num ) as srcIpCount,
            t1.src_ip as srcIp
        FROM
        `tbl_threaten_alarm` t1
        LEFT JOIN tbl_network_ip_mac nim ON nim.ipv4=t1.src_ip
        LEFT JOIN tbl_server t2 ON t2.asset_id=nim.asset_id
        LEFT JOIN sys_dept sd ON sd.dept_id=t2.dept_id
        <where>
            <if test="params.startTime != null and params.endTime != null">
                AND t1.update_time  BETWEEN #{params.startTime} AND #{params.endTime}
            </if>
        </where>
        GROUP BY
             t1.src_ip
        ORDER BY
            SUM(DISTINCT t1.alarm_num ) DESC
        LIMIT 5
    </select>

    <select id="getThreatenTypeTop5" resultType="com.ruoyi.safe.vo.visualBigScreen.ThreatenTypeTop5VO">
        select threaten_type as threatenType,
               count(threaten_type) as threatenTypeCount
        FROM tbl_threaten_alarm
        <where>
            <if test="params.startTime != null and params.endTime != null">
                AND update_time  BETWEEN #{params.startTime} AND #{params.endTime}
            </if>
        </where>
        group by threaten_type
        order by threatenTypeCount desc
        limit 5
    </select>


    <select id="getTrendOfAttackAlarmChanges" resultType="com.ruoyi.safe.vo.visualBigScreen.TrendOfAttackAlarmChangesVO">
        SELECT
            DATE_FORMAT(update_time, '%Y-%m-%d') as date,
            SUM(CASE WHEN alarm_level = 0 THEN 1 ELSE 0 END) as unknownCount,
            SUM(CASE WHEN alarm_level = 2 THEN 1 ELSE 0 END) as lowRiskCount,
            SUM(CASE WHEN alarm_level = 3 THEN 1 ELSE 0 END) as mediumRiskCount,
            SUM(CASE WHEN alarm_level = 4 THEN 1 ELSE 0 END) as highRiskCount,
            SUM(CASE WHEN alarm_level = 5 THEN 1 ELSE 0 END) as criticalCount
        FROM tbl_threaten_alarm
        WHERE update_time BETWEEN #{params.startTime} AND #{params.endTime}
        GROUP BY DATE_FORMAT(update_time, '%Y-%m-%d')
        ORDER BY date ASC
    </select>

</mapper>
