<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblComponentMapper">
    
    <resultMap type="TblComponent" id="TblComponentResult">
        <result property="componentId"    column="component_id"    />
        <result property="moduleId"    column="module_id"    />
        <result property="moduleName"    column="module_name"    />
        <result property="moduleVersion"    column="module_version"    />
        <result property="prdid"    column="prdid"    />
        <result property="procName"    column="proc_name"    />
        <result property="procType"    column="proc_type"    />
        <result property="procVersion"    column="proc_version"    />
    </resultMap>

    <sql id="selectTblComponentVo">
        select component_id, module_id, module_name, module_version, prdid, proc_name, proc_type, proc_version from tbl_component
    </sql>

    <select id="selectTblComponentList" parameterType="TblComponent" resultMap="TblComponentResult">
        <include refid="selectTblComponentVo"/>
        <where>  
            <if test="moduleName != null  and moduleName != ''"> and module_name like concat('%', #{moduleName}, '%')</if>
            <if test="moduleId != null  and moduleId != ''"> and module_id like concat('%', #{moduleId}, '%')</if>
            <if test="procName != null  and procName != ''"> and proc_name like concat('%', #{procName}, '%')</if>
            <if test="procType != null  and procType != ''"> and proc_type = #{procType}</if>
            <if test="procVersion != null  and procVersion != ''"> and proc_version = #{procVersion}</if>
        </where>
    </select>
    
    <select id="selectTblComponentByComponentId" parameterType="Long" resultMap="TblComponentResult">
        <include refid="selectTblComponentVo"/>
        where component_id = #{componentId}
    </select>
        
    <insert id="insertTblComponent" parameterType="TblComponent">
        insert into tbl_component
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="componentId != null">component_id,</if>
            <if test="moduleId != null">module_id,</if>
            <if test="moduleName != null and moduleName != ''">module_name,</if>
            <if test="moduleVersion != null">module_version,</if>
            <if test="prdid != null">prdid,</if>
            <if test="procName != null">proc_name,</if>
            <if test="procType != null">proc_type,</if>
            <if test="procVersion != null">proc_version,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="componentId != null">#{componentId},</if>
            <if test="moduleId != null">#{moduleId},</if>
            <if test="moduleName != null and moduleName != ''">#{moduleName},</if>
            <if test="moduleVersion != null">#{moduleVersion},</if>
            <if test="prdid != null">#{prdid},</if>
            <if test="procName != null">#{procName},</if>
            <if test="procType != null">#{procType},</if>
            <if test="procVersion != null">#{procVersion},</if>
         </trim>
    </insert>

    <update id="updateTblComponent" parameterType="TblComponent">
        update tbl_component
        <trim prefix="SET" suffixOverrides=",">
            <if test="moduleId != null">module_id = #{moduleId},</if>
            <if test="moduleName != null and moduleName != ''">module_name = #{moduleName},</if>
            <if test="moduleVersion != null">module_version = #{moduleVersion},</if>
            <if test="prdid != null">prdid = #{prdid},</if>
            <if test="procName != null">proc_name = #{procName},</if>
            <if test="procType != null">proc_type = #{procType},</if>
            <if test="procVersion != null">proc_version = #{procVersion},</if>
        </trim>
        where component_id = #{componentId}
    </update>

    <update id="updateTblComponentByModuleId" parameterType="TblComponent">
        update tbl_component
        <trim prefix="SET" suffixOverrides=",">
            <if test="moduleName != null and moduleName != ''">module_name = #{moduleName},</if>
            <if test="moduleVersion != null">module_version = #{moduleVersion},</if>
        </trim>
        where module_id = #{moduleId}
    </update>

    <delete id="deleteTblComponentByComponentId" parameterType="Long">
        delete from tbl_component where component_id = #{componentId}
    </delete>

    <delete id="deleteTblComponentByComponentIds" parameterType="String">
        delete from tbl_component where component_id in 
        <foreach item="componentId" collection="array" open="(" separator="," close=")">
            #{componentId}
        </foreach>
    </delete>
</mapper>