<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblEquipmentInfoMapper">

    <resultMap type="TblEquipmentInfo" id="TblEquipmentInfoResult">
        <result property="id"    column="id"    />
        <result property="equipmentName"    column="equipment_name"    />
        <result property="hashValue"    column="hash_value"    />
        <result property="equipmentNumber"    column="equipment_number"    />
        <result property="servicePlatformKey"    column="service_platform_key"    />
        <result property="servicePlatformIp"    column="service_platform_ip"    />
        <result property="serverPort"    column="server_port"    />
        <result property="registrationTime"    column="registration_time"    />
    </resultMap>

    <sql id="selectTblEquipmentInfoVo">
        select id, equipment_name, hash_value, equipment_number, service_platform_key, service_platform_ip, server_port, registration_time from tbl_equipment_info
    </sql>

    <select id="selectTblEquipmentInfoList" parameterType="TblEquipmentInfo" resultMap="TblEquipmentInfoResult">
        <include refid="selectTblEquipmentInfoVo"/>
        <where>
            <if test="equipmentName != null  and equipmentName != ''"> and equipment_name like concat('%', #{equipmentName}, '%')</if>
            <if test="hashValue != null  and hashValue != ''"> and hash_value = #{hashValue}</if>
            <if test="equipmentNumber != null  and equipmentNumber != ''"> and equipment_number = #{equipmentNumber}</if>
            <if test="servicePlatformKey != null  and servicePlatformKey != ''"> and service_platform_key = #{servicePlatformKey}</if>
            <if test="servicePlatformIp != null  and servicePlatformIp != ''"> and service_platform_ip = #{servicePlatformIp}</if>
            <if test="serverPort != null  and serverPort != ''"> and server_port = #{serverPort}</if>
            <if test="registrationTime != null "> and registration_time = #{registrationTime}</if>
        </where>
    </select>

    <select id="selectTblEquipmentInfoById" parameterType="String" resultMap="TblEquipmentInfoResult">
        <include refid="selectTblEquipmentInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectTblEquipmentInfoByIds" parameterType="String" resultMap="TblEquipmentInfoResult">
        <include refid="selectTblEquipmentInfoVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 修改插入语句包含id字段 -->
    <insert id="insertTblEquipmentInfo" parameterType="TblEquipmentInfo">
        insert into tbl_equipment_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id, <!-- 新增id字段 -->
            <if test="equipmentName != null">equipment_name,</if>
            <if test="hashValue != null">hash_value,</if>
            <if test="equipmentNumber != null">equipment_number,</if>
            <if test="servicePlatformKey != null">service_platform_key,</if>
            <if test="servicePlatformIp != null">service_platform_ip,</if>
            <if test="serverPort != null">server_port,</if>
            <if test="registrationTime != null">registration_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id}, <!-- 新增id值 -->
            <if test="equipmentName != null">#{equipmentName},</if>
            <if test="hashValue != null">#{hashValue},</if>
            <if test="equipmentNumber != null">#{equipmentNumber},</if>
            <if test="servicePlatformKey != null">#{servicePlatformKey},</if>
            <if test="servicePlatformIp != null">#{servicePlatformIp},</if>
            <if test="serverPort != null">#{serverPort},</if>
            <if test="registrationTime != null">#{registrationTime},</if>
        </trim>
    </insert>

    <update id="updateTblEquipmentInfo" parameterType="TblEquipmentInfo">
        update tbl_equipment_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="equipmentName != null">equipment_name = #{equipmentName},</if>
            <if test="hashValue != null">hash_value = #{hashValue},</if>
            <if test="equipmentNumber != null">equipment_number = #{equipmentNumber},</if>
            <if test="servicePlatformKey != null">service_platform_key = #{servicePlatformKey},</if>
            <if test="servicePlatformIp != null">service_platform_ip = #{servicePlatformIp},</if>
            <if test="serverPort != null">server_port = #{serverPort},</if>
            <if test="registrationTime != null">registration_time = #{registrationTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblEquipmentInfoById" parameterType="String">
        delete from tbl_equipment_info where id = #{id}
    </delete>

    <delete id="deleteTblEquipmentInfoByIds" parameterType="String">
        delete from tbl_equipment_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
