<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.host.mapper.AssetHostApplicationMapper">

    <select id="queryPage" resultType="com.ruoyi.monitor2.host.model.AssetHostApplicationListVO">
        select a.* from asset_host_application a 
        <where>
                <if test="query.id!=null">
                    and a.id = #{query.id}
                </if>
                <if test="query.hostId!=null">
                    and a.host_id = #{query.hostId}
                </if>
                <if test="query.app!=null and query.app!=''">
                    and a.app like concat('%', #{query.app}, '%')
                </if>
                <if test="query.category!=null and query.category!=''">
                    and a.category like concat('%', #{query.category}, '%')
                </if>
                <if test="query.cpe!=null and query.cpe!=''">
                    and a.cpe like concat('%', #{query.cpe}, '%')
                </if>
                <if test="query.edition!=null and query.edition!=''">
                    and a.edition like concat('%', #{query.edition}, '%')
                </if>
                <if test="query.path!=null and query.path!=''">
                    and a.path like concat('%', #{query.path}, '%')
                </if>
                <if test="query.vendor!=null and query.vendor!=''">
                    and a.vendor like concat('%', #{query.vendor}, '%')
                </if>
                <if test="query.version!=null and query.version!=''">
                    and a.version like concat('%', #{query.version}, '%')
                </if>
        </where>
    </select>
    <select id="queryById" resultType="com.ruoyi.monitor2.host.model.AssetHostApplicationListVO">
        select a.* from asset_host_application a 
        where a.id=#{id}
    </select>
</mapper>
