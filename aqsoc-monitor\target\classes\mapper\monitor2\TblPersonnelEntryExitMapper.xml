<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblPersonnelEntryExitMapper">

    <resultMap type="TblPersonnelEntryExit" id="TblPersonnelEntryExitResult">
        <result property="name"    column="F_name"    />
        <result property="orgName"    column="F_org_full_name"    />
        <result property="type"    column="F_type"    />
        <result property="comeTime"    column="F_time"    />
    </resultMap>

    <select id="selectPersonnelComeOutList" parameterType="TblPersonnelEntryExit" resultMap="TblPersonnelEntryExitResult">
        SELECT rp.F_name, po.F_org_full_name,  pee.F_type, pee.F_time
        FROM tbl_personnel_entry_exit pee
        LEFT JOIN tbl_resident_personnel rp ON pee.F_personnel_id = rp.F_Id
        LEFT JOIN tbl_partner_organization po ON pee.F_org_id = po.F_Id
        WHERE TO_DAYS(pee.F_time) = TO_DAYS(NOW())
        ORDER BY pee.F_time DESC
    </select>

</mapper>
