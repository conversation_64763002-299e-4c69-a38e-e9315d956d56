<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblEmpAccessMapper">
    
    <resultMap type="TblEmpAccess" id="TblEmpAccessResult">
        <result property="id"    column="id"    />
        <result property="eid"    column="eid"    />
        <result property="name"    column="name"    />
        <result property="addr"    column="addr"    />
        <result property="account"    column="account"    />
        <result property="bgtm"    column="bgtm"    />
        <result property="edtm"    column="edtm"    />
        <result property="memo"    column="memo"    />
        <result property="state"    column="state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectTblEmpAccessVo">
        select id, eid, name, addr, account, bgtm, edtm, memo, state, create_by, create_time from tbl_emp_access
    </sql>

    <select id="selectTblEmpAccessList" parameterType="TblEmpAccess" resultMap="TblEmpAccessResult">
        <include refid="selectTblEmpAccessVo"/>
        <where>  
            <if test="eid != null "> and eid = #{eid}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="addr != null  and addr != ''"> and addr = #{addr}</if>
            <if test="account != null  and account != ''"> and account = #{account}</if>
            <if test="bgtm != null "> and bgtm = #{bgtm}</if>
            <if test="edtm != null "> and edtm = #{edtm}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
        </where>
    </select>
    
    <select id="selectTblEmpAccessById" parameterType="Long" resultMap="TblEmpAccessResult">
        <include refid="selectTblEmpAccessVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblEmpAccess" parameterType="TblEmpAccess">
        insert into tbl_emp_access
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="eid != null">eid,</if>
            <if test="name != null">name,</if>
            <if test="addr != null">addr,</if>
            <if test="account != null">account,</if>
            <if test="bgtm != null">bgtm,</if>
            <if test="edtm != null">edtm,</if>
            <if test="memo != null">memo,</if>
            <if test="state != null">state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="eid != null">#{eid},</if>
            <if test="name != null">#{name},</if>
            <if test="addr != null">#{addr},</if>
            <if test="account != null">#{account},</if>
            <if test="bgtm != null">#{bgtm},</if>
            <if test="edtm != null">#{edtm},</if>
            <if test="memo != null">#{memo},</if>
            <if test="state != null">#{state},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateTblEmpAccess" parameterType="TblEmpAccess">
        update tbl_emp_access
        <trim prefix="SET" suffixOverrides=",">
            <if test="eid != null">eid = #{eid},</if>
            <if test="name != null">name = #{name},</if>
            <if test="addr != null">addr = #{addr},</if>
            <if test="account != null">account = #{account},</if>
            <if test="bgtm != null">bgtm = #{bgtm},</if>
            <if test="edtm != null">edtm = #{edtm},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblEmpAccessById" parameterType="Long">
        delete from tbl_emp_access where id = #{id}
    </delete>

    <delete id="deleteTblEmpAccessByIds" parameterType="String">
        delete from tbl_emp_access where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>