{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\machroom\\index.vue?vue&type=template&id=0bcdb722&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\machroom\\index.vue", "mtime": 1756381475352}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}