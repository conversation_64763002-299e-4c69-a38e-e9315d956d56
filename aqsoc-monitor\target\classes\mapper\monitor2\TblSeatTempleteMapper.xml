<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblSeatTempleteMapper">

    <resultMap type="TblSeatTemplete" id="TblSeatTempleteResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="x"    column="x"    />
        <result property="y"    column="y"    />
        <result property="onsiteLocationValue"    column="onsite_location_value"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="flag"    column="flag"    />
        <result property="roomSize"    column="room_size"    />
    </resultMap>

    <sql id="selectTblSeatTempleteVo">
        select id, name, x, y, onsite_location_value, create_time, update_time, flag,room_size from tbl_seat_templete
    </sql>

    <select id="selectTblSeatTempleteList" parameterType="TblSeatTemplete" resultMap="TblSeatTempleteResult">
        <include refid="selectTblSeatTempleteVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="x != null "> and x = #{x}</if>
            <if test="y != null "> and y = #{y}</if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
            <if test="onsiteLocationValue != null  and onsiteLocationValue != ''"> and onsite_location_value=#{onsiteLocationValue} </if>
            <if test="dictType != null  and dictType != ''"> and dict_type=#{dictType} </if>
        </where>
    </select>

    <select id="selectTblSeatTempleteById" parameterType="Long" resultMap="TblSeatTempleteResult">
        <include refid="selectTblSeatTempleteVo"/>
        where id = #{id}
    </select>

    <select id="selectTblSeatTempleteByIds" parameterType="Long" resultMap="TblSeatTempleteResult">
        <include refid="selectTblSeatTempleteVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblSeatTemplete" parameterType="TblSeatTemplete" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_seat_templete
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="x != null">x,</if>
            <if test="y != null">y,</if>
            <if test="onsiteLocationValue != null">onsite_location_value,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="flag != null">flag,</if>
            <if test="dictType != null">dict_type,</if>
            <if test="roomSize != null">room_size,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="x != null">#{x},</if>
            <if test="y != null">#{y},</if>
            <if test="onsiteLocationValue != null">#{onsiteLocationValue},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="flag != null">#{flag},</if>
            <if test="dictType != null">#{dictType},</if>
            <if test="roomSize != null">#{roomSize},</if>
         </trim>
    </insert>

    <update id="updateTblSeatTemplete" parameterType="TblSeatTemplete">
        update tbl_seat_templete
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="x != null">x = #{x},</if>
            <if test="y != null">y = #{y},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="dictType != null">dict_type = #{dictType},</if>
            <if test="roomSize != null">room_size = #{roomSize},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblSeatTempleteById" parameterType="Long">
        delete from tbl_seat_templete where id = #{id}
    </delete>

    <delete id="deleteTblSeatTempleteByIds" parameterType="String">
        delete from tbl_seat_templete where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectSeatTemplatePage" resultType="com.ruoyi.monitor2.domain.SeatTempleteResult" parameterType="com.ruoyi.monitor2.domain.SeatTempleteQuery">
        <!--SELECT
        st.id,
        sd.dict_label AS dictLabel,
        st.name AS name,
        st.room_size AS roomSize,
        st.x,
        st.y
        FROM
        tbl_seat_templete st
        LEFT JOIN sys_dict_type dt ON dt.dict_type=st.dict_type
        LEFT JOIN sys_dict_data sd ON dt.dict_type = sd.dict_type
        LEFT JOIN tbl_templete_user tu ON tu.templete_id = st.id
        LEFT JOIN sys_user su ON su.user_id = tu.user_id
        <where>
            <if test="dictLabel != null">
                AND sd.dict_label LIKE CONCAT('%', #{dictLabel}, '%')
            </if>
            <if test="name != null">
                AND st.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="userName != null">
                AND su.nick_name LIKE CONCAT('%', #{userName}, '%')
            </if>
        </where>
        GROUP BY st.id
        ORDER BY st.id DESC-->

        SELECT
        st.id,
        st.name AS name,
        st.room_size AS roomSize,
        st.x,
        st.y
        FROM
        tbl_seat_templete st
        LEFT JOIN tbl_templete_user tu ON tu.templete_id = st.id
        LEFT JOIN sys_user su ON su.user_id = tu.user_id
        <where>
            <if test="name != null">
                AND st.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="userName != null">
                AND su.nick_name LIKE CONCAT('%', #{userName}, '%')
            </if>
        </where>
        GROUP BY st.id
        ORDER BY st.id DESC
    </select>

    <select id="selectBindStatus" resultType="boolean" parameterType="PersonnelBindSeat">
        SELECT EXISTS
                   (SELECT 1
                    FROM tbl_seats s
                    INNER JOIN tbl_resident_personnel rp ON rp.F_Id = s.personnel_id
                    WHERE s.templete_id != #{templeteId}
                    AND s.personnel_id IN
                    <foreach item="personnelSeatBinding" collection="personnelSeatBindings" open="(" separator="," close=")">
                        #{personnelSeatBinding.personnelId}
                    </foreach>
                       ) AS bind
    </select>

    <select id="selectExistsByName" parameterType="String" resultType="boolean">
        SELECT EXISTS(SELECT 1 FROM tbl_seat_templete WHERE name= #{name}) AS count
    </select>

    <select id="selectCurrentAtSeatPersonnel" resultType="PersonnelInfo">
        <!-- 如果当天有进有出，只有进的时间大于出的时间才查询出进出记录 -->
        SELECT
        latest_entries.F_personnel_id AS personnelId,
        o.F_org_nick_name AS orgNickName
        FROM
        (
        SELECT
        pe.F_personnel_id,
        MAX(pe.F_time) AS max_in_time
        FROM
        tbl_personnel_entry_exit pe
        WHERE
        pe.F_type = 1
        AND pe.F_time BETWEEN DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00') AND DATE_FORMAT(NOW(), '%Y-%m-%d 23:59:59')
        GROUP BY
        pe.F_personnel_id
        ) AS latest_entries
        INNER JOIN tbl_resident_personnel p ON latest_entries.F_personnel_id = p.F_Id
        INNER JOIN tbl_partner_organization o ON o.F_Id = p.F_org_id
        WHERE
        p.F_templete_id = #{templeteId}
        AND latest_entries.max_in_time = (
        SELECT MAX(pe_inner.F_time)
        FROM tbl_personnel_entry_exit pe_inner
        WHERE pe_inner.F_personnel_id = latest_entries.F_personnel_id
        AND pe_inner.F_time BETWEEN DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00') AND DATE_FORMAT(NOW(), '%Y-%m-%d 23:59:59')
        )
    </select>

</mapper>
