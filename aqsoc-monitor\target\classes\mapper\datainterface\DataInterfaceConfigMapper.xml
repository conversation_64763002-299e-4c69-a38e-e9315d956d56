<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.datainterface.mapper.DataInterfaceConfigMapper">

    <resultMap type="DataInterfaceConfig" id="DataInterfaceConfigResult">
        <result property="id"    column="id"    />
        <result property="configName"    column="config_name"    />
        <result property="arrayName"    column="array_name"    />
        <result property="configDesc"    column="config_desc"    />
        <result property="srcConfig"    column="src_config"    />
        <result property="cmdKey"    column="cmd_key"    />
        <result property="dataSample"    column="data_sample"    />
        <result property="predealSample"    column="predeal_sample"    />
        <result property="decodeSample"    column="decode_sample"    />
        <result property="predealConfig"    column="predeal_config"    />
        <result property="decodeConfig"    column="decode_config"    />
        <result property="correspondConfig"    column="correspond_config"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDataInterfaceConfigVo">
        select id, config_name, array_name, config_desc, src_config, cmd_key, data_sample,predeal_sample, decode_sample, predeal_config, decode_config, correspond_config, status, create_by, create_time, update_by, update_time from data_interface_config
    </sql>

    <select id="selectDataInterfaceConfigList" parameterType="DataInterfaceConfig" resultMap="DataInterfaceConfigResult">
        <include refid="selectDataInterfaceConfigVo"/>
        <where>
            <if test="configName != null  and configName != ''"> and config_name like concat('%', #{configName}, '%')</if>
            <if test="arrayName != null  and arrayName != ''"> and array_name like concat('%', #{arrayName}, '%')</if>
            <if test="configDesc != null  and configDesc != ''"> and config_desc = #{configDesc}</if>
            <if test="srcConfig != null  and srcConfig != ''"> and src_config = #{srcConfig}</if>
            <if test="cmdKey != null  and cmdKey != ''"> and cmd_key = #{cmdKey}</if>
            <if test="dataSample != null  and dataSample != ''"> and data_sample = #{dataSample}</if>
            <if test="predealConfig != null  and predealConfig != ''"> and predeal_config = #{predealConfig}</if>
            <if test="decodeConfig != null  and decodeConfig != ''"> and decode_config = #{decodeConfig}</if>
            <if test="correspondConfig != null  and correspondConfig != ''"> and correspond_config = #{correspondConfig}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectDataInterfaceConfigById" parameterType="Long" resultMap="DataInterfaceConfigResult">
        <include refid="selectDataInterfaceConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectDataInterfaceConfigByIds" parameterType="Long" resultMap="DataInterfaceConfigResult">
        <include refid="selectDataInterfaceConfigVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertDataInterfaceConfig" parameterType="DataInterfaceConfig" useGeneratedKeys="true" keyProperty="id">
        insert into data_interface_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configName != null">config_name,</if>
            <if test="arrayName != null">array_name,</if>
            <if test="configDesc != null">config_desc,</if>
            <if test="srcConfig != null">src_config,</if>
            <if test="cmdKey != null">cmd_key,</if>
            <if test="dataSample != null">data_sample,</if>
            <if test="predealSample != null">predeal_sample,</if>
            <if test="decodeSample != null">decode_sample,</if>
            <if test="predealConfig != null">predeal_config,</if>
            <if test="decodeConfig != null">decode_config,</if>
            <if test="correspondConfig != null">correspond_config,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configName != null">#{configName},</if>
            <if test="arrayName != null">#{arrayName},</if>
            <if test="configDesc != null">#{configDesc},</if>
            <if test="srcConfig != null">#{srcConfig},</if>
            <if test="cmdKey != null">#{cmdKey},</if>
            <if test="dataSample != null">#{dataSample},</if>
            <if test="predealSample != null">#{predealSample},</if>
            <if test="decodeSample != null">#{decodeSample},</if>
            <if test="predealConfig != null">#{predealConfig},</if>
            <if test="decodeConfig != null">#{decodeConfig},</if>
            <if test="correspondConfig != null">#{correspondConfig},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDataInterfaceConfig" parameterType="DataInterfaceConfig">
        update data_interface_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configName != null">config_name = #{configName},</if>
            <if test="arrayName != null">array_name = #{arrayName},</if>
            <if test="configDesc != null">config_desc = #{configDesc},</if>
            <if test="srcConfig != null">src_config = #{srcConfig},</if>
            <if test="cmdKey != null">cmd_key = #{cmdKey},</if>
            <if test="dataSample != null">data_sample = #{dataSample},</if>
            <if test="predealSample != null">predeal_sample = #{predealSample},</if>
            <if test="decodeSample != null">decode_sample = #{decodeSample},</if>
            <if test="predealConfig != null">predeal_config = #{predealConfig},</if>
            <if test="decodeConfig != null">decode_config = #{decodeConfig},</if>
            <if test="correspondConfig != null">correspond_config = #{correspondConfig},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataInterfaceConfigById" parameterType="Long">
        delete from data_interface_config where id = #{id}
    </delete>

    <delete id="deleteDataInterfaceConfigByIds" parameterType="String">
        delete from data_interface_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getDataConfigTree" resultMap="DataInterfaceConfigResult">
        SELECT array_name FROM data_interface_config GROUP BY array_name
    </select>
</mapper>