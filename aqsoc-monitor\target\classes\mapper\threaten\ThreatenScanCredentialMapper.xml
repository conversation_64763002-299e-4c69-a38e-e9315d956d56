<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenScanCredentialMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenScanCredentialListVO">
        select a.* from threaten_scan_credential a left join threaten_alarm_origin b on a.id = b.origin_id and (b.threaten_type = 'threaten_scan_credential' or b.threaten_type = '网络扫描探测')
        <where>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.baseId!=null">
                and a.base_id = #{query.baseId}
            </if>
            <if test="query.isEncry!=null">
                and a.is_encry = #{query.isEncry}
            </if>
            <if test="query.attackNum!=null">
                and a.attack_num = #{query.attackNum}
            </if>
            <if test="query.attackDirection!=null">
                and a.attack_direction = #{query.attackDirection}
            </if>
            <if test="query.attackStage!=null">
                and a.attack_stage = #{query.attackStage}
            </if>
            <if test="query.attackResult!=null">
                and a.attack_result = #{query.attackResult}
            </if>
            <if test="query.handleAction!=null">
                and a.handle_action = #{query.handleAction}
            </if>
            <if test="query.organization!=null and query.organization!=''">
                and a.organization like concat('%', #{query.organization}, '%')
            </if>
            <if test="query.portList!=null and query.portList!=''">
                and a.port_list like concat('%', #{query.portList}, '%')
            </if>
            <if test="query.scanType!=null and query.scanType!=''">
                and a.scan_type like concat('%', #{query.scanType}, '%')
            </if>
            <if test="query.scanFreq!=null">
                and a.scan_freq = #{query.scanFreq}
            </if>
            <if test="query.returnInfo!=null and query.returnInfo!=''">
                and a.return_info like concat('%', #{query.returnInfo}, '%')
            </if>
            <if test="query.credentialInfo!=null and query.credentialInfo!=''">
                and a.credential_info like concat('%', #{query.credentialInfo}, '%')
            </if>
            <if test="query.attackTime!=null">
                and a.attack_time = #{query.attackTime}
            </if>
            <if test="query.alarmId!=null">
                and b.alarm_id = #{query.alarmId}
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenScanCredentialListVO">
        select a.* from threaten_scan_credential a 
        where a.id=#{id}
    </select>
</mapper>
