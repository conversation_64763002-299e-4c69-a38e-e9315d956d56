package com.ruoyi.ffsafe.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.ffsafe.api.domain.*;
import com.ruoyi.ffsafe.api.mapper.*;
import com.ruoyi.ffsafe.api.service.IApiResultSevice;
import com.ruoyi.ffsafe.component.FFSafeRequestComponent;
import com.ruoyi.monitor2.changting.client.FfsafeClientService;
import com.ruoyi.rabbitmq.domain.SyncMessage;
import com.ruoyi.rabbitmq.enums.DataTypeEnum;
import com.ruoyi.rabbitmq.enums.OperationTypeEnum;
import com.ruoyi.rabbitmq.service.IHandleDataSyncSender;
import com.ruoyi.safe.domain.TblNetworkIpMac;
import com.ruoyi.safe.mapper.TblNetworkIpMacMapper;
import com.ruoyi.safe.service.ITblDeductionDetailService;
import com.ruoyi.safe.task.FfSafeFlowAlarmTask;
import com.ruoyi.safe.task.TblDeductionDetailTask;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.threaten.domain.TblThreatenAlarm;
import com.ruoyi.threaten.mapper.TblThreatenAlarmMapper;
import com.ruoyi.threaten.service.IAttackDirectionService;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class ApiResultSeviceImpl implements IApiResultSevice {
    private Logger logger = LoggerFactory.getLogger(ApiResultSeviceImpl.class);

    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private TblNetworkIpMacMapper tblNetworkIpMacMapper;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ITblDeductionDetailService tblDeductionDetailService;

    @Autowired
    private IAttackDirectionService attackDirectionService;

    @Resource
    private IHandleDataSyncSender handleDataSyncSender;

    @Value("${aqsoc.rabbitmq.sync.enabled:false}")
    private boolean rabbitmqSyncEnabled;

    @Resource
    private FfSafeFlowAlarmTask ffSafeFlowAlarmTask;
    @Resource
    private TblDeductionDetailTask deductionDetailTask;
    @Resource(name = "deductionDetailTaskExecutor")
    private ExecutorService deductionDetailTaskExecutor;
    @Resource(name = "syncAttackAlarmTaskExecutor")
    private ExecutorService syncAttackAlarmTaskExecutor;

    private TblNetworkIpMac getAssetInfoByIp(String ip) {
        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
        tblNetworkIpMac.setIpv4(ip);
        tblNetworkIpMac.setMainIp("1");  // 表示主ip
        List<TblNetworkIpMac> tblNetworkIpMacList = tblNetworkIpMacMapper.selectTblNetworkIpMacList(tblNetworkIpMac);
        if ((tblNetworkIpMacList != null) && (tblNetworkIpMacList.size() > 0)) {
            return tblNetworkIpMacList.get(0);
        }

        return null;
    }

    @Override
    public boolean dealFlowDetailResult(List<FfsafeFlowDetail> ffsafeFlowDeailList, Map<String, TblThreatenAlarm>threatenAlarmMap, Date lastDataTime) {
        boolean ret = false;
        SqlSession sqlSession = null;
        try {
            TblDeviceConfig deviceConfig = FfsafeClientService.deviceConfigThreadLocal.get();
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            FfsafeFlowDetailMapper ffsafeFlowDetailMapper = sqlSession.getMapper(FfsafeFlowDetailMapper.class);
            TblThreatenAlarmMapper threatenAlarmMapper = sqlSession.getMapper(TblThreatenAlarmMapper.class);
            FfsafeInterfaceConfigMapper interfaceConfigMapper = sqlSession.getMapper(FfsafeInterfaceConfigMapper.class);

            AtomicInteger count = new AtomicInteger(0);
            SqlSession finalSqlSession = sqlSession;
            ffsafeFlowDeailList.forEach(ffsafeFlowDetail -> {
                ffsafeFlowDetail.setDeviceConfigId(deviceConfig.getId());
                ffsafeFlowDetailMapper.insertFfsafeFlowDetail(ffsafeFlowDetail);
                count.getAndIncrement();
                if(count.get() % 100 == 0){
                    finalSqlSession.flushStatements();
                    finalSqlSession.clearCache();
                }
            });

            List<TblThreatenAlarm> saveThreatenAlarmList = new ArrayList<>();
            List<TblThreatenAlarm> updateThreatenAlarmList = new ArrayList<>();
            count.set(0);
            for (Map.Entry<String, TblThreatenAlarm> entry: threatenAlarmMap.entrySet()) {
                TblThreatenAlarm threatenAlarm = entry.getValue();
                //排除已处置的告警，因为已处置的需要生成新的告警
                List<TblThreatenAlarm> threatenAlarmList = threatenAlarmMapper.getTblThreatenAlarmList(threatenAlarm);
                if(CollUtil.isNotEmpty(threatenAlarmList)){
                    threatenAlarmList.removeIf(item -> !"0".equals(item.getHandleState()));
                }
                threatenAlarm.setDeviceConfigId(deviceConfig.getId());
                if ((threatenAlarmList == null)||(threatenAlarmList.size() == 0)) {
                    // 关联资产
                    TblNetworkIpMac tblNetworkIpMac = getAssetInfoByIp(threatenAlarm.getDestIp());
                    if (tblNetworkIpMac != null) {
                        threatenAlarm.setAssetId(tblNetworkIpMac.getAssetId());
                        threatenAlarm.setDeptId(tblNetworkIpMac.getDeptId());
                    } else {
                        logger.warn("当前告警未绑定主机资产：  ip: {}",  threatenAlarm.getDestIp());
                    }
                    // 设置攻击方向
                    threatenAlarm.setAttackDirection(attackDirectionService.determineAttackDirection(threatenAlarm.getSrcIp(), threatenAlarm.getDestIp()));
                    threatenAlarmMapper.insertTblThreatenAlarm(threatenAlarm);
                    if((threatenAlarm.getAlarmLevel() != null && threatenAlarm.getAlarmLevel() > 2) || "其他/蜜罐诱捕".equals(threatenAlarm.getThreatenType())){
                        saveThreatenAlarmList.add(threatenAlarm);
                    }
                } else {
                    TblThreatenAlarm temp = threatenAlarmList.get(0);
                    temp.setAlarmNum(temp.getAlarmNum() + threatenAlarm.getAlarmNum());
                    temp.setUpdateTime(threatenAlarm.getUpdateTime());
                    // 设置攻击方向
                    temp.setAttackDirection(attackDirectionService.determineAttackDirection(temp.getSrcIp(), temp.getDestIp()));
                    threatenAlarmMapper.updateTblThreatenAlarm(temp);
                    if((temp.getAlarmLevel() != null && temp.getAlarmLevel() > 2) || "其他/蜜罐诱捕".equals(temp.getThreatenType())){
                        updateThreatenAlarmList.add(temp);
                    }
                }
                count.getAndIncrement();
                if(count.get() % 100 == 0){
                    sqlSession.flushStatements();
                    sqlSession.clearCache();
                }
            }

            FfsafeInterfaceConfig ffsafeInterfaceConfig = new FfsafeInterfaceConfig();
            ffsafeInterfaceConfig.setInterfacePath("/v2/flow-alarm-detail");
            ffsafeInterfaceConfig.setDataLastTime(lastDataTime);
            interfaceConfigMapper.updateDataLastTime(ffsafeInterfaceConfig);
            // 提交数据
            sqlSession.commit();
            ret = true;

            if(rabbitmqSyncEnabled) {
                //推送到中台
                ffsafeFlowDeailList.forEach(saveItem -> {
                    if((saveItem.getAlarmLevel() != null && saveItem.getAlarmLevel() > 2) || "其他/蜜罐诱捕".equals(saveItem.getThreatenType())){
                        SyncMessage<FfsafeFlowDetail> message = new SyncMessage<>();
                        message.setDataType(DataTypeEnum.FF_FLOW_DETAIL);
                        message.setOperationType(OperationTypeEnum.INSERT);
                        message.setData(saveItem);
                        message.setTimestamp(System.currentTimeMillis());
                        handleDataSyncSender.sendDataSync(message);
                    }
                });
                saveThreatenAlarmList.forEach(saveItem -> {
                    SyncMessage<TblThreatenAlarm> message = new SyncMessage<>();
                    message.setDataType(DataTypeEnum.BASE_THREATEN_ALARM);
                    message.setOperationType(OperationTypeEnum.INSERT);
                    message.setData(saveItem);
                    message.setTimestamp(System.currentTimeMillis());
                    handleDataSyncSender.sendDataSync(message);
                });
                updateThreatenAlarmList.forEach(saveItem -> {
                    SyncMessage<TblThreatenAlarm> message = new SyncMessage<>();
                    message.setDataType(DataTypeEnum.BASE_THREATEN_ALARM);
                    message.setOperationType(OperationTypeEnum.INSERT);
                    message.setData(saveItem);
                    message.setTimestamp(System.currentTimeMillis());
                    handleDataSyncSender.sendDataSync(message);
                });
            }

            //同步攻击者
            if(CollUtil.isNotEmpty(ffsafeFlowDeailList)){
                FfsafeFlowDetail first = ffsafeFlowDeailList.get(0);
                FfsafeFlowDetail last = ffsafeFlowDeailList.get(ffsafeFlowDeailList.size() - 1);
                /*if(redisCache.getCacheObject(Constants.FFSAFE_FLOW_ATTACK_SYNC_TIME_START_KEY) == null){
                    redisCache.setCacheObject(Constants.FFSAFE_FLOW_ATTACK_SYNC_TIME_START_KEY, last.getCreateTime().getTime());
                }

                redisCache.setCacheObject(Constants.FFSAFE_FLOW_ATTACK_SYNC_TIME_END_KEY, first.getCreateTime().getTime());*/
                syncAttackAlarmTaskExecutor.execute(() -> {
                    try {
                        FFSafeRequestComponent.deviceConfigThreadLocal.set(deviceConfig);
                        ffSafeFlowAlarmTask.syncTask(first.getCreateTime(), last.getCreateTime());
                    } finally {
                        FFSafeRequestComponent.deviceConfigThreadLocal.remove();
                    }
                });
            }
            //同步扣分记录
            if(CollUtil.isNotEmpty(saveThreatenAlarmList) || CollUtil.isNotEmpty(updateThreatenAlarmList)){
                deductionDetailTaskExecutor.execute(() -> deductionDetailTask.syncThreaten());
            }
        } catch (Exception e) {
            if (sqlSession != null)
                sqlSession.rollback();
            logger.error("非凡流量入库出错: " + e.getMessage());
        } finally {
            if (sqlSession != null)
                sqlSession.close();
        }

        return ret;
    }

    @Override
    public boolean dealIpFilterLogResult(List<FfsafeIpfilterLog> ffsafeIpfilterLogList, Date lastDateTime) {
        boolean ret = false;
        SqlSession sqlSession = null;
        try {
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            FfsafeIpfilterLogMapper ffsafeIpfilterLogMapper = sqlSession.getMapper(FfsafeIpfilterLogMapper.class);
            FfsafeInterfaceConfigMapper interfaceConfigMapper = sqlSession.getMapper(FfsafeInterfaceConfigMapper.class);

            TblDeviceConfig deviceConfig = FfsafeClientService.deviceConfigThreadLocal.get();
            AtomicInteger count = new AtomicInteger(0);
            SqlSession finalSqlSession = sqlSession;
            ffsafeIpfilterLogList.stream().forEach(ipfilterLog -> {
                if(deviceConfig != null){
                    ipfilterLog.setDeviceConfigId(deviceConfig.getId());
                }
                ffsafeIpfilterLogMapper.insertFfsafeIpfilterLog(ipfilterLog);
                count.getAndIncrement();
                if(count.get() % 100 == 0){
                    finalSqlSession.flushStatements();
                    finalSqlSession.clearCache();
                }
            });
            FfsafeInterfaceConfig ffsafeInterfaceConfig = new FfsafeInterfaceConfig();
            ffsafeInterfaceConfig.setInterfacePath("/v2/flow-bypass-filtering-log");
            ffsafeInterfaceConfig.setDataLastTime(lastDateTime);
            interfaceConfigMapper.updateDataLastTime(ffsafeInterfaceConfig);

            sqlSession.commit();
            ret = true;

            if(rabbitmqSyncEnabled) {
                ffsafeIpfilterLogList.forEach(saveItem -> {
                    SyncMessage<FfsafeIpfilterLog> message = new SyncMessage<>();
                    message.setDataType(DataTypeEnum.FF_SAFE_IP_FILTER_LOG);
                    message.setOperationType(OperationTypeEnum.INSERT);
                    message.setData(saveItem);
                    message.setTimestamp(System.currentTimeMillis());
                    handleDataSyncSender.sendDataSync(message);
                });
            }
        } catch (Exception e) {
            if (sqlSession != null)
                sqlSession.rollback();
            logger.error("非凡ip封禁日志入库出错: " + e.getMessage());
        } finally {
            if (sqlSession != null)
                sqlSession.close();
        }

        return ret;
    }

    private FfsafeInterfaceConfig getInterfaceConfig(int type, Date lastDateTime) {
        FfsafeInterfaceConfig ffsafeInterfaceConfig = new FfsafeInterfaceConfig();
        ffsafeInterfaceConfig.setInterfacePath("/v1/web-monitor-summary/detail");
        ffsafeInterfaceConfig.setDataLastTime(lastDateTime);
        ffsafeInterfaceConfig.setType(type);

        return ffsafeInterfaceConfig;
    }

    @Override
    public boolean dealWebtamperResult(int type, WebTamperDetail webTamperDetail, Date lastDateTime) {
        boolean ret = false;
        SqlSession sqlSession = null;
        try {
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            FfsafeWebmonitorTamperMapper webmonitorTamperMapper = sqlSession.getMapper(FfsafeWebmonitorTamperMapper.class);
            FfsafeInterfaceConfigMapper interfaceConfigMapper = sqlSession.getMapper(FfsafeInterfaceConfigMapper.class);

            List<WebTamperDetail.WebTamperData> webTamperDataList = webTamperDetail.getWebTamperDataList();
            AtomicInteger count = new AtomicInteger(0);
            SqlSession finalSqlSession = sqlSession;
            webTamperDataList.forEach(webTamperData -> {
                webmonitorTamperMapper.insertFfsafeWebmonitorTamper(webTamperData.toFfsafeWebmonitorTamper());
                count.getAndIncrement();
                if(count.get() % 100 == 0){
                    finalSqlSession.flushStatements();
                    finalSqlSession.clearCache();
                }
            });

            FfsafeInterfaceConfig ffsafeInterfaceConfig = getInterfaceConfig(type, lastDateTime);
            interfaceConfigMapper.updateDataLastTime(ffsafeInterfaceConfig);

            sqlSession.commit();
            ret = true;
        } catch (Exception e) {
            if (sqlSession != null)
                sqlSession.rollback();
            logger.error("非凡网页篡改结果入库出错: " + e.getMessage());
        } finally {
            if (sqlSession != null)
                sqlSession.close();
        }

        return ret;
    }

    @Override
    public boolean dealWebusableResult(int type, List<WebUsableDetail> webUsableDetailList, Date lastDateTime) {
        boolean ret = false;
        SqlSession sqlSession = null;
        try {
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            FfsafeWebmonitorUsableMapper webmonitorUsableMapper = sqlSession.getMapper(FfsafeWebmonitorUsableMapper.class);
            FfsafeInterfaceConfigMapper interfaceConfigMapper = sqlSession.getMapper(FfsafeInterfaceConfigMapper.class);

            List<FfsafeWebmonitorUsable> ffsafeWebmonitorUsableList = new ArrayList<FfsafeWebmonitorUsable>();
            for (WebUsableDetail webUsableDetail: webUsableDetailList) {
                List<FfsafeWebmonitorUsable> tempList = webUsableDetail.toFfsafeWebmonitorusableList();
                ffsafeWebmonitorUsableList.addAll(tempList);
            }

            AtomicInteger count = new AtomicInteger(0);
            SqlSession finalSqlSession = sqlSession;
            ffsafeWebmonitorUsableList.forEach(ffsafeWebmonitorUsable -> {
                webmonitorUsableMapper.insertFfsafeWebmonitorUsable(ffsafeWebmonitorUsable);
                count.getAndIncrement();
                if(count.get() % 100 == 0){
                    finalSqlSession.flushStatements();
                    finalSqlSession.clearCache();
                }
            });
            FfsafeInterfaceConfig ffsafeInterfaceConfig = getInterfaceConfig(type, lastDateTime);
            interfaceConfigMapper.updateDataLastTime(ffsafeInterfaceConfig);

            sqlSession.commit();
            ret = true;
        } catch (Exception e) {
            if (sqlSession != null)
                sqlSession.rollback();
            logger.error("非凡网页可用性结果入库出错: " + e.getMessage());
        } finally {
            if (sqlSession != null)
                sqlSession.close();
        }

        return ret;
    }

    @Override
    public boolean dealWebhijackResult(int type, List<WebHijackedDetail> webHijackedDetailList, Date lastDateTime) {
        boolean ret = false;
        SqlSession sqlSession = null;
        try {
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            FfsafeWebmonitorHijackedMapper ffsafeWebmonitorHijackedMapper = sqlSession.getMapper(FfsafeWebmonitorHijackedMapper.class);
            FfsafeInterfaceConfigMapper interfaceConfigMapper = sqlSession.getMapper(FfsafeInterfaceConfigMapper.class);

            AtomicInteger count = new AtomicInteger(0);
            SqlSession finalSqlSession = sqlSession;
            webHijackedDetailList.forEach(webHijackedDetail -> {
                ffsafeWebmonitorHijackedMapper.insertFfsafeWebmonitorHijacked(webHijackedDetail.toFfsafeWebmonitoHijacked());
                count.getAndIncrement();
                if(count.get() % 100 == 0){
                    finalSqlSession.flushStatements();
                    finalSqlSession.clearCache();
                }
            });
            FfsafeInterfaceConfig ffsafeInterfaceConfig = getInterfaceConfig(type, lastDateTime);
            interfaceConfigMapper.updateDataLastTime(ffsafeInterfaceConfig);

            sqlSession.commit();
            ret = true;
        } catch (Exception e) {
            if (sqlSession != null)
                sqlSession.rollback();
            logger.error("非凡网页域名劫持结果入库出错: " + e.getMessage());
        } finally {
            if (sqlSession != null)
                sqlSession.close();
        }

        return ret;
    }

    @Override
    public boolean dealWebSensitiveWordDetail(int type, List<WebSensitiveWordDetail> webSensitiveWordDetailList, Date lastDateTime) {
        boolean ret = false;
        SqlSession sqlSession = null;
        try {
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            FfsafeWebmonitorSensitivewordMapper ffsafeWebmonitorSensitivewordMapper = sqlSession.getMapper(FfsafeWebmonitorSensitivewordMapper.class);
            FfsafeInterfaceConfigMapper interfaceConfigMapper = sqlSession.getMapper(FfsafeInterfaceConfigMapper.class);

            List<FfsafeWebmonitorSensitiveword> ffsafeWebmonitorSensitivewordList = new ArrayList<FfsafeWebmonitorSensitiveword>();
            for (WebSensitiveWordDetail webSensitiveWordDetail: webSensitiveWordDetailList) {
                List<FfsafeWebmonitorSensitiveword> tempList = webSensitiveWordDetail.toFfsafeWebmonitorSensitivewordList();
                ffsafeWebmonitorSensitivewordList.addAll(tempList);
            }

            AtomicInteger count = new AtomicInteger(0);
            SqlSession finalSqlSession = sqlSession;
            ffsafeWebmonitorSensitivewordList.forEach(ffsafeWebmonitorSensitiveword -> {
                ffsafeWebmonitorSensitivewordMapper.insertFfsafeWebmonitorSensitiveword(ffsafeWebmonitorSensitiveword);
                count.getAndIncrement();
                if(count.get() % 100 == 0){
                    finalSqlSession.flushStatements();
                    finalSqlSession.clearCache();
                }
            });
            FfsafeInterfaceConfig ffsafeInterfaceConfig = getInterfaceConfig(type, lastDateTime);
            interfaceConfigMapper.updateDataLastTime(ffsafeInterfaceConfig);

            sqlSession.commit();
            ret = true;
        } catch (Exception e) {
            if (sqlSession != null)
                sqlSession.rollback();
            logger.error("非凡网页敏感词结果入库出错: " + e.getMessage());
        } finally {
            if (sqlSession != null)
                sqlSession.close();
        }

        return ret;
    }

    @Override
    public boolean dealWebSensitiveFileDetail(int type, List<WebSensitiveFileDetail> webSensitiveFileDetailList, Date lastDateTime) {
        boolean ret = false;
        SqlSession sqlSession = null;
        try {
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            FfsafeWebmonitorSensitivefileMapper ffsafeWebmonitorSensitivefileMapper = sqlSession.getMapper(FfsafeWebmonitorSensitivefileMapper.class);
            FfsafeInterfaceConfigMapper interfaceConfigMapper = sqlSession.getMapper(FfsafeInterfaceConfigMapper.class);

            List<FfsafeWebmonitorSensitivefile> ffsafeWebmonitorSensitivefileList = new ArrayList<FfsafeWebmonitorSensitivefile>();
            for (WebSensitiveFileDetail webSensitiveFileDetail: webSensitiveFileDetailList) {
                List<FfsafeWebmonitorSensitivefile> tempList = webSensitiveFileDetail.toFfsafeWebmonitorSensitivefileList();
                ffsafeWebmonitorSensitivefileList.addAll(tempList);
            }

            AtomicInteger count = new AtomicInteger(0);
            SqlSession finalSqlSession = sqlSession;
            ffsafeWebmonitorSensitivefileList.forEach(ffsafeWebmonitorSensitivefile -> {
                ffsafeWebmonitorSensitivefileMapper.insertFfsafeWebmonitorSensitivefile(ffsafeWebmonitorSensitivefile);
                count.getAndIncrement();
                if(count.get() % 100 == 0){
                    finalSqlSession.flushStatements();
                    finalSqlSession.clearCache();
                }
            });
            FfsafeInterfaceConfig ffsafeInterfaceConfig = getInterfaceConfig(type, lastDateTime);
            interfaceConfigMapper.updateDataLastTime(ffsafeInterfaceConfig);

            sqlSession.commit();
            ret = true;
        } catch (Exception e) {
            if (sqlSession != null)
                sqlSession.rollback();
            logger.error("非凡网页敏感文件结果入库出错: " + e.getMessage());
        } finally {
            if (sqlSession != null)
                sqlSession.close();
        }

        return ret;
    }

    @Override
    public boolean dealWebBlackchainDetail(int type, List<WebBlackChainDetail> webBlackChainDetailList, Date lastDateTime) {
        boolean ret = false;
        SqlSession sqlSession = null;
        try {
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            FfsafeWebmonitorBlackchainMapper ffsafeWebmonitorBlackchainMapper = sqlSession.getMapper(FfsafeWebmonitorBlackchainMapper.class);
            FfsafeInterfaceConfigMapper interfaceConfigMapper = sqlSession.getMapper(FfsafeInterfaceConfigMapper.class);

            AtomicInteger count = new AtomicInteger(0);
            SqlSession finalSqlSession = sqlSession;
            webBlackChainDetailList.forEach(webBlackChainDetail -> {
                ffsafeWebmonitorBlackchainMapper.insertFfsafeWebmonitorBlackchain(webBlackChainDetail.toFfsafeWebmonitorBlackchain());
                count.getAndIncrement();
                if(count.get() % 100 == 0){
                    finalSqlSession.flushStatements();
                    finalSqlSession.clearCache();
                }
            });
            FfsafeInterfaceConfig ffsafeInterfaceConfig = getInterfaceConfig(type, lastDateTime);
            interfaceConfigMapper.updateDataLastTime(ffsafeInterfaceConfig);

            sqlSession.commit();
            ret = true;
        } catch (Exception e) {
            if (sqlSession != null)
                sqlSession.rollback();
            logger.error("非凡网页黑链结果入库出错: " + e.getMessage());
        } finally {
            if (sqlSession != null)
                sqlSession.close();
        }

        return ret;
    }

    @Override
    public boolean dealFlowRiskAssetsResult(List<FfsafeFlowRiskAssets> entityList) {
        boolean ret = false;
        SqlSession sqlSession = null;
        try {
            if (entityList == null || entityList.isEmpty()) {
                return true;
            }

            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            FfsafeFlowRiskAssetsMapper flowRiskAssetsMapper = sqlSession.getMapper(FfsafeFlowRiskAssetsMapper.class);

            int count = 0;
            for (FfsafeFlowRiskAssets entity : entityList) {
                // 设置默认处置状态为未处置
                entity.setHandleState(0); // 未处置
                flowRiskAssetsMapper.insertFfsafeFlowRiskAssets(entity);
                count++;
                if (count % 100 == 0) {
                    sqlSession.flushStatements();
                    sqlSession.clearCache();
                }
            }

            sqlSession.commit();

            // 同步到其他系统
            /*if (rabbitmqSyncEnabled && entityList.size() > 0) {
                try {
                    SyncMessage<List<FfsafeFlowRiskAssets>> syncMessage = new SyncMessage<>();
                    syncMessage.setData(entityList);
                    syncMessage.setDataType(DataTypeEnum.FLOW_RISK_ASSETS);
                    syncMessage.setOperationType(OperationTypeEnum.INSERT);
                    syncMessage.setTimestamp(System.currentTimeMillis());
                    handleDataSyncSender.sendDataSync(syncMessage);
                } catch (Exception e) {
                    logger.error("同步流量风险资产数据失败", e);
                }
            }*/

            ret = true;
            logger.info("成功保存{}条流量风险资产数据", entityList.size());
        } catch (Exception e) {
            if (sqlSession != null) {
                sqlSession.rollback();
            }
            logger.error("保存流量风险资产数据失败: {}", e.getMessage());
        } finally {
            if (sqlSession != null) {
                sqlSession.close();
            }
        }
        return ret;
    }
}
