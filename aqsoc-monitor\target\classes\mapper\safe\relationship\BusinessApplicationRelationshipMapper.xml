<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.relationship.mapper.BusinessApplicationRelationshipMapper">


    <resultMap type="TblBusinessApplicationLink" id="TblBusinessApplicationLinkResult">
        <result property="applicationId"    column="application_id"    />
        <result property="applicationName"    column="application_name"    />
        <result property="linkId"    column="link_id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="linkName"    column="link_name"    />
        <result property="linkIp"    column="link_ip"    />
        <result property="linkPort"    column="link_port"    />
        <result property="linkType"    column="link_type"    />
        <result property="linkModel"    column="link_model"    />
        <result property="leaderCheck" column="leader_check"/>
        <result property="boundaryId" column="boundary_id"/>
        <result property="boundaryName" column="asset_name"/>
    </resultMap>

    <select id="applicationLinkQuery" parameterType="Long" resultMap="TblBusinessApplicationLinkResult">
        
        SELECT L.asset_id as application_id ,L.asset_name as application_name,R.*,T.asset_name
        from tbl_business_application L left join tbl_application_link R
        on L.asset_id = R.asset_id left join tbl_asset_overview T on T.asset_id=R.boundary_id where  R.asset_id
        <if test="applicationId != null">
            and  L.asset_id  = #{applicationId}
        </if>
    </select>

</mapper>