<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dict.mapper.NetworkDomainMapper">

    <resultMap type="com.ruoyi.dict.domain.NetworkDomain" id="NetworkDomainResult">
        <result property="domainId" column="domain_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="domainName" column="domain_name"/>
        <result property="domainFullName" column="domain_full_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="iparea" column="iparea"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="isLastLevel" column="is_last_level" />
        <result property="deviceConfigId" column="device_config_id" />
    </resultMap>

    <sql id="selectNetworkDomainVo">
        select n.domain_id, n.parent_id, n.ancestors, n.domain_name, n.domain_full_name, n.order_num, n.iparea, n.status, n.remark,
        n.create_by, n.create_time, n.update_by, n.update_time, n.user_id, n.dept_id,n.is_last_level, n.device_config_id,d.dept_name
        from tbl_network_domain as n
        left join sys_dept as d on d.dept_id = n.dept_id
    </sql>

    <select id="selectNetworkDomainList" parameterType="NetworkDomain" resultMap="NetworkDomainResult">
        select n.domain_id, n.parent_id, n.ancestors, n.domain_name, n.domain_full_name, n.order_num, n.iparea, n.status, n.remark,
        n.create_by, n.create_time, n.update_by, n.update_time, n.user_id, n.dept_id,n.is_last_level,n.device_config_id, d.dept_name
        from tbl_network_domain as n
        left join sys_dept as d on d.dept_id = n.dept_id
        <where>
            <if test="isLastLevel != null"> and is_last_level = #{isLastLevel}</if>
            <if test="domainId != null  and domainId != ''"> and domain_id = #{domainId}</if>
            <if test="domainName != null  and domainName != ''"> and domain_name like concat('%', #{domainName}, '%')</if>
            <if test="domainFullName != null  and domainFullName != ''"> and domain_full_name like concat('%', #{domainFullName}, '%')</if>
            <if test="deptId != null and deptId != 0"> and (n.dept_id = #{deptId} OR n.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))</if>
            <if test="deptIdList != null and deptIdList.size() > 0">
                n.dept_id in
                <foreach collection="deptIdList" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="iparea != null and iparea != ''"> and n.iparea = #{iparea}</if>
            <if test="isQueryAllData == null or isQueryAllData == false">${params.dataScope}</if>
            <if test="ids != null">n.domain_id in
                <foreach collection="ids" item="domainId" open="(" separator="," close=")">
                    #{domainId}
                </foreach>
            </if>
            <if test="deviceConfigId != null"> and n.device_config_id = #{deviceConfigId} </if>
        </where>
        order by n.parent_id, n.order_num
    </select>

    <select id="selectNetworkDomainByDomainId" parameterType="Long" resultMap="NetworkDomainResult">
        <include refid="selectNetworkDomainVo"/>
        where n.domain_id = #{domainId}
    </select>

    <select id="selectNetworkDomainByDomainIds" parameterType="string" resultMap="NetworkDomainResult">
        <include refid="selectNetworkDomainVo"/>
        <where>
        <if test="ids != null">n.domain_id in
        <foreach collection="ids" item="domainId" open="(" separator="," close=")">
            #{domainId}
        </foreach>
        </if>
            <if test="net.domainId != null  and net.domainId != ''"> and domain_id = #{net.domainId}</if>
            <if test="net.domainName != null  and net.domainName != ''"> and domain_name like concat('%', #{net.domainName}, '%')</if>
            <if test="net.domainFullName != null  and net.domainFullName != ''"> and domain_full_name like concat('%', #{net.domainFullName}, '%')</if>
            <if test="net.deptId != null and net.deptId != 0">AND (n.dept_id = #{net.deptId} OR n.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{net.deptId}, ancestors) ))</if>
        </where>
    </select>

    <select id="selectChildrenNetworkDomainById" parameterType="Long" resultMap="NetworkDomainResult">
        <include refid="selectNetworkDomainVo"/>
		where find_in_set(#{domainId}, n.ancestors)
	</select>

    <select id="selectNormalChildrenNetworkDomainById" parameterType="Long" resultType="int">
		select count(*) from tbl_network_domain where status = '0' and find_in_set(#{domainId}, ancestors)
	</select>

    <select id="hasChildById" parameterType="Long" resultType="int">
		select count(1) from tbl_network_domain
		where parent_id = #{domainId} limit 1
	</select>

    <select id="checkDomainNameUnique" resultMap="NetworkDomainResult">
        <include refid="selectNetworkDomainVo"/>
        where n.domain_name = #{domainName}
        <if test="domainId != null">
            AND n.domain_id != #{domainId}
        </if>
    </select>

    <update id="updateNetworkDomainChildren" parameterType="java.util.List">
        update tbl_network_domain set ancestors =
        <foreach collection="networkDomains" item="item" index="index"
                 separator=" " open="case domain_id" close="end">
            when #{item.domainId} then #{item.ancestors}
        </foreach>
        , domain_full_name =
        <foreach collection="networkDomains" item="item" index="index"
                 separator=" " open="case domain_id" close="end">
            when #{item.domainId} then #{item.domainFullName}
        </foreach>
        where domain_id in
        <foreach collection="networkDomains" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.domainId}
        </foreach>
    </update>

    <update id="updateNetworkDomainStatusNormal" parameterType="Long">
        update tbl_network_domain set status = '0' where domain_id in
        <foreach collection="array" item="domainId" open="(" separator="," close=")">
            #{domainId}
        </foreach>
    </update>

    <insert id="insertNetworkDomain" parameterType="NetworkDomain" useGeneratedKeys="true" keyProperty="domainId">
        insert into tbl_network_domain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="domainId != null">domain_id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="domainName != null">domain_name,</if>
            <if test="domainFullName != null">domain_full_name,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="iparea != null">iparea,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="isLastLevel != null">is_last_level,</if>
            <if test="deviceConfigId != null">device_config_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="domainId != null">#{domainId},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="domainName != null">#{domainName},</if>
            <if test="domainFullName != null">#{domainFullName},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="iparea != null">#{iparea},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="isLastLevel != null">#{isLastLevel},</if>
            <if test="deviceConfigId != null">#{deviceConfigId},</if>
        </trim>
    </insert>

    <update id="updateNetworkDomain" parameterType="NetworkDomain">
        update tbl_network_domain
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="domainName != null">domain_name = #{domainName},</if>
            <if test="domainFullName != null">domain_full_name = #{domainFullName},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="iparea != null">iparea = #{iparea},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="isLastLevel != null">is_last_level = #{isLastLevel},</if>
            <if test="deviceConfigId != null">device_config_id = #{deviceConfigId},</if>
        </trim>
        where domain_id = #{domainId}
    </update>

    <delete id="deleteNetworkDomainByDomainId" parameterType="Long">
        delete from tbl_network_domain where domain_id = #{domainId}
    </delete>

    <delete id="deleteNetworkDomainByDomainIds" parameterType="String">
        delete from tbl_network_domain where domain_id in
        <foreach item="domainId" collection="array" open="(" separator="," close=")">
            #{domainId}
        </foreach>
    </delete>
</mapper>
