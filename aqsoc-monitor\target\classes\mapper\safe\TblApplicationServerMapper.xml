<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblApplicationServerMapper">

    <resultMap type="TblApplicationServer" id="TblApplicationServerResult">
        <result property="id"    column="id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="serverId"    column="server_id"    />
        <result property="functionId"    column="function_id"    />
        <result property="prdids"    column="prdids"    />
        <result property="type"    column="type"    />
        <result property="port"    column="port"    />
    </resultMap>

    <sql id="selectTblApplicationServerVo">
        select id, asset_id, server_id, function_id, prdids,type,port from tbl_application_server
    </sql>

    <select id="selectTblApplicationServerList" parameterType="TblApplicationServer" resultMap="TblApplicationServerResult">
        <include refid="selectTblApplicationServerVo"/>
        <where>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="serverId != null "> and server_id = #{serverId}</if>
            <if test="functionId != null "> and function_id = #{functionId}</if>
            <if test="prdids != null  and prdids != ''"> and prdids = #{prdids}</if>
            <if test="type != null  and type != ''"> and `type` = #{type}</if>
            <if test="port != null  and port != ''"> and `port` = #{port}</if>
             <if test="types != null and types.size() > 0">
             and `type` in
                 <foreach item="type" collection="types" open="(" separator="," close=")">
                    #{type}
                </foreach>
             </if>
        </where>
        order by server_id desc
    </select>

    <select id="selectTblApplicationServerById" parameterType="Long" resultMap="TblApplicationServerResult">
        <include refid="selectTblApplicationServerVo"/>
        where id = #{id}
    </select>

    <select id="selectTblApplicationServerByIds" parameterType="Long" resultMap="TblApplicationServerResult">
        <include refid="selectTblApplicationServerVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblApplicationServer" parameterType="TblApplicationServer">
        insert into tbl_application_server
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="serverId != null">server_id,</if>
            <if test="functionId != null">function_id,</if>
            <if test="prdids != null">prdids,</if>
            <if test="type != null">`type`,</if>
            <if test="port != null">port,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="serverId != null">#{serverId},</if>
            <if test="functionId != null">#{functionId},</if>
            <if test="prdids != null">#{prdids},</if>
            <if test="type != null">#{type},</if>
            <if test="port != null">#{port},</if>
         </trim>
    </insert>

    <update id="updateTblApplicationServer" parameterType="TblApplicationServer">
        update tbl_application_server
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="serverId != null">server_id = #{serverId},</if>
            <if test="functionId != null">function_id = #{functionId},</if>
            <if test="prdids != null">prdids = #{prdids},</if>
            <if test="type != null">type = #{type},</if>
            <if test="port != null">port = #{port},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblApplicationServerById" parameterType="Long">
        delete from tbl_application_server where id = #{id}
    </delete>

    <delete id="deleteTblApplicationServerByIds" parameterType="String">
        delete from tbl_application_server where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTblApplicationServer" parameterType="TblApplicationServer">
        delete from tbl_application_server
        <where>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="serverId != null "> and server_id = #{serverId}</if>
            <if test="functionId != null "> and function_id = #{functionId}</if>
            <if test="prdids != null  and prdids != ''"> and prdids = #{prdids}</if>
            <if test="type != null  and type != ''"> and `type` = #{type}</if>
            <if test="port != null  and port != ''"> and `port` = #{port}</if>
        </where>
    </delete>

    <select id="selectCountNum" parameterType="TblApplicationServer" resultType="java.lang.Integer">
        select count(0) from tbl_application_server
        <where>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="type != null  and type != ''"> and `type` = #{type}</if>
        </where>
    </select>
    <select id="selectApplicationGroupIps" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t2.asset_id assetId,GROUP_CONCAT(t1.ipv4) ipv4
        FROM
            tbl_network_ip_mac t1
            INNER JOIN tbl_application_server t2 ON t2.server_id=t1.asset_id
        WHERE
            t2.type = '0' and t2.asset_id in
            <foreach item="id" collection="assetIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        GROUP BY
            t2.asset_id
    </select>

    <delete id="deleteApplicationServerByAssetIds" parameterType="Long">
        delete from tbl_application_server where asset_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
