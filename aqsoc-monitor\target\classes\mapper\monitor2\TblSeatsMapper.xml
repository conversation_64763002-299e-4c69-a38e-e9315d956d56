<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblSeatsMapper">

    <resultMap type="TblSeats" id="TblSeatsResult">
        <result property="id"    column="id"    />
        <result property="x"    column="x"    />
        <result property="y"    column="y"    />
        <result property="row"    column="row"    />
        <result property="col"    column="col"    />
        <result property="typeId"    column="type_id"    />
        <result property="flag"    column="flag"    />
        <result property="templeteId"    column="templete_id"    />
        <result property="orientation"    column="orientation"    />
        <result property="personnelId"    column="personnel_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblSeatsVo">
        select id, x, y, 'row', col, type_id, flag, templete_id, orientation, personnel_id, create_time, update_time from tbl_seats
    </sql>

    <select id="selectTblSeatsList" parameterType="TblSeats" resultMap="TblSeatsResult">
        <include refid="selectTblSeatsVo"/>
        <where>
            <if test="x != null "> and x = #{x}</if>
            <if test="y != null "> and y = #{y}</if>
            <if test="row != null  and row != ''"> and `row` = #{row}</if>
            <if test="col != null  and col != ''"> and col = #{col}</if>
            <if test="typeId != null "> and type_id = #{typeId}</if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
            <if test="templeteId != null "> and templete_id = #{templeteId}</if>
            <if test="orientation != null "> and orientation = #{orientation}</if>
        </where>
    </select>

    <select id="selectTblSeatsById" parameterType="Long" resultMap="TblSeatsResult">
        <include refid="selectTblSeatsVo"/>
        where id = #{id}
    </select>

    <select id="selectTblSeatsByIds" parameterType="Long" resultMap="TblSeatsResult">
        <include refid="selectTblSeatsVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblSeats" parameterType="TblSeats" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_seats
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="x != null">x,</if>
            <if test="y != null">y,</if>
            <if test="row != null">`row`,</if>
            <if test="col != null">col,</if>
            <if test="typeId != null">type_id,</if>
            <if test="flag != null">flag,</if>
            <if test="templeteId != null">templete_id,</if>
            <if test="orientation != null">orientation,</if>
            <if test="personnelId != null">personnel_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="x != null">#{x},</if>
            <if test="y != null">#{y},</if>
            <if test="row != null">#{row},</if>
            <if test="col != null">#{col},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="flag != null">#{flag},</if>
            <if test="templeteId != null">#{templeteId},</if>
            <if test="orientation != null">#{orientation},</if>
            <if test="personnelId != null">#{personnelId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblSeats" parameterType="TblSeats">
        update tbl_seats
        <trim prefix="SET" suffixOverrides=",">
            <if test="x != null">x = #{x},</if>
            <if test="y != null">y = #{y},</if>
            <if test="row != null">`row` = #{row},</if>
            <if test="col != null">col = #{col},</if>
            <if test="typeId != null">type_id = #{typeId},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="templeteId != null">templete_id = #{templeteId},</if>
            <if test="orientation != null">orientation = #{orientation},</if>
            <if test="personnelId != null">personnel_id = #{personnelId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblSeatsById" parameterType="Long">
        delete from tbl_seats where id = #{id}
    </delete>

    <delete id="deleteTblSeatsByIds" parameterType="String">
        delete from tbl_seats where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTblSeatsByTemplateIds" parameterType="String">
        delete from tbl_seats where templete_id in
        <foreach item="templeteId" collection="array" open="(" separator="," close=")">
            #{templeteId}
        </foreach>
    </delete>

    <select id="findByTempleteId" parameterType="Long" resultMap="TblSeatsResult">
        <include refid="selectTblSeatsVo"/>
        WHERE templete_id = #{templeteId}
    </select>

    <insert id="insertBatchSeat">
        insert into tbl_seats (x, y,`row`,col,type_id,flag,templete_id,orientation,create_time,update_time)
        values
        <foreach collection="seatList" index="index" item="item" separator=",">
            (
            #{item.x},
            #{item.y},
            #{item.row},
            #{item.col},
            #{item.typeId},
            #{item.flag},
            #{item.templeteId},
            #{item.orientation},
            #{item.createTime},
            #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 定义结果对象 -->
    <resultMap id="statisticsResultMap" type="OnsiteStatisticsResult">
        <result property="totalCount" column="totalCount"/>
        <result property="totalSurplusCount" column="totalSurplusCount"/>
        <result property="totalOnsiteCount" column="totalOnsiteCount"/>
    </resultMap>

    <select id="getTotalCounts" resultMap="statisticsResultMap" >
        SELECT
            IFNULL(SUM(hs.total_seats), 0) AS totalCount,
            IFNULL(SUM(hs.total_seats - hs.registered_count), 0) AS totalSurplusCount,
            IFNULL(SUM(hs.registered_count), 0) AS totalOnsiteCount
        FROM
            maintenance_hall_statistics hs
    </select>

    <select id="selectHallStatisticsByTempleteId" resultMap="hallStatisticsResultMap">
        SELECT
            templete_id,
            total_seats,
            unit_count,
            registered_count,
            complete_materials_count,
            current_presence_count
        FROM
            maintenance_hall_statistics
        WHERE
            templete_id = #{templeteId}
    </select>

    <!-- 如果你已经定义了resultMap，可以复用 -->
    <resultMap id="hallStatisticsResultMap" type="HallStatistics">
        <id property="templeteId" column="templete_id"/>
        <result property="totalSeats" column="total_seats"/>
        <result property="unitCount" column="unit_count"/>
        <result property="registeredCount" column="registered_count"/>
        <result property="completeMaterialsCount" column="complete_materials_count"/>
        <result property="currentPresenceCount" column="current_presence_count"/>
    </resultMap>


    <select id="findByTempleteIds" parameterType="string" resultMap="TblSeatsResult">
        <include refid="selectTblSeatsVo"/>
        WHERE templete_id IN
        <foreach collection="array" item="templeteId" open="(" separator="," close=")">
            #{templeteId}
        </foreach>
    </select>

    <update id="updatePersonnelBatch" parameterType="PersonnelBindSeat">
        UPDATE tbl_seats
        SET personnel_id = CASE id
        <foreach collection="personnelSeatBindings" item="item" index="index">
            WHEN #{item.seatId} THEN #{item.personnelId}
        </foreach>
        END,update_time = now()
        WHERE id IN
        <foreach collection="personnelSeatBindings" item="item" index="index" open="(" separator="," close=")">
            #{item.seatId}
        </foreach>
        AND templete_id = #{templeteId}
    </update>

    <select id="findSeatInfoByTempleteId" parameterType="Long" resultType="SeatInfo">
        SELECT
            s.id,
            s.x,
            s.y,
            s.ROW AS 'row',
            s.col,
            s.type_id AS typeId,
            s.flag,
            s.templete_id AS templeteId,
            s.orientation,
            p.F_Id AS personnelId,
            p.F_name AS personnelName,
            s.create_time AS createTime,
            s.update_time AS updateTime,
            o.F_org_nick_name AS orgNickName
        FROM
            tbl_seats s
                LEFT JOIN tbl_resident_personnel p ON p.F_Id = s.personnel_id
                LEFT JOIN tbl_partner_organization o on o.F_Id = p.F_org_id
        WHERE
            templete_id = #{templeteId}
    </select>

    <update id="updatePersonnelUnbind"  parameterType="Long">
        UPDATE tbl_seats
        SET personnel_id = null,update_time = now()
        WHERE templete_id = #{templeteId}
    </update>

    <update id="updatePersonnelBind" parameterType="PersonnelBindSeat">
        UPDATE tbl_resident_personnel
        <set>
            F_templete_id = #{templeteId}
        </set>
        WHERE F_Id IN
        <foreach collection="personnelSeatBindings" item="item" open="(" separator="," close=")">
            #{item.personnelId}
        </foreach>
    </update>
</mapper>
