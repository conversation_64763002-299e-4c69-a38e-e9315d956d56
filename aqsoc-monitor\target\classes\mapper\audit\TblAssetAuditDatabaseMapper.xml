<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.audit.mapper.TblAssetAuditDatabaseMapper">
    
    <resultMap type="TblAssetAuditDatabase" id="TblAssetAuditDatabaseResult">
        <result property="id"    column="id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="databaseProduct"    column="database_product"    />
        <result property="databaseIp"    column="database_ip"    />
        <result property="databasePost"    column="database_post"    />
        <result property="databaseSid"    column="database_sid"    />
        <result property="databaseDbName"    column="database_db_name"    />
        <result property="databaseUsername"    column="database_username"    />
        <result property="databasePassword"    column="database_password"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <sql id="selectTblAssetAuditDatabaseVo">
        select id, asset_id, database_product, database_ip, database_post, database_sid, database_db_name, database_username, database_password, remark, create_time, create_by, update_time, update_by, user_id, dept_id from tbl_asset_audit_database
    </sql>

    <select id="selectTblAssetAuditDatabaseList" parameterType="TblAssetAuditDatabase" resultMap="TblAssetAuditDatabaseResult">
        <include refid="selectTblAssetAuditDatabaseVo"/>
        <where>  
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="databaseProduct != null  and databaseProduct != ''"> and database_product = #{databaseProduct}</if>
            <if test="databaseIp != null  and databaseIp != ''"> and database_ip = #{databaseIp}</if>
            <if test="databasePost != null  and databasePost != ''"> and database_post = #{databasePost}</if>
            <if test="databaseSid != null  and databaseSid != ''"> and database_sid = #{databaseSid}</if>
            <if test="databaseDbName != null  and databaseDbName != ''"> and database_db_name like concat('%', #{databaseDbName}, '%')</if>
            <if test="databaseUsername != null  and databaseUsername != ''"> and database_username like concat('%', #{databaseUsername}, '%')</if>
            <if test="databasePassword != null  and databasePassword != ''"> and database_password = #{databasePassword}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
        </where>
    </select>
    
    <select id="selectTblAssetAuditDatabaseById" parameterType="Long" resultMap="TblAssetAuditDatabaseResult">
        <include refid="selectTblAssetAuditDatabaseVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblAssetAuditDatabase" parameterType="TblAssetAuditDatabase">
        insert into tbl_asset_audit_database
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="databaseProduct != null">database_product,</if>
            <if test="databaseIp != null">database_ip,</if>
            <if test="databasePost != null">database_post,</if>
            <if test="databaseSid != null">database_sid,</if>
            <if test="databaseDbName != null">database_db_name,</if>
            <if test="databaseUsername != null">database_username,</if>
            <if test="databasePassword != null">database_password,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="databaseProduct != null">#{databaseProduct},</if>
            <if test="databaseIp != null">#{databaseIp},</if>
            <if test="databasePost != null">#{databasePost},</if>
            <if test="databaseSid != null">#{databaseSid},</if>
            <if test="databaseDbName != null">#{databaseDbName},</if>
            <if test="databaseUsername != null">#{databaseUsername},</if>
            <if test="databasePassword != null">#{databasePassword},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>

    <update id="updateTblAssetAuditDatabase" parameterType="TblAssetAuditDatabase">
        update tbl_asset_audit_database
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="databaseProduct != null">database_product = #{databaseProduct},</if>
            <if test="databaseIp != null">database_ip = #{databaseIp},</if>
            <if test="databasePost != null">database_post = #{databasePost},</if>
            <if test="databaseSid != null">database_sid = #{databaseSid},</if>
            <if test="databaseDbName != null">database_db_name = #{databaseDbName},</if>
            <if test="databaseUsername != null">database_username = #{databaseUsername},</if>
            <if test="databasePassword != null">database_password = #{databasePassword},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblAssetAuditDatabaseById" parameterType="Long">
        delete from tbl_asset_audit_database where id = #{id}
    </delete>

    <delete id="deleteTblAssetAuditDatabaseByIds" parameterType="String">
        delete from tbl_asset_audit_database where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>