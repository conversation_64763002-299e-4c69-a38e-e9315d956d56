<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblServerMapper">

    <resultMap type="com.ruoyi.safe.domain.TblServer" id="TblServerResult">
        <result property="assetId" column="asset_id"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="isVirtual" column="is_virtual"/>
        <result property="baseAsset" column="base_asset"/>
        <result property="baseAssetName" column="base_asset_name"/>
        <result property="vendor" column="vendor"/>
        <result property="degreeImportance" column="degree_importance"/>
        <result property="assetType" column="asset_type"/>
        <result property="assetTypeDesc" column="asset_type_desc"/>
        <result property="assetClass" column="asset_class"/>
        <result property="assetClassDesc" column="asset_class_desc"/>
        <result property="details" column="details"/>
        <result property="tags" column="tags"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="orgnId" column="orgn_id"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="mangerName" column="manger_name"/>
        <result property="ip" column="ip"/>
        <result property="mac" column="mac"/>
        <result property="domainId" column="domain_id"/>
        <result property="facilityManufacturer" column="facility_manufacturer"/>
        <result property="facilityType" column="facility_type"/>
        <result property="maintainUnit" column="maintain_unit"/>
        <result property="exposedIp" column="exposed_ip"/>
        <result property="serial" column="serial"/>
        <result property="cpuFrame" column="cpu_frame"/>
        <result property="cwHostId" column="cw_host_id"/>
        <result property="tags" column="tags"/>
        <result property="locationId" column="location_id"/>
        <result property="locationDetail" column="location_detail"/>
        <result property="locationFullName" column="location_full_name"/>
        <result property="domainId" column="domain_id"/>
        <result property="domainName" column="domain_name"/>
        <result property="laseScanState" column="last_scan_state"/>
        <result property="lastScanTime" column="last_scan_time"/>
        <result property="vulnNum" column="vuln_num"/>
        <result property="vnlnUpdateTime" column="vuln_update_time"/>
        <result property="state" column="state"/>
        <result property="uodTime" column="uod_time"/>
        <result property="hostName" column="host_name"/>
        <result property="isSparing" column="is_sparing"/>
        <association property="optsystem" column="opt_id" javaType="TblDeploy" resultMap="optResult"/>
        <association property="dbsystem" column="db_id" javaType="TblDeploy" resultMap="dbResult"/>
        <association property="mdsystem" column="md_id" javaType="TblDeploy" resultMap="mdResult"/>
    </resultMap>
    <resultMap id="optResult" type="TblDeploy">
        <id property="deployId" column="opt_id"/>
        <result property="procName" column="opt_name"/>
        <result property="prdid" column="opt_prdid"/>
    </resultMap>
    <resultMap id="dbResult" type="TblDeploy">
        <id property="deployId" column="db_id"/>
        <result property="procName" column="db_name"/>
        <result property="prdid" column="db_prdid"/>
    </resultMap>
    <resultMap id="mdResult" type="TblDeploy">
        <id property="deployId" column="md_id"/>
        <result property="procName" column="md_name"/>
        <result property="prdid" column="md_prdid"/>
    </resultMap>

    <sql id="selectTblServerVo">
        select a.asset_id,
               a.asset_code,
               a.asset_name,
               a.is_virtual,
               a.vendor,
               a.manger,
               a.degree_importance,
               a.asset_type,
               a.asset_type_desc,
               a.asset_class,
               a.asset_class_desc,
               a.details,
               a.tags,
               a.remark,
               a.create_by,
               a.create_time,
               a.update_by,
               a.update_time,
               a.user_id,
               a.dept_id,
               a.orgn_id,
               a.base_asset,
               a.facility_manufacturer,
               a.facility_type,
               a.cpu_Frame,
               a.maintain_unit,
               a.cw_host_id,
               a.exposed_ip,
                  a.serial,
               b.user_name,
               c.dept_name,
               d.vendor_name,
               e.nick_name  as manger_name,
               f.deploy_id  as opt_id,
               f.prdid      as opt_prdid,
               f.proc_name  as opt_name,
               g.deploy_id  as db_id,
               g.prdid      as db_prdid,
               g.proc_name  as db_name,
               h.deploy_id  as md_id,
               h.prdid      as md_prdid,
               h.proc_name  as md_name,
               i.asset_name as base_asset_name,
               a.host_name,a.is_sparing
        from tbl_server a
        left join sys_user b on a.user_id=b.user_id
        left join sys_dept c on a.dept_id = c.dept_id
        left join tbl_vendor d on a.vendor = d.id
        left join sys_user e on a.manger= e.user_id
        left join tbl_deploy f on a.asset_id=f.asset_id and f.application_id is null and f.moduel_id is null and f.softlx='1'
        left join tbl_deploy g on a.asset_id=g.asset_id and g.application_id is null and g.moduel_id is null and g.softlx='2'
        left join tbl_deploy h on a.asset_id=h.asset_id and h.application_id is null and h.moduel_id is null and h.softlx='3'
        left join tbl_server i on a.base_asset = i.asset_id
    </sql>

    <sql id="selectTblServerVo2">
        select a.asset_id,
               a.asset_code,
               a.asset_name,
               a.is_virtual,
               a.vendor,
               a.manger,
               a.degree_importance,
               a.asset_type,
               a.asset_type_desc,
               a.asset_class,
               a.asset_class_desc,
               a.details,
               a.tags,
               a.remark,
               a.create_by,
               a.create_time,
               a.update_by,
               a.update_time,
               a.user_id,
               a.dept_id,
               a.orgn_id,
               a.base_asset,
               a.facility_manufacturer,
               a.facility_type,
               a.maintain_unit,
               a.cpu_Frame,
                  a.serial,
               net.ipv4 as ip,
               net.mac,
               net.domain_id
        from tbl_server a
        left join tbl_network_ip_mac net on net.asset_id = a.asset_id and main_ip = '1'
    </sql>

<!--    <select id="selectTblServerList" parameterType="TblServer" resultMap="TblServerResult">-->
<!--        <include refid="selectTblServerVo2"/>-->
<!--        <where>-->
<!--            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>-->
<!--            <if test="assetCode != null  and assetCode != ''"> and a.asset_code = #{assetCode}</if>-->
<!--            <if test="assetName != null  and assetName != ''"> and a.asset_name like concat('%', #{assetName}, '%')</if>-->
<!--            <if test="isVirtual != null  and isVirtual != ''"> and a.is_virtual = #{isVirtual}</if>-->
<!--            <if test="assetType != null  and assetType != ''"> and a.asset_type = #{assetType}</if>-->
<!--            <if test="degreeImportance != null  and degreeImportance != ''"> and a.degree_importance = #{degreeImportance}</if>-->
<!--            <if test="domainId != null">and domain_id = #{domainId}</if>-->
<!--            <if test="tags != null">and tags = #{tags}</if>-->
<!--            <if test="deptId != null and deptId != 0">-->
<!--                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))-->
<!--            </if>-->
<!--            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>-->
<!--        </where>-->
<!--    </select>-->
    <select id="selectTblServerList" parameterType="TblServer" resultMap="TblServerResult">
        select DISTINCT a.asset_id,a.asset_code,a.asset_name,a.is_virtual,a.vendor,a.manger,a.degree_importance,a.asset_type,a.asset_type_desc,a.asset_class,a.asset_class_desc,a.details,a.tags,a.remark,a.create_by,a.create_time,
        a.update_by,a.update_time,a.user_id,a.dept_id,a.orgn_id,a.base_asset,a.facility_manufacturer,a.facility_type,a.maintain_unit,a.cpu_Frame,a.cw_host_id,a.exposed_ip,a.serial,
        e.tags as tags2, e.location_id, e.location_detail,e.uod_time, e.state,
        f.location_full_name,
        g.ipv4 as ip, g.mac, g.domain_id,g.last_scan_state,g.last_scan_time,g.vuln_num,g.vuln_update_time,a.host_name,a.is_sparing
        from tbl_server a
        left join  tbl_asset_overview as e on a.asset_id = e.asset_id
        left join tbl_location as f on f.location_id = e.location_id
        left join tbl_network_ip_mac as g on g.asset_id = e.asset_id and g.main_ip='1'
        LEFT JOIN tbl_application_server t2 ON t2.server_id=a.asset_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id=t2.asset_id
        LEFT JOIN tbl_deploy td ON td.asset_id = a.asset_id
        <where>
            <if test="userId != null and userId != ''">and a.user_id = #{userId}</if>
            <if test="domainId != null and domainId != ''">and g.domain_id = #{domainId}</if>
            <if test="domainIdsList != null and domainIdsList.size() > 0">g.domain_id in
                <foreach collection="domainIdsList" item="domainId" open="(" separator="," close=")">
                    #{domainId}
                </foreach>
            </if>
            <if test="procName != null and procName != ''">and td.proc_name = #{procName}</if>
            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>
            <if test="assetCode != null  and assetCode != ''"> and a.asset_code = #{assetCode}</if>
            <if test="assetName != null  and assetName != ''"> and a.asset_name like concat('%', #{assetName}, '%')</if>
            <if test="isVirtual != null  and isVirtual != ''"> and a.is_virtual = #{isVirtual}</if>
            <if test="assetType != null  and assetType != ''"> and a.asset_type = #{assetType}</if>
            <if test="degreeImportance != null  and degreeImportance != ''"> and a.degree_importance = #{degreeImportance}</if>
            <!--<if test="domainId != null">and domain_id = #{domainId}</if>-->
            <if test="tags != null">and a.tags = #{tags}</if>
            <if test="deptId != null and deptId != 100">
                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="params.dataScope != null and params.dataScope != ''">
                ${params.dataScope}
            </if>
            <if test="ip != null "> and g.ipv4 = #{ip}</if>
            <if test="state != null "> and ((g.last_scan_state is not null And g.last_scan_state = #{state}) or (g.last_scan_state is null And e.state = #{state}))</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
            <if test="ips != null and ips.size() > 0">
                and g.ipv4 in
                <foreach collection="ips" item="ip" open="(" separator="," close=")">
                    #{ip}
                </foreach>
            </if>
            <if test="applicationId != null">
                and tba.asset_id=#{applicationId}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and
                <foreach item="deptId" collection="deptIds" open="(" close=")" separator=" OR ">
                    a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) )
                </foreach>
            </if>
        </where>
        order by a.update_time desc
    </select>

    <select id="selectTblServerByAssetId" parameterType="Long" resultMap="TblServerResult">
        <include refid="selectTblServerVo"/>
        where a.asset_id = #{assetId}
    </select>

    <select id="selectTblServerByAssetIds" parameterType="Long" resultMap="TblServerResult">
        <include refid="selectTblServerVo"/>
        where a.asset_id in
        <foreach item="assetId" collection="array" open="(" close=")" separator=",">
            #{assetId}
        </foreach>
    </select>

    <insert id="insertTblServer" parameterType="TblServer">
        insert into tbl_server
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="assetCode != null">asset_code,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="isVirtual != null">is_virtual,</if>
            <if test="baseAsset != null">base_asset,</if>
            <if test="vendor != null">vendor,</if>
            <if test="manger != null">manger,</if>
            <if test="degreeImportance != null">degree_importance,</if>
            <if test="assetType != null">asset_type,</if>
            <if test="assetTypeDesc != null">asset_type_desc,</if>
            <if test="assetClass != null and assetClass != ''">asset_class,</if>
            <if test="assetClassDesc != null">asset_class_desc,</if>
            <if test="details != null">details,</if>
            <if test="tags != null">tags,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="orgnId != null">orgn_id,</if>
            <if test="facilityManufacturer != null">facility_manufacturer,</if>
            <if test="facilityType != null">facility_type,</if>
            <if test="maintainUnit != null">maintain_unit,</if>
            <if test="cpuFrame != null">cpu_frame,</if>
            <if test="cwHostId != null">cw_host_id,</if>
            <if test="exposedIp != null">exposed_ip,</if>
            <if test="serial != null">serial,</if>
            <if test="hostName != null">host_name,</if>
            <if test="isSparing != null">is_sparing,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="assetCode != null">#{assetCode},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="isVirtual != null">#{isVirtual},</if>
            <if test="baseAsset != null">#{baseAsset},</if>
            <if test="vendor != null">#{vendor},</if>
            <if test="manger != null">#{manger},</if>
            <if test="degreeImportance != null">#{degreeImportance},</if>
            <if test="assetType != null">#{assetType},</if>
            <if test="assetTypeDesc != null">#{assetTypeDesc},</if>
            <if test="assetClass != null and assetClass != ''">#{assetClass},</if>
            <if test="assetClassDesc != null">#{assetClassDesc},</if>
            <if test="details != null">#{details},</if>
            <if test="tags != null">#{tags},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="orgnId != null">#{orgnId},</if>
            <if test="facilityManufacturer != null">#{facilityManufacturer},</if>
            <if test="facilityType != null">#{facilityType},</if>
            <if test="maintainUnit != null">#{maintainUnit},</if>
            <if test="cpuFrame != null">#{cpuFrame},</if>
            <if test="cwHostId != null">#{cwHostId},</if>
            <if test="exposedIp != null">#{exposedIp},</if>
            <if test="serial != null">#{serial},</if>
            <if test="hostName != null">#{hostName},</if>
            <if test="isSparing != null">#{isSparing},</if>
         </trim>
    </insert>

    <update id="updateTblServer" parameterType="TblServer">
        update tbl_server
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="isVirtual != null">is_virtual = #{isVirtual},</if>
            <if test="baseAsset != null">base_asset = #{baseAsset},</if>
            <if test="vendor != null">vendor = #{vendor},</if>
            <if test="manger != null">manger = #{manger},</if>
            <if test="degreeImportance != null">degree_importance = #{degreeImportance},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="assetTypeDesc != null">asset_type_desc = #{assetTypeDesc},</if>
            <if test="assetClass != null and assetClass != ''">asset_class = #{assetClass},</if>
            <if test="assetClassDesc != null">asset_class_desc = #{assetClassDesc},</if>
            <if test="details != null">details = #{details},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="orgnId != null">orgn_id = #{orgnId},</if>
            <if test="facilityManufacturer != null">facility_manufacturer = #{facilityManufacturer},</if>
            <if test="facilityType != null">facility_type = #{facilityType},</if>
            <if test="maintainUnit != null">maintain_unit = #{maintainUnit},</if>
            <if test="cpuFrame != null">cpu_frame = #{cpuFrame},</if>
            <if test="cwHostId != null">cw_host_id = #{cwHostId},</if>
            <if test="exposedIp != null">exposed_ip = #{exposedIp},</if>
            <if test="serial != null">serial = #{serial},</if>
            <if test="hostName != null">host_name = #{hostName},</if>
            <if test="isSparing != null">is_sparing = #{isSparing},</if>
        </trim>
        where asset_id = #{assetId}
    </update>

    <update id="updateTblServerFeifan">
          update tbl_server set serial = null
    </update>
    <delete id="deleteTblServerByAssetId" parameterType="Long">
        delete from tbl_server where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblServerByAssetIds" parameterType="String">
        delete from tbl_server where asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>

    <resultMap type="com.ruoyi.safe.domain.TblServer" id="TblServerResult2">
        <result property="assetId" column="asset_id"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="isVirtual" column="is_virtual"/>
        <result property="baseAsset" column="base_asset"/>
        <result property="baseAssetName" column="base_asset_name"/>
        <result property="vendor" column="vendor"/>
        <result property="degreeImportance" column="degree_importance"/>
        <result property="assetType" column="asset_type"/>
        <result property="assetTypeDesc" column="asset_type_desc"/>
        <result property="assetClass" column="asset_class"/>
        <result property="assetClassDesc" column="asset_class_desc"/>
        <result property="details" column="details"/>
        <result property="tags" column="tags"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="orgnId" column="orgn_id"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="mangerName" column="manger_name"/>
        <result property="ip" column="ip"/>
        <result property="mac" column="mac"/>
        <result property="domainId" column="domain_id"/>
        <result property="facilityManufacturer" column="facility_manufacturer"/>
        <result property="facilityType" column="facility_type"/>
        <result property="maintainUnit" column="maintain_unit"/>
        <result property="cpuFrame" column="cpu_frame"/>
        <result property="serial" column="serial"/>
    </resultMap>
    <select id="assetSelectByServer" parameterType="hashmap" resultMap="TblServerResult2">
        <if test="applicationId != null">
            select server.*,tba.asset_name as applicationName,tnim.ipv4 as ip
            from tbl_server server
            left join (SELECT asset_id,server_id,`port` FROM tbl_application_server group by server_id,asset_id)as tas on server.asset_id = tas.server_id
            left join tbl_business_application tba on tas.asset_id = tba.asset_id
            left join tbl_network_ip_mac tnim on server.asset_id = tnim.asset_id and tnim.main_ip = '1'
            <where>
                <if test="assetId!= null">
                    and server.asset_id = #{assetId}
                </if>
                <if test="deptId != null and deptId != 0">
                    and (server.dept_id = #{deptId} or server.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
                </if>
                <if test="assetName != null and assetName != ''">
                    and server.asset_name like concat('%', #{assetName}, '%')
                </if>
                <if test="ip != null and ip != ''">
                    and tnim.ipv4 = #{ip}
                </if>
                <if test="applicationId!= null">
                    and tba.asset_id = #{applicationId}
                </if>
            </where>
        </if>
    </select>
    <select id="assetSelectByServer2" parameterType="hashmap" resultMap="TblServerResult2">
        select server.*,group_concat(tba.asset_name) as applicationName,tnim.ipv4 as ip
        from tbl_server server
        left join (SELECT asset_id,server_id,`port` FROM tbl_application_server group by server_id,asset_id)as tas on server.asset_id = tas.server_id
        left join tbl_business_application tba on tas.asset_id = tba.asset_id
        left join tbl_network_ip_mac tnim on server.asset_id = tnim.asset_id
        <where>
            <if test="assetId!= null">
                and server.asset_id = #{assetId}
            </if>
            <if test="deptId != null and deptId != 0">
                and (server.dept_id = #{deptId} or server.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="assetName != null and assetName != ''">
                and server.asset_name like concat('%', #{assetName}, '%')
            </if>
            <if test="ip != null and ip != ''">
                and tnim.ipv4 = #{ip}
            </if>
        </where>
        group by server.asset_id
    </select>

    <select id="selectTblServerLocationIdIsNotNull" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            a.asset_id assetId,
            a.asset_name assetName,
            a.asset_type_desc deviceType,
            e.location_id locationId,
            IF(e.state = 0,'离线','在线') state,
            d.vendor_phone vendorPhone,
            IF(d.vendor_name != '',d.vendor_name,'') vendorName,
            c.dept_name deptName,
            g.ipv4 AS ipAddr,
            g.last_scan_state lastScanState
        FROM
            tbl_server a
                LEFT JOIN tbl_asset_overview AS e ON a.asset_id = e.asset_id
                LEFT JOIN tbl_location AS f ON f.location_id = e.location_id
                LEFT JOIN tbl_vendor d ON a.vendor = d.id
                LEFT JOIN sys_dept c ON a.dept_id = c.dept_id
                LEFT JOIN tbl_network_ip_mac AS g ON g.asset_id = e.asset_id
                AND g.main_ip = '1'
        WHERE
            e.location_id != ''
    </select>

    <select id="unionAssetListByCondition" resultType="com.alibaba.fastjson2.JSONObject">
        select server.asset_id assetId,tba.asset_name as applicationName,tnim.ipv4 as ip,0 as assetType
        from tbl_server server
        left join (SELECT asset_id,server_id,`port` FROM tbl_application_server group by server_id,asset_id)as tas on server.asset_id = tas.server_id
        left join tbl_business_application tba on tas.asset_id = tba.asset_id
        left join tbl_network_ip_mac tnim on server.asset_id = tnim.asset_id and tnim.main_ip = '1'
        <where>
            <if test="params.applicationId != null">
                and tba.asset_id = #{params.applicationId}
            </if>
            <if test="params.ip != null and params.ip != ''">
                <if test="params.eventType == null">
                    and tnim.ipv4 = #{params.ip}
                </if>
                <if test="params.eventType != null and params.eventType == 2">
                    and tba.url like concat('%', #{params.ip}, '%')
                </if>
                <if test="params.eventType != null and params.eventType != 2">
                    and tnim.ipv4 = #{params.ip}
                </if>
            </if>
        </where>
        union all
        select tnd.asset_id assetId,tba.asset_name as applicationName,tnim.ipv4 as ip,1 as assetType
        from tbl_network_devices tnd
        left join tbl_application_netware tas on tnd.asset_id = tas.netware_id
        left join tbl_business_application tba on tas.asset_id = tba.asset_id
        left join tbl_network_ip_mac tnim on tnd.asset_id = tnim.asset_id and tnim.main_ip = '1'
        <where>
            <if test="params.applicationId!= null">
                and tba.asset_id = #{params.applicationId}
            </if>
            <if test="params.ip != null and params.ip != ''">
                <if test="params.eventType == null">
                    and tnim.ipv4 = #{params.ip}
                </if>
                <if test="params.eventType != null and params.eventType == 2">
                    and tba.url like concat('%', #{params.ip}, '%')
                </if>
                <if test="params.eventType != null and params.eventType != 2">
                    and tnim.ipv4 = #{params.ip}
                </if>
            </if>
        </where>
        union all
        select safety.asset_id assetId,tba.asset_name as applicationName,tnim.ipv4 as ip,2 as assetType
        from tbl_safety safety
        left join tbl_application_safe tas on safety.asset_id = tas.safe_id
        left join tbl_business_application tba on tas.asset_id = tba.asset_id
        left join tbl_network_ip_mac tnim on safety.asset_id = tnim.asset_id and tnim.main_ip = '1'
        <where>
            <if test="params.applicationId!= null">
                and tba.asset_id = #{params.applicationId}
            </if>
            <if test="params.ip != null and params.ip != ''">
                <if test="params.eventType == null">
                    and tnim.ipv4 = #{params.ip}
                </if>
                <if test="params.eventType != null and params.eventType == 2">
                    and tba.url like concat('%', #{params.ip}, '%')
                </if>
                <if test="params.eventType != null and params.eventType != 2">
                    and tnim.ipv4 = #{params.ip}
                </if>
            </if>
        </where>
        order by assetType
    </select>
    <select id="selectTblServerByHostId" resultType="com.ruoyi.safe.domain.TblServer">
        select * from tbl_server where cw_host_id=#{hostId}
    </select>
    <select id="countNum" resultType="java.lang.Integer">
        select count(1) from tbl_server
    </select>

    <select id="selectTblServerBySerial"  resultMap="TblServerResult">
        select * from tbl_server
        where serial is not null
    </select>

    <resultMap id="DeptServerCountMap" type="com.ruoyi.safe.vo.DeptServerCount">
        <result property="deptId" column="deptId"/>
        <result property="ancestors" column="ancestors"/>
        <result property="serverCount" column="serverCount"/>
    </resultMap>

    <select id="getDeptServerCount" parameterType="QueryDeptServerCountDto" resultMap="DeptServerCountMap">
        SELECT
        sd.dept_id AS deptId,sd.ancestors,
        <choose>
            <when test="params.userId != null">
                COALESCE(SUM(CASE WHEN a.user_id = #{params.userId} THEN 1 ELSE 0 END), 0)
            </when>
            <otherwise>
                COALESCE(COUNT(distinct a.asset_id), 0)
            </otherwise>
        </choose>
        AS serverCount
        FROM sys_dept sd
        LEFT JOIN tbl_server a ON a.dept_id = sd.dept_id <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        left join  tbl_asset_overview as e on a.asset_id = e.asset_id
        left join tbl_location as f on f.location_id = e.location_id
        left join tbl_network_ip_mac as g on g.asset_id = e.asset_id and g.main_ip='1'
        LEFT JOIN tbl_application_server t2 ON t2.server_id=a.asset_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id=t2.asset_id
        LEFT JOIN tbl_deploy td ON td.asset_id = a.asset_id
        WHERE sd.dept_id IN
        <foreach collection="deptIdList" item="deptId" separator="," close=")" open="(">
            #{deptId}
        </foreach>
        GROUP BY sd.dept_id
    </select>
</mapper>
