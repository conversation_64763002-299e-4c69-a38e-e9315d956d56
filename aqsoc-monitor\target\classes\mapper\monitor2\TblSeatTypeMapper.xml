<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblSeatTypeMapper">

    <resultMap type="TblSeatType" id="TblSeatTypeResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="name"    column="name"    />
        <result property="icon"    column="icon"    />
        <result property="isShow"    column="is_show"    />
        <result property="position"    column="position"    />
    </resultMap>

    <sql id="selectTblSeatTypeVo">
        select id, type, name, icon, is_show, position from tbl_seat_type
    </sql>

    <select id="selectTblSeatTypeList" parameterType="TblSeatType" resultMap="TblSeatTypeResult">
        <include refid="selectTblSeatTypeVo"/>
        <where>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="icon != null  and icon != ''"> and icon = #{icon}</if>
            <if test="isShow != null  and isShow != ''"> and is_show = #{isShow}</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
        </where>
    </select>

    <select id="selectTblSeatTypeById" parameterType="Long" resultMap="TblSeatTypeResult">
        <include refid="selectTblSeatTypeVo"/>
        where id = #{id}
    </select>

    <select id="selectTblSeatTypeByIds" parameterType="Long" resultMap="TblSeatTypeResult">
        <include refid="selectTblSeatTypeVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblSeatType" parameterType="TblSeatType" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_seat_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="name != null">name,</if>
            <if test="icon != null">icon,</if>
            <if test="isShow != null">is_show,</if>
            <if test="position != null">position,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="name != null">#{name},</if>
            <if test="icon != null">#{icon},</if>
            <if test="isShow != null">#{isShow},</if>
            <if test="position != null">#{position},</if>
         </trim>
    </insert>

    <update id="updateTblSeatType" parameterType="TblSeatType">
        update tbl_seat_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="name != null">name = #{name},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="isShow != null">is_show = #{isShow},</if>
            <if test="position != null">position = #{position},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblSeatTypeById" parameterType="Long">
        delete from tbl_seat_type where id = #{id}
    </delete>

    <delete id="deleteTblSeatTypeByIds" parameterType="String">
        delete from tbl_seat_type where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
