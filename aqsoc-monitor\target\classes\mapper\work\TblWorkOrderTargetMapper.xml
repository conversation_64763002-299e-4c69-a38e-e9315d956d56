<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.work.mapper.TblWorkOrderTargetMapper">

    <resultMap type="TblWorkOrderTarget" id="TblWorkOrderTargetResult">
        <result property="id"    column="id"    />
        <result property="workOrderId"    column="work_order_id"    />
        <result property="workOrderDeptId"    column="work_order_dept_id"    />
        <result property="handleDept"    column="handle_dept"    />
        <result property="handleUser"    column="handle_user"    />
        <result property="applicationId"    column="application_id"    />
        <result property="loginUrl"    column="login_url"    />
        <result property="manager"    column="manager"    />
        <result property="phone"    column="phone"    />
        <result property="handleStatus"    column="handle_status"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="assetName" column="asset_name" />
        <result property="handleDeptName" column="handle_dept_name" />
    </resultMap>

    <sql id="selectTblWorkOrderTargetVo">
        SELECT
            t1.id,
            t1.work_order_id,
            t1.work_order_dept_id,
            t1.handle_dept,
            t1.handle_user,
            t1.application_id,
            t1.login_url,
            t1.manager,
            t1.phone,
            t1.handle_status,
            t1.handle_time,
            t2.asset_name,
            su.dept_name as handle_dept_name
        FROM
            tbl_work_order_target t1
            LEFT JOIN tbl_business_application t2 ON t2.asset_id = t1.application_id
            LEFT JOIN sys_dept su ON t1.handle_dept = su.dept_id
    </sql>

    <select id="selectTblWorkOrderTargetList" parameterType="TblWorkOrderTarget" resultMap="TblWorkOrderTargetResult">
        <include refid="selectTblWorkOrderTargetVo"/>
        <where>
            <if test="workOrderId != null "> and t1.work_order_id = #{workOrderId}</if>
            <if test="workOrderDeptId != null "> and t1.work_order_dept_id = #{workOrderDeptId}</if>
            <if test="workOrderDeptIds != null and workOrderDeptIds.size()>0">
                and t1.work_order_dept_id in
                <foreach item="item" index="index" collection="workOrderDeptIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="handleDept != null "> and t1.handle_dept = #{handleDept}</if>
            <if test="handleUser != null "> and t1.handle_user = #{handleUser}</if>
            <if test="applicationId != null "> and t1.application_id = #{applicationId}</if>
            <if test="loginUrl != null  and loginUrl != ''"> and t1.login_url = #{loginUrl}</if>
            <if test="manager != null "> and t1.manager = #{manager}</if>
            <if test="phone != null  and phone != ''"> and t1.phone like concat('%', #{phone}, '%')</if>
            <if test="handleStatus != null "> and t1.handle_status = #{handleStatus}</if>
            <if test="params.beginHandleTime != null and params.beginHandleTime != '' and params.endHandleTime != null and params.endHandleTime != ''"> and t1.handle_time between #{params.beginHandleTime} and #{params.endHandleTime}</if>
        </where>
    </select>

    <select id="selectTblWorkOrderTargetById" parameterType="Long" resultMap="TblWorkOrderTargetResult">
        <include refid="selectTblWorkOrderTargetVo"/>
        where t1.id = #{id}
    </select>

    <select id="selectTblWorkOrderTargetByIds" parameterType="Long" resultMap="TblWorkOrderTargetResult">
        <include refid="selectTblWorkOrderTargetVo"/>
        where t1.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectTblWorkOrderTargetByWorkId" resultType="com.ruoyi.work.domain.TblWorkOrderTarget" resultMap="TblWorkOrderTargetResult">
        <include refid="selectTblWorkOrderTargetVo"/>
        where t1.work_order_id = #{workId}
    </select>

    <insert id="insertTblWorkOrderTarget" parameterType="TblWorkOrderTarget" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_work_order_target
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id,</if>
            <if test="workOrderDeptId != null">work_order_dept_id,</if>
            <if test="handleDept != null">handle_dept,</if>
            <if test="handleUser != null">handle_user,</if>
            <if test="applicationId != null">application_id,</if>
            <if test="loginUrl != null">login_url,</if>
            <if test="manager != null">manager,</if>
            <if test="phone != null">phone,</if>
            <if test="handleStatus != null">handle_status,</if>
            <if test="handleTime != null">handle_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">#{workOrderId},</if>
            <if test="workOrderDeptId != null">#{workOrderDeptId},</if>
            <if test="handleDept != null">#{handleDept},</if>
            <if test="handleUser != null">#{handleUser},</if>
            <if test="applicationId != null">#{applicationId},</if>
            <if test="loginUrl != null">#{loginUrl},</if>
            <if test="manager != null">#{manager},</if>
            <if test="phone != null">#{phone},</if>
            <if test="handleStatus != null">#{handleStatus},</if>
            <if test="handleTime != null">#{handleTime},</if>
         </trim>
    </insert>
    <insert id="batchInsert">
        insert into tbl_work_order_target (id, work_order_id, work_order_dept_id, handle_dept, handle_user, application_id, login_url, manager, phone, handle_status, handle_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id}, #{item.workOrderId}, #{item.workOrderDeptId}, #{item.handleDept}, #{item.handleUser}, #{item.applicationId}, #{item.loginUrl}, #{item.manager}, #{item.phone}, #{item.handleStatus}, #{item.handleTime})
        </foreach>
    </insert>

    <update id="updateTblWorkOrderTarget" parameterType="TblWorkOrderTarget">
        update tbl_work_order_target
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id = #{workOrderId},</if>
            <if test="workOrderDeptId != null">work_order_dept_id = #{workOrderDeptId},</if>
            <if test="handleDept != null">handle_dept = #{handleDept},</if>
            <if test="handleUser != null">handle_user = #{handleUser},</if>
            <if test="applicationId != null">application_id = #{applicationId},</if>
            <if test="loginUrl != null">login_url = #{loginUrl},</if>
            <if test="manager != null">manager = #{manager},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchUpdate">
        UPDATE tbl_work_order_target
        SET
        handle_dept = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.handleDept != null">#{item.handleDept}</if> <if test="item.handleDept == null">handle_dept</if>
        </foreach>
        ELSE handle_dept
        END,
        handle_user = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.handleUser != null">#{item.handleUser}</if> <if test="item.handleUser == null">handle_user</if>
        </foreach>
        ELSE handle_user
        END,
        application_id = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.applicationId != null">#{item.applicationId}</if> <if test="item.applicationId == null">application_id</if>
        </foreach>
        ELSE application_id
        END,
        login_url = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.loginUrl != null and item.loginUrl != ''">#{item.loginUrl}</if> <if test="item.loginUrl == null or item.loginUrl == ''">login_url</if>
        </foreach>
        ELSE login_url
        END,
        manager = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.manager != null">#{item.manager}</if> <if test="item.manager == null">manager</if>
        </foreach>
        ELSE manager
        END,
        phone = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.phone != null and item.phone != ''">#{item.phone}</if> <if test="item.phone == null or item.phone == ''">phone</if>
        </foreach>
        ELSE phone
        END,
        handle_status = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.handleStatus != null">#{item.handleStatus}</if> <if test="item.handleStatus == null">handle_status</if>
        </foreach>
        ELSE handle_status
        END,
        handle_time = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN <if test="item.handleTime != null">#{item.handleTime}</if> <if test="item.handleTime == null">handle_time</if>
        </foreach>
        ELSE handle_time
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <delete id="deleteTblWorkOrderTargetById" parameterType="Long">
        delete from tbl_work_order_target where id = #{id}
    </delete>

    <delete id="deleteTblWorkOrderTargetByIds" parameterType="String">
        delete from tbl_work_order_target where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteTblWorkOrderTargetByWorkId">
        delete from tbl_work_order_target where work_order_id = #{workOrderId}
    </delete>
</mapper>
