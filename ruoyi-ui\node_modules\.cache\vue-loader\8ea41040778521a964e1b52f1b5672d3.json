{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\netConf.vue?vue&type=style&index=0&id=8550f6a6&scoped=true&lang=css", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\netConf.vue", "mtime": 1756369455993}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLm9wZXJhdGV7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOwogIG1hcmdpbi10b3A6IDMwcHg7Cn0K"}, {"version": 3, "sources": ["netConf.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyVA;AACA;AACA;AACA;AACA", "file": "netConf.vue", "sourceRoot": "src/views/safe/server", "sourcesContent": ["<template>\n  <div style=\"width: 99%\">\n    <el-row :gutter=\"10\" class=\"mb8\" style=\"display: flex\">\n      <el-button style=\"margin-left: auto\" v-if=\"!disabled\" type=\"primary\" @click=\"handleAdd\">添 加</el-button>\n<!--      <right-toolbar :showSearch.sync=\"showSearch\" :columns=\"columns\" @queryTable=\"getList\"></right-toolbar>-->\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"macAipList\" @selection-change=\"handleSelectionChange\" @sort-change=\"sortChange\">\n      <el-table-column label=\"所属网络\"  prop=\"domainFullName\" v-if=\"columns[0].visible\"/>\n      <el-table-column label=\"ip\"  prop=\"ipv4\" v-if=\"columns[1].visible\" />\n      <el-table-column label=\"mac\"  prop=\"mac\" v-if=\"columns[2].visible\" />\n      <el-table-column label=\"是否主ip\"  prop=\"mainIp\" v-if=\"columns[3].visible\" >\n        <template slot-scope=\"scope\">\n          <el-tag\n            v-if=\"scope.row.mainIp==1\"\n            key=\"1\"\n            type=\"success\"\n            effect=\"plain\">\n            是\n          </el-tag>\n          <el-tag\n            v-if=\"scope.row.mainIp==0\"\n            key=\"1\"\n            type=\"warning\"\n            effect=\"plain\">\n            否\n          </el-tag>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"备注\"  prop=\"remark\" v-if=\"columns[4].visible\"  show-overflow-tooltip/>\n      <el-table-column v-if=\"!disabled\" label=\"操作\" align=\"left\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row,false)\" v-hasPermi=\"['safe:macAip:list']\">查看</el-button>\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" v-hasPermi=\"['safe:macAip:edit']\">修改</el-button>\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\" v-hasPermi=\"['safe:macAip:remove']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\"/>\n    <el-row>\n<!--      <div class=\"operate\">-->\n\n<!--        <el-button type=\"success\" @click=\"getList\">刷 新</el-button>-->\n<!--        <el-button @click=\"closeMM\">关 闭</el-button>-->\n<!--      </div>-->\n    </el-row>\n    <!-- 添加或修改macAip对话框! -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"700px\" append-to-body>\n      <el-row>\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" :disabled=\"!editable\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"所属网络\" prop=\"domainId\">\n              <NetworkSelect v-model=\"form.domainId\"/>\n<!--              <treeselect v-model=\"form.domainId\" :options=\"networkDomainOptions\" :normalizer=\"normalizerNetworkDomain\" placeholder=\"请选择所属网络\" />-->\n<!--              <el-select v-model=\"form.domainId\" placeholder=\"请选择所属网络\" @change=\"domainChange\">\n                <el-option\n                  v-for=\"dict in networkDomainOptions\"\n                  :key=\"dict.domainId\"\n                  :label=\"dict.domainFullName\"\n                  :value=\"dict.domainId\"\n                ></el-option>\n              </el-select>-->\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"ip\" prop=\"ipv4\">\n              <el-input v-model=\"form.ipv4\" placeholder=\"请输入\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"是否主Ip\" prop=\"mainIp\">\n              <el-radio-group :disabled=\"!editable\" v-model=\"form.mainIp\">\n                <el-radio\n                  key=\"1\"\n                  label=\"1\"\n                >是\n                </el-radio>\n                <el-radio\n                  key=\"0\"\n                  label=\"0\"\n                >否\n                </el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"mac\" prop=\"mac\">\n              <el-input v-model=\"form.mac\" placeholder=\"请输入\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button v-if=\"editable\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listMacAip, getMacAip, delMacAip, addMacAip, updateMacAip } from \"@/api/safe/macAip\";\nimport { listDomain } from \"@/api/dict/domain\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport {listSegment} from \"@/api/dict/segment\";\nimport NetworkSelect from \"@/views/components/select/networkSelect.vue\";\nexport default {\n  name: \"MacAip\",\n  components: {NetworkSelect, Treeselect},\n  dicts:['ip_type'],\n  props: {\n    assetId: {\n      type:String,\n      required: true,\n      default: ''\n    },\n    disabled:{\n      type:Boolean,\n      default:false,\n    }\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // macAip表格数据\n      macAipList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        isAsc: undefined,\n        orderByColumn : undefined,\n        pageNum: 1,\n        pageSize: 10,\n        assetId: null,\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        domainId:[{\n          required:true,message:\"所属网络区域不能为空\",trigger:\"blur\"\n        }],\n        ipv4:[{\n          required:true,message:\"ip地址不能为空\",trigger:\"blur\"\n        },{\n          pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n          message: \"IP地址格式不正确\",\n          trigger: \"blur\"\n        },],\n        mac: [\n          {pattern: '^([0-9a-fA-F]{2}(:|-)){5}[0-9a-fA-F]{2}$', message: \"Mac地址格式不正确\", trigger: \"blur\"},\n        ]\n      },\n      columns:[\n        {key: 0, label: '所属网络', visible: true},\n        {key: 1, label: 'mac', visible: true},\n        {key: 2, label: 'IP', visible: true},\n        {key: 3, label: '主Ip', visible: true},\n        {key: 4, label: '备注', visible: false},\n      ],\n      // 网络区域树选项\n      networkDomainOptions: [],\n      editable:true,\n    };\n  },\n  created() {\n    this.queryParams.assetId = this.assetId;\n    this.getList();\n  },\n  watch:{\n    assetId(val){\n      this.queryParams={\n        ...this.queryParams,\n        pageNum: 1,\n        pageSize: 10,\n        assetId: val,\n      };\n      this.getList();\n    },\n  },\n  methods: {\n    //排序\n    sortChange(column, prop, order){\n      if (column.order != null){\n        this.queryParams.isAsc ='desc';\n      }else {\n        this.queryParams.isAsc ='asc';\n      }\n      this.queryParams.orderByColumn = column.prop;\n      this.getList(this.queryParams);\n    },\n    /** 查询macAip列表 */\n    getList() {\n      this.loading = true;\n      listMacAip(this.queryParams).then(response => {\n        this.macAipList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        assetId: null,\n        ipv4: null,\n        mainIp:'0',\n        mac: null,\n        remark: null,\n        createBy: null,\n        createTime: null,\n        updateBy: null,\n        updateTime: null,\n        domainId: null,\n        coerce:false,\n      };\n      this.resetForm(\"form\");\n      this.editable = true;\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.getNetworkDomainTreeselect();\n      this.open = true;\n      this.title = \"添加IP地址\";\n    },\n    closeMM(){\n      this.$emit(\"cancel\")\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row,edit  =true) {\n      this.reset();\n      this.editable = edit;\n      const id = row.id || this.ids\n      getMacAip(id).then(response => {\n        this.form = response.data;\n        this.getNetworkDomainTreeselect();\n        this.open = true;\n        this.title = \"修改IP地址\";\n      });\n    },\n    domainChange(){\n      // this.form.ipv4=null;\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(async valid => {\n        if (valid && this.editable) {\n          if (this.form.id != null) {\n            this.form.assetId = this.assetId;\n            updateMacAip(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            })\n          } else {\n            this.form.assetId = this.assetId;\n            addMacAip(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            })\n          }\n        }\n      })\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除macAip编号为\"' + ids + '\"的数据项？').then(function() {\n        return delMacAip(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('safe/macAip/export', {\n        ...this.queryParams\n      }, `macAip_${new Date().getTime()}.xlsx`)\n    },\n    /** 转换网络区域数据结构 */\n    normalizerNetworkDomain(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.domainId,\n        label: node.domainName,\n        children: node.children,\n      };\n    },\n    /** 查询网络区域下拉树结构 */\n    getNetworkDomainTreeselect() {\n      listDomain().then(response => {\n        this.networkDomainOptions = response.data\n         //this.networkDomainOptions = this.handleTree(doamins, \"domainId\", \"parentId\");\n      });\n    },\n  }\n};\n</script>\n<style scoped>\n.operate{\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 30px;\n}\n</style>\n"]}]}