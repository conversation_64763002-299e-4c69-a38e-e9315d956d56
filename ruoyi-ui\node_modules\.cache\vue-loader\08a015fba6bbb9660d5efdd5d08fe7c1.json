{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\alertEvent.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\alertEvent.vue", "mtime": 1756369455960}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["alertEvent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAq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file": "alertEvent.vue", "sourceRoot": "src/views/frailty/event", "sourcesContent": ["<template>\n  <div class=\"alert-event-box\">\n    <div class=\"head-card-box\">\n      <div class=\"head-card-item\" v-for=\"(headItem,index) in headCardOptions\" :key=\"'head-'+index\">\n        <div :class=\"currentCard === headItem.key ? 'head-card active' : 'head-card'\" @click=\"headItem.click\">\n          <div class=\"head-card-title\">\n            {{ headItem.title }}\n            <span style=\"margin-left: 5px\">\n              <el-tag type=\"primary\" effect=\"dark\" size=\"mini\">{{ headItem.total() }}</el-tag>\n            </span>\n          </div>\n          <div class=\"head-card-btn-box\">\n            <el-row :gutter=\"20\" style=\"height: 100%;display: flex;align-items: center;margin-left: 0;margin-right: 0\">\n              <el-col\n                v-for=\"(btnItem,btnIndex) in headItem.btnArr\"\n                :key=\"'btn-'+btnIndex\"\n                :span=\"24/headItem.btnArr.length\"\n                :class=\"currentCard === headItem.key && currentBtn === btnItem.key ? 'head-btn-active' : ''\"\n                style=\"padding-top: 10px;padding-bottom: 10px;\"\n              >\n                <div class=\"head-card-btn\" @click.stop=\"btnItem.click\">\n                  <div :class=\"[ btnItem.label === '弱口令账号' ? 'btn-icon1' : 'btn-icon']\">\n                    <el-image :src=\"btnItem.icon\" />\n                  </div>\n                  <div class=\"btn-content\">\n                    <div class=\"btn-content-value\">\n                      {{ btnItem.value() }}\n                    </div>\n                    <div class=\"btn-content-label\">\n                      {{ btnItem.label }}\n                    </div>\n                  </div>\n                </div>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div v-if=\"currentCard === 1\" class=\"box-content\">\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"告警列表\" name=\"first\" />\n        <el-tab-pane label=\"攻击者视角\" name=\"second\" />\n        <el-tab-pane label=\"受害者视角\" name=\"third\" />\n        <el-tab-pane label=\"资产视角\" name=\"four\" />\n        <!--        <el-tab-pane label=\"阻断IP\" name=\"five\"></el-tab-pane>-->\n      </el-tabs>\n      <div style=\"height: calc(100% - 41px); margin-top: 3px\">\n        <event-list v-if=\"propActiveName === 'first'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetEventList\" />\n        <attack-view-list v-if=\"propActiveName === 'second'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetAttackEventList\" />\n        <suffer-view-list v-if=\"propActiveName === 'third'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetEventList\" />\n        <asset-view v-if=\"propActiveName === 'four'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetEventList\" />\n      </div>\n    </div>\n    <div v-if=\"currentCard === 2\" class=\"box-content\">\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"告警列表\" name=\"first\" />\n        <el-tab-pane label=\"攻击者视角\" name=\"second\" />\n      </el-tabs>\n      <div style=\"height: calc(100% - 41px); margin-top: 3px\">\n        <honeypotAlarmList v-if=\"propActiveName === 'first'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetHoneyEventList\" />\n        <honeypot-attack-view-list v-if=\"propActiveName === 'second'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetHoneyEventList\" />\n      </div>\n    </div>\n    <div v-if=\"currentCard === 3\" class=\"box-content\">\n      <api-alarm-list v-if=\"currentCard === 3\" :props-active-name=\"'apiAlarm'\" :props-query-params=\"queryParams\" @reset-button=\"handleResetButton\" @query-change=\"handleApiQueryChange\" @getList=\"handleGetApiEventList\" />\n    </div>\n    <div v-if=\"currentCard === 4\" class=\"box-content\">\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"阻断中列表\" name=\"first\" />\n        <el-tab-pane label=\"阻断历史记录\" name=\"second\" />\n      </el-tabs>\n      <div style=\"height: calc(100% - 47px); margin-top: 8px\">\n        <IpfilterLog v-if=\"propActiveName === 'first'\" ref=\"ipFilterLog\" :props-active-name=\"propActiveName\" @getList=\"handleGetIpFilterList\" />\n        <IpFilterLogHistory v-if=\"propActiveName === 'second'\" ref=\"ipFilterLogHistory\" :props-active-name=\"propActiveName\" @getList=\"handleGetIpFilterList\" />\n      </div>\n    </div>\n    <div v-if=\"currentCard === 5\" class=\"box-content\">\n      <invade-attack ref=\"invadeAttack\" v-if=\"propActiveName === 'first'\" @getList=\"getInvadeAttackStatistics\"/>\n      <host-event ref=\"hostEvent\" v-if=\"propActiveName === 'second'\"/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport EventList from './component/eventList'\nimport AttackViewList from './component/attackViewList'\nimport SufferViewList from './component/sufferViewList'\nimport AssetView from '@/views/threat/asset/index'\nimport IpfilterLog from '@/views/aqsoc/ffsafe-ipfilter-log/index'\nimport IpFilterLogHistory from '@/views/aqsoc/ffsafe-ipfilter-log/history'\nimport { groupAlarmLevelStatistics, groupHoneypotAlarmLevelStatistics } from '@/api/threat/threat'\nimport { groupAlarmLevelStatistics as groupAttackAlarmLevelStatistics } from '@/api/threaten/AttackAlarm';\nimport HoneypotAlarmList from './component/honeypotAlarmList'\nimport HoneypotAttackViewList from './component/honeypotAttackViewList'\nimport ApiAlarmList from './component/apiAlarmList'\nimport HostEvent from './component/hostEvent.vue'\nimport InvadeAttack from './component/invadeAttack.vue'\nimport { getFilterLogStatistic } from '@/api/safe/ffsafeIpFilterblocking'\nimport { getFlowRiskAssetsStatistics } from '@/api/ffsafe/flowRiskAssets'\nimport { parseTime } from '@/utils/ruoyi'\nimport {countHostInvasion} from \"@/api/monitor/hostAgent\";\n\nexport default {\n  name: 'AlertEvent',\n  components: {\n    SufferViewList,\n    AttackViewList,\n    EventList,\n    AssetView,\n    IpfilterLog,\n    IpFilterLogHistory,\n    HoneypotAlarmList,\n    HoneypotAttackViewList,\n    ApiAlarmList,\n    HostEvent,\n    InvadeAttack\n  },\n  data() {\n    return {\n      activeName: 'first',\n      propActiveName: 'first',\n      srcQueryParams: {},\n      queryParams: {},\n      currentQueryParams: {},\n      currentCard: 1,\n      currentBtn: null,\n      headCardOptions: [\n        {\n          title: '流量威胁告警',\n          total: () => this.getStatisticsValue(this.getGroupStatisticsData, 'total'),\n          key: 1,\n          click: () => {\n            this.headBtnClick(1, null)\n            this.currentQueryParams.alarmLevel = null;\n            this.currentQueryParams.riskLevel = null;\n            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n          },\n          btnArr: [\n            /*{\n              icon: require('@/assets/icons/event/level5.png'),\n              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel5'),\n              label: '严重',\n              key: 5,\n              click: () => {\n                this.headBtnClick(1, 5)\n                this.currentQueryParams.alarmLevel = '5'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },*/\n            {\n              icon: require('@/assets/icons/event/level4.png'),\n              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel4'),\n              label: '高危',\n              key: 4,\n              click: () => {\n                this.headBtnClick(1, 4)\n                this.currentQueryParams.alarmLevel = '4'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level3.png'),\n              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel3'),\n              label: '中危',\n              key: 3,\n              click: () => {\n                this.headBtnClick(1, 3)\n                this.currentQueryParams.alarmLevel = '3'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level2.png'),\n              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel2'),\n              label: '低危',\n              key: 2,\n              click: () => {\n                this.headBtnClick(1, 2)\n                this.currentQueryParams.alarmLevel = '2'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            }\n          ]\n        },\n        {\n          title: '蜜罐诱捕告警',\n          total: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'total'),\n          key: 2,\n          click: () => this.headBtnClick(2, null),\n          btnArr: [\n            {\n              icon: require('@/assets/icons/event/level5.png'),\n              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel5'),\n              label: '严重',\n              key: 5,\n              click: () => {\n                this.headBtnClick(2, 5)\n                this.currentQueryParams.alarmLevel = '5'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level4.png'),\n              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel4'),\n              label: '高危',\n              key: 4,\n              click: () => {\n                this.headBtnClick(2, 4)\n                this.currentQueryParams.alarmLevel = '4'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level3.png'),\n              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel3'),\n              label: '中危',\n              key: 3,\n              click: () => {\n                this.headBtnClick(2, 3)\n                this.currentQueryParams.alarmLevel = '3'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level2.png'),\n              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel2'),\n              label: '低危',\n              key: 2,\n              click: () => {\n                this.headBtnClick(2, 2)\n                this.currentQueryParams.alarmLevel = '2'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            }\n          ]\n        },\n        {\n          title: 'API告警',\n          total: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'total'),\n          key: 3,\n          click: () => {\n            this.headBtnClick(3, null)\n            this.currentQueryParams.riskType = null\n            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n          },\n          btnArr: [\n            {\n              icon: require('@/assets/icons/event/weakPassword.png'),\n              value: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'weakPassword'),\n              label: '弱口令账号',\n              key: 1,\n              click: () => {\n                this.headBtnClick(3, 1)\n                this.currentQueryParams.riskType = 'weak_password'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/敏感信息.png'),\n              value: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'sensitiveInfo'),\n              label: '敏感信息',\n              key: 2,\n              click: () => {\n                this.headBtnClick(3, 2)\n                this.currentQueryParams.riskType = 'sensitive_info'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/高危资产.png'),\n              value: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'highRiskAssets'),\n              label: '高危资产',\n              key: 3,\n              click: () => {\n                this.headBtnClick(3, 3)\n                this.currentQueryParams.riskType = 'high_risk_assets'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            }\n          ]\n        },\n        {\n          title: '主机Agent事件',\n          total: () => this.getStatisticsValue(this.hostAgentEventStatisticsData, 'total'),\n          key: 5,\n          click: () => {\n            this.headBtnClick(5, null)\n            // this.currentQueryParams.riskType = 'host_agent_event'\n            this.currentQueryParams.riskType = null\n            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n          },\n          btnArr: [\n            {\n              icon: require('@/assets/icons/event/invade.png'),\n              value: () => this.getStatisticsValue(this.invadeAttackStatisticsData, 'total'),\n              label: '入侵攻击',\n              key: 1,\n              click: () => {\n                this.headBtnClick(5, 1)\n                this.propActiveName = 'first'\n                // this.currentQueryParams.riskType = 'invade'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/host-event.png'),\n              value: () => this.getStatisticsValue(this.hostAgentEventStatisticsData, 'hostEvent'),\n              label: '主机事件',\n              key: 2,\n              click: () => {\n                this.headBtnClick(5, 2)\n                this.propActiveName = 'second'\n                // this.currentQueryParams.riskType = 'malware'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            }\n          ]\n        },\n        {\n          title: '实时阻断',\n          total: () => this.getStatisticsValue(this.filterLogStatisticData, 'total'),\n          key: 4,\n          click: () => {\n            this.headBtnClick(4, null)\n            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n          },\n          btnArr: [\n            {\n              icon: require('@/assets/icons/event/正在阻断.png'),\n              value: () => this.getStatisticsValue(this.filterLogStatisticData, 'blockingCount'),\n              label: '正在阻断',\n              key: 1,\n              click: () => {\n                this.propActiveName = 'first'\n                this.activeName = 'first'\n                this.headBtnClick(4, 1)\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/阻断历史.png'),\n              value: () => this.getStatisticsValue(this.filterLogStatisticData, 'blockLogCount'),\n              label: '历史记录',\n              key: 2,\n              click: () => {\n                this.propActiveName = 'second'\n                this.activeName = 'second'\n                this.$forceUpdate()\n                this.headBtnClick(4, 2)\n              }\n            }\n          ]\n        }\n      ],\n      threatenAlarmStatisticsData: {},\n      attackAlarmStatisticsData: {},\n      honeypotAlarmStatisticsData: {},\n      filterLogStatisticData: {},\n      apiAlarmStatisticsData: {},\n      hostAgentEventStatisticsData: {},\n      hostEventStatisticsData: {},\n      invadeAttackStatisticsData: {},\n    }\n  },\n  watch: {\n    $route: {\n      handler(newVal) {\n        if (newVal.query.type === '4') {\n          // 设置当前选中卡片和按钮\n          this.currentCard = 4 // 对应实时阻断卡片\n          this.currentBtn = 1 // 对应正在阻断按钮\n\n          // 如果需要触发按钮点击逻辑\n          this.$nextTick(() => {\n            // 调用按钮点击方法\n            this.headCardOptions[3].btnArr[0].click()\n          })\n        }\n        if (newVal.query.type === '2') {\n          this.currentCard = 2\n          this.currentBtn = 2\n        }\n      },\n      immediate: true\n    },\n    currentCard: {\n      handler(newVal) {\n        // 当切换到API告警卡片时，刷新数据\n        // 注释掉这个调用，避免重复调用统计接口，统计数据由子组件的getList事件触发\n        // if (newVal === 3) {\n        //   this.refreshApiAlarmStatistics()\n        // }\n      }\n    },\n    activeName: {\n      handler(newVal) {\n        if (this.currentCard === 4) {\n          if (newVal === 'first') {\n            this.currentBtn = 1\n          }\n          if (newVal === 'second') {\n            this.currentBtn = 2\n          }\n        }\n      }\n    }\n  },\n  mounted() {\n    const query = this.$route.query\n    if (query) {\n      this.srcQueryParams = query\n      this.queryParams = { ...this.srcQueryParams }\n      if (query.tabs) {\n        this.propActiveName = query.tabs\n        this.activeName = query.tabs\n      }\n    }\n    const params = {\n      startTime: parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'),\n      endTime: parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'),\n      handleState: '0'\n    }\n    this.getAlarmLevelStatistics(params)\n    this.getHoneypotAlarmLevelStatistics(params)\n    this.getFilterLogStatistic({})\n    this.getFlowRiskAssetsStatistics({\n      params: {\n        beginTime: params.startTime,\n        endTime: params.endTime\n      },\n      handleState: params.handleState\n    })\n    this.getInvadeAttackStatistics({\n      beginTime: params.startTime,\n      endTime: params.endTime\n    })\n    // this.getHostEventStatistics()\n  },\n  computed: {\n    getGroupStatisticsData() {\n      return this.activeName === 'second' ? this.attackAlarmStatisticsData : this.threatenAlarmStatisticsData;\n    }\n  },\n  methods: {\n    getAlarmLevelStatistics(params) {\n      groupAlarmLevelStatistics(params).then(res => {\n        this.threatenAlarmStatisticsData = res.data\n      })\n    },\n    getAttackAlarmLevelStatistics(params) {\n      groupAttackAlarmLevelStatistics(params).then(res => {\n        this.attackAlarmStatisticsData = res.data\n      })\n    },\n    getHoneypotAlarmLevelStatistics(params) {\n      groupHoneypotAlarmLevelStatistics(params).then(res => {\n        this.honeypotAlarmStatisticsData = res.data\n      })\n    },\n    getFilterLogStatistic(params) {\n      getFilterLogStatistic(params).then(res => {\n        this.filterLogStatisticData = res.data\n      })\n    },\n    getInvadeAttackStatistics(params) {\n      countHostInvasion(params).then(res => {\n        console.log(res.data, 'getInvadeAttackStatistics')\n        this.invadeAttackStatisticsData = res.data\n      })\n    },\n    getFlowRiskAssetsStatistics(params) {\n      getFlowRiskAssetsStatistics(params).then(res => {\n        this.apiAlarmStatisticsData = res.data\n      })\n    },\n    handleClick() {\n      this.propActiveName = this.activeName\n      this.$router.push({ query: {}})\n    },\n    headBtnClick(cardKey, btnKey) {\n      if (this.currentCard !== cardKey) {\n        this.currentBtn = null\n      }\n      this.currentQueryParams = {}\n\n      // 根据卡片类型设置对应的默认标签页\n      if (cardKey === 1) {\n        // 流量威胁告警：重置为告警列表标签页\n        if (this.currentCard !== cardKey) {\n          this.activeName = 'first'\n          this.propActiveName = 'first'\n        }\n      } else if (cardKey === 2) {\n        // 蜜罐诱捕告警：重置为告警列表标签页\n        if (this.currentCard !== cardKey) {\n          this.activeName = 'first'\n          this.propActiveName = 'first'\n        }\n      }\n      this.currentCard = cardKey\n      this.currentBtn = btnKey\n      // cardKey === 3(API告警) 不需要设置标签页，因为没有子标签页\n      // currentCard === 4 (实时阻断) 不需要设置标签页，因为没有子标签页\n    },\n    getStatisticsValue(srcData, key) {\n      if (!srcData) {\n        return 0\n      }\n      return srcData[key] || 0\n    },\n    handleResetButton() {\n      // 重置API告警按钮选中状态\n      if (this.currentCard === 3) {\n        this.currentBtn = null\n        // 重置查询参数中的风险类型\n        this.currentQueryParams.riskType = null\n        this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n      }\n    },\n    handleApiQueryChange(queryChange) {\n      // 处理API告警查询参数变化，同步按钮选中状态\n      if (queryChange.riskType) {\n        // 根据风险类型设置对应的按钮选中状态\n        switch (queryChange.riskType) {\n          case 'weak_password':\n            this.currentBtn = 1\n            break\n          case 'sensitive_info':\n            this.currentBtn = 2\n            break\n          case 'high_risk_assets':\n            this.currentBtn = 3\n            break\n          default:\n            this.currentBtn = null\n        }\n      } else {\n        this.currentBtn = null\n      }\n    },\n    handleGetEventList(params) {\n      if (!params.alarmLevel) {\n        this.currentBtn = null\n      }\n      params.alarmLevel = null\n      this.getAlarmLevelStatistics(params)\n    },\n    handleGetAttackEventList(params) {\n      if (!params.riskLevel) {\n        this.currentBtn = null\n      }\n      params.riskLevel = null\n      this.getAttackAlarmLevelStatistics(params)\n    },\n    handleGetHoneyEventList(params) {\n      if (!params.alarmLevel) {\n        this.currentBtn = null\n      }\n      params.alarmLevel = null\n      this.getHoneypotAlarmLevelStatistics(params)\n    },\n    handleGetApiEventList(params) {\n      // 构建统计接口的查询参数，包含所有查询条件\n      const statisticsParams = {\n        params: {\n          beginTime: params.params ? params.params.beginTime : undefined,\n          endTime: params.params ? params.params.endTime : undefined\n        },\n        deviceConfigId: params.deviceConfigId\n      }\n      // 传递所有查询条件给统计接口\n      if (params.riskAssets) {\n        statisticsParams.riskAssets = params.riskAssets\n      }\n\n      if (params.handleState !== undefined && params.handleState !== null) {\n        statisticsParams.handleState = params.handleState\n      }\n\n      params.riskType = null\n      this.getFlowRiskAssetsStatistics(statisticsParams)\n    },\n    handleGetIpFilterList(params) {\n      this.getFilterLogStatistic(params)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../../assets/styles/tabs.scss\";\n@font-face {\n  font-family: electronicFont;\n  src: url(../../../assets/fonts/DS-DIGI.ttf);\n}\n\n.alert-event-box {\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  .head-card-box {\n    width: 100%;\n    height: 109px;\n    display: flex;\n    flex-direction: row;\n    gap: 8px;\n    margin-bottom: 8px;\n    .head-card-item {\n      flex: 1;\n      &:nth-last-child(-n+2) {\n        width: 17%;\n        flex: none;\n      }\n    }\n  }\n  .box-content {\n    height: calc(100% - 115px);\n  }\n}\n\n.head-card{\n  //height: 119px;\n  background-color: #FFFFFF;\n  padding: 8px 10px;\n  font-size: 14px;\n  display: flex;\n  flex-direction: column;\n  cursor: pointer;\n  .head-card-title{\n    color: #242424;\n    font-size: 14px;\n    font-weight: 700;\n  }\n  .head-card-btn-box{\n    flex: 1;\n    margin-top: 5px;\n    margin-bottom: 5px;\n    .head-card-btn{\n      display: flex;\n      align-items: center;\n      cursor: pointer;\n      .btn-icon{\n        width: 35%;\n        text-align: center;\n        .el-image{\n          width: 24px;\n          height: 24px;\n        }\n      }\n      .btn-icon1{\n        width: 35px;\n        text-align: center;\n        .el-image{\n          width: 24px;\n          height: 24px;\n        }\n      }\n      .btn-content{\n        height: 40px;\n        padding-left: 5px;\n        display: flex;\n        flex-direction: column;\n        position: relative;\n        flex: 1;\n        .btn-content-value{\n          font-size: 18px;\n          font-weight: 700;\n          font-family: electronicFont;\n        }\n        .btn-content-label{\n          position: absolute;\n          bottom: 0;\n          font-weight: 400;\n          white-space: nowrap;\n        }\n      }\n    }\n\n    .head-btn-active{\n      background-color: #f3f8fe;\n    }\n  }\n}\n.active{\n  border: 1px solid #4382FD;\n}\n</style>\n"]}]}