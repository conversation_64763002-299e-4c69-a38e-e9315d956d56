<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblNetworkIpMacMapper">

    <resultMap type="com.ruoyi.safe.domain.TblNetworkIpMac" id="TblNetworkIpMacResult">
        <result property="id" column="id"/>
        <result property="assetId" column="asset_id"/>
        <result property="ipv4" column="ipv4"/>
        <result property="ipv6" column="ipv6"/>
        <result property="mac" column="mac"/>
        <result property="macType" column="mac_type"/>
        <result property="defaultGateway" column="default_gateway"/>
        <result property="assetVlan" column="asset_vlan"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="domainId" column="domain_id"/>
        <result property="assetName" column="asset_name"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetTypeDesc" column="asset_type_desc"/>
        <result property="assetClassDesc" column="asset_class_desc"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="mainIp" column="main_ip"/>
        <result property="lastScanState" column="last_scan_state"/>
        <result property="lastScanTime" column="last_scan_time"/>
        <result property="vulnNum" column="vuln_num"/>
        <result property="vulnUpdateTime" column="vuln_update_time"/>
    </resultMap>

    <resultMap id="NetworkIpMacInfoResult" type="NetworkIpMacInfo">
        <result property="assetId" column="asset_id"/>
        <result property="ipv4" column="ipv4" />
        <result property="deptName" column="dept_name" />
        <result property="deptId" column="dept_id" />
        <result property="assetIdList" column="asset_id_list" />
        <result property="assetName"    column="asset_name"    />
        <result property="assetTypeDesc"    column="asset_type_desc" />
        <result property="assetClassDesc"    column="asset_class_desc" />
    </resultMap>

    <sql id="selectTblNetworkIpMacVo">
        SELECT
            a.id,
            a.asset_id,
            a.ipv4,
            a.ipv6,
            a.mac,
            a.mac_type,
            a.default_gateway,
            a.asset_vlan,
            a.remark,
            a.create_by,
            a.create_time,
            a.last_scan_state,
            a.last_scan_time,
            a.vuln_num,
            a.vuln_update_time,
            a.update_by,
            a.update_time,
            a.domain_id,
            c.domain_name AS domainName,
            a.main_ip,
            b.asset_code,
            b.asset_name,
            b.asset_type_desc,
            b.asset_class_desc,
            c.domain_full_name
        FROM
            tbl_network_ip_mac a
                LEFT JOIN tbl_asset_overview b ON a.asset_id = b.asset_id
                LEFT JOIN tbl_network_domain c ON a.domain_id = c.domain_id
    </sql>

    <select id="selectTblNetworkIpMacList" parameterType="com.ruoyi.safe.domain.TblNetworkIpMac" resultMap="TblNetworkIpMacResult">
        <include refid="selectTblNetworkIpMacVo"/>
        <where>
            <if test="assetId != null "> and a.asset_id = #{assetId}</if>
            <if test="ipv4 != null  and ipv4 != ''"> and ipv4 =#{ipv4}</if>
            <if test="ipv6 != null  and ipv6 != ''"> and ipv6 = #{ipv6}</if>
            <if test="mac != null  and mac != ''"> and mac = #{mac}</if>
            <if test="defaultGateway != null  and defaultGateway != ''"> and default_gateway = #{defaultGateway}</if>
            <if test="assetVlan != null  and assetVlan != ''"> and asset_vlan = #{assetVlan}</if>
            <if test="domainId != null  and domainId != ''"> and a.domain_id = #{domainId}</if>
            <if test="mainIp != null  and mainIp != ''"> and main_ip = #{mainIp}</if>
            <!--数据过滤-->
            ${params.dataScope}
        </where>
    </select>
    <select id="selectTblNetworkMacList" parameterType="com.ruoyi.safe.domain.TblNetworkIpMac" resultMap="TblNetworkIpMacResult">
        <include refid="selectTblNetworkIpMacVo"/>
        <where>
            <if test="assetId != null "> and a.asset_id = #{assetId}</if>
            <if test="1==1"> and ipv4 is null</if>
            <if test="1==1"> and ipv6 is null</if>
            <if test="1==1"> and mac is not null and mac != ""</if>
            <if test="defaultGateway != null  and defaultGateway != ''"> and default_gateway = #{defaultGateway}</if>
            <if test="assetVlan != null  and assetVlan != ''"> and asset_vlan = #{assetVlan}</if>
        </where>
    </select>
    <select id="selectTblNetworkIpv4List" parameterType="com.ruoyi.safe.domain.TblNetworkIpMac" resultMap="TblNetworkIpMacResult">
        <include refid="selectTblNetworkIpMacVo"/>
        <where>
            <if test="assetId != null "> and a.asset_id = #{assetId}</if>
            <if test="1==1"> and ipv4 is not null and ipv4 != ""</if>
            <if test="ipv6 != null  and ipv6 != ''"> and ipv6 = #{ipv6}</if>
            <if test="mac != null  and mac != ''"> and mac = #{mac}</if>
            <if test="defaultGateway != null  and defaultGateway != ''"> and default_gateway = #{defaultGateway}</if>
            <if test="assetVlan != null  and assetVlan != ''"> and asset_vlan = #{assetVlan}</if>
        </where>
    </select>
    <select id="selectTblNetworkIpv6List" parameterType="com.ruoyi.safe.domain.TblNetworkIpMac" resultMap="TblNetworkIpMacResult">
        <include refid="selectTblNetworkIpMacVo"/>
        <where>
            <if test="assetId != null "> and a.asset_id = #{assetId}</if>
            <if test="ipv4 != null  and ipv4 != ''"> and ipv4 = #{ipv4}</if>
            <if test="1==1"> and ipv6 is not null and ipv6 != ""</if>
            <if test="mac != null  and mac != ''"> and mac = #{mac}</if>
            <if test="defaultGateway != null  and defaultGateway != ''"> and default_gateway = #{defaultGateway}</if>
            <if test="assetVlan != null  and assetVlan != ''"> and asset_vlan = #{assetVlan}</if>
        </where>
    </select>
    <select id="selectTblNetworkIpMacById" parameterType="java.lang.Long" resultMap="TblNetworkIpMacResult">
        <include refid="selectTblNetworkIpMacVo"/>
        where id = #{id}
    </select>
    <select id="selectTblNetworkIpMacByIds" parameterType="java.lang.Long" resultMap="TblNetworkIpMacResult">
        <include refid="selectTblNetworkIpMacVo"/>
        where id in
        <foreach collection="list" open="(" close=")" separator="," item="id">#{id}</foreach>
    </select>

    <select id="selectTblAssetByIP" parameterType="string" resultType="hashmap">
        select a.asset_id, a.ipv4, a.mac, a.mac_type,a.main_ip,a.domain_id
        from tbl_network_ip_mac a
        where main_ip='1' and domain_id is not null
        and a.ipv4= #{ip}
    </select>

    <select id="selectListByAssetIds" resultType="com.ruoyi.safe.domain.TblNetworkIpMac" resultMap="TblNetworkIpMacResult">
        SELECT
            id,
            asset_id,
            ipv4,
            main_ip
        FROM
            tbl_network_ip_mac
        WHERE
            asset_id in
            <foreach collection="assetIds" item="assetId" open="(" separator="," close=")">
                #{assetId}
            </foreach>
    </select>
    <select id="selectListByIps" resultType="com.ruoyi.safe.domain.TblNetworkIpMac" resultMap="TblNetworkIpMacResult">
        SELECT
            id,
            asset_id,
            ipv4,
            main_ip
        FROM
            tbl_network_ip_mac
        WHERE
            ipv4 in
            <foreach collection="ips" item="ip" open="(" separator="," close=")">
                #{ip}
            </foreach>
    </select>
    <select id="selectAssetInfoList" resultType="NetworkIpMacInfo" resultMap="NetworkIpMacInfoResult">
        SELECT
            im.asset_id,im.ipv4,GROUP_CONCAT(sd.dept_id) dept_id,GROUP_CONCAT(sd.dept_name) dept_name,GROUP_CONCAT(tba.asset_id) asset_id_list,
            t2.asset_name,
            t2.asset_type_desc,
            t2.asset_class_desc
        FROM
            tbl_network_ip_mac im
            left join tbl_asset_overview t2 on t2.asset_id=im.asset_id
            left join sys_dept sd on sd.dept_id=t2.dept_id
            LEFT JOIN tbl_application_server t3 ON t3.server_id=im.asset_id
            LEFT JOIN tbl_business_application tba ON (tba.asset_id=im.asset_id OR tba.asset_id=t3.asset_id)
        <where>
            <if test="domainId != null  and domainId != ''"> and im.domain_id = #{domainId} </if>
            <if test="deptId != null">and (t2.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))</if>
            <if test="applicationId != null">and tba.asset_id=#{applicationId}</if>
            <if test="paramsDataScope != null and paramsDataScope != ''"> ${paramsDataScope}</if>
        </where>
        GROUP BY
            im.asset_id
    </select>

    <select id="getIpv4List" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            DISTINCT nim.ipv4,GROUP_CONCAT(nim.asset_id) assetIds,GROUP_CONCAT(tba.asset_id) applicationIds,tnd.domain_id domainId
        FROM
            tbl_network_ip_mac nim
            LEFT JOIN tbl_asset_overview tao ON tao.asset_id=nim.asset_id
            LEFT JOIN sys_dept sd ON sd.dept_id=tao.dept_id
            LEFT JOIN tbl_application_server tas ON tas.server_id=tao.asset_id
            LEFT JOIN tbl_business_application tba ON (tba.asset_id=tas.asset_id)
            LEFT JOIN tbl_network_domain tnd ON tnd.domain_id=nim.domain_id
        <where>
            <if test="deptId != null">AND (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors)) </if>
            <if test="applicationId != null">AND tba.asset_id=#{applicationId} </if>
            <if test="domainId != null">AND tnd.domain_id=#{domainId} </if>
            <if test="isLastLevel != null">AND tnd.is_last_level=#{isLastLevel} </if>
        </where>
        GROUP BY
            nim.ipv4
    </select>

    <insert id="insertTblNetworkIpMac" parameterType="com.ruoyi.safe.domain.TblNetworkIpMac">
        insert into tbl_network_ip_mac
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="ipv4 != null">ipv4,</if>
            <if test="ipv6 != null">ipv6,</if>
            <if test="mac != null">mac,</if>
            <if test="macType != null">mac_type,</if>
            <if test="defaultGateway != null">default_gateway,</if>
            <if test="assetVlan != null">asset_vlan,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="domainId != null">domain_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="mainIp != null">main_ip,</if>
            <if test="lastScanState != null">last_scan_state,</if>
            <if test="lastScanTime != null">last_scan_time,</if>
            <if test="vulnNum != null">vuln_num,</if>
            <if test="vulnUpdateTime != null">vuln_update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="ipv4 != null">#{ipv4},</if>
            <if test="ipv6 != null">#{ipv6},</if>
            <if test="mac != null">#{mac},</if>
            <if test="macType != null">#{macType},</if>
            <if test="defaultGateway != null">#{defaultGateway},</if>
            <if test="assetVlan != null">#{assetVlan},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="domainId != null">#{domainId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="mainIp != null">#{mainIp},</if>
            <if test="lastScanState != null">#{lastScanState},</if>
            <if test="lastScanTime != null">#{lastScanTime},</if>
            <if test="vulnNum != null">#{vulnNum},</if>
            <if test="vulnUpdateTime != null">#{vulnUpdateTime},</if>
         </trim>
    </insert>

    <update id="updateTblNetworkIpMac" parameterType="com.ruoyi.safe.domain.TblNetworkIpMac">
        update tbl_network_ip_mac
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="ipv4 != null">ipv4 = #{ipv4},</if>
            <if test="ipv6 != null">ipv6 = #{ipv6},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="macType != null">mac_type = #{macType},</if>
            <if test="defaultGateway != null">default_gateway = #{defaultGateway},</if>
            <if test="assetVlan != null">asset_vlan = #{assetVlan},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="domainId != null">domain_id = #{domainId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="mainIp != null">main_ip = #{mainIp},</if>
            <if test="lastScanState != null">last_scan_state = #{lastScanState},</if>
            <if test="lastScanTime != null">last_scan_time = #{lastScanTime},</if>
            <if test="vulnNum != null">vuln_num = #{vulnNum},</if>
            <if test="vulnUpdateTime != null">vuln_update_time = #{vulnUpdateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateScheduleInfo" parameterType="com.ruoyi.safe.domain.TblNetworkIpMac">
        update tbl_network_ip_mac
        <trim prefix="SET" suffixOverrides=",">
            <if test="mac != null and mac != ''">mac = #{mac},</if>
            <if test="lastScanState != null">last_scan_state = #{lastScanState},</if>
            <if test="lastScanTime != null">last_scan_time = #{lastScanTime},</if>
            <if test="vulnNum != null">vuln_num = #{vulnNum},</if>
            <if test="vulnUpdateTime != null">vuln_update_time = #{vulnUpdateTime},</if>
            <if test="ipv6 != null">ipv6 = #{ipv6},</if>
            <if test="defaultGateway != null">default_gateway = #{defaultGateway},</if>
            <if test="networkCardName != null">network_card_name = #{networkCardName},</if>
        </trim>
        <where>
            <if test="assetId != null">and asset_id = #{assetId}</if>
            <if test="ipv4 != null"> and ipv4 = #{ipv4}</if>
        </where>
    </update>

    <update id="updateLastScanStateByAssetIds">
        update tbl_network_ip_mac set last_scan_state = #{state}
        <where>
            main_ip = '1'
            and asset_id in
            <foreach item="assetId" collection="assetIds" open="(" separator="," close=")">
                #{assetId}
            </foreach>
        </where>
    </update>

    <delete id="deleteTblNetworkIpMacById" parameterType="java.lang.Long">
        delete from tbl_network_ip_mac where id = #{id}
    </delete>

    <delete id="deleteTblNetworkIpMacByIds" parameterType="java.lang.String">
        delete from tbl_network_ip_mac where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTblNetworkIpMacByAssetId" parameterType="java.lang.Long">
        delete from tbl_network_ip_mac where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblNetworkIpMacByAssetIds" parameterType="java.lang.String">
        delete from tbl_network_ip_mac where asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
</mapper>
