<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.FfsafeIpfilterBlockingMapper">

    <resultMap type="FfsafeIpfilterBlocking" id="FfsafeIpfilterBlockingResult">
        <result property="id"    column="id"    />
        <result property="ip"    column="ip"    />
        <result property="location"    column="location"    />
        <result property="action"    column="action"    />
        <result property="conditions"    column="conditions"    />
        <result property="filterSrc"    column="filter_src"    />
        <result property="createTime"    column="create_time"    />
        <result property="releaseTime"    column="release_time"    />
        <result property="remark"    column="remark"    />
        <result property="deviceConfigId" column="device_config_id" />
    </resultMap>

    <sql id="selectFfsafeIpfilterBlockingVo">
        select id, ip, location, action, conditions, filter_src, create_time, release_time, remark,device_config_id from ffsafe_ipfilter_blocking
    </sql>

    <select id="selectFfsafeIpfilterBlockingList" parameterType="FfsafeIpfilterBlocking" resultMap="FfsafeIpfilterBlockingResult">
        <include refid="selectFfsafeIpfilterBlockingVo"/>
        <where>
            <if test="ip != null  and ip != ''"> and ip like concat('%',#{ip},'%')</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
            <if test="action != null  and action != ''"> and action = #{action}</if>
            <if test="conditions != null  and conditions != ''"> and conditions = #{conditions}</if>
            <if test="filterSrc != null  and filterSrc != ''"> and filter_src = #{filterSrc}</if>
            <if test="releaseTime != null "> and release_time = #{releaseTime}</if>
            <if test="deviceConfigId != null"> and device_config_id = #{deviceConfigId}</if>
            <if test="startTime != null and endTime != null">
            and (create_time between #{startTime} and #{endTime})
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectFfsafeIpfilterBlockingById" parameterType="Long" resultMap="FfsafeIpfilterBlockingResult">
        <include refid="selectFfsafeIpfilterBlockingVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeIpfilterBlockingByIds" parameterType="Long" resultMap="FfsafeIpfilterBlockingResult">
        <include refid="selectFfsafeIpfilterBlockingVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeIpfilterBlocking" parameterType="FfsafeIpfilterBlocking">
        insert into ffsafe_ipfilter_blocking
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="ip != null">ip,</if>
            <if test="location != null">location,</if>
            <if test="action != null">action,</if>
            <if test="conditions != null">conditions,</if>
            <if test="filterSrc != null">filter_src,</if>
            <if test="createTime != null">create_time,</if>
            <if test="releaseTime != null">release_time,</if>
            <if test="remark != null">remark,</if>
            <if test="deviceConfigId != null">device_config_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="ip != null">#{ip},</if>
            <if test="location != null">#{location},</if>
            <if test="action != null">#{action},</if>
            <if test="conditions != null">#{conditions},</if>
            <if test="filterSrc != null">#{filterSrc},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="releaseTime != null">#{releaseTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deviceConfigId != null">#{deviceConfigId},</if>
         </trim>
    </insert>
    <insert id="batchInsert">
        insert into ffsafe_ipfilter_blocking(id, ip, location, action, conditions, filter_src, create_time, release_time, remark, device_config_id) values
        <foreach collection="saveList" item="item" index="index" separator=",">
            (#{item.id}, #{item.ip}, #{item.location}, #{item.action}, #{item.conditions}, #{item.filterSrc}, #{item.createTime}, #{item.releaseTime}, #{item.remark}, #{item.deviceConfigId})
        </foreach>
    </insert>

    <update id="updateFfsafeIpfilterBlocking" parameterType="FfsafeIpfilterBlocking">
        update ffsafe_ipfilter_blocking
        <trim prefix="SET" suffixOverrides=",">
            <if test="ip != null">ip = #{ip},</if>
            <if test="location != null">location = #{location},</if>
            <if test="action != null">action = #{action},</if>
            <if test="conditions != null">conditions = #{conditions},</if>
            <if test="filterSrc != null">filter_src = #{filterSrc},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="releaseTime != null">release_time = #{releaseTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deviceConfigId != null">device_config_id = #{deviceConfigId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeIpfilterBlockingById" parameterType="Long">
        delete from ffsafe_ipfilter_blocking where id = #{id}
    </delete>

    <delete id="deleteFfsafeIpfilterBlockingByIds" parameterType="String">
        delete from ffsafe_ipfilter_blocking where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="clear">
        delete from ffsafe_ipfilter_blocking
    </delete>
</mapper>
