<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblAssetsTypeMapper">
    
    <resultMap type="TblAssetsType" id="TblAssetsTypeResult">
        <result property="tcd"    column="TCD"    />
        <result property="name"    column="name"    />
        <result property="pid"    column="PID"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="ordno"        column="ordno" />
        <result property="memo"     column="memo" />
    </resultMap>

    <sql id="selectTblAssetsTypeVo">
        select tcd, name, pid, ancestors, ordno, memo from tbl_assets_type
    </sql>

    <select id="selectTblAssetsTypeList" parameterType="TblAssetsType" resultMap="TblAssetsTypeResult">
        <include refid="selectTblAssetsTypeVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="pid != null "> and PID = #{pid}</if>
            <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
        </where>
        order by pid, ordno
    </select>
    <select id="selectChildrenTypeByTcd" parameterType="Long" resultMap="TblAssetsTypeResult">
        select * from tbl_assets_type where find_in_set(#{tcd}, ancestors)
    </select>

    <select id="selectTblAssetsTypeByTcd" parameterType="Long" resultMap="TblAssetsTypeResult">
        <include refid="selectTblAssetsTypeVo"/>
        where TCD = #{tcd}
    </select>
        
    <insert id="insertTblAssetsType" parameterType="TblAssetsType">
        insert into tbl_assets_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tcd != null">TCD,</if>
            <if test="name != null">name,</if>
            <if test="pid != null">PID,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="ordno != null">ordno,</if>
            <if test="memo != null">memo,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tcd != null">#{tcd},</if>
            <if test="name != null">#{name},</if>
            <if test="pid != null">#{pid},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="ordno != null">#{ordno},</if>
            <if test="memo != null">#{memo},</if>
         </trim>
    </insert>

    <update id="updateTblAssetsType" parameterType="TblAssetsType">
        update tbl_assets_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="pid != null">PID = #{pid},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="ordno != null">ordno = #{ordno},</if>
            <if test="memo != null">memo = #{memo},</if>
        </trim>
        where TCD = #{tcd}
    </update>

    <delete id="deleteTblAssetsTypeByTcd" parameterType="Long">
        delete from tbl_assets_type where TCD = #{tcd}
    </delete>

    <delete id="deleteTblAssetsTypeByTcds" parameterType="String">
        delete from tbl_assets_type where TCD in 
        <foreach item="tcd" collection="array" open="(" separator="," close=")">
            #{tcd}
        </foreach>
    </delete>
</mapper>