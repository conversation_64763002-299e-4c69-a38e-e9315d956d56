<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeWebmonitorSensitivefileMapper">

    <resultMap type="FfsafeWebmonitorSensitivefile" id="FfsafeWebmonitorSensitivefileResult">
        <result property="id"    column="id"    />
        <result property="sensitiveType"    column="sensitive_type"    />
        <result property="url"    column="url"    />
        <result property="systemName"    column="system_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="handleStatus"    column="handle_status"    />
        <result property="word"    column="word"    />
        <result property="source"    column="source"    />
        <result property="recentlyTime"    column="recently_time"    />
    </resultMap>

    <sql id="selectFfsafeWebmonitorSensitivefileVo">
        select id, sensitive_type, url, system_name, file_url, handle_status, word, source, recently_time from ffsafe_webmonitor_sensitivefile
    </sql>

    <select id="selectFfsafeWebmonitorSensitivefileList" parameterType="FfsafeWebmonitorSensitivefile" resultMap="FfsafeWebmonitorSensitivefileResult">
        <include refid="selectFfsafeWebmonitorSensitivefileVo"/>
        <where>
            <if test="sensitiveType != null  and sensitiveType != ''"> and sensitive_type = #{sensitiveType}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="handleStatus != null  and handleStatus != ''"> and handle_status = #{handleStatus}</if>
            <if test="word != null  and word != ''"> and word = #{word}</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
            <if test="recentlyTime != null "> and recently_time = #{recentlyTime}</if>
        </where>
    </select>

    <select id="selectFfsafeWebmonitorSensitivefileById" parameterType="Long" resultMap="FfsafeWebmonitorSensitivefileResult">
        <include refid="selectFfsafeWebmonitorSensitivefileVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeWebmonitorSensitivefileByIds" parameterType="Long" resultMap="FfsafeWebmonitorSensitivefileResult">
        <include refid="selectFfsafeWebmonitorSensitivefileVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeWebmonitorSensitivefile" parameterType="FfsafeWebmonitorSensitivefile" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_webmonitor_sensitivefile
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sensitiveType != null">sensitive_type,</if>
            <if test="url != null">url,</if>
            <if test="systemName != null">system_name,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="handleStatus != null">handle_status,</if>
            <if test="word != null">word,</if>
            <if test="source != null">source,</if>
            <if test="recentlyTime != null">recently_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sensitiveType != null">#{sensitiveType},</if>
            <if test="url != null">#{url},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="handleStatus != null">#{handleStatus},</if>
            <if test="word != null">#{word},</if>
            <if test="source != null">#{source},</if>
            <if test="recentlyTime != null">#{recentlyTime},</if>
        </trim>
    </insert>

    <update id="updateFfsafeWebmonitorSensitivefile" parameterType="FfsafeWebmonitorSensitivefile">
        update ffsafe_webmonitor_sensitivefile
        <trim prefix="SET" suffixOverrides=",">
            <if test="sensitiveType != null">sensitive_type = #{sensitiveType},</if>
            <if test="url != null">url = #{url},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
            <if test="word != null">word = #{word},</if>
            <if test="source != null">source = #{source},</if>
            <if test="recentlyTime != null">recently_time = #{recentlyTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeWebmonitorSensitivefileById" parameterType="Long">
        delete from ffsafe_webmonitor_sensitivefile where id = #{id}
    </delete>

    <delete id="deleteFfsafeWebmonitorSensitivefileByIds" parameterType="String">
        delete from ffsafe_webmonitor_sensitivefile where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>