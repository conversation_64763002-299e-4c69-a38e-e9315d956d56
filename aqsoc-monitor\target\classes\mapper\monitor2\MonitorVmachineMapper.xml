<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.MonitorVmachineMapper">
    
    <resultMap type="MonitorVmachine" id="MonitorVmachineResult">
        <result property="tid"    column="tid"    />
        <result property="name"    column="name"    />
        <result property="brand"    column="brand"    />
        <result property="location"    column="location"    />
        <result property="assetno"    column="assetno"    />
        <result property="greed"    column="greed"    />
        <result property="fgrpno"    column="fgrpno"    />
        <result property="pid"    column="pid"    />
        <result property="ip"    column="IP"    />
        <result property="ipv4"    column="ipv4"    />
        <result property="ipv6"    column="ipv6"    />
        <result property="mac"    column="mac"    />
        <result property="tags"    column="tags"    />
        <result property="memo"    column="memo"    />
        <result property="state"    column="state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="osVer"    column="os_ver"    />
        <result property="isoften"    column="isoften"    />
        <result property="isvm"    column="isvm"    />
        <result property="istest"    column="istest"    />
        <result property="cpuXh"    column="cpu_xh"    />
        <result property="cpuNum"    column="cpu_num"    />
        <result property="cpuPer"    column="cpu_per"    />
        <result property="memSum"    column="mem_sum"    />
        <result property="memPer"    column="mem_per"    />
    </resultMap>

    <sql id="selectMonitorVmachineVo">
        select tid, name, brand, location, assetno, greed, fgrpno, pid, IP, ipv4, ipv6, mac, tags, memo, state, create_by, create_time, update_by, update_time, os_ver, isoften, isvm, istest, cpu_xh, cpu_num, cpu_per, mem_sum, mem_per from monitor_vmachine
    </sql>

    <select id="selectMonitorVmachineList" parameterType="MonitorVmachine" resultMap="MonitorVmachineResult">
        <include refid="selectMonitorVmachineVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
        </where>
    </select>
    
    <select id="selectMonitorVmachineByTid" parameterType="String" resultMap="MonitorVmachineResult">
        <include refid="selectMonitorVmachineVo"/>
        where tid = #{tid}
    </select>
    <select id="selectMaxTid" parameterType="String" resultType="java.lang.String">
        select tid from monitor_vmachine where tid like concat("%", #{prix}, "%")
        order by tid desc limit 1
    </select>
        
    <insert id="insertMonitorVmachine" parameterType="MonitorVmachine">
        insert into monitor_vmachine
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tid != null">tid,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="brand != null">brand,</if>
            <if test="location != null">location,</if>
            <if test="assetno != null">assetno,</if>
            <if test="greed != null">greed,</if>
            <if test="fgrpno != null">fgrpno,</if>
            <if test="pid != null">pid,</if>
            <if test="ip != null">IP,</if>
            <if test="ipv4 != null">ipv4,</if>
            <if test="ipv6 != null">ipv6,</if>
            <if test="mac != null">mac,</if>
            <if test="tags != null">tags,</if>
            <if test="memo != null">memo,</if>
            <if test="state != null and state != ''">state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="osVer != null">os_ver,</if>
            <if test="isoften != null">isoften,</if>
            <if test="isvm != null and isvm != ''">isvm,</if>
            <if test="istest != null">istest,</if>
            <if test="cpuXh != null">cpu_xh,</if>
            <if test="cpuNum != null">cpu_num,</if>
            <if test="cpuPer != null">cpu_per,</if>
            <if test="memSum != null">mem_sum,</if>
            <if test="memPer != null">mem_per,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tid != null">#{tid},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="brand != null">#{brand},</if>
            <if test="location != null">#{location},</if>
            <if test="assetno != null">#{assetno},</if>
            <if test="greed != null">#{greed},</if>
            <if test="fgrpno != null">#{fgrpno},</if>
            <if test="pid != null">#{pid},</if>
            <if test="ip != null">#{ip},</if>
            <if test="ipv4 != null">#{ipv4},</if>
            <if test="ipv6 != null">#{ipv6},</if>
            <if test="mac != null">#{mac},</if>
            <if test="tags != null">#{tags},</if>
            <if test="memo != null">#{memo},</if>
            <if test="state != null and state != ''">#{state},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="osVer != null">#{osVer},</if>
            <if test="isoften != null">#{isoften},</if>
            <if test="isvm != null and isvm != ''">#{isvm},</if>
            <if test="istest != null">#{istest},</if>
            <if test="cpuXh != null">#{cpuXh},</if>
            <if test="cpuNum != null">#{cpuNum},</if>
            <if test="cpuPer != null">#{cpuPer},</if>
            <if test="memSum != null">#{memSum},</if>
            <if test="memPer != null">#{memPer},</if>
         </trim>
    </insert>

    <update id="updateMonitorVmachine" parameterType="MonitorVmachine">
        update monitor_vmachine
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="location != null">location = #{location},</if>
            <if test="assetno != null">assetno = #{assetno},</if>
            <if test="greed != null">greed = #{greed},</if>
            <if test="fgrpno != null">fgrpno = #{fgrpno},</if>
            <if test="pid != null">pid = #{pid},</if>
            <if test="ip != null">IP = #{ip},</if>
            <if test="ipv4 != null">ipv4 = #{ipv4},</if>
            <if test="ipv6 != null">ipv6 = #{ipv6},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="osVer != null">os_ver = #{osVer},</if>
            <if test="isoften != null">isoften = #{isoften},</if>
            <if test="isvm != null and isvm != ''">isvm = #{isvm},</if>
            <if test="istest != null">istest = #{istest},</if>
            <if test="cpuXh != null">cpu_xh = #{cpuXh},</if>
            <if test="cpuNum != null">cpu_num = #{cpuNum},</if>
            <if test="cpuPer != null">cpu_per = #{cpuPer},</if>
            <if test="memSum != null">mem_sum = #{memSum},</if>
            <if test="memPer != null">mem_per = #{memPer},</if>
        </trim>
        where tid = #{tid}
    </update>

    <delete id="deleteMonitorVmachineByTid" parameterType="String">
        delete from monitor_vmachine where tid = #{tid}
    </delete>

    <delete id="deleteMonitorVmachineByTids" parameterType="String">
        delete from monitor_vmachine where tid in 
        <foreach item="tid" collection="array" open="(" separator="," close=")">
            #{tid}
        </foreach>
    </delete>
</mapper>