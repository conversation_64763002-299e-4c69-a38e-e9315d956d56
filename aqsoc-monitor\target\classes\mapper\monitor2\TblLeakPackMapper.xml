<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblLeakPackMapper">
    
    <resultMap type="TblLeakPack" id="TblLeakPackResult">
        <result property="cnpd"    column="CNPD"    />
        <result property="cnnvd"    column="cnnvd"    />
        <result property="name"    column="name"    />
        <result property="manu"    column="manu"    />
        <result property="homepage"    column="homepage"    />
        <result property="length"    column="length"    />
        <result property="addr"    column="addr"    />
        <result property="grade"    column="grade"    />
        <result property="pubtime"    column="pubtime"    />
    </resultMap>

    <sql id="selectTblLeakPackVo">
        select CNPD, cnnvd, name, manu, homepage, length, addr, grade, pubtime from tbl_leak_pack
    </sql>

    <select id="selectTblLeakPackList" parameterType="TblLeakPack" resultMap="TblLeakPackResult">
        <include refid="selectTblLeakPackVo"/>
        <where>  
            <if test="cnnvd != null  and cnnvd != ''"> and cnnvd = #{cnnvd}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="manu != null  and manu != ''"> and manu = #{manu}</if>
            <if test="homepage != null  and homepage != ''"> and homepage = #{homepage}</if>
            <if test="length != null  and length != ''"> and length = #{length}</if>
            <if test="addr != null  and addr != ''"> and addr = #{addr}</if>
            <if test="grade != null  and grade != ''"> and grade = #{grade}</if>
            <if test="pubtime != null "> and pubtime = #{pubtime}</if>
        </where>
    </select>
    
    <select id="selectTblLeakPackByCnpd" parameterType="String" resultMap="TblLeakPackResult">
        <include refid="selectTblLeakPackVo"/>
        where CNPD = #{cnpd}
    </select>
        
    <insert id="insertTblLeakPack" parameterType="TblLeakPack">
        insert into tbl_leak_pack
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cnpd != null">CNPD,</if>
            <if test="cnnvd != null">cnnvd,</if>
            <if test="name != null">name,</if>
            <if test="manu != null">manu,</if>
            <if test="homepage != null">homepage,</if>
            <if test="length != null">length,</if>
            <if test="addr != null">addr,</if>
            <if test="grade != null">grade,</if>
            <if test="pubtime != null">pubtime,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cnpd != null">#{cnpd},</if>
            <if test="cnnvd != null">#{cnnvd},</if>
            <if test="name != null">#{name},</if>
            <if test="manu != null">#{manu},</if>
            <if test="homepage != null">#{homepage},</if>
            <if test="length != null">#{length},</if>
            <if test="addr != null">#{addr},</if>
            <if test="grade != null">#{grade},</if>
            <if test="pubtime != null">#{pubtime},</if>
         </trim>
    </insert>

    <update id="updateTblLeakPack" parameterType="TblLeakPack">
        update tbl_leak_pack
        <trim prefix="SET" suffixOverrides=",">
            <if test="cnnvd != null">cnnvd = #{cnnvd},</if>
            <if test="name != null">name = #{name},</if>
            <if test="manu != null">manu = #{manu},</if>
            <if test="homepage != null">homepage = #{homepage},</if>
            <if test="length != null">length = #{length},</if>
            <if test="addr != null">addr = #{addr},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="pubtime != null">pubtime = #{pubtime},</if>
        </trim>
        where CNPD = #{cnpd}
    </update>

    <delete id="deleteTblLeakPackByCnpd" parameterType="String">
        delete from tbl_leak_pack where CNPD = #{cnpd}
    </delete>

    <delete id="deleteTblLeakPackByCnpds" parameterType="String">
        delete from tbl_leak_pack where CNPD in 
        <foreach item="cnpd" collection="array" open="(" separator="," close=")">
            #{cnpd}
        </foreach>
    </delete>
</mapper>