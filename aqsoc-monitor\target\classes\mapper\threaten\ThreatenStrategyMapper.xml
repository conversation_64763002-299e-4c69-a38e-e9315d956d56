<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.strategy.config.mapper.ThreatenStrategyMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.strategy.config.model.ThreatenStrategyListVO">
        select a.*, b.strategy_name AS strategyName from threaten_strategy a left join threaten_strategy_class b on a.class_id = b.id
        <where>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.classId!=null">
                and a.class_id = #{query.classId}
            </if>
            <if test="query.name!=null and query.name!=''">
                and a.name like concat('%', #{query.name}, '%')
            </if>
            <if test="query.description!=null and query.description!=''">
                and a.description like concat('%', #{query.description}, '%')
            </if>
            <if test="query.sortNum!=null">
                and a.sort_num = #{query.sortNum}
            </if>
            <if test="query.parameterJson!=null and query.parameterJson!=''">
                and a.parameter_json like concat('%', #{query.parameterJson}, '%')
            </if>
        </where>
        order by sort_num
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.strategy.config.model.ThreatenStrategyListVO">
        select a.* from threaten_strategy a 
        where a.id=#{id}
    </select>
</mapper>
