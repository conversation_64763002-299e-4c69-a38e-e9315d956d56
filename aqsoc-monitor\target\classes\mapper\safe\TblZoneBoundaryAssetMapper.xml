<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblZoneBoundaryAssetMapper">
    
    <resultMap type="TblZoneBoundaryAsset" id="TblZoneBoundaryAssetResult">
        <result property="id"    column="id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="boundaryId"    column="boundary_id"    />
        <result property="assetName"    column="asset_name"    />
        <result property="assetCode"    column="asset_code"    />
        <result property="assetTypeDesc"    column="asset_type_desc"    />
        <result property="BoundaryDegreeImportance"    column="boundary_degree_importance"    />
        <result property="boundaryName"    column="boundary_name"    />
        <result property="degreeImportance"    column="degree_importance"    />
    </resultMap>

    <sql id="selectTblZoneBoundaryAssetVo">
        select a.id, a.asset_id, a.boundary_id,b.asset_name,b.asset_code,b.asset_type_desc,
               b.asset_name as boundary_name,c.degree_importance as boundary_degree_importance
        from tbl_zone_boundary_asset a
        left join tbl_asset_overview b on a.asset_id = b.asset_id
        left join tbl_zone_boundary c on a.boundary_id = c.asset_id
    </sql>

    <select id="selectTblZoneBoundaryAssetList" parameterType="TblZoneBoundaryAsset" resultMap="TblZoneBoundaryAssetResult">
        <include refid="selectTblZoneBoundaryAssetVo"/>
        <where>  
            <if test="assetId != null "> and a.asset_id = #{assetId}</if>
            <if test="boundaryId != null "> and boundary_id = #{boundaryId}</if>
            <if test="assetName != null "> and b.asset_name = #{assetName}</if>
            <if test="assetCode != null "> and b.asset_code = #{assetCode}</if>
            <if test="assetClass != null "> and b.asset_class = #{assetClass}</if>
            <if test="assetType != null "> and b.asset_type = #{assetType}</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
    </select>
    
    <select id="selectTblZoneBoundaryAssetById" parameterType="Long" resultMap="TblZoneBoundaryAssetResult">
        <include refid="selectTblZoneBoundaryAssetVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblZoneBoundaryAsset" parameterType="TblZoneBoundaryAsset">
        insert into tbl_zone_boundary_asset
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="boundaryId != null">boundary_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="boundaryId != null">#{boundaryId},</if>
         </trim>
    </insert>
    <insert id="insertTblZoneBoundaryAssets" parameterType="TblZoneBoundaryAsset">
        <if test="bas!=null">
        insert into tbl_zone_boundary_asset
            ( asset_id,
            boundary_id)
            <foreach collection="bas" open="value " item="item" separator=",">
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="1==1">#{item.assetId},</if>
                    <if test="1==1">#{item.boundaryId},</if>
                </trim>
            </foreach>
        </if>
    </insert>

    <update id="updateTblZoneBoundaryAsset" parameterType="TblZoneBoundaryAsset">
        update tbl_zone_boundary_asset
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="boundaryId != null">boundary_id = #{boundaryId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblZoneBoundaryAssetById" parameterType="Long">
        delete from tbl_zone_boundary_asset where id = #{id}
    </delete>

    <delete id="deleteTblZoneBoundaryAssetByIds" parameterType="String">
        delete from tbl_zone_boundary_asset where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>