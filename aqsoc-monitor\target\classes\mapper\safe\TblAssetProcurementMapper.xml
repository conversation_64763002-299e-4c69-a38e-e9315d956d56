<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblAssetProcurementMapper">
    
    <resultMap type="TblAssetProcurement" id="TblAssetProcurementResult">
        <result property="id"    column="id"    />
        <result property="procCode"    column="proc_code"    />
        <result property="projCode"    column="proj_code"    />
        <result property="procName"    column="proc_name"    />
        <result property="assetId"    column="asset_id"    />
        <result property="assetClass"    column="asset_class"    />
        <result property="assetName"    column="asset_name"    />
        <result property="assetNum"    column="asset_num"    />
        <result property="maintenanceTime"    column="maintenance_time"    />
        <result property="qualityTime"    column="quality_time"    />
        <result property="vendor"    column="vendor"    />
        <result property="brandModel"    column="brand_model"    />
        <result property="procRoute"    column="proc_route"    />
        <result property="procAmount"    column="proc_amount"    />
        <result property="procTime"    column="proc_time"    />
        <result property="arriveTime"    column="arrive_time"    />
        <result property="procInitiator"    column="proc_initiator"    />
        <result property="initatorPhone"    column="initator_phone"    />
        <result property="procSatrap"    column="proc_satrap"    />
        <result property="satrapPhone"    column="satrap_phone"    />
        <result property="procAgent"    column="proc_agent"    />
        <result property="agentPhone"    column="agent_phone"    />
        <result property="procAcceptor"    column="proc_acceptor"    />
        <result property="acceptorPhone"    column="acceptor_phone"    />
        <result property="checkFile"    column="check_file"    />
        <result property="distributionMode"    column="distribution_mode"    />
        <result property="procReason"    column="proc_reason"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="processKey"    column="process_key"    />
        <result property="instanceId"    column="instance_id"    />
        <result property="state"    column="state"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="cilentId"    column="cilent_id"    />
        <result property="procDept"    column="proc_dept"    />
        <result property="procDeptName"    column="proc_dept_name"    />
        <result property="depTime"    column="dep_time"    />
    </resultMap>

    <sql id="selectTblAssetProcurementVo">
        select a.id, proc_code, proj_code, proc_name, asset_id, asset_class, asset_name, asset_num, maintenance_time, quality_time,
        vendor, brand_model, proc_route, proc_amount, proc_time, arrive_time, proc_initiator, initator_phone, proc_satrap,
        satrap_phone, proc_agent, agent_phone, proc_acceptor, acceptor_phone, check_file, distribution_mode, proc_reason,
        a.create_time, a.create_by, a.update_time, a.update_by, a.remark, process_key, instance_id, a.state, a.user_id, a.dept_id, a.cilent_id,
        a.dep_time,a.proc_dept,b.dept_name as proc_dept_name
        from tbl_asset_procurement a
        left join sys_dept b on proc_dept = b.dept_id
    </sql>

    <select id="selectTblAssetProcurementList" parameterType="TblAssetProcurement" resultMap="TblAssetProcurementResult">
        <include refid="selectTblAssetProcurementVo"/>
        <where>  
            <if test="procCode != null  and procCode != ''"> and proc_code like concat('%',#{procCode},'%')</if>
            <if test="projCode != null  and projCode != ''"> and proj_code = #{projCode}</if>
            <if test="procName != null  and procName != ''"> and proc_name like concat('%', #{procName}, '%')</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="vendor != null  and vendor != ''"> and vendor = #{vendor}</if>
            <if test="brandModel != null  and brandModel != ''"> and brand_model = #{brandModel}</if>
            <if test="procRoute != null  and procRoute != ''"> and proc_route = #{procRoute}</if>
            <if test="procAmount != null  and procAmount != ''"> and proc_amount = #{procAmount}</if>
            <if test="procSatrap != null  and procSatrap != ''"> and proc_satrap = #{procSatrap}</if>
            <if test="assetId != null  and assetId != ''"> and asset_id = #{assetId}</if>
            <if test="maintenanceTime != null  and maintenanceTime != ''"> and maintenance_time <![CDATA[<=]]]> #{maintenanceTime}</if>
            <if test="maintenanceTime != null  and maintenanceTime != ''"> and maintenance_time <![CDATA[>]]]> current_timestamp()</if>
            <if test="procDept != null "> and find_in_set(#{procDept},b.ancestors)</if>
        </where>
    </select>
    
    <select id="selectTblAssetProcurementById" parameterType="Long" resultMap="TblAssetProcurementResult">
        <include refid="selectTblAssetProcurementVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblAssetProcurement" parameterType="TblAssetProcurement">
        insert into tbl_asset_procurement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="procCode != null">proc_code,</if>
            <if test="projCode != null">proj_code,</if>
            <if test="procName != null and procName != ''">proc_name,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="assetClass != null">asset_class,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="assetNum != null">asset_num,</if>
            <if test="maintenanceTime != null">maintenance_time,</if>
            <if test="qualityTime != null">quality_time,</if>
            <if test="vendor != null">vendor,</if>
            <if test="brandModel != null">brand_model,</if>
            <if test="procRoute != null">proc_route,</if>
            <if test="procAmount != null and procAmount != ''">proc_amount,</if>
            <if test="procTime != null">proc_time,</if>
            <if test="arriveTime != null">arrive_time,</if>
            <if test="procInitiator != null">proc_initiator,</if>
            <if test="initatorPhone != null">initator_phone,</if>
            <if test="procSatrap != null">proc_satrap,</if>
            <if test="satrapPhone != null">satrap_phone,</if>
            <if test="procAgent != null">proc_agent,</if>
            <if test="agentPhone != null">agent_phone,</if>
            <if test="procAcceptor != null">proc_acceptor,</if>
            <if test="acceptorPhone != null">acceptor_phone,</if>
            <if test="checkFile != null">check_file,</if>
            <if test="distributionMode != null">distribution_mode,</if>
            <if test="procReason != null">proc_reason,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="processKey != null">process_key,</if>
            <if test="instanceId != null">instance_id,</if>
            <if test="state != null">state,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="cilentId != null">cilent_id,</if>
            <if test="procDept != null">proc_dept,</if>
            <if test="depTime != null">dep_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="procCode != null">#{procCode},</if>
            <if test="projCode != null">#{projCode},</if>
            <if test="procName != null and procName != ''">#{procName},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="assetClass != null">#{assetClass},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="assetNum != null">#{assetNum},</if>
            <if test="maintenanceTime != null">#{maintenanceTime},</if>
            <if test="qualityTime != null">#{qualityTime},</if>
            <if test="vendor != null">#{vendor},</if>
            <if test="brandModel != null">#{brandModel},</if>
            <if test="procRoute != null">#{procRoute},</if>
            <if test="procAmount != null and procAmount != ''">#{procAmount},</if>
            <if test="procTime != null">#{procTime},</if>
            <if test="arriveTime != null">#{arriveTime},</if>
            <if test="procInitiator != null">#{procInitiator},</if>
            <if test="initatorPhone != null">#{initatorPhone},</if>
            <if test="procSatrap != null">#{procSatrap},</if>
            <if test="satrapPhone != null">#{satrapPhone},</if>
            <if test="procAgent != null">#{procAgent},</if>
            <if test="agentPhone != null">#{agentPhone},</if>
            <if test="procAcceptor != null">#{procAcceptor},</if>
            <if test="acceptorPhone != null">#{acceptorPhone},</if>
            <if test="checkFile != null">#{checkFile},</if>
            <if test="distributionMode != null">#{distributionMode},</if>
            <if test="procReason != null">#{procReason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="processKey != null">#{processKey},</if>
            <if test="instanceId != null">#{instanceId},</if>
            <if test="state != null">#{state},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="cilentId != null">#{cilentId},</if>
            <if test="procDept != null">#{procDept},</if>
            <if test="depTime != null">#{depTime},</if>
         </trim>
    </insert>

    <update id="updateTblAssetProcurement" parameterType="TblAssetProcurement">
        update tbl_asset_procurement
        <trim prefix="SET" suffixOverrides=",">
            <if test="procCode != null">proc_code = #{procCode},</if>
            <if test="projCode != null">proj_code = #{projCode},</if>
            <if test="procName != null and procName != ''">proc_name = #{procName},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="assetClass != null">asset_class = #{assetClass},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="assetNum != null">asset_num = #{assetNum},</if>
            <if test="maintenanceTime != null">maintenance_time = #{maintenanceTime},</if>
            <if test="qualityTime != null">quality_time = #{qualityTime},</if>
            <if test="vendor != null">vendor = #{vendor},</if>
            <if test="brandModel != null">brand_model = #{brandModel},</if>
            <if test="procRoute != null">proc_route = #{procRoute},</if>
            <if test="procAmount != null and procAmount != ''">proc_amount = #{procAmount},</if>
            <if test="procTime != null">proc_time = #{procTime},</if>
            <if test="arriveTime != null">arrive_time = #{arriveTime},</if>
            <if test="procInitiator != null">proc_initiator = #{procInitiator},</if>
            <if test="initatorPhone != null">initator_phone = #{initatorPhone},</if>
            <if test="procSatrap != null">proc_satrap = #{procSatrap},</if>
            <if test="satrapPhone != null">satrap_phone = #{satrapPhone},</if>
            <if test="procAgent != null">proc_agent = #{procAgent},</if>
            <if test="agentPhone != null">agent_phone = #{agentPhone},</if>
            <if test="procAcceptor != null">proc_acceptor = #{procAcceptor},</if>
            <if test="acceptorPhone != null">acceptor_phone = #{acceptorPhone},</if>
            <if test="checkFile != null">check_file = #{checkFile},</if>
            <if test="distributionMode != null">distribution_mode = #{distributionMode},</if>
            <if test="procReason != null">proc_reason = #{procReason},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="processKey != null">process_key = #{processKey},</if>
            <if test="instanceId != null">instance_id = #{instanceId},</if>
            <if test="state != null">state = #{state},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="cilentId != null">cilent_id = #{cilentId},</if>
            <if test="procDept != null">proc_dept = #{procDept},</if>
            <if test="depTime != null">dep_time = #{depTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblAssetProcurementById" parameterType="Long">
        delete from tbl_asset_procurement where id = #{id}
    </delete>

    <delete id="deleteTblAssetProcurementByIds" parameterType="String">
        delete from tbl_asset_procurement where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>