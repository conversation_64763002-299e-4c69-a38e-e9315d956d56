<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblDeployMapper">

    <resultMap type="TblDeploy" id="TblDeployResult">
        <result property="deployId"    column="deploy_id"    />
        <result property="deployType"    column="deploy_type"    />
        <result property="applicationId"    column="application_id"    />
        <result property="applicationName"    column="application_name"    />
        <result property="moduelId"    column="moduel_id"    />
        <result property="moduleName"    column="module_name"    />
        <result property="componentId"    column="component_id"    />
        <result property="prdid"    column="prdid"    />
        <result property="procName"    column="proc_name"    />
        <result property="dataResourceId"    column="data_resource_id"    />
        <result property="dataResourceName"    column="data_resource_name"    />
        <result property="assetId"    column="asset_id"    />
        <result property="assetName"    column="asset_name"    />
        <result property="deployPosition"    column="deploy_position"    />
        <result property="port"    column="port"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="softlx"    column="softlx"    />
        <result property="ip"    column="ip"    />
    </resultMap>

    <sql id="selectTblDeployVo">
        select deploy_id, deploy_type, application_id, application_name, moduel_id, module_name,component_id, prdid, proc_name, data_resource_id, data_resource_name, asset_id, asset_name, deploy_position, port, remark, create_by, create_time,softlx,
               (select GROUP_CONCAT(ipv4) from tbl_network_ip_mac net where net.asset_id = a.asset_id and main_ip = '1') as ip
        from tbl_deploy a
    </sql>

    <select id="selectTblDeployList" parameterType="TblDeploy" resultMap="TblDeployResult">
        <include refid="selectTblDeployVo"/>
        <where>
            <if test="deployType != null  and deployType != ''"> and deploy_type = #{deployType}</if>
            <if test="applicationId != null "> and application_id = #{applicationId}</if>
            <if test="applicationName != null  and applicationName != ''"> and application_name like concat('%', #{applicationName}, '%')</if>
            <if test="moduelId != null "> and moduel_id = #{moduelId}</if>
            <if test="moduleName != null  and moduleName != ''"> and module_name like concat('%', #{moduleName}, '%')</if>
            <if test="componentId != null  and componentId != ''"> and component_id = #{componentId} </if>
            <if test="prdid != null "> and prdid = #{prdid}</if>
            <if test="procName != null  and procName != ''"> and proc_name like concat('%', #{procName}, '%')</if>
            <if test="dataResourceId != null "> and data_resource_id = #{dataResourceId}</if>
            <if test="dataResourceName != null  and dataResourceName != ''"> and data_resource_name like concat('%', #{dataResourceName}, '%')</if>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="deployPosition != null  and deployPosition != ''"> and deploy_position = #{deployPosition}</if>
            <if test="port != null  and port != ''"> and port = #{port}</if>
            <if test="notDeployId != null"> and deploy_id != #{notDeployId}</if>
        </where>
    </select>
    <select id="selectTblDataDeployList" parameterType="TblDeploy" resultMap="TblDeployResult">
        <include refid="selectTblDeployVo"/>
        <where>
            <if test="deployType != null  and deployType != ''"> and deploy_type = #{deployType}</if>
            <if test="applicationId != null "> and application_id = #{applicationId}</if>
            <if test="applicationName != null  and applicationName != ''"> and application_name like concat('%', #{applicationName}, '%')</if>
            <if test="moduelId != null "> and moduel_id = #{moduelId}</if>
            <if test="moduleName != null  and moduleName != ''"> and module_name like concat('%', #{moduleName}, '%')</if>
            <if test="componentId != null  and componentId != ''"> and component_id = #{componentId} </if>
            <if test="prdid != null "> and prdid = #{prdid}</if>
            <if test="procName != null  and procName != ''"> and proc_name like concat('%', #{procName}, '%')</if>
            <if test="dataResourceId != null "> and data_resource_id not null</if>
            <if test="dataResourceId == null "> and data_resource_id = #{dataResourceId}</if>
            <if test="dataResourceName != null  and dataResourceName != ''"> and data_resource_name like concat('%', #{dataResourceName}, '%')</if>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="deployPosition != null  and deployPosition != ''"> and deploy_position = #{deployPosition}</if>
            <if test="port != null  and port != ''"> and port = #{port}</if>
        </where>
    </select>

    <select id="selectPcServerDeployList" parameterType="TblDeploy" resultMap="TblDeployResult">
        <include refid="selectTblDeployVo"/>
        <where>
            <if test="1 == 1"> and moduel_id is null and application_id is null and data_resource_id is null and softlx is null</if>
            <if test="deployType != null  and deployType != ''"> and deploy_type = #{deployType}</if>
            <if test="prdid != null "> and prdid = #{prdid}</if>
            <if test="procName != null  and procName != ''"> and proc_name like concat('%', #{procName}, '%')</if>
            <if test="dataResourceId != null "> and data_resource_id not null</if>
            <!--if test="dataResourceId == null "> and data_resource_id = #{dataResourceId}</if-->
            <if test="dataResourceName != null  and dataResourceName != ''"> and data_resource_name like concat('%', #{dataResourceName}, '%')</if>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="deployPosition != null  and deployPosition != ''"> and deploy_position = #{deployPosition}</if>
            <if test="port != null and port != ''"> and port = #{port}</if>
            <if test="softlx != null and softlx != ''"> and softlx = #{softlx}</if>
        </where>
    </select>

    <select id="selectTblDeployByDeployId" parameterType="Long" resultMap="TblDeployResult">
        <include refid="selectTblDeployVo"/>
        where deploy_id = #{deployId}
    </select>
    <select id="selectTblDeployByAssetId" parameterType="Long" resultMap="TblDeployResult">
        <include refid="selectTblDeployVo"/>
        where asset_id = #{assetId} and softlx= '1'
    </select>

    <select id="selectTblDeployByDeploy" parameterType="Long" resultMap="TblDeployResult">
        <include refid="selectTblDeployVo"/>
        <where>
        <if test="dep.deployType != null and deployType != ''">and deploy_type= #{dep.deployType}</if>
        <if test="dep.applicationId != null">and application_id= #{dep.applicationId}</if>
        <if test="dep.assetId != null">and asset_id= #{dep.assetId}</if>
        <if test="dep.applicationName != null">and application_name= #{dep.applicationName}</if>
        <if test="dep.moduelId != null">and moduel_id= #{dep.moduelId}</if>
        <if test="dep.moduleName != null">and module_name= #{dep.moduleName}</if>
        <if test="dep.componentId != null">and component_id= #{dep.componentId} </if>
        <if test="dep.prdid != null">and prdid= #{dep.prdid}</if>
        <if test="dep.procName != null">and proc_name= #{dep.procName}</if>
        <if test="dep.dataResourceId != null">and data_resource_id= #{dep.dataResourceId}</if>
        <if test="dep.dataResourceName != null">and data_resource_name= #{dep.dataResourceName}</if>
        <if test="dep.assetName != null">and asset_name= #{dep.assetName}</if>
        <if test="dep.deployPosition != null">and deploy_position= #{dep.deployPosition}</if>
        <if test="dep.port != null">and port= #{dep.port}</if>
        <if test="dep.remark != null">and remark= #{dep.remark}</if>
        <if test="dep.createBy != null">and create_by= #{dep.createBy}</if>
        <if test="dep.createTime != null">and create_time= #{dep.createTime}</if>
        <if test="dep.softlx != null">and softlx= #{dep.softlx}</if>
        </where>
    </select>
    <select id="selectTblDeployByAssetIds" parameterType="string" resultMap="TblDeployResult">
        <include refid="selectTblDeployVo"/>
        where asset_id in
            <foreach collection="ids" item="id" separator="," close=")" open="(">
                #{id}
            </foreach>
        <if test="dep.deployId != null">and deploy_id = #{dep.deployId}</if>
        <if test="dep.deployType != null and deployType != ''">and deploy_type= #{dep.deployType}</if>
        <if test="dep.applicationId != null">and application_id= #{dep.applicationId}</if>
        <if test="dep.applicationName != null">and application_name= #{dep.applicationName}</if>
        <if test="dep.moduelId != null">and moduel_id= #{dep.moduelId}</if>
        <if test="dep.moduleName != null">and module_name= #{dep.moduleName}</if>
        <if test="dep.componentId != null">and component_id= #{dep.componentId} </if>
        <if test="dep.prdid != null">and prdid= #{dep.prdid}</if>
        <if test="dep.procName != null">and proc_name= #{dep.procName}</if>
        <if test="dep.dataResourceId != null">and data_resource_id= #{dep.dataResourceId}</if>
        <if test="dep.dataResourceName != null">and data_resource_name= #{dep.dataResourceName}</if>
        <if test="dep.assetId != null">and asset_id= #{dep.assetId}</if>
        <if test="dep.assetName != null">and asset_name= #{dep.assetName}</if>
        <if test="dep.deployPosition != null">and deploy_position= #{dep.deployPosition}</if>
        <if test="dep.port != null">and port= #{dep.port}</if>
        <if test="dep.remark != null">and remark= #{dep.remark}</if>
        <if test="dep.createBy != null">and create_by= #{dep.createBy}</if>
        <if test="dep.createTime != null">and create_time= #{dep.createTime}</if>
        <if test="dep.softlx != null">and softlx= #{dep.softlx}</if>
        <if test="isSoft == true">and moduel_id is null and application_id is null</if>

    </select>
    <select id="selectAssetIdsByPrdid" parameterType="String" resultType="java.lang.Long">
        select distinct asset_id from tbl_deploy
        where prdid = #{prdid}
    </select>
    <select id="selectAssetIdsByApplicationIds" parameterType="String" resultType="java.lang.Long">
        select distinct asset_id from tbl_deploy
        where application_id in
        <foreach collection="applicationIds" open="(" close=")" separator="," item="applicationId">
            #{applicationId}
        </foreach>
    </select>
    <select id="selectTblDeployByApplicationIds" parameterType="Long" resultMap="TblDeployResult">
        <include refid="selectTblDeployVo"></include>
        where prdid = #{prdid} and application_id in
        <foreach collection="applicationIds" open="(" close=")" separator="," item="applicationId">
            #{applicationId}
        </foreach>
    </select>
    <select id="selectApplicationIdsByPrdid" parameterType="String" resultType="java.lang.Long">
        select distinct application_id from tbl_deploy
        where prdid = #{prdid} and application_id is not null
    </select>
    <select id="selectTblDeployByApplicationId" resultType="com.ruoyi.safe.domain.TblDeploy">
        <include refid="selectTblDeployVo"/>
        where application_id = #{applicationId}
    </select>

    <insert id="insertTblDeploy" parameterType="TblDeploy">
        insert IGNORE into tbl_deploy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deployId != null">deploy_id,</if>
            <if test="deployType != null and deployType != ''">deploy_type,</if>
            <if test="applicationId != null">application_id,</if>
            <if test="applicationName != null">application_name,</if>
            <if test="moduelId != null">moduel_id,</if>
            <if test="moduleName != null">module_name,</if>
            <if test="componentId != null">component_id, </if>
            <if test="prdid != null">prdid,</if>
            <if test="procName != null">proc_name,</if>
            <if test="dataResourceId != null">data_resource_id,</if>
            <if test="dataResourceName != null">data_resource_name,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="assetName != null">asset_name,</if>
            <if test="deployPosition != null">deploy_position,</if>
            <if test="port != null">port,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="softlx != null">softlx,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deployId != null">#{deployId},</if>
            <if test="deployType != null and deployType != ''">#{deployType},</if>
            <if test="applicationId != null">#{applicationId},</if>
            <if test="applicationName != null">#{applicationName},</if>
            <if test="moduelId != null">#{moduelId},</if>
            <if test="moduleName != null">#{moduleName},</if>
            <if test="componentId != null">#{componentId}, </if>
            <if test="prdid != null">#{prdid},</if>
            <if test="procName != null">#{procName},</if>
            <if test="dataResourceId != null">#{dataResourceId},</if>
            <if test="dataResourceName != null">#{dataResourceName},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="assetName != null">#{assetName},</if>
            <if test="deployPosition != null">#{deployPosition},</if>
            <if test="port != null">#{port},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="softlx != null">#{softlx},</if>
         </trim>
    </insert>

    <update id="updateTblDeploy" parameterType="TblDeploy">
        update tbl_deploy
        <trim prefix="SET" suffixOverrides=",">
            <if test="deployType != null and deployType != ''">deploy_type = #{deployType},</if>
            <if test="applicationId != null">application_id = #{applicationId},</if>
            <if test="applicationName != null">application_name = #{applicationName},</if>
            <if test="moduelId != null">moduel_id = #{moduelId},</if>
            <if test="moduleName != null">module_name = #{moduleName},</if>
            <if test="componentId != null">component_id = #{componentId}, </if>
            <if test="prdid != null">prdid = #{prdid},</if>
            <if test="procName != null">proc_name = #{procName},</if>
            <if test="dataResourceId != null">data_resource_id = #{dataResourceId},</if>
            <if test="dataResourceName != null">data_resource_name = #{dataResourceName},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="assetName != null">asset_name = #{assetName},</if>
            <if test="deployPosition != null">deploy_position = #{deployPosition},</if>
            <if test="port != null">port = #{port},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="softlx != null">softlx = #{softlx},</if>
        </trim>
        where deploy_id = #{deployId}
    </update>
    <update id="updateTblDeployByApplicationId" parameterType="TblDeploy">
        update tbl_deploy
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationName != null">application_name = #{applicationName},</if>
        </trim>
        where application_id = #{applicationId}
    </update>
    <update id="updateTblDeployByComponentId" parameterType="TblDeploy">
        update tbl_deploy
        <trim prefix="SET" suffixOverrides=",">
            <if test="prdid != null">prdid = #{prdid},</if>
            <if test="procName != null">proc_name = #{procName},</if>
        </trim>
        where component_id = #{componentId}
    </update>

    <delete id="deleteTblDeployByDeploy" parameterType="TblDeploy">
        delete from tbl_deploy
        <where>
            <if test="deployType != null  and deployType != ''"> and deploy_type = #{deployType}</if>
            <if test="prdid != null "> and prdid = #{prdid}</if>
            <if test="procName != null  and procName != ''"> and proc_name like concat('%', #{procName}, '%')</if>
            <if test="dataResourceId != null "> and data_resource_id not null</if>
            <if test="dataResourceId != null "> and data_resource_id = #{dataResourceId}</if>
            <if test="dataResourceName != null  and dataResourceName != ''"> and data_resource_name like concat('%', #{dataResourceName}, '%')</if>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="deployPosition != null  and deployPosition != ''"> and deploy_position = #{deployPosition}</if>
            <if test="port != null and port != ''"> and port = #{port}</if>
            <if test="softlx != null and softlx != ''"> and softlx = #{softlx}</if>
            <if test="componentId != null and componentId != ''"> and component_id = #{componentId}</if>
        </where>
    </delete>
    <delete id="deleteTblDeployByDeployId" parameterType="Long">
        delete from tbl_deploy where deploy_id = #{deployId}
    </delete>
    <delete id="deleteTblDeployByModuleId" parameterType="Long">
        delete from tbl_deploy where moduel_id = #{moduelId}
    </delete>
    <delete id="deleteTblDeployByApplicationId" parameterType="Long">
        delete from tbl_deploy where application_id = #{applicationId}
    </delete>
    <delete id="deleteTblDeployByDataResourceId" parameterType="Long">
        delete from tbl_deploy where data_resource_id = #{dataResourceId}
    </delete>


    <delete id="deleteDeployByAssetId" parameterType="Long">
        delete from tbl_deploy where asset_id = #{assetId} and (moduel_id is null and application_id is null and data_resource_id is null) and (softlx='1' or softlx='2' or softlx='3')
    </delete>

    <delete id="deleteDeployByAssetIds" parameterType="String">
        delete from tbl_deploy where asset_id
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>

    <delete id="deleteTblDeployByDeployIds" parameterType="String">
        delete from tbl_deploy where deploy_id in
        <foreach item="deployId" collection="array" open="(" separator="," close=")">
            #{deployId}
        </foreach>
    </delete>
    <delete id="deleteTblDeployByModuleIds" parameterType="String">
        delete from tbl_deploy where moduel_id in
        <foreach item="moduleId" collection="array" open="(" separator="," close=")">
            #{moduleId}
        </foreach>
    </delete>
    <delete id="deleteTblDeployByApplicationIds" parameterType="String">
        delete from tbl_deploy where application_id in
        <foreach item="ApplicationId" collection="array" open="(" separator="," close=")">
            #{ApplicationId}
        </foreach>
    </delete>
    <delete id="deleteTblDeployByDataResourceIds" parameterType="String">
        delete from tbl_deploy where data_resource_id in
        <foreach item="dataResourceId" collection="array" open="(" separator="," close=")">
            #{dataResourceId}
        </foreach>
    </delete>
    <delete id="deleteByCondition">
        delete from tbl_deploy where
        application_id = #{applicationId}
        and moduel_id = #{moduelId}
        and asset_id = #{assetId}
    </delete>
    <delete id="deleteNotInAssetIds">
        delete from tbl_deploy where
        application_id = #{applicationId}
        and moduel_id = #{moduleId}
        and asset_id not in
        <foreach item="assetId" collection="assetIds" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
</mapper>
