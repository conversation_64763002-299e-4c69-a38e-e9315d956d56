<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenBlackdoorExploitMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenBlackdoorExploitListVO">
        select a.* from threaten_blackdoor_exploit a left join threaten_alarm_origin b on a.id = b.origin_id and (b.threaten_type = 'threaten_blackdoor_exploit' or b.threaten_type = '后门利用')
        <where>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.baseId!=null">
                and a.base_id = #{query.baseId}
            </if>
            <if test="query.isEncry!=null">
                and a.is_encry = #{query.isEncry}
            </if>
            <if test="query.attackNum!=null">
                and a.attack_num = #{query.attackNum}
            </if>
            <if test="query.attackDirection!=null">
                and a.attack_direction = #{query.attackDirection}
            </if>
            <if test="query.attackStage!=null">
                and a.attack_stage = #{query.attackStage}
            </if>
            <if test="query.attackResult!=null">
                and a.attack_result = #{query.attackResult}
            </if>
            <if test="query.handleAction!=null">
                and a.handle_action = #{query.handleAction}
            </if>
            <if test="query.organization!=null and query.organization!=''">
                and a.organization like concat('%', #{query.organization}, '%')
            </if>
            <if test="query.fileName!=null and query.fileName!=''">
                and a.file_name like concat('%', #{query.fileName}, '%')
            </if>
            <if test="query.filePath!=null and query.filePath!=''">
                and a.file_path like concat('%', #{query.filePath}, '%')
            </if>
            <if test="query.fileMd5!=null and query.fileMd5!=''">
                and a.file_md5 like concat('%', #{query.fileMd5}, '%')
            </if>
            <if test="query.fileSha1!=null and query.fileSha1!=''">
                and a.file_sha1 like concat('%', #{query.fileSha1}, '%')
            </if>
            <if test="query.userName!=null and query.userName!=''">
                and a.user_name like concat('%', #{query.userName}, '%')
            </if>
            <if test="query.attackTime!=null">
                and a.attack_time = #{query.attackTime}
            </if>
            <if test="query.alarmId!=null">
                and b.alarm_id = #{query.alarmId}
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenBlackdoorExploitListVO">
        select a.* from threaten_blackdoor_exploit a 
        where a.id=#{id}
    </select>
</mapper>
