<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.MonitorPortMapper">

    <resultMap type="com.ruoyi.monitor2.domain.MonitorPortVO" id="MonitorPortResult">
        <result property="id"    column="id"    />
        <result property="ip"    column="IP"    />
        <result property="port"    column="port"    />
        <result property="protocol"    column="protocol"    />
        <result property="app"    column="app"    />
        <result property="appver"      column="appver"  />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="serverPid"    column="server_pid"    />
        <result property="serverCount" column="server_count" />
        <result property="applicationCount" column="application_count" />
    </resultMap>
    <sql id="selectMonitorPortVo">
        select a.id, a.IP, a.port, a.protocol, a.app, a.appver, a.create_time, a.update_time,a.server_pid
        from monitor_server_port a
    </sql>

    <select id="selectMonitorPortList" parameterType="com.ruoyi.monitor2.domain.MonitorPort" resultMap="MonitorPortResult">
        <include refid="selectMonitorPortVo"/>
        <where>
            <if test="ip != null  and ip != ''"> and a.IP = #{ip}</if>
            <if test="port != null "> and a.port = #{port}</if>
            <if test="protocol != null  and protocol != ''"> and a.protocol = #{protocol}</if>
            <if test="serverPid != null "> and a.server_pid = #{serverPid}</if>
        </where>
        group by a.ip,a.port
    </select>

    <select id="selectMonitorPortById" parameterType="java.lang.String" resultMap="MonitorPortResult">
        select a.id, a.IP, a.port, a.protocol, a.create_time, a.update_time
        from monitor_server_port a
        where a.id = #{id}
    </select>

    <select id="getMonitorPortGroupByPort" resultType="com.ruoyi.monitor2.domain.MonitorPortVO" resultMap="MonitorPortResult">
        SELECT
            t1.`port`,
            COUNT(DISTINCT t3.asset_id) server_count,
            COUNT(DISTINCT t5.asset_id) application_count
        FROM
            tbl_application_server t1
            LEFT JOIN tbl_server t3 ON t3.asset_id = t1.server_id
            LEFT JOIN sys_dept sd ON sd.dept_id = t3.dept_id
            LEFT JOIN tbl_business_application t5 ON t5.asset_id = t1.asset_id
        <where>
            t1.`port` IS NOT NULL
            <if test="deptId != null">AND (t3.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))</if>
            <if test="applicationId != null">AND t5.asset_id=#{applicationId}</if>
        </where>
        GROUP BY
            t1.`port`
    </select>

    <insert id="insertMonitorPort" parameterType="com.ruoyi.monitor2.domain.MonitorPort">
        insert into monitor_server_port
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="serverPid != null">server_pid,</if>
            <if test="state != null">state,</if>
            <if test="ip != null">IP,</if>
            <if test="port != null">port,</if>
            <if test="protocol != null">protocol,</if>
            <if test="app != null and app != ''">app,</if>
            <if test="appver != null and appver != ''">appver,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="serverPid != null">#{serverPid},</if>
            <if test="state != null">#{state},</if>
            <if test="ip != null">#{ip},</if>
            <if test="port != null">#{port},</if>
            <if test="protocol != null">#{protocol},</if>
            <if test="app != null and app != ''">#{app},</if>
            <if test="appver != null and appver != ''">#{appver},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMonitorPort" parameterType="com.ruoyi.monitor2.domain.MonitorPort">
        update monitor_server_port
        <trim prefix="SET" suffixOverrides=",">
            <if test="ip != null">IP = #{ip},</if>
            <if test="port != null">port = #{port},</if>
            <if test="protocol != null">protocol = #{protocol},</if>
            <if test="app != null and app != ''">app = #{app},</if>
            <if test="appver != null">appver = #{appver},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorPortById" parameterType="java.lang.String">
        delete from monitor_server_port where id = #{id}
    </delete>

    <delete id="deleteMonitorPortByIds" parameterType="java.lang.String">
        delete from monitor_server_port where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteMonitorPortByServerPid">
        delete from monitor_server_port where IP = #{ip}
    </delete>
</mapper>
