{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\invadeAttack.vue?vue&type=style&index=0&id=41ca4de8&scoped=true&lang=scss", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\invadeAttack.vue", "mtime": 1756369455984}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAiQC9hc3NldHMvc3R5bGVzL2N1c3RvbUZvcm0iOwouZGV0YWlsLWRpYWxvZyB7CiAgOjp2LWRlZXAgLmVsLWRpYWxvZ19fYm9keSB7CiAgICBwYWRkaW5nOiAwIDIwcHggMjBweDsKICB9Cn0KLnRhYmxlLXNlcmlhbC1udW1iZXIgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiA4cHg7CiAgYm9yZGVyLXJhZGl1czogMTBweDsKfQo="}, {"version": 3, "sources": ["invadeAttack.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "invadeAttack.vue", "sourceRoot": "src/views/frailty/event/component", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label-width=\"100px\" label=\"最近告警时间\">\n                <el-date-picker\n                  v-model=\"rangeTime\"\n                  type=\"datetimerange\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                  :default-time=\"['00:00:00', '23:59:59']\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"攻击源IP\">\n                <el-input v-model=\"queryParams.sip\" placeholder=\"请输入攻击源IP\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"目标IP\">\n                <el-input v-model=\"queryParams.dip\" placeholder=\"请输入目标IP\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\"\n                >查询\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\"\n                >重置\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置状态\">\n                <el-select v-model=\"queryParams.handleState\" placeholder=\"请选择处置状态\">\n                  <el-option label=\"未处置\" :value=\"0\"></el-option>\n                  <el-option label=\"已处置\" :value=\"1\"></el-option>\n                  <el-option label=\"忽略\" :value=\"2\"></el-option>\n                  <!--                  <el-option label=\"处置中\" value=\"3\"></el-option>-->\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属探针\">\n                <el-select v-model=\"queryParams.deviceConfigId\" filterable clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in deviceConfigList\"\n                    :key=\"item.id\"\n                    :label=\"item.deviceName\"\n                    :value=\"item.id\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">入侵攻击列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"!multipleSelection.length\"\n                  @click=\"handleBatchDetail\"\n                >批量删除\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <div class=\"tableContainer\">\n          <el-table\n            :data=\"list\"\n            height=\"100%\"\n            v-loading=\"loading\"\n            @selection-change=\"handleSelectionChange\"\n          >\n            <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n            <el-table-column label=\"攻击源IP\" align=\"left\">\n              <template slot-scope=\"scope\">\n                <span class=\"table-serial-number\">{{ scope.row.sip }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"目标IP\" align=\"left\" prop=\"dip\"/>\n            <el-table-column label=\"告警名称\" align=\"left\" prop=\"alertName\">\n              <template slot-scope=\"scope\">\n                <span\n                  class=\"table-serial-number\"\n                  :style=\"scope.row.alertName.includes('web') ? {color: '#bf1a1a', backgroundColor: '#fd828233', borderColor: '#bf1a1a'} : scope.row.alertName.includes('主机') ? {color: '#1EA086', backgroundColor: '#1ea0861a', borderColor: '#1EA086'} : scope.row.alertName.includes('SSH') ? {color: '#1a2bbf', backgroundColor: '#7899e033', borderColor: '#1a2bbf'} : ''\">{{ scope.row.alertName }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"开始时间\" align=\"left\" prop=\"startTime\"/>\n            <el-table-column label=\"最近告警时间\" align=\"left\" prop=\"updateTime\"/>\n            <el-table-column label=\"处置状态\" align=\"left\" prop=\"handleState\" :formatter=\"handleStateFormatter\"/>\n            <el-table-column label=\"操作\" width=\"150\" :show-overflow-tooltip=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button\n                  size=\"mini\"\n                  type=\"text\"\n                  @click=\"handleDetail(scope.row)\"\n                >详情\n                </el-button>\n                <el-button\n                  size=\"mini\"\n                  type=\"text\"\n                  class=\"table-delBtn\"\n                  @click=\"handleDelete(scope.row)\"\n                >删除\n                </el-button>\n                <el-button\n                  size=\"mini\"\n                  type=\"text\"\n                  v-if=\"scope.row.handleState === 0 || scope.row.handleState === 2\"\n                  @click=\"handleDispose(scope.row)\"\n                >处置\n                </el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleBatchDialog\"\n      width=\"600px\"\n      append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"formRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\n          <el-select v-model=\"form.handleState\" clearable placeholder=\"请选择处置状态\">\n            <el-option v-for=\"dict in dict.type.handle_state\"\n                       :key=\"dict.value\" :label=\"dict.label\"\n                       :value=\"parseInt(dict.value)\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"form.handleDesc\" placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showHandleBatchDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"batchDispose\">确定</el-button>\n      </span>\n    </el-dialog>\n\n    <el-dialog\n      title=\"详情\"\n      :visible.sync=\"showDetailDialog\"\n      width=\"800px\"\n      class=\"detail-dialog\"\n      append-to-body>\n      <div class=\"customForm-container\" style=\"height: 100%; padding: 10px\">\n<!--        <div class=\"my-title\"><img src=\"@/assets/images/application/jbxx.png\" alt=\"\">基本信息</div>-->\n        <el-descriptions\n          class=\"custom-column\"\n          direction=\"vertical\"\n          size=\"medium\"\n          :colon=\"false\"\n          label-class-name=\"custom-label-style\"\n          content-class-name=\"custom-content-style\"\n          :column=\"3\"\n        >\n          <template v-if=\"detailData.detailType === 'vuln_scan'\">\n            <el-descriptions-item :label=\"alertInfoLabel\">{{ alertInfoValue }}</el-descriptions-item>\n          </template>\n          <template v-if=\"detailData.detailType === 'brute_force'\">\n            <el-descriptions-item label=\"源IP\">{{ JSON.parse(detailData.detailData).ip }}</el-descriptions-item>\n            <el-descriptions-item label=\"类型\">{{ JSON.parse(detailData.detailData).type }}</el-descriptions-item>\n            <el-descriptions-item label=\"登录账号\">{{ JSON.parse(detailData.detailData).login_account }}</el-descriptions-item>\n            <el-descriptions-item label=\"登录结果\">{{ JSON.parse(detailData.detailData).login_status }}</el-descriptions-item>\n            <el-descriptions-item label=\"尝试次数\">{{ JSON.parse(detailData.detailData).try_counts }}</el-descriptions-item>\n            <el-descriptions-item label=\"日志记录开始时间\">{{ JSON.parse(detailData.detailData).log_start_time }}</el-descriptions-item>\n          </template>\n          <template v-if=\"detailData.detailType === 'web_attack'\">\n            <el-descriptions-item label=\"告警名称\">{{ JSON.parse(detailData.detailData).alert_name }}</el-descriptions-item>\n            <el-descriptions-item label=\"攻击源IP\">{{ JSON.parse(detailData.detailData).sip }}</el-descriptions-item>\n            <el-descriptions-item label=\"目标IP\">{{ JSON.parse(detailData.detailData).dip }}</el-descriptions-item>\n            <el-descriptions-item label=\"web端口\">{{ JSON.parse(detailData.detailData).dport }}</el-descriptions-item>\n            <el-descriptions-item label=\"日志文件\">{{ JSON.parse(detailData.detailData).log_file }}</el-descriptions-item>\n            <el-descriptions-item label=\"攻击时长\">{{ JSON.parse(detailData.detailData).attack_duration }}</el-descriptions-item>\n            <template>\n              <el-descriptions-item\n                :span=\"3\"\n                :key=\"item.key\"\n                v-for=\"item in JSON.parse(detailData.detailData).log_alert_info\"\n                :label=\"item.log_alert_title\">{{ item.log_alert_meta }}</el-descriptions-item>\n            </template>\n          </template>\n        </el-descriptions>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  batchDeleteHostInvasion,\n  deleteHostInvasion,\n  disposeHostInvasion, getHostIntrusionAttackDetail, getHostInvasionDetail,\n  getHostInvasionList\n} from \"@/api/monitor/hostAgent\";\nimport {listDeviceConfig} from \"@/api/ffsafe/deviceConfig\";\nimport {parseTime} from \"@/utils/ruoyi\";\n\nexport default {\n  name: \"invadeAttack\",\n  dicts: ['handle_state'],\n  data() {\n    return {\n      showAll: false,\n      rangeTime: [],\n      form: {},\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      total: 0,\n      list: [],\n      loading: false,\n      multipleSelection: [],\n      showHandleBatchDialog: false,\n      showDetailDialog: false,\n      formRules: {\n        handleState: [\n          {required: true, message: '请选择处置状态', trigger: 'blur'}\n        ],\n      },\n      detailData: {},\n      detailTitleName: '',\n      deviceConfigList: []\n    }\n  },\n  created() {\n    this.init()\n  },\n  computed: {\n    alertInfoLabel() {\n      try {\n        let detailData = JSON.parse(this.detailData.detailData);\n        let alertInfo = detailData.alert_info || '';\n\n        // 返回冒号之前的内容\n        if (alertInfo && alertInfo.includes(':')) {\n          return alertInfo.substring(0, alertInfo.indexOf(':')) + '：';\n        }\n        return alertInfo;\n      } catch (e) {\n        console.error('解析detailData失败', e);\n        return '';\n      }\n    },\n    alertInfoValue() {\n      try {\n        let detailData = JSON.parse(this.detailData.detailData);\n        let alertInfo = detailData.alert_info || '';\n\n        // 返回冒号之后的内容\n        if (alertInfo && alertInfo.includes(':')) {\n          return alertInfo.substring(alertInfo.indexOf(':') + 2); // +2 是为了跳过冒号和空格\n        }\n        return '';\n      } catch (e) {\n        console.error('解析detailData失败', e);\n        return '';\n      }\n    }\n  },\n  methods: {\n    init() {\n      this.setDefaultDateRange();\n      this.getDeviceConfigList();\n      this.getList();\n    },\n\n    // 获取列表\n    getList() {\n      this.loading = true\n      getHostInvasionList(this.queryParams).then(res => {\n        console.log(res, 'res')\n        this.list = res.rows\n        this.total = res.total\n      }).finally(() => {\n        this.loading = false\n      })\n    },\n\n    getDeviceConfigList(){\n      listDeviceConfig({queryAllData: true}).then(res => {\n        this.deviceConfigList = res.rows;\n      })\n    },\n\n    /** 设置默认日期范围 */\n    setDefaultDateRange() {\n      const end = new Date()\n      const start = new Date()\n      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\n\n      // 开始时间为 00:00:00，结束时间为 23:59:59\n      start.setHours(0, 0, 0, 0)\n      end.setHours(23, 59, 59, 999)\n\n      this.rangeTime = [\n        this.parseTime(start, '{y}-{m}-{d} {h}:{i}:{s}'),\n        this.parseTime(end, '{y}-{m}-{d} {h}:{i}:{s}')\n      ]\n      this.queryParams.beginTime = this.rangeTime[0]\n      this.queryParams.endTime = this.rangeTime[1]\n    },\n\n    // 查询\n    handleQuery() {\n      if (this.rangeTime != null) {\n        this.queryParams.beginTime = parseTime(this.rangeTime[0]);\n        this.queryParams.endTime = parseTime(this.rangeTime[1]);\n      } else {\n        this.queryParams.beginTime = null;\n        this.queryParams.endTime = null;\n      }\n      this.queryParams.pageNum = 1;\n      this.$emit('getList', { ...this.queryParams })\n      this.getList();\n    },\n\n    // 处置状态\n    handleStateFormatter(row, column, cellValue, index) {\n      if (cellValue === 0) {\n        return '未处置'\n      } else if (cellValue === 1) {\n        return '已处置'\n      } else if (cellValue === 2) {\n        return '忽略'\n      }\n    },\n\n    // 处置赋值\n    handleDispose(row) {\n      if (row.handleState === 2 ) {\n        this.form.handleState = row.handleState;\n        this.form.handleDesc = row.handleDesc;\n      }else {\n        this.form = {\n          handleDesc: '',\n          handleState: ''\n        }\n      }\n      this.form.id = row.id;\n      this.showHandleBatchDialog = true;\n    },\n\n    // 处置\n    batchDispose() {\n      this.$refs['form'].validate(valid => {\n        if (valid) {\n          disposeHostInvasion(this.form).then(res => {\n            if (res.code === 200) {\n              this.$message.success('处置成功')\n              this.showHandleBatchDialog = false;\n              this.$refs['form'].resetFields()\n              this.getList();\n            }\n          })\n        }\n      })\n    },\n\n    // 详情\n    handleDetail(row) {\n      getHostIntrusionAttackDetail(row.id).then(res => {\n        console.log(res, 'res');\n        this.showDetailDialog = true;\n        this.detailData = res.data;\n      })\n    },\n\n    // 删除\n    handleDelete(row) {\n      this.$confirm('是否删除该入侵攻击?', '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteHostInvasion(row.id).then(res => {\n          this.$message.success('删除成功')\n          this.handleQuery()\n        })\n      }).catch(() => {\n      })\n    },\n\n    // 批量删除\n    handleBatchDetail() {\n      console.log(this.multipleSelection.map(item => item.id).join(','), 'this.multipleSelection.map(item => item.id)')\n      if (!this.multipleSelection.length) {\n        return this.$message.error('请选择需要删除的入侵攻击数据')\n      } else {\n        this.$confirm('是否删除所选入侵攻击数据?', '警告', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          batchDeleteHostInvasion(this.multipleSelection.map(item => item.id).join(',')).then(res => {\n            this.$message.success('删除成功')\n            this.handleQuery()\n          })\n        })\n      }\n    },\n    // 导出\n    handleExport() {\n      this.download(\n        \"/ffsafe/hostIntrusionAttack/export\",\n        {\n          ...this.queryParams,\n        },\n        `入侵攻击_${new Date().getTime()}.xlsx`\n      );\n    },\n    // 多选\n    handleSelectionChange(val) {\n      this.multipleSelection = val\n    },\n    // 重置\n    resetQuery() {\n      this.queryParams = {\n        alertName: null,\n        sip: null,\n        dip: null,\n        handleState: null,\n        deviceId: null,\n        pageNum: 1,\n        pageSize: 10\n      }\n      this.setDefaultDateRange();\n      this.handleQuery()\n    },\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n.detail-dialog {\n  ::v-deep .el-dialog__body {\n    padding: 0 20px 20px;\n  }\n}\n.table-serial-number {\n  text-align: center;\n  padding: 8px;\n  border-radius: 10px;\n}\n</style>\n"]}]}