<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="1200px"
    @open="openDialog"
    class="customForm"
    append-to-body>
    <div class="customForm-container" style="height: 100%; padding: 0 20px;">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-position="top">
        <el-row>
          <el-col :span="24">
            <template v-for="ele in visibleAssetFields">
              <el-row
                :gutter="20"
                :key="ele.id"
                type="flex"
                v-if="ele.isShow"
                style="flex-wrap: wrap;margin-bottom: 10px;">
                <!-- 基本信息、硬件/软件概况信息、位置信息等模块 -->
                <el-col
                  :span="24"
                  style="display: flex; flex-wrap: wrap;"
                  v-if="ele.formName === '基本信息' || ele.formName === '硬件/软件概况信息' || ele.formName === '位置信息'">
                  <el-col class="my-title">
                    <img v-if="ele.formName === '基本信息' && ele.isShow" src="@/assets/images/application/jbxx.png" alt=""/>
                    <i v-else-if="ele.formName === '硬件/软件概况信息' && ele.isShow" class="el-icon-cpu icon" />
                    <i v-else-if="ele.formName === '位置信息' && ele.isShow" class="el-icon-location-information icon" />
                    {{ ele.formName }}
                  </el-col>
                  <template v-for="item in ele.fieldsItems">
                    <el-col :key="item.fieldKey" :span="getSpan(item.fieldKey)" v-if="shouldShowField(item)">
                      <el-form-item
                        :label="item.fieldName"
                        :prop="item.fieldKey"
                        :rules="getRulesForField(item)">
                        <!-- 资产类型字段 -->
                        <template v-if="item.fieldKey === 'assetType'">
                          <el-select v-model="form.assetType" placeholder="请选择资产类型" @change="assetTypeChange">
                            <el-option v-for="children in children" :key="children.id" :label="children.typeName"
                                       :value="children.id"/>
                          </el-select>
                        </template>

                        <!-- 重要程度字段 -->
                        <template v-else-if="item.fieldKey === 'degreeImportance'">
                          <el-select v-model="form.degreeImportance" :placeholder="'请选择' + item.fieldName">
                            <el-option
                              v-for="dict in dict.type.impt_grade"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            ></el-option>
                          </el-select>
                        </template>

                        <!-- 所属部门字段 -->
                        <template v-else-if="item.fieldKey === 'deptId'">
                          <dept-select v-model="form.deptId" is-current/>
                        </template>

                        <!-- 供应商字段 -->
                        <template v-else-if="item.fieldKey === 'vendor'">
                          <el-input
                            v-model="form.vendorName"
                            @focus="showVendorDialog('vendor')"
                            :placeholder="'请输入' + item.fieldName"/>
                        </template>

                        <!-- 设备厂商字段 -->
                        <template v-else-if="item.fieldKey === 'facilityManufacturer'">
                          <el-input
                            v-model="form.facilityManufacturerName"
                            @focus="showVendorDialog('facilityManufacturer')"
                            :placeholder="'请输入' + item.fieldName"/>
                        </template>

                        <!-- 维保单位字段 -->
                        <template v-else-if="item.fieldKey === 'maintainUnit'">
                          <el-input
                            v-model="form.maintainUnitName"
                            @focus="showVendorDialog('maintainUnit')"
                            :placeholder="'请输入' + item.fieldName"/>
                        </template>

                        <!-- 虚拟设备字段 -->
                        <template v-else-if="['isVirtual', 'isSparing'].includes(item.fieldKey)">
                          <el-radio-group
                            v-model="form[item.fieldKey]"
                            @input="item.fieldKey === 'isVirtual' ? changeIsVirtual : undefined"
                          >
                            <el-radio
                              v-for="dict in getDictDataInfo(item.fieldKey)"
                              :key="dict.value"
                              :label="dict.value"
                            >
                              {{ dict.label }}
                            </el-radio>
                          </el-radio-group>
                        </template>

                        <!-- 承载设备字段 -->
                        <template v-else-if="item.fieldKey === 'baseAsset'">
                          <el-input
                            v-model="form.baseAssetName"
                            @focus="baseAssetSelectHandle"
                            :placeholder="'请输入' + item.fieldName"/>
                        </template>

                        <template v-else-if="item.fieldKey === 'optId'">
                          <el-input
                            v-model="form.optId"
                            @focus="setupOptSystem('optId')"
                            placeholder="请输入操作系统"/>
                        </template>

                        <template v-else-if="item.fieldKey === 'mdId'">
                          <el-input
                          v-model="form.mdId"
                          @focus="setupOptSystem('mdId')"
                          placeholder="请输入中间件"/>
                        </template>

                        <template v-else-if="item.fieldKey === 'dbId'">
                          <el-input
                            v-model="form.dbId"
                            @focus="setupOptSystem('dbId')"
                            placeholder="请输入数据库"/>
                        </template>

                        <!-- 资产标签字段 -->
                        <template v-else-if="item.fieldKey === 'tags'">
                          <dynamic-tag v-model="form.tags"/>
                        </template>

                        <!-- 所在机房 -->
                        <template v-else-if="item.fieldKey === 'locationId'">
                          <location-select v-model="form.locationId"/>
                        </template>

                        <!-- 默认文本输入框 -->
                        <template v-else>
                          <el-input
                            v-model="form[item.fieldKey]"
                            :type="item.fieldKey === 'remark' ? 'textarea' : 'text'"
                            :placeholder="'请输入' + item.fieldName"/>
                        </template>
                      </el-form-item>
                    </el-col>
                  </template>
                </el-col>

                <!-- 网络信息模块单独处理 -->
                <el-col :span="24" v-if="ele.formName === '网络信息' && ele.isShow">
                  <div class="my-title"><img src="@/assets/images/application/netinfo.png" alt=""/>网络信息</div>
                  <template v-for="item in ele.fieldsItems">
                    <el-form-item
                      :key="item.fieldKey"
                      :label="item.fieldName"
                      :prop="item.fieldKey"
                      v-if="item.fieldKey === 'exposedIp'"
                      :rules="getRulesForField(item)">
                      <el-input v-model="form.exposedIp" placeholder="请输入外网IP"/>
                    </el-form-item>
                  </template>
                  <el-table :data="form.ipMacArr" v-if="netinfoTable.length" style="width: 100%">
                    <el-table-column
                      v-for="(colum, index) in netinfoTable"
                      :key="index"
                      :label="colum.fieldName"
                      :width="colum.fieldKey === 'isMainIp' ? '60px' : ''">
                      <template slot-scope="scope">
                        <!-- 主IP复选框 -->
                        <el-checkbox
                          v-if="colum.fieldKey === 'isMainIp'"
                          v-model="scope.row.isMainIp"/>

                        <!-- 网络区域选择器 -->
                        <NetworkSelect
                          v-else-if="colum.fieldKey === 'domainId'"
                          v-model="scope.row.domainId"/>

                        <!-- IP地址输入框 -->
                        <el-input
                          v-else-if="colum.fieldKey === 'ipv4'"
                          v-model="scope.row.ipv4"
                          placeholder="请输入IP"/>

                        <!-- MAC地址输入框 -->
                        <el-input
                          v-else-if="colum.fieldKey === 'mac'"
                          v-model="scope.row.mac"
                          placeholder="请输入MAC地址"/>
                      </template>
                    </el-table-column>
                    <el-table-column align="center">
                      <template slot="header">
                        <div style="display: inline;">操作</div>
                        <div style="display: inline;float: right;font-size: 18px;margin-left: 10px;"><i
                          class="el-icon-circle-plus-outline" @click="addIpMac"/></div>
                      </template>
                      <template slot-scope="scope">
                        <span style="font-size: 18px;">
                          <i class="el-icon-remove-outline" @click="removeIpMac(scope.$index)"/>
                        </span>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </template>
          </el-col>
        </el-row>
      </el-form>

      <el-dialog title="选择供应商" :visible.sync="vendorDialog" width="900px" append-to-body>
        <vendor-select v-if="vendorDialog" :value="getSelectedVendor()" @confirm="selectConfirm2" @cancel="userCancel2"/>
      </el-dialog>
      <el-dialog title="选择系统软件" :visible.sync="softDialog" width="800px" append-to-body>
        <product-select v-if="softDialog" :type="softType" :multipleMode="multiple" :value="getSelectedProduct()"
                        @confirm="softConfirm"
                        @cancel="softCancel" @change="softDialog= false" ref="softForm"/>
      </el-dialog>
      <el-dialog title="选择服务器" :visible.sync="baseAssetDialog" width="800px" append-to-body>
        <over-view-select v-if="baseAssetDialog" :asset-class="4" :type="softType" :value="valueInit()"
                          @confirm="baseAssetConfirm" @cancel="baseAssetCancel"></over-view-select>
      </el-dialog>
      <el-dialog title="选择绑定主机" :visible.sync="assetHostDialog" width="800px" append-to-body :show-close="false">
        <el-select v-model="host">
          <el-option v-for="host in assetHosts"
                     :key="host.id"
                     :label="host.hostName"
                     :value="host.id"></el-option>
        </el-select>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="chooseHost">确定</el-button>
        </div>
      </el-dialog>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="editItem === 'edit'" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>

</template>

<script>
import {addServer2, updateServer2, getServer} from "@/api/safe/server";
import {getVendor} from "@/api/safe/vendor";
import {getAssetTypeChildrenByid} from "@/api/safe/overview";
import vendorSelect from "@/views/components/select/vendorSelect";
import productSelect from "@/views/components/select/productSelect";
import DeptSelect from "@/views/components/select/deptSelect";
import NetworkSelect from '@/views/components/select/networkSelect';
import LocationSelect from "@/views/components/select/locationSelect";
import DynamicTag from "@/components/DynamicTag";
import OverViewSelect from "@/views/components/select/overViewSelect";
import {getHostInfo, getHostList} from "@/api/asset/host";
import {listProduct} from "@/api/monitor2/product";
import {listDomain} from "@/api/dict/domain";
import assetRegister from "@/mixins/assetRegister";

export default {
  name: 'EditServer',
  mixins: [assetRegister],
  dicts: ['impt_grade', 'sys_yes_no', 'proc_type','is_sparing'],
  components: {
    vendorSelect,
    productSelect,
    DeptSelect,
    NetworkSelect,
    LocationSelect,
    DynamicTag,
    OverViewSelect,
  },
  props: {
    assetId: {
      type: [Number, String],
      default: null,
      required: false
    },
    handleData: {
      type: Object,
      default: null,
      required: false
    },
    title: {
      type: String,
      default: '新增服务器'
    },
    editFlagVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      host: undefined,
      assetHosts: [],
      assetHostDialog: false,
      myTags: ['国产化', '安可'],
      classId: 4,
      children: [],
      typeTreeData: [],
      typelist: [],
      vendorDialog: false,
      softDialog: false,
      softType: 'system',
      softField: '',
      editItem: 'edit',
      addEvent: '',
      multiple: false,
      form: {
        ipMacArr: [],
        assetName: '',
        exposedIp: '',
        handleId: undefined,
        dbsystemArr: [],
        mdsystemArr: [],
      },
      rules: {
        assetCode: [
          /*{required: true, message: "资产编码不能为空", trigger: "blur"},*/
          {min: 0, max: 64, message: '资产编码不能超过 64 个字符', trigger: 'blur'},
        ],
        assetName: [
          {required: true, message: "资产名称不能为空", trigger: "blur"},
          {min: 0, max: 64, message: '资产名称不能超过 64 个字符', trigger: 'blur'},
        ],
        assetType: [
          {required: true, message: "资产类型不能为空", trigger: "blur"},
        ],
        ip: [
          {
            required: true,
            pattern: '^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$',
            message: "IP地址不能为空或格式不正确",
            trigger: "blur"
          },
        ],
        mac: [
          {pattern: '^([0-9a-fA-F]{2}(:|-)){5}[0-9a-fA-F]{2}$', message: "Mac地址格式不正确", trigger: "blur"},
        ],
        domainId: [
          {required: true, message: "网络区域不能为空！", trigger: 'blur'}
        ],
        locationDetail: [
          {min: 0, max: 128, message: '详细地址不能超过 128 个字符', trigger: 'blur'},
        ],
        deptId: [
          {required: true, message: "所属部门不能为空", trigger: "blur"},
        ]
      },
      baseAssetDialog: false,
      assetAllocationType: '2',
    }
  },
  computed: {
    visible: {
      get() {
        return this.editFlagVisible
      },
      set(val) {
        this.$emit('update:editFlagVisible', val)
      }
    },
    // 动态字段
    visibleAssetFields() {
      return this.assetList.map(group => {
        const newGroup = { ...group };
        newGroup.fieldsItems = group.fieldsItems.filter(item => item.isShow);
        return newGroup;
      });
    },
    // 动态规则
    dynamicRules() {
      const rules = {};
      this.visibleAssetFields.forEach(group => {
        group.fieldsItems.forEach(item => {
          const fieldKey = item.fieldKey;

          if (!rules[fieldKey]) {
            rules[fieldKey] = [];
          }

          if (this.rules[fieldKey]) {
            const filteredRules = this.rules[fieldKey].filter(rule => !rule.required);
            rules[fieldKey].push(...filteredRules);
          }

          if (item.required) {
            const hasRequiredRule = rules[fieldKey].some(rule => rule.required);
            if (!hasRequiredRule) {
              rules[fieldKey].push({
                required: true,
                message: `${item.fieldName}不能为空`,
                trigger: 'blur'
              });
            }
          }
        });
      });
      return rules;
    },

    // 网络信息列表字段
    netinfoTable() {
      let assetFields = this.assetList.filter(group => group.formName === '网络信息')
      return assetFields[0].fieldsItems.filter(item => item.fieldKey !== 'exposedIp' && item.isShow)
    }
  },
  mounted() {

  },
  methods: {
    getDictDataInfo(fieldKey) {
      switch (fieldKey) {
        case 'isVirtual':
          return this.dict.type.sys_yes_no;
        case 'isSparing':
          return this.dict.type.is_sparing;
        default:
          return [];
      }
    },

    openDialog() {
      this.init();
      this.initData();
      this.$nextTick(() => {
        if (!this.form.assetCode){
          this.form.assetCode = 'ZJ' + this.getFormattedDate()
        }
      })
    },
    chooseHost() {
      if(!this.host){
        this.$message('请选择一台主机！')
      }else{
        this.gethost(this.host)
        this.assetHostDialog = false
      }
    },
    init() {
      getAssetTypeChildrenByid(this.classId).then(res => {
        this.typelist = res.rows;
        for (let i = 0; i < this.typelist.length; i++) {
          if (this.typelist[i].typeGradedProtection == 1) {
            this.typelist.splice(Number(i), 1);
            i = i - 1;
          }
        }
        this.typeTreeData = this.handleTree(this.typelist, 'id', 'pid');
        this.children = this.typeTreeData[0].children;
        if (this.children && this.children.length) this.rules.assetType = [
          {required: true, message: "资产类型不能为空", trigger: "blur"},
        ]
      });
    },
    async initData() {
      if (this.assetId) {
        await getServer(this.assetId).then(response => {
          if (response.data.ipMacArr) {
            response.data.ipMacArr.forEach(e => {
              if (e.mainIp === '1') {
                e.isMainIp = true
              } else if (e.mainIp === '0') {
                e.isMainIp = false
              }
            })
          }
          this.form = response.data;
          if (this.form.optsystem != null) this.form.optId = this.form.optsystem.procName;
          if (this.form.dbsystemArr !== null && this.form.dbsystemArr !== undefined && this.form.dbsystemArr.length) {
            this.form.dbId = ''
            this.form.dbsystemArr.forEach(e => {
              this.form.dbId += '【' + e.procName + '】'
            })
          }
          if (this.form.mdsystemArr !== null && this.form.mdsystemArr !== undefined && this.form.mdsystemArr.length) {
            this.form.mdId = ''
            this.form.mdsystemArr.forEach(e => {
              this.form.mdId += '【' + e.procName + '】'
            })
          }
          this.myTags = this.form.tags ? this.form.tags.split(',') : [];

          if (this.form.facilityManufacturer) {
            getVendor(this.form.facilityManufacturer).then(res => {
              this.form.facilityManufacturerName = res.data.vendorName;
            });
          }

          if (this.form.maintainUnit) {
            getVendor(this.form.maintainUnit).then(res => {
              this.form.maintainUnitName = res.data.vendorName;
            });
          }
        })
      }
      if (this.handleData) {
        if(this.handleData.deptId){
          this.form.deptId = this.handleData.deptId;
        }
        this.form.handleId = this.handleData.handleId
        if(this.handleData.ip){
          let ipMacItem = {
            isMainIp: true,
            ipv4: this.handleData.ip
          }
          await listDomain({iparea: this.handleData.ip}).then(domainRes => {
            if(domainRes.data && domainRes.data.length>0){
              ipMacItem.domainId = domainRes.data[0].domainId;
            }
          })
          this.form.ipMacArr.push(ipMacItem);
        }
        if(this.handleData.name){
          await listProduct({procName: this.handleData.name}).then(productRes => {
            if(productRes.rows){
              let matchArr = productRes.rows.filter(item => this.handleData.name === item.procName);
              if(matchArr && matchArr.length > 0){
                this.$set(this.form,'optId',matchArr[0].procName);
                let optsystem = this.form.optsystem || {};
                optsystem.prdid = matchArr[0].prdid;
                optsystem.procName = matchArr[0].procName;
                this.$set(this.form,'optsystem',optsystem);
              }
            }
          })
        }
        if (this.handleData.hostId) {
          if (this.assetId) {
            if (this.handleData.hostId.indexOf(',') > 0) {
              this.assetHostDialog = true

              await getHostList({pageSize: -1, hostIds: this.handleData.hostId.split(',')}).then(res => {
                console.log('res', res)
                this.assetHosts = res.data.list
              })
            } else {
              this.$confirm('是否使用探测数据替换本地数据?').then(() => {
                this.gethost(this.handleData.hostId)
              })
            }
          } else {
            this.gethost(this.handleData.hostId)
          }
        }
      }
    },
    gethost(hostId) {
      getHostInfo(hostId).then(res => {
        const host = res.data
        this.form.assetName = host.hostName
        this.form.cpuFrame = host.hostPhysical.arch
        let ipMacArr = host.hostNicList.filter(row => row.ipv4.indexOf(host.hostIp) > -1)
        const oldIpMacArr = this.form.ipMacArr.filter(row => row.ipv4.indexOf(host.hostIp) > -1)
        console.log('oldIpMacArr', oldIpMacArr)
        ipMacArr.forEach(row => {
          row.ipv4 = row.ipv4.replace(/[\[\]]/g, '').split('/')[0]
          row.ipv6 = ''
          row.id = ''
          if (oldIpMacArr.length > 0) {
            row.domainId = oldIpMacArr[0].domainId
            row.isMainIp = oldIpMacArr[0].isMainIp
          }
        })
        this.form.exposedIp = host.exposedIp
        this.form.ipMacArr = ipMacArr
        this.form.cwHostId = host.id
        this.form.optsystem = {prdid: host.prdid, procName: host.osReleaseName}
        this.form.optId = this.form.optsystem.procName;
        this.form.dbsystemArr = []
        this.form.mdsystemArr = []
        for (let row of host.hostApplicationList) {
          if (row.category === 'database') {
            this.form.dbsystemArr.push({prdid: row.prdid, procName: row.app})
          } else if (row.category === 'web_server') {
            this.form.mdsystemArr.push({prdid: row.prdid, procName: row.app})
          }
        }
        this.form.dbId = ''
        this.form.dbsystemArr.forEach(e => {
          this.form.dbId += '【' + e.procName + '】'
        })
        this.form.mdId = ''
        this.form.mdsystemArr.forEach(e => {
          this.form.mdId += '【' + e.procName + '】'
        })
      })
    },
    /**
     * 选择供应商
     */
    showVendorDialog(vendor) {
      if (vendor == 'vendor') {
        this.vendorType = 'vendor';
      }
      if (vendor == 'facilityManufacturer') {
        this.vendorType = 'facilityManufacturer';
      }
      if (vendor == 'maintainUnit') {
        this.vendorType = 'maintainUnit';
      }
      this.vendorDialog = true
    },
    getSelectedVendor() {
      if (this.vendorType == 'vendor' && this.form.vendor != undefined) {
        return this.form.vendor;
      } else if (this.vendorType == 'facilityManufacturer' && this.form.facilityManufacturer != undefined) {
        return this.form.facilityManufacturer;
      } else if (this.vendorType == 'maintainUnit' && this.form.maintainUnit != undefined) {
        return this.form.maintainUnit;
      }
    },
    /**
     * 选择产商
     */
    selectConfirm2(vendor) {
      if (this.vendorType == 'vendor') {
        this.form.vendor = vendor.id;
        this.form.vendorName = vendor.vendorName;
      }
      if (this.vendorType == 'facilityManufacturer') {
        this.form.facilityManufacturer = vendor.id;
        this.form.facilityManufacturerName = vendor.vendorName;
      }
      if (this.vendorType == 'maintainUnit') {
        this.form.maintainUnit = vendor.id;
        this.form.maintainUnitName = vendor.vendorName;
      }
      this.vendorDialog = false;
    },
    userCancel2() {
      this.vendorDialog = false;
    },
    //选择操作系统
    setupOptSystem(field) {
      this.softField = field;
      if (field == 'optId') {
        this.multiple = false
        this.softType = 'system'
      }
      if (field == 'dbId') {
        this.multiple = true
        this.softType = 'database';
      }
      if (field == 'mdId') {
        this.multiple = true
        this.softType = 'middleware';
      }
      this.softDialog = true;
    },
    getSelectedProduct() {
      if (this.softField == 'optId' && this.form.optsystem != undefined) {
        return this.form.optsystem.prdid;
      } else if (this.softField == 'dbId' && this.form.dbsystemArr != null && this.form.dbsystemArr != undefined && this.form.dbsystemArr.length > 0) {
        return this.form.dbsystemArr.map(e => e.prdid)
      } else if (this.softField == 'mdId' && this.form.mdsystemArr != null && this.form.mdsystemArr != undefined && this.form.mdsystemArr.length > 0) {
        return this.form.mdsystemArr.map(e => e.prdid)
      }
    },
    softConfirm(row) {
      let value = ''
      if (this.softField == 'optId') {
        if (row == null) {
          this.form.optsystem = null;
        } else {
          if (this.form.optsystem == null) this.form.optsystem = {};
          this.form.optsystem.prdid = row.prdid;
          this.form.optsystem.procName = row.procName;
        }
        value = row.procName
      }
      if (this.softField == 'dbId') {
        if (row == null || row.length === 0) {
          this.form.dbsystemArr = [];
        } else {
          this.form.dbsystemArr = [];
          row.forEach(e => {
            this.form.dbsystemArr.push({prdid: e.prdid, procName: e.procName})
            value += '【' + e.procName + '】'
          })
        }
      }
      if (this.softField == 'mdId') {
        if (row == null || row.length === 0) {
          this.form.mdsystemArr = [];
        } else {
          this.form.mdsystemArr = [];
          row.forEach(e => {
            this.form.mdsystemArr.push({prdid: e.prdid, procName: e.procName})
            value += '【' + e.procName + '】'
          })
        }
      }
      this.form[this.softField] = value
      this.softDialog = false;
    },
    softCancel() {
      this.softDialog = false;
    },
    /** 提交按钮 */
    submitForm() {
      this.form = {...this.form}
      this.form.assetClass = this.classId
      this.form.assetClassDesc = this.className
      this.$refs['form'].validate(valid => {
        if (valid && this.checkIpMacArr()) {
          const flag = (this.children && this.children.length > 0)
          if (flag == undefined) {
            this.form.assetType = this.classId
            this.form.assetTypeDesc = this.className
          }
          this.form.ipMacArr.forEach(e => {
            if (e.isMainIp) {
              e.mainIp = '1'
            } else {
              e.mainIp = '0'
            }
          })
          if (this.form.assetId != null) {
            updateServer2(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.form.ipMacArr = [];
              this.$refs.form.resetFields()
              this.$emit('confirm', response.data)
              this.visible = false;
              if(this.$parent.$parent){
                // this.$parent.$parent.editFlag = false;
                this.$parent.$parent.getList && this.$parent.$parent.getList();
                this.$parent.$parent.getServerCountByDictData && this.$parent.$parent.getServerCountByDictData();
              }
            });
          } else {
            addServer2(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.$refs.form.resetFields()
              this.form.ipMacArr = [];
              this.$emit('confirm', response.data)
              this.open = false
              if (this.$route.params.add) {
                this.$router.back()
              }
              this.backAdd(this.form)
              this.visible = false;
              if(this.$parent.$parent){
                // this.$parent.$parent.editFlag = false;
                this.$parent.$parent.getList && this.$parent.$parent.getList();
                this.$parent.$parent.getServerCountByDictData && this.$parent.$parent.getServerCountByDictData();
              }
            })
          }
        }
      });
    },
    cancel() {
      this.$refs['form'].resetFields()
      this.$emit('cancel')
    },
    /**
     * 其他页面增加的回调
     */
    backAdd(addrow) {
      this.$root.$emit(this.addEvent, addrow)
    },
    addIpMac() {
      this.form.ipMacArr.push({isMainIp: false})
    },
    removeIpMac(index) {
      this.form.ipMacArr.splice(index, 1)
    },
    checkIpMacArr() {
      if (this.form.ipMacArr === null || this.form.ipMacArr.length <= 0) {
        this.$modal.msgError('网络信息不可为空')
        return false
      }

      let mainIpCount = 0
      for (let i = 0; i < this.form.ipMacArr.length; i++) {
        const e = this.form.ipMacArr[i]
        if (e.domainId === null || e.domainId === '' || e.domainId === undefined) {
          this.$modal.msgError('所属网络不可为空')
          return false
        }
        if (e.ipv4 === null || e.ipv4 === '' || e.ipv4 === undefined) {
          this.$modal.msgError('IP不可为空')
          return false
        } else {
          if (!this.isValidIP(e.ipv4)) {
            this.$modal.msgError('请输入正确格式的IP')
            return false
          }
        }
        if (e.isMainIp) {
          mainIpCount++
        }
      }
      if (mainIpCount === 0) {
        this.$modal.msgError('必须选择一个主IP')
        return false
      } else if (mainIpCount > 1) {
        this.$modal.msgError('只能选择一个主IP')
        return false
      }

      return true
    },
    inputLossOfFocus(val, index){
      if (this.form.assetCode == '' || !this.form.assetCode){
        this.form.assetCode = 'ZJ' + this.getFormattedDate()
      }
    },
    /**
     * 资产类型变化
     */
    assetTypeChange(data) {
      let item = this.children.find(item => {
        return item.id == data;
      })
      if (item) {
        this.form.assetTypeDesc = item.typeName;
      }
    },

    getRulesForField(item) {
      return this.dynamicRules[item.fieldKey] || [];
    },

    // 获取字段所占列数
    getSpan(fieldKey) {
      return fieldKey === 'remark' ? 16 : 8;
    },

    // 判断字段是否应该显示
    shouldShowField(item) {
      // 承载设备只在虚拟设备为Y时显示
      if (item.fieldKey === 'baseAsset') {
        return this.form.isVirtual === 'Y';
      }
      // 资产类型只在有子类型时显示
      if (item.fieldKey === 'assetType') {
        return this.children && this.children.length;
      }
      return true;
    },

    getFormattedDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      return `${year}${month}${day}${hours}${minutes}${seconds}`;
    },
    isValidIP(ip) {
      const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
      const flag = ipRegex.test(ip)
      console.log(flag)
      return flag
    },
    baseAssetSelectHandle() {
      this.baseAssetDialog = true;
    },
    baseAssetCancel() {
      this.baseAssetDialog = false;
    },
    baseAssetConfirm(row) {
      this.form.baseAsset = row.assetId;
      this.form.baseAssetName = row.assetName;
      this.baseAssetCancel();
    },
    valueInit() {
      if (this.form.baseAsset != null && this.form.baseAsset != undefined) {
        return [{assetId: this.form.baseAsset}];
      }
    },
    changeIsVirtual(value) {
      if (value === 'N') {
        this.form.baseAsset = ''
        this.form.baseAssetName = ''
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/customForm";
.customForm {
  ::v-deep .el-dialog__body {
    height: 75vh;
    overflow-y: auto;
    padding: 0 !important;
  }
}
</style>
