{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\eventList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\eventList.vue", "mtime": 1756449809347}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ruoyi", "require", "_data", "_applicationAssets", "_<PERSON><PERSON><PERSON>n", "_overview", "_DynamicTag", "_interopRequireDefault", "_alarmDetail", "_importThreaten", "_threatenConfigList", "_serverAdd", "_safeAdd", "_viewStrategy", "_publishClickDialog", "_FlowBox", "_FlowTemplateSelect", "_attackStage", "_attackViewList", "_sufferViewList", "_index", "_deptSelect", "_utils", "_FlowEngine", "_user", "_attackStageText", "_deviceConfig", "name", "components", "AttackStageText", "DeptSelect", "SufferViewList", "AttackViewList", "AttackStage", "FlowTemplateSelect", "FlowBox", "PublishClickDialog", "attackDetail", "sufferDetail", "ThreatenConfigList", "ViewStrategy", "SafeAdd", "ServerAdd", "importThreaten", "AlarmDetail", "DynamicTag", "dicts", "props", "propsActiveName", "type", "String", "props<PERSON>ueryP<PERSON><PERSON>", "Object", "default", "currentBtn", "Number", "data", "validateBlockIp", "rule", "value", "callback", "Error", "pattern", "test", "userList", "showHandleDialog", "handleForm", "id", "handleDesc", "handleState", "handleRules", "required", "message", "trigger", "showAll", "threatenDict", "queryParams", "pageNum", "pageSize", "deptOptions", "rangeTime", "loading", "threatenWarnList", "total", "title", "open<PERSON>hren<PERSON>", "form", "rules", "<PERSON><PERSON><PERSON>", "min", "max", "alarmLevel", "threatenType", "reason", "handSuggest", "logTime", "createTime", "srcIp", "srcPort", "destIp", "destPort", "mateRule", "associaDevice", "attackType", "attackStage", "attackResult", "blockingForm", "blockingRules", "block_ip", "validator", "duration_time", "remarks", "blockingIpList", "blockingDialogVisible", "editable", "assetInfoList", "openDialog", "assetData", "importDialog", "serverOpen", "assetId", "safeOpen", "threatenConfigFlag", "viewStrategy", "publishDialogVisible", "flowVisible", "flowTemplateSelectVisible", "flowStateOptions", "label", "handleStateOptions", "activeName", "syncStateOptions", "blockingDuration", "multipleSelection", "deviceConfigList", "watch", "formDestIp", "oldValue", "_this", "rg", "reg", "getAssetInfoByIp", "then", "response", "length", "for<PERSON>ach", "item", "assetName", "assetTypeDesc", "deptId", "$message", "warning", "init", "handler", "val", "handlePropsQuery", "split", "map", "ip", "trim", "filter", "immediate", "created", "getDeviceConfigList", "mounted", "$route", "query", "keys", "methods", "getThreatenDict", "handleQuery", "getDeptsData", "getUserList", "_this2", "listUser", "res", "rows", "_this3", "referenceId", "_objectSpread2", "startTime", "parseTime", "endTime", "Date", "setHours", "getList", "$nextTick", "JSON", "parse", "stringify", "join", "$refs", "atcAge", "initAttackStage", "_this4", "getMulTypeDict", "dictType", "_this5", "getDeptSystem", "handleChange", "reset<PERSON><PERSON>y", "flowState", "updateTime", "attackDirection", "currentSelectedCard", "handleAdd", "$set", "handleImport", "handleExport", "download", "concat", "getTime", "handleRowClick", "row", "column", "event", "_this6", "property", "listAlarm", "_defineProperty2", "assetType", "assetClassDesc", "toString", "handleSelectionChange", "flowStateFormatter", "cellValue", "index", "match", "find", "disposer<PERSON><PERSON><PERSON><PERSON>", "e", "userId", "nick<PERSON><PERSON>", "handleStateFormatter", "handleDetail", "openDetail", "showHandle", "parseInt", "handleEdit", "_this7", "getAlarm", "attackNum", "handleDelete", "_this8", "ids", "$modal", "confirm", "delAlarm", "success", "catch", "addOrUpdateFlowHandle", "_this9", "formType", "opType", "status", "isWork", "workType", "eventType", "originType", "currentFlowData", "getConfigKey", "flowId", "msg", "getFlowEngineInfo", "finally", "_this10", "FlowEngineInfo", "flowTemplateJson", "error", "_this11", "$emit", "deptName", "deptNameArr", "uniqueArr", "handleClose", "done", "resetFields", "submitHandleForm", "_this12", "validate", "valid", "updateAlarm", "submitForm", "_this13", "addAlarm", "cancel", "closeDialog", "closeAssetDialog", "networkOpen", "closeFlow", "isrRefresh", "flowTemplateSelectChange", "_this14", "handleAtcAgeClick", "attackSeg", "handle", "datasource", "dataSource", "handleApplicationTagShow", "applicationList", "result", "handleBlocking", "arr", "Array", "from", "Set", "blockingSubmit", "_this15", "addBlockIp", "multipleTable", "clearSelection", "_this16", "listDeviceConfig", "queryAllData", "handleRefreshAttackDirection", "_this17", "refreshing", "refreshAttackDirection", "code", "refreshResult", "console"], "sources": ["src/views/frailty/event/component/eventList.vue"], "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          ref=\"queryForm\"\n          :model=\"queryParams\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"最近告警时间\" label-width=\"98px\">\n                <el-date-picker\n                  v-model=\"rangeTime\"\n                  type=\"datetimerange\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                  :default-time=\"['00:00:00', '23:59:59']\"\n                />\n              </el-form-item>\n            </el-col>\n            <!--            <el-col :span=\"6\">\n              <el-form-item label=\"告警等级\" prop=\"alarmLevel\">\n                <el-select\n                  clearable\n                  v-model=\"queryParams.alarmLevel\"\n                  placeholder=\"请选择告警等级\"\n                >\n                  <el-option\n                    v-for=\"dict in dict.type.threaten_type\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置状态\" prop=\"\">\n                <el-select v-model=\"queryParams.handleState\" placeholder=\"请选择处置状态\" clearable>\n                  <el-option\n                    v-for=\"(item,index) in handleStateOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警类型\" prop=\"threatenType\">\n                <el-cascader\n                  v-model=\"queryParams.threatenType\"\n                  :options=\"threatenDict\"\n                  clearable\n                  :props=\"{ label: 'dictLabel', value: 'dictValue' }\"\n                  placeholder=\"请选择告警类型\"\n                >\n                  <template slot-scope=\"{ node, data }\">\n                    <span>{{ data.dictLabel }}</span>\n                    <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n                  </template>\n                </el-cascader>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\"\n                >查询\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n                <el-button v-if=\"!showAll\" class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\">\n                  展开\n                </el-button>\n                <el-button v-else class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\">收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-if=\"showAll\" :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警名称\" prop=\"threatenName\">\n                <el-input\n                  v-model=\"queryParams.threatenName\"\n                  placeholder=\"请输入告警名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"源IP\" prop=\"srcIp\">\n                <el-input\n                  v-model=\"queryParams.srcIp\"\n                  placeholder=\"请输入源IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"目标IP\" prop=\"destIp\">\n                <el-input\n                  v-model=\"queryParams.destIp\"\n                  placeholder=\"请输入目标IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-if=\"showAll\" :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"数据来源\" prop=\"dataSource\">\n                <el-select\n                  v-model=\"queryParams.dataSource\"\n                  placeholder=\"请选择数据来源\"\n                  clearable\n                  @change=\"$forceUpdate()\"\n                >\n                  <el-option :key=\"1\" label=\"探测\" :value=\"1\" />\n                  <el-option :key=\"2\" label=\"手动\" :value=\"2\" />\n                  <el-option :key=\"8\" label=\"流量\" :value=\"8\" />\n                  <el-option :key=\"9\" label=\"探针\" :value=\"9\" />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属部门\" prop=\"deptId\">\n                <dept-select v-model=\"queryParams.deptId\" />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"同步状态\">\n                <el-select v-model=\"queryParams.synchronizationStatus\" placeholder=\"请选择同步状态\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.synchronization_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-if=\"showAll\" :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"通报状态\" prop=\"flowState\">\n                <el-select v-model=\"queryParams.flowState\" placeholder=\"请选择通报状态\" clearable>\n                  <el-option\n                    v-for=\"(item,index) in flowStateOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置人\">\n                <el-input\n                  v-model=\"queryParams.disposer\"\n                  placeholder=\"请输入处置人\"\n                  clearable\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属探针\">\n                <el-select v-model=\"queryParams.deviceConfigId\" filterable clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in deviceConfigList\"\n                    :key=\"item.id\"\n                    :label=\"item.deviceName\"\n                    :value=\"item.id\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-if=\"showAll\" :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"攻击方向\">\n                <el-select v-model=\"queryParams.attackDirection\" placeholder=\"请选择攻击方向\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.attack_direction\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <!--      <div class=\"custom-content-search-chunk\" style=\"margin-bottom: 8px\">\n        <attack-stage ref=\"atcAge\" @handleClick=\"handleAtcAgeClick\"/>\n      </div>-->\n      <div\n        class=\"custom-content-container\"\n        :style=\"showAll ? { height: 'calc(100% - 298px)' } :{ height: 'calc(100% - 208px)' }\"\n      >\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">告警列表</span></div>\n          <div style=\"width: 60%; margin-left: 10%\">\n            <!--            <attack-stage-text ref=\"atcAge\" @handleClick=\"handleAtcAgeClick\" />-->\n            <attack-stage-text ref=\"atcAge\" />\n          </div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-col :span=\"1.5\">\n                  <el-button\n                    v-hasPermi=\"['system:threadten:add']\"\n                    type=\"primary\"\n                    size=\"small\"\n                    @click=\"handleAdd\"\n                  >新增\n                  </el-button>\n                </el-col>\n                <el-col :span=\"1.5\">\n                  <el-button\n                    class=\"btn1\"\n                    size=\"small\"\n                    @click=\"handleBlocking\"\n                  >批量阻断</el-button>\n                </el-col>\n                <el-col :span=\"1.5\">\n                  <el-button\n                    v-hasPermi=\"['system:threadten:import']\"\n                    class=\"btn1\"\n                    size=\"small\"\n                    @click=\"handleImport\"\n                  >导入\n                  </el-button>\n                </el-col>\n                <el-button\n                  v-hasPermi=\"['system:threadten:export']\"\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          ref=\"multipleTable\"\n          v-loading=\"loading\"\n          height=\"100%\"\n          :data=\"threatenWarnList\"\n          @row-click=\"handleRowClick\"\n          @selection-change=\"handleSelectionChange\"\n        >\n          <el-table-column type=\"selection\" width=\"55\" />\n          <!--          <el-table-column type=\"index\" width=\"100\" label=\"序号\"/>-->\n          <el-table-column label=\"最近告警时间\" width=\"200\" prop=\"updateTime\" />\n          <el-table-column label=\"告警名称\" prop=\"threatenName\" min-width=\"260\" />\n          <el-table-column label=\"告警类型\" prop=\"threatenType\" width=\"150\" />\n          <el-table-column label=\"告警等级\" prop=\"alarmLevel\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <dict-tag :options=\"dict.type.threaten_type\" :value=\"scope.row.alarmLevel\" />\n            </template>\n          </el-table-column>\n          <el-table-column label=\"源IP\" prop=\"srcIp\" width=\"180\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: flex-start\">\n                <span>{{ scope.row.srcIp }}</span>\n                <img v-if=\"scope.row.isBlocking\" style=\"width: 24px;margin-left: 10px\" src=\"@/assets/images/block.png\" alt=\"\">\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"目标IP/应用\" width=\"150\" prop=\"destIp\" />\n          <el-table-column label=\"处置人\" prop=\"disposer\" width=\"150\" :formatter=\"disposerFormatter\" />\n          <el-table-column label=\"关联业务系统\" prop=\"businessApplicationList\" width=\"200\">\n            <template slot-scope=\"scope\">\n              <el-tooltip\n                v-if=\"scope.row.businessApplications && scope.row.businessApplications.length > 0\"\n                placement=\"bottom-end\"\n                effect=\"light\"\n              >\n                <div slot=\"content\">\n                  <div\n                    v-for=\"(item,tagIndex) in scope.row.businessApplications\"\n                    v-if=\"tagIndex <= 5\"\n                    :key=\"item.assetId\"\n                    class=\"overflow-tag\"\n                  >\n                    <el-tag type=\"primary\"><span>{{ item.assetName }}</span></el-tag>\n                  </div>\n                  <div v-if=\"scope.row.businessApplications.length > 5\">\n                    <el-tag type=\"primary\"><span>...</span></el-tag>\n                  </div>\n                </div>\n                <el-tag type=\"primary\" class=\"asset-tag\">\n                  <span>{{ handleApplicationTagShow(scope.row.businessApplications) }}</span></el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"通报状态\" prop=\"flowState\" width=\"150\" :formatter=\"flowStateFormatter\" />\n          <el-table-column label=\"处置状态\" prop=\"handleState\" width=\"150\" :formatter=\"handleStateFormatter\" />\n          <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"150\" />\n          <el-table-column label=\"数据来源\" prop=\"dataSource\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.dataSource == '1'\">探测</span>\n              <span v-else-if=\"scope.row.dataSource == '2'\">手动</span>\n              <span v-else-if=\"scope.row.dataSource == '8'\">流量</span>\n              <span v-else-if=\"scope.row.dataSource == '9'\">探针</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"发现次数\" prop=\"alarmNum\" width=\"150\" />\n          <el-table-column label=\"同步状态\" prop=\"synchronizationStatus\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.synchronizationStatus === '0'\">未同步</span>\n              <span v-else-if=\"scope.row.synchronizationStatus === '1'\">已同步</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"攻击方向\" prop=\"attackDirection\" width=\"180\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: space-between\">\n                <dict-tag :options=\"dict.type.attack_direction\" :value=\"scope.row.attackDirection\" />\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  icon=\"el-icon-refresh\"\n                  :loading=\"scope.row.refreshing\"\n                  title=\"刷新攻击方向\"\n                  style=\"margin-left: 8px; color: #409EFF;\"\n                  @click=\"handleRefreshAttackDirection(scope.row)\"\n                />\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"操作\"\n            width=\"250\"\n            fixed=\"right\"\n            :show-overflow-tooltip=\"false\"\n            class-name=\"small-padding fixed-width\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                v-hasPermi=\"['system:threadten:query']\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleDetail(scope.row)\"\n              >详情\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                v-hasPermi=\"['system:threadten:edit']\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleEdit(scope.row)\"\n              >编辑\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId==null && !(scope.row.handleState === '3')\"\n                v-hasPermi=\"['system:threadten:remove']\"\n                size=\"mini\"\n                type=\"text\"\n                class=\"table-delBtn\"\n                @click=\"handleDelete(scope.row)\"\n              >删除\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                v-hasPermi=\"['system:threadten:edit']\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"showHandle(scope.row)\"\n              >处置\n              </el-button>\n              <el-button\n                v-if=\"!(scope.row.handleState === '1' || scope.row.handleState === '3') && (scope.row.flowState == null || scope.row.flowState === '99')\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"addOrUpdateFlowHandle(null,null,scope.row)\"\n              >创建通报\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n    <!-- 处置威胁情报对话框! -->\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"handleStateForm\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\n          <el-select v-model=\"handleForm.handleState\" clearable placeholder=\"请选择处置状态\">\n            <el-option\n              v-for=\"dict in dict.type.handle_state\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"parseInt(dict.value)\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input v-model=\"handleForm.handleDesc\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入处置说明\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleForm\">确 定</el-button>\n        <el-button @click=\"showHandleDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 添加或修改威胁情报对话框! -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"openThrenten\"\n      width=\"80%\"\n      append-to-body\n      :before-close=\"handleClose\"\n    >\n      <el-row>\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"106px\" :disabled=\"!editable\">\n          <el-col :span=\"24\" class=\"mb8\">\n            <el-divider direction=\"vertical\" />\n            <div class=\"my-title\">基本信息</div>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"告警名称\" prop=\"threatenName\">\n              <el-input v-model=\"form.threatenName\" placeholder=\"请输入告警名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警等级\" prop=\"alarmLevel\">\n              <el-select v-model=\"form.alarmLevel\" placeholder=\"请选择告警等级\" clearable>\n                <el-option\n                  v-for=\"dict in dict.type.threaten_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警类型\" prop=\"threatenType\">\n              <el-cascader\n                v-model=\"form.threatenType\"\n                :options=\"threatenDict\"\n                clearable\n                placeholder=\"请选择告警类型\"\n                :props=\"{ label: 'dictLabel', value: 'dictValue' }\"\n                style=\"width: 100%\"\n              >\n                <template slot-scope=\"{ node, data }\">\n                  <span>{{ data.dictLabel }}</span>\n                  <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n                </template>\n              </el-cascader>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"告警原因\" prop=\"reason\">\n              <el-input\n                v-model=\"form.reason\"\n                :autosize=\"{minRows: 3, maxRows: 3}\"\n                type=\"textarea\"\n                placeholder=\"请输入告警原因\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"处置建议\" prop=\"handSuggest\">\n              <el-input\n                v-model=\"form.handSuggest\"\n                :autosize=\"{minRows: 3, maxRows: 3}\"\n                type=\"textarea\"\n                placeholder=\"请输入告警建议\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警时间\" prop=\"createTime\">\n              <el-date-picker\n                v-model=\"form.createTime\"\n                type=\"date\"\n                placeholder=\"选择告警时间\"\n                format=\"yyyy 年 MM 月 dd 日\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"最近告警时间\" prop=\"updateTime\">\n              <el-date-picker\n                v-model=\"form.updateTime\"\n                type=\"date\"\n                placeholder=\"选择最近告警时间\"\n                format=\"yyyy 年 MM 月 dd 日\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"威胁标签\" prop=\"label\">\n              <DynamicTag v-model=\"form.label\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"关联设备\" prop=\"associaDevice\">\n              <el-input\n                v-model=\"form.associaDevice\"\n                placeholder=\"请输入关联设备\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\" class=\"mb8\">\n            <el-divider direction=\"vertical\" />\n            <div class=\"my-title\">攻击关系</div>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"源IP\" prop=\"srcIp\">\n              <el-input\n                v-model=\"form.srcIp\"\n                style=\"width: 50%\"\n                placeholder=\"请输入源IP\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"源IP端口\" prop=\"srcPort\">\n              <el-input\n                v-model=\"form.srcPort\"\n                style=\"width: 30%\"\n                placeholder=\"请输入源IP端口\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"目标IP/应用\" prop=\"destIp\">\n              <el-input\n                v-model=\"form.destIp\"\n                style=\"width: 50%\"\n                placeholder=\"请输入目标IP\"\n              />\n              <!--目标ip查询后有多个及显示资产数据，提交表单传(assetId： 资产id,deptId：部门id)-->\n              <el-select\n                v-show=\"assetInfoList.length >= 2\"\n                v-model=\"form.assetId\"\n                style=\"width: 50%;\"\n                placeholder=\"请确认疑似资产\"\n              >\n                <el-option\n                  v-for=\"item in assetInfoList\"\n                  :key=\"item.assetId\"\n                  :label=\"item.value\"\n                  :value=\"item.assetId\"\n                />\n              </el-select>\n              <el-select v-show=\"false\" v-model=\"form.deptId\" style=\"width: 50%;\" placeholder=\"请选择资产\">\n                <el-option v-for=\"item in assetInfoList\" :label=\"item.value\" :value=\"item.deptId\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"目标IP端口\" prop=\"destPort\">\n              <el-input\n                v-model=\"form.destPort\"\n                style=\"width: 30%\"\n                placeholder=\"请输入目标IP端口\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col v-if=\"form.fileUrl!=null||editable\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\" />\n              <div class=\"my-title\">文件上传</div>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"上传文件\" prop=\"fileUrl\">\n                <file-upload\n                  v-model=\"form.fileUrl\"\n                  :dis-upload=\"!editable\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button\n          v-if=\"editable\"\n          type=\"primary\"\n          @click=\"submitForm\"\n        >确 定\n        </el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      v-if=\"openDialog\"\n      :title=\"title\"\n      :visible.sync=\"openDialog\"\n      width=\"80%\"\n      append-to-body\n    >\n      <el-tabs v-model=\"activeName\">\n        <el-tab-pane label=\"事件详情\" name=\"detail\">\n          <alarm-detail\n            v-if=\"openDialog\"\n            :asset-data=\"assetData\"\n            @openDetail=\"openDetail\"\n          />\n        </el-tab-pane>\n        <el-tab-pane v-if=\"assetData.srcIp\" label=\"攻击IP关联事件\" name=\"attack\">\n          <attack-detail :detail-type=\"'attack'\" :host-ip=\"assetData.srcIp\" :current-asset-data=\"assetData\" />\n        </el-tab-pane>\n        <el-tab-pane v-if=\"assetData.destIp\" label=\"受害IP关联事件\" name=\"suffer\">\n          <suffer-detail :detail-type=\"'suffer'\" :host-ip=\"assetData.destIp\" :current-asset-data=\"assetData\" />\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n\n    <el-dialog title=\"导入威胁告警\" :visible.sync=\"importDialog\" width=\"800px\" append-to-body>\n      <import-threaten v-if=\"importDialog\" @closeDialog=\"closeDialog\" />\n    </el-dialog>\n\n    <el-dialog title=\"查看服务器资产\" :visible.sync=\"serverOpen\" width=\"80%\" append-to-body>\n      <server-add v-if=\"serverOpen\" :asset-id=\"assetId\" :editable=\"editable\" @cancel=\"closeAssetDialog()\" />\n    </el-dialog>\n\n    <el-dialog title=\"查看安全设备资产\" :visible.sync=\"safeOpen\" width=\"80%\" append-to-body>\n      <safe-add v-if=\"safeOpen\" :asset-id=\"assetId\" :editable=\"editable\" @cancel=\"closeAssetDialog()\" />\n    </el-dialog>\n\n    <el-dialog title=\"查看告警策略\" :visible.sync=\"viewStrategy\" width=\"400\" append-to-body>\n      <view-strategy v-if=\"viewStrategy\" @close=\"viewStrategy=false\" />\n    </el-dialog>\n\n    <el-dialog title=\"告警策略配置\" :visible.sync=\"threatenConfigFlag\" width=\"800\" append-to-body>\n      <threaten-config-list v-if=\"threatenConfigFlag\" @close=\"threatenConfigFlag=false\" />\n    </el-dialog>\n\n    <el-dialog title=\"批量阻断\" :visible.sync=\"blockingDialogVisible\" width=\"400\">\n      <el-form ref=\"blockingForm\" :model=\"blockingForm\" :rules=\"blockingRules\" class=\"blocking-form\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"阻断ip\" prop=\"block_ip\">\n              <span slot=\"label\">\n                阻断ip\n                <template>\n                  <el-tooltip placement=\"top\">\n                    <div slot=\"content\">默认加载选择的事件的源IP，多个则以“;”隔开</div>\n                    <i class=\"el-icon-info\" />\n                  </el-tooltip>\n                </template>\n              </span>\n              <el-input v-model=\"blockingForm.block_ip\" placeholder=\"请输入ip\">\n                <el-popover\n                  slot=\"suffix\"\n                  placement=\"bottom\"\n                  width=\"100\"\n                  trigger=\"hover\"\n                >\n                  <ul>\n                    <li v-for=\"(ip, index) in blockingIpList\" :key=\"index\">{{ ip }}</li>\n                  </ul>\n                  <i slot=\"reference\" class=\"el-icon-more\" />\n                </el-popover>\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"阻断时长\" prop=\"duration_time\">\n              <el-select v-model=\"blockingForm.duration_time\" placeholder=\"请选择阻断时长\">\n                <el-option\n                  v-for=\"item in blockingDuration\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-form-item label=\"备注\" prop=\"remarks\">\n            <el-input v-model=\"blockingForm.remarks\" type=\"textarea\" maxlength=\"500\" show-word-limit placeholder=\"请输入阻断描述\" />\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"blockingSubmit\">确 定</el-button>\n        <el-button @click=\"blockingDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <FlowBox v-if=\"flowVisible\" ref=\"FlowBox\" @close=\"closeFlow\" />\n    <flow-template-select :show.sync=\"flowTemplateSelectVisible\" @change=\"flowTemplateSelectChange\" />\n\n    <publish-click-dialog\n      :publish-dialog-visible=\"publishDialogVisible\"\n      title=\"发布告警事件\"\n      width=\"30%\"\n      @updateVisible=\"(val) => { this.publishDialogVisible = val}\"\n    />\n  </div>\n</template>\n\n<script>\nimport { parseTime } from '@/utils/ruoyi'\nimport { getMulTypeDict } from '../../../../api/system/dict/data'\nimport { getDeptSystem } from '../../../../api/monitor2/applicationAssets'\nimport { getAlarm, delAlarm, listAlarm, addAlarm, updateAlarm, addBlockIp, refreshAttackDirection } from '../../../../api/threaten/threatenWarn'\nimport { getAssetInfoByIp } from '../../../../api/safe/overview'\nimport DynamicTag from '../../../../components/DynamicTag'\nimport AlarmDetail from '../../../basis/securityWarn/alarmDetail'\nimport importThreaten from '@/views/basis/securityWarn/importThreaten.vue'\nimport ThreatenConfigList from '@/views/basis/securityWarn/threatenConfigList.vue'\nimport ServerAdd from '../../../hhlCode/component/application/adds/serverAdd'\nimport SafeAdd from '../../../hhlCode/component/application/adds/safeAdd'\nimport ViewStrategy from '../../../basis/securityWarn/viewStrategy'\nimport PublishClickDialog from '../../../basis/securityWarn/publishClickDialog'\nimport FlowBox from '../../../zeroCode/workFlow/components/FlowBox'\nimport FlowTemplateSelect from '../../../../components/FlowTemplateSelect'\nimport AttackStage from '../../../threat/overview/attackStage'\nimport AttackViewList from './attackViewList'\nimport SufferViewList from './sufferViewList'\nimport attackDetail from './detail/index.vue'\nimport sufferDetail from './detail/index.vue'\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport { uniqueArr } from '@/utils'\nimport { FlowEngineInfo } from '@/api/lowCode/FlowEngine'\nimport { listUser } from '@/api/system/user'\nimport AttackStageText from '@/views/threat/overview/attackStageText.vue'\nimport {\n  listDeviceConfig\n} from '@/api/ffsafe/deviceConfig'\n\nexport default {\n  name: 'EventList',\n  components: {\n    AttackStageText,\n    DeptSelect,\n    SufferViewList,\n    AttackViewList,\n    AttackStage,\n    FlowTemplateSelect,\n    FlowBox,\n    PublishClickDialog,\n    attackDetail,\n    sufferDetail,\n    ThreatenConfigList, ViewStrategy, SafeAdd, ServerAdd, importThreaten, AlarmDetail, DynamicTag\n  },\n  dicts: ['threaten_type', 'attack_stage', 'attack_result', 'handle_state', 'synchronization_status', 'attack_direction'],\n  props: {\n    propsActiveName: {\n      type: String\n    },\n    propsQueryParams: {\n      type: Object,\n      default: function() {\n        return null\n      }\n    },\n    currentBtn: {\n      type: Number,\n      default: null\n    }\n  },\n  data() {\n    const validateBlockIp = (rule, value, callback) => {\n      if (!value) {\n        return callback(new Error('IP不能为空'))\n      }\n      // let pattern = /^((1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2})\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0)\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0)\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0))$/;\n      const pattern = /^\\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\\s*;\\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))*\\s*$/\n      if (!pattern.test(value)) {\n        return callback(new Error('请输入正确的IP'))\n      }\n      return callback()\n    }\n    return {\n      userList: [],\n      showHandleDialog: false,\n      handleForm: {\n        id: '',\n        handleDesc: '',\n        handleState: ''\n      },\n      handleRules: {\n        handleState: [\n          { required: true, message: '请选择处理状态', trigger: 'blur' }\n        ]\n      },\n      showAll: false,\n      threatenDict: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        handleState: '0'\n      },\n      deptOptions: [],\n      rangeTime: [],\n      loading: false,\n      threatenWarnList: [],\n      total: 0,\n      title: '',\n      openThrenten: false,\n      form: {},\n      rules: {\n        threatenName: [\n          { required: false, min: 0, max: 500, message: '告警名称不能超过500字符', trigger: 'blur' },\n          { required: true, message: '请输入告警名称', trigger: 'blur' },\n          {\n            required: true,\n            pattern: /^[^\\s]+/,\n            message: '不能以空格开头！',\n            trigger: 'blur'\n          }\n        ],\n        alarmLevel: [\n          { required: true, message: '请输入告警等级', trigger: 'blur' }\n        ],\n        threatenType: [\n          { required: true, message: '请输入告警类型', trigger: 'blur' }\n        ],\n        reason: [\n          { required: false, min: 0, max: 2000, message: '告警原因不能超过2000字符', trigger: 'blur' },\n          { required: true, message: '请输入告警原因', trigger: 'blur' }\n        ],\n        handSuggest: [\n          { required: false, min: 0, max: 2000, message: '告警建议不能超2000字符', trigger: 'blur' },\n          { required: true, message: '请输入告警建议', trigger: 'blur' }\n        ],\n        logTime: [\n          { required: true, message: '请输入日志时间', trigger: 'blur' }\n        ],\n        createTime: [\n          { required: true, message: '请输入告警时间', trigger: 'blur' }\n        ],\n        srcIp: [\n          { required: false, min: 0, max: 30, message: '源IP不能超过30字符', trigger: 'blur' },\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: 'IP地址不能为空或格式不正确',\n            trigger: 'blur'\n          }\n        ],\n        srcPort: [\n          { required: false, min: 0, max: 11, message: '源IP端口不能超过11字符', trigger: 'blur' },\n          { required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '源IP端口不能为空或格式不正确', trigger: 'blur' }\n        ],\n        destIp: [\n          { required: false, min: 0, max: 30, message: '目标IP不能超过30字符', trigger: 'blur' },\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: 'IP地址不能为空或格式不正确',\n            trigger: 'blur'\n          }\n        ],\n        destPort: [\n          { required: false, min: 0, max: 11, message: '目标IP端口不能超过11字符', trigger: 'blur' },\n          { required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '目标IP端口不能为空或格式不正确', trigger: 'blur' }\n        ],\n        mateRule: [\n          { required: false, min: 0, max: 200, message: '分析规则不能超过200字符', trigger: 'blur' }\n        ],\n        associaDevice: [\n          { required: false, min: 0, max: 200, message: '关联设备不能超过200字符', trigger: 'blur' }\n        ],\n        attackType: [\n          { required: false, min: 0, max: 100, message: '攻击方式不能超过100字符', trigger: 'blur' },\n          { required: true, message: '请输入攻击方式', trigger: 'blur' }\n        ],\n        attackStage: [\n          { required: false, min: 0, max: 100, message: '攻击链阶段不能超过100字符', trigger: 'blur' },\n          { required: true, message: '请输入攻击链阶段', trigger: 'blur' }\n        ],\n        attackResult: [\n          { required: false, min: 0, max: 100, message: '攻击结果不能超过100字符', trigger: 'blur' },\n          { required: true, message: '请输入攻击结果', trigger: 'blur' }\n        ]\n      },\n      blockingForm: {},\n      blockingRules: {\n        block_ip: [\n          // 可同时传多个，用\";\"隔开\n          { validator: validateBlockIp, trigger: 'blur' }\n        ],\n        duration_time: [\n          { required: true, message: '请选择阻断时长', trigger: 'blur' }\n        ],\n        remarks: [\n          { required: false, min: 0, max: 500, message: '备注不能超过500字符', trigger: 'blur' }\n        ]\n      },\n      blockingIpList: [],\n      blockingDialogVisible: false, // 批量阻断弹窗\n      editable: true,\n      assetInfoList: [],\n      openDialog: false,\n      assetData: {},\n      importDialog: false,\n      serverOpen: false,\n      assetId: null,\n      safeOpen: false,\n      threatenConfigFlag: false,\n      viewStrategy: false,\n      publishDialogVisible: false,\n      flowVisible: false,\n      flowTemplateSelectVisible: false,\n      flowStateOptions: [\n        {\n          label: '待审核',\n          value: 0\n        },\n        {\n          label: '待处置',\n          value: 1\n        },\n        {\n          label: '待反馈审核',\n          value: 2\n        },\n        {\n          label: '待验证',\n          value: 3\n        },\n        {\n          label: '已完成',\n          value: 4\n        },\n        {\n          label: '待提交',\n          value: -1\n        },\n        {\n          label: '未分配',\n          value: 99\n        }\n      ],\n      handleStateOptions: [\n        {\n          label: '未处置',\n          value: '0'\n        },\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        },\n        {\n          label: '处置中',\n          value: '3'\n        }\n      ],\n      activeName: 'detail',\n      syncStateOptions: [\n        {\n          label: '未同步',\n          value: 0\n        },\n        {\n          label: '已同步',\n          value: 1\n        }\n      ],\n      blockingDuration: [\n        {\n          label: '30分钟',\n          value: '30m'\n        },\n        {\n          label: '24小时',\n          value: '24h'\n        },\n        {\n          label: '48小时',\n          value: '48h'\n        },\n        {\n          label: '7天',\n          value: '168h'\n        },\n        {\n          label: '永久',\n          value: '永久'\n        }\n      ],\n      multipleSelection: [],\n      deviceConfigList: []\n    }\n  },\n  watch: {\n    // 监听目标ip\n    'form.destIp'(value, oldValue) {\n      var rg = /^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$/\n      var reg = rg.test(value)\n      if (reg) {\n        // 根据ip获取资产数据\n        getAssetInfoByIp(value).then(response => {\n          if (response.data.length) {\n            const assetData = response.data\n            assetData.forEach(item => item.value = item.assetName + '-' + item.assetTypeDesc)\n            if (value !== oldValue && oldValue) {\n              this.form.assetId = ''\n              this.form.deptId = ''\n            }\n            // 资产数据有多条显示下拉框，只有一条不显示\n            if (assetData.length === 1) {\n              this.form.assetId = assetData[0].assetId\n              this.form.deptId = assetData[0].deptId\n            }\n            if (assetData.length > 1 && !this.form.assetId) {\n              this.form.assetId = ''\n              this.form.deptId = ''\n            }\n            this.assetInfoList = assetData\n          } else {\n            this.assetInfoList = []\n            return this.$message.warning('未查询到资产数据')\n          }\n        })\n      } else {\n        this.assetInfoList = []\n        this.form.assetId = ''\n        this.form.deptId = ''\n      }\n    },\n    propsActiveName() {\n      this.init()\n    },\n    propsQueryParams: {\n      handler(val) {\n        this.handlePropsQuery(val)\n      }\n    },\n    /* rangeTime(val) {\n      console.log(val)\n    },*/\n    'blockingForm.block_ip': {\n      handler(value) {\n        if (value) {\n          this.blockingIpList = value.split(';').map(ip => ip.trim()).filter(ip => ip)\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getDeviceConfigList()\n  },\n  mounted() {\n    if (!this.$route.query || Object.keys(this.$route.query).length < 1) {\n      this.init()\n    } else {\n      this.handlePropsQuery(this.$route.query)\n    }\n  },\n  methods: {\n    init() {\n      // this.resetQuery()\n      this.getThreatenDict()\n      this.handleQuery()\n      this.getDeptsData()\n      this.getUserList()\n    },\n    getUserList() {\n      listUser({ pageNum: 1, pageSize: 1000 }).then(res => {\n        if (res.rows) {\n          this.userList = res.rows\n        }\n      })\n    },\n    handleQuery() {\n      this.propsQueryParams.alarmLevel = this.queryParams.alarmLevel\n      this.propsQueryParams.referenceId = this.queryParams.referenceId\n      // this.$emit('update:currentBtn',this.queryParams.alarmLevel?parseInt(this.queryParams.alarmLevel) : null)\n      this.queryParams = { ...this.queryParams, ...this.propsQueryParams }\n      if (this.rangeTime != null) {\n        this.queryParams.startTime = parseTime(this.rangeTime[0])\n        this.queryParams.endTime = parseTime(this.rangeTime[1])\n      } else {\n        this.queryParams.startTime = null\n        this.queryParams.endTime = null\n      }\n      this.queryParams.pageNum = 1\n      this.queryParams.pageSize = 10\n\n      if (!this.queryParams.startTime) {\n        this.queryParams.startTime = parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00') // 一周前，时间部分为 00:00:00\n      }\n      if (!this.queryParams.endTime) {\n        this.queryParams.endTime = parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59') // 当前日期，时间部分为 23:59:59\n      }\n      this.rangeTime = [this.queryParams.startTime, this.queryParams.endTime]\n      this.total = 0\n      this.getList()\n      this.$nextTick(() => {\n        const data = JSON.parse(JSON.stringify(this.queryParams))\n        if (data.threatenType != null) {\n          data.threatenType = data.threatenType.join('/')\n        }\n        this.$refs.atcAge.initAttackStage(data)\n      })\n    },\n    // 获取告警类型多级字典数据\n    getThreatenDict() {\n      getMulTypeDict({\n        dictType: 'threaten_alarm_type'\n      }).then(res => {\n        this.threatenDict = res.data\n      })\n    },\n    // 获取部门数据\n    getDeptsData() {\n      getDeptSystem().then(res => this.deptOptions = res.data)\n    },\n    handleChange(val) {\n      // 获取所属部门最后id\n      if (val) {\n        this.queryParams.deptId = val[val.length - 1]\n      } else {\n        this.queryParams.deptId = ''\n      }\n    },\n    resetQuery() {\n      this.queryParams = {\n        threatenName: null,\n        threatenType: null,\n        alarmLevel: null,\n        referenceId: null,\n        srcIp: null,\n        destIp: null,\n        handleState: '0',\n        flowState: null,\n        updateTime: null,\n        attackDirection: null,\n        pageNum: 1,\n        pageSize: 10\n      }\n      const atcAge = this.$refs.atcAge\n      if (atcAge) {\n        atcAge.currentSelectedCard = null\n      }\n      this.rangeTime = null\n      this.handleQuery()\n    },\n    // 新增威胁情报\n    handleAdd() {\n      this.openThrenten = true\n      this.form = {}\n      this.editable = true\n      this.title = '新增威胁情报'\n      this.$set(this.form, 'assetId', '') // 解决el-select无法视图与数据的更新\n    },\n    // 导入功能\n    handleImport() {\n      this.importDialog = true\n    },\n    handleExport() {\n      this.download(\n        '/system/threadten/export',\n        {\n          ...this.queryParams\n        },\n        `威胁告警_${new Date().getTime()}.xlsx`\n      )\n    },\n\n    // 获取列表数据查询\n    handleRowClick(row, column, event) {\n      // 获取告警详情单个单元格数据进行筛选\n      if (row && row.id) {\n        if (column.property) {\n          if (column.property === 'flowState') {\n            this.queryParams[column.property] = !row[column.property] ? 99 : Number(row[column.property])\n            listAlarm({\n              [column.property]: !row[column.property] ? 99 : Number(row[column.property]),\n              pageNum: 1,\n              pageSize: 10,\n              startTime: parseTime(this.rangeTime[0]),\n              endTime: parseTime(this.rangeTime[1])\n            }).then(response => {\n              this.threatenWarnList = response.rows\n              this.threatenWarnList.forEach(item => {\n                item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc\n                if (item.assetType == 'null-null') {\n                  item.assetType = null\n                }\n              })\n              this.total = response.total\n              this.loading = false\n            })\n            return\n          } else if (column.property === 'threatenType') {\n            this.queryParams[column.property] = row[column.property].split('/')\n          } else if (column.property === 'alarmLevel') {\n            this.queryParams[column.property] = row[column.property].toString()\n          } else {\n            this.queryParams[column.property] = row[column.property]\n          }\n          listAlarm({\n            [column.property]: row[column.property],\n            pageNum: 1,\n            pageSize: 10,\n            startTime: parseTime(this.rangeTime[0]),\n            endTime: parseTime(this.rangeTime[1])\n          }).then(response => {\n            this.threatenWarnList = response.rows\n            this.threatenWarnList.forEach(item => {\n              item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc\n              if (item.assetType == 'null-null') {\n                item.assetType = null\n              }\n            })\n            this.total = response.total\n            this.loading = false\n          })\n        }\n      }\n    },\n\n    // 多选\n    handleSelectionChange(val) {\n      this.multipleSelection = val\n    },\n\n    flowStateFormatter(row, column, cellValue, index) {\n      let name = '未分配'\n      const match = this.flowStateOptions.find(item => item.value == cellValue)\n      if (match) {\n        name = match.label\n      }\n      return name\n    },\n    disposerFormatter(row, column, cellValue, index) {\n      let name = ''\n      if (cellValue) {\n        this.userList.forEach(e => {\n          if (e.userId == cellValue) {\n            name = e.nickName\n          }\n        })\n        return name\n      }\n      return name\n    },\n\n    handleStateFormatter(row, column, cellValue, index) {\n      let name = '未处置'\n      const match = this.handleStateOptions.find(item => item.value == cellValue)\n      if (match) {\n        name = match.label\n      }\n      return name\n    },\n    handleDetail(row) {\n      this.assetData = { ...row }\n      this.title = '查看告警详情'\n      this.openDetail(true)\n    },\n    showHandle(row) {\n      // 获取事件详情单个单元格数据进行筛选\n      if (row.handleState === '1' || row.handleState === '2') {\n        this.handleForm.handleState = parseInt(row.handleState)\n        this.handleForm.handleDesc = row.handleDesc\n      } else {\n        this.handleForm = {\n          handleDesc: '',\n          handleState: ''\n        }\n      }\n      this.handleForm.id = row.id\n      this.showHandleDialog = true\n    },\n    handleEdit(row) {\n      getAlarm(row.id).then(res => {\n        this.form = { ...res.data }\n        if (this.form.alarmLevel != null) {\n          this.form.alarmLevel = (this.form.alarmLevel).toString()\n        }\n        if (this.form.threatenType != null) {\n          this.form.threatenType = this.form.threatenType.split('/')\n        }\n        if (this.form.attackNum != null) {\n          this.form.attackNum = (this.form.attackNum).toString()\n        }\n        if (this.form.srcPort != null) {\n          this.form.srcPort = (this.form.srcPort).toString()\n        }\n        if (this.form.destPort != null) {\n          this.form.destPort = (this.form.destPort).toString()\n        }\n        this.title = '修改威胁情报'\n        this.openThrenten = true\n      })\n    },\n    handleDelete(row) {\n      const ids = row.id\n      const title = row.threatenName\n      this.$modal.confirm('是否确认删除告警名称为【' + title + '】的数据项?').then(() => {\n        return delAlarm(ids)\n      }).then(() => {\n        this.$message.success('删除成功')\n        this.getList()\n      }).catch(() => {\n\n      })\n    },\n    addOrUpdateFlowHandle(id, flowState, row) {\n      const data = {\n        id: id || '',\n        formType: 1,\n        opType: flowState ? 0 : '-1',\n        status: flowState,\n        row: row,\n        isWork: true\n      }\n      data.row.workType = '2'\n      data.row.eventType = 3\n      data.originType = 'event'\n      this.currentFlowData = data\n      this.loading = true\n      this.getConfigKey('default.flowTemplateId').then(res => {\n        const flowId = res.msg\n        if (flowId) {\n          this.getFlowEngineInfo(flowId)\n        } else {\n          this.flowTemplateSelectVisible = true\n        }\n      }).finally(() => {\n        this.loading = false\n      })\n    },\n    getFlowEngineInfo(val) {\n      FlowEngineInfo(val).then(res => {\n        if (res.data && res.data.flowTemplateJson) {\n          const data = JSON.parse(res.data.flowTemplateJson)\n          if (!data[0].flowId) {\n            this.$message.error('该流程模板异常,请重新选择')\n          } else {\n            this.currentFlowData.flowId = data[0].flowId\n            this.flowVisible = true\n            this.$nextTick(() => {\n              this.$refs.FlowBox.init(this.currentFlowData)\n            })\n          }\n        }\n      }).finally(() => {\n        this.loading = false\n      })\n    },\n    getList() {\n      this.loading = true\n      const queryParams = {\n        ...this.queryParams\n      }\n      if (queryParams.threatenType != null) {\n        queryParams.threatenType = queryParams.threatenType.join('/')\n      }\n      // 同步请求类型统计数据\n      this.$emit('getList', { ...queryParams })\n      listAlarm(queryParams).then(response => {\n        this.threatenWarnList = response.rows\n        this.threatenWarnList.forEach(item => {\n          item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc\n          if (item.assetType == 'null-null') {\n            item.assetType = null\n          }\n          if (item.deptName) {\n            const deptNameArr = uniqueArr(item.deptName.split(','))\n            item.deptName = deptNameArr.join(',')\n          }\n        })\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    handleClose(done) {\n      done()\n      this.form = {}\n      this.$refs.form.resetFields()\n    },\n    submitHandleForm() {\n      this.$refs['handleStateForm'].validate(valid => {\n        if (valid) {\n          updateAlarm(this.handleForm).then(res => {\n            this.$message.success('处置成功')\n            this.handleForm = {}\n            this.showHandleDialog = false\n            this.getList()\n          })\n        }\n      })\n    },\n    submitForm() {\n      if (this.form.threatenType != null) {\n        this.form.threatenType = this.form.threatenType.join('/')\n      }\n      this.$refs['form'].validate(valid => {\n        if (valid) {\n          if (this.form.id == null) {\n            addAlarm(this.form).then(res => {\n              this.$message.success('新增成功')\n              this.form = {}\n              this.openThrenten = false\n              this.getList()\n            })\n          } else {\n            updateAlarm(this.form).then(res => {\n              this.$message.success('修改成功')\n              this.form = {}\n              this.openThrenten = false\n              this.getList()\n            })\n          }\n        }\n      })\n    },\n    cancel() {\n      this.openThrenten = false\n      this.$refs.form.resetFields()\n    },\n    openDetail(val) {\n      this.openDialog = val\n    },\n    closeDialog() {\n      this.importDialog = false\n      this.handleQuery()\n    },\n    closeAssetDialog() {\n      this.serverOpen = false\n      this.safeOpen = false\n      this.networkOpen = false\n    },\n    closeFlow(isrRefresh) {\n      this.flowVisible = false\n      if (isrRefresh) this.getList()\n    },\n    flowTemplateSelectChange(val) {\n      this.flowTemplateSelectVisible = false\n      this.flowVisible = true\n      this.currentFlowData.flowId = val\n      this.$nextTick(() => {\n        this.$refs.FlowBox.init(this.currentFlowData)\n      })\n    },\n    handleAtcAgeClick(atcAge) {\n      this.queryParams.attackSeg = atcAge\n      this.handleQuery()\n    },\n    handlePropsQuery(val) {\n      if (val && Object.keys(val).length > 0) {\n        if (val.attackSeg && this.$refs.atcAge) {\n          this.$refs.atcAge.currentSelectedCard = val.attackSeg\n        }\n        this.queryParams = val\n        if (val.startTime && val.endTime) {\n          this.rangeTime = [val.startTime, val.endTime]\n        }\n        if (val.handle == '1') {\n          this.queryParams.handleState = '1'\n        } else if (val.handle == '0') {\n          this.queryParams.handleState = '0'\n        }\n        if (val.datasource) {\n          this.queryParams.dataSource = parseInt(val.datasource)\n        }\n        this.getThreatenDict()\n        this.getDeptsData()\n        this.handleQuery()\n      }\n    },\n    handleApplicationTagShow(applicationList) {\n      if (!applicationList || applicationList.length < 1) {\n        return ''\n      }\n      let result = applicationList[0].assetName\n      if (applicationList.length > 1) {\n        result += '...'\n      }\n      return result\n    },\n\n    handleBlocking() {\n      if (this.multipleSelection.length < 1) return this.$message.warning('请选择要阻断的ip')\n      this.blockingDialogVisible = true\n      let arr = this.multipleSelection.map(item => item.srcIp)\n      arr = Array.from(new Set(arr))\n      this.$set(this.blockingForm, 'block_ip', arr.join(';'))\n    },\n    blockingSubmit() {\n      this.$refs['blockingForm'].validate(valid => {\n        if (valid) {\n          addBlockIp(this.blockingForm).then(res => {\n            this.$message.success('添加成功')\n          }).finally(() => {\n            this.blockingDialogVisible = false\n            this.$refs.multipleTable.clearSelection()\n            this.multipleSelection = []\n          })\n        }\n      })\n    },\n    getDeviceConfigList() {\n      listDeviceConfig({ queryAllData: true }).then(res => {\n        this.deviceConfigList = res.rows\n      })\n    },\n\n    // 刷新攻击方向\n    handleRefreshAttackDirection(row) {\n      // 防止重复点击\n      if (row.refreshing) {\n        return\n      }\n\n      this.$set(row, 'refreshing', true)\n\n      refreshAttackDirection([row.id]).then(response => {\n        if (response.code === 200 && response.data && response.data.length > 0) {\n          // 更新当前行的攻击方向\n          const refreshResult = response.data[0]\n          this.$set(row, 'attackDirection', refreshResult.attackDirection)\n          this.$message.success('攻击方向刷新成功')\n        } else {\n          this.$message.error('攻击方向刷新失败')\n        }\n      }).catch(error => {\n        console.error('刷新攻击方向失败:', error)\n        this.$message.error('攻击方向刷新失败：' + (error.message || '未知错误'))\n      }).finally(() => {\n        this.$set(row, 'refreshing', false)\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.el-divider {\n  background: #0E94EA;\n}\n\n.el-divider--vertical {\n  display: inline-block;\n  width: 5px;\n  height: 2em;\n  margin: 0 8px 0 0;\n  vertical-align: middle;\n  position: relative;\n}\n\n.my-title {\n  display: inline-block;\n  vertical-align: center;\n}\n\n.asset-tag {\n  margin-left: 5px;\n}\n\n.asset-tag {\n  max-width: 100%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n\n.el-tooltip__popper {\n  font-size: 12px;\n  max-width: 300px;\n}\n\n.overflow-tag:not(:first-child) {\n  margin-top: 5px;\n}\n.blocking-form {\n  ::v-deep .el-form-item__label {\n    float: none;\n  }\n}\n\n::v-deep .el-tabs__content {\n  overflow: hidden;\n}\n::v-deep .el-dialog__body {\n  max-height: 80vh;\n  overflow-y: auto;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwtBA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,kBAAA,GAAAF,OAAA;AACA,IAAAG,aAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAC,sBAAA,CAAAN,OAAA;AACA,IAAAO,YAAA,GAAAD,sBAAA,CAAAN,OAAA;AACA,IAAAQ,eAAA,GAAAF,sBAAA,CAAAN,OAAA;AACA,IAAAS,mBAAA,GAAAH,sBAAA,CAAAN,OAAA;AACA,IAAAU,UAAA,GAAAJ,sBAAA,CAAAN,OAAA;AACA,IAAAW,QAAA,GAAAL,sBAAA,CAAAN,OAAA;AACA,IAAAY,aAAA,GAAAN,sBAAA,CAAAN,OAAA;AACA,IAAAa,mBAAA,GAAAP,sBAAA,CAAAN,OAAA;AACA,IAAAc,QAAA,GAAAR,sBAAA,CAAAN,OAAA;AACA,IAAAe,mBAAA,GAAAT,sBAAA,CAAAN,OAAA;AACA,IAAAgB,YAAA,GAAAV,sBAAA,CAAAN,OAAA;AACA,IAAAiB,eAAA,GAAAX,sBAAA,CAAAN,OAAA;AACA,IAAAkB,eAAA,GAAAZ,sBAAA,CAAAN,OAAA;AACA,IAAAmB,MAAA,GAAAb,sBAAA,CAAAN,OAAA;AAEA,IAAAoB,WAAA,GAAAd,sBAAA,CAAAN,OAAA;AACA,IAAAqB,MAAA,GAAArB,OAAA;AACA,IAAAsB,WAAA,GAAAtB,OAAA;AACA,IAAAuB,KAAA,GAAAvB,OAAA;AACA,IAAAwB,gBAAA,GAAAlB,sBAAA,CAAAN,OAAA;AACA,IAAAyB,aAAA,GAAAzB,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAIA;EACA0B,IAAA;EACAC,UAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,kBAAA,EAAAA,2BAAA;IACAC,OAAA,EAAAA,gBAAA;IACAC,kBAAA,EAAAA,2BAAA;IACAC,YAAA,EAAAA,cAAA;IACAC,YAAA,EAAAA,cAAA;IACAC,kBAAA,EAAAA,2BAAA;IAAAC,YAAA,EAAAA,qBAAA;IAAAC,OAAA,EAAAA,gBAAA;IAAAC,SAAA,EAAAA,kBAAA;IAAAC,cAAA,EAAAA,uBAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,UAAA,EAAAA;EACA;EACAC,KAAA;EACAC,KAAA;IACAC,eAAA;MACAC,IAAA,EAAAC;IACA;IACAC,gBAAA;MACAF,IAAA,EAAAG,MAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAC,UAAA;MACAL,IAAA,EAAAM,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA,IAAAC,eAAA,YAAAA,gBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACA,OAAAC,QAAA,KAAAC,KAAA;MACA;MACA;MACA,IAAAC,OAAA;MACA,KAAAA,OAAA,CAAAC,IAAA,CAAAJ,KAAA;QACA,OAAAC,QAAA,KAAAC,KAAA;MACA;MACA,OAAAD,QAAA;IACA;IACA;MACAI,QAAA;MACAC,gBAAA;MACAC,UAAA;QACAC,EAAA;QACAC,UAAA;QACAC,WAAA;MACA;MACAC,WAAA;QACAD,WAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,OAAA;MACAC,YAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAT,WAAA;MACA;MACAU,WAAA;MACAC,SAAA;MACAC,OAAA;MACAC,gBAAA;MACAC,KAAA;MACAC,KAAA;MACAC,YAAA;MACAC,IAAA;MACAC,KAAA;QACAC,YAAA,GACA;UAAAjB,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAF,QAAA;UACAT,OAAA;UACAU,OAAA;UACAC,OAAA;QACA,EACA;QACAkB,UAAA,GACA;UAAApB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAmB,YAAA,GACA;UAAArB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAoB,MAAA,GACA;UAAAtB,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAqB,WAAA,GACA;UAAAvB,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAsB,OAAA,GACA;UAAAxB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAuB,UAAA,GACA;UAAAzB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAwB,KAAA,GACA;UAAA1B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UACAF,QAAA;UACAT,OAAA;UACAU,OAAA;UACAC,OAAA;QACA,EACA;QACAyB,OAAA,GACA;UAAA3B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAT,OAAA;UAAAU,OAAA;UAAAC,OAAA;QAAA,EACA;QACA0B,MAAA,GACA;UAAA5B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UACAF,QAAA;UACAT,OAAA;UACAU,OAAA;UACAC,OAAA;QACA,EACA;QACA2B,QAAA,GACA;UAAA7B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAT,OAAA;UAAAU,OAAA;UAAAC,OAAA;QAAA,EACA;QACA4B,QAAA,GACA;UAAA9B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,EACA;QACA6B,aAAA,GACA;UAAA/B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,EACA;QACA8B,UAAA,GACA;UAAAhC,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA+B,WAAA,GACA;UAAAjC,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAgC,YAAA,GACA;UAAAlC,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAiC,YAAA;MACAC,aAAA;QACAC,QAAA;QACA;QACA;UAAAC,SAAA,EAAApD,eAAA;UAAAgB,OAAA;QAAA,EACA;QACAqC,aAAA,GACA;UAAAvC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAsC,OAAA,GACA;UAAAxC,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAuC,cAAA;MACAC,qBAAA;MAAA;MACAC,QAAA;MACAC,aAAA;MACAC,UAAA;MACAC,SAAA;MACAC,YAAA;MACAC,UAAA;MACAC,OAAA;MACAC,QAAA;MACAC,kBAAA;MACAC,YAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,yBAAA;MACAC,gBAAA,GACA;QACAC,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,EACA;MACAsE,kBAAA,GACA;QACAD,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,EACA;MACAuE,UAAA;MACAC,gBAAA,GACA;QACAH,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,EACA;MACAyE,gBAAA,GACA;QACAJ,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,EACA;MACA0E,iBAAA;MACAC,gBAAA;IACA;EACA;EACAC,KAAA;IACA;IACA,wBAAAC,WAAA7E,KAAA,EAAA8E,QAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,EAAA;MACA,IAAAC,GAAA,GAAAD,EAAA,CAAA5E,IAAA,CAAAJ,KAAA;MACA,IAAAiF,GAAA;QACA;QACA,IAAAC,0BAAA,EAAAlF,KAAA,EAAAmF,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAvF,IAAA,CAAAwF,MAAA;YACA,IAAA3B,SAAA,GAAA0B,QAAA,CAAAvF,IAAA;YACA6D,SAAA,CAAA4B,OAAA,WAAAC,IAAA;cAAA,OAAAA,IAAA,CAAAvF,KAAA,GAAAuF,IAAA,CAAAC,SAAA,SAAAD,IAAA,CAAAE,aAAA;YAAA;YACA,IAAAzF,KAAA,KAAA8E,QAAA,IAAAA,QAAA;cACAC,KAAA,CAAApD,IAAA,CAAAkC,OAAA;cACAkB,KAAA,CAAApD,IAAA,CAAA+D,MAAA;YACA;YACA;YACA,IAAAhC,SAAA,CAAA2B,MAAA;cACAN,KAAA,CAAApD,IAAA,CAAAkC,OAAA,GAAAH,SAAA,IAAAG,OAAA;cACAkB,KAAA,CAAApD,IAAA,CAAA+D,MAAA,GAAAhC,SAAA,IAAAgC,MAAA;YACA;YACA,IAAAhC,SAAA,CAAA2B,MAAA,SAAAN,KAAA,CAAApD,IAAA,CAAAkC,OAAA;cACAkB,KAAA,CAAApD,IAAA,CAAAkC,OAAA;cACAkB,KAAA,CAAApD,IAAA,CAAA+D,MAAA;YACA;YACAX,KAAA,CAAAvB,aAAA,GAAAE,SAAA;UACA;YACAqB,KAAA,CAAAvB,aAAA;YACA,OAAAuB,KAAA,CAAAY,QAAA,CAAAC,OAAA;UACA;QACA;MACA;QACA,KAAApC,aAAA;QACA,KAAA7B,IAAA,CAAAkC,OAAA;QACA,KAAAlC,IAAA,CAAA+D,MAAA;MACA;IACA;IACArG,eAAA,WAAAA,gBAAA;MACA,KAAAwG,IAAA;IACA;IACArG,gBAAA;MACAsG,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAAC,gBAAA,CAAAD,GAAA;MACA;IACA;IACA;AACA;AACA;IACA;MACAD,OAAA,WAAAA,QAAA9F,KAAA;QACA,IAAAA,KAAA;UACA,KAAAqD,cAAA,GAAArD,KAAA,CAAAiG,KAAA,MAAAC,GAAA,WAAAC,EAAA;YAAA,OAAAA,EAAA,CAAAC,IAAA;UAAA,GAAAC,MAAA,WAAAF,EAAA;YAAA,OAAAA,EAAA;UAAA;QACA;MACA;MACAG,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,mBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,UAAAC,MAAA,CAAAC,KAAA,IAAAlH,MAAA,CAAAmH,IAAA,MAAAF,MAAA,CAAAC,KAAA,EAAAtB,MAAA;MACA,KAAAQ,IAAA;IACA;MACA,KAAAG,gBAAA,MAAAU,MAAA,CAAAC,KAAA;IACA;EACA;EACAE,OAAA;IACAhB,IAAA,WAAAA,KAAA;MACA;MACA,KAAAiB,eAAA;MACA,KAAAC,WAAA;MACA,KAAAC,YAAA;MACA,KAAAC,WAAA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,cAAA;QAAAjG,OAAA;QAAAC,QAAA;MAAA,GAAAgE,IAAA,WAAAiC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAH,MAAA,CAAA7G,QAAA,GAAA+G,GAAA,CAAAC,IAAA;QACA;MACA;IACA;IACAN,WAAA,WAAAA,YAAA;MAAA,IAAAO,MAAA;MACA,KAAA9H,gBAAA,CAAAwC,UAAA,QAAAf,WAAA,CAAAe,UAAA;MACA,KAAAxC,gBAAA,CAAA+H,WAAA,QAAAtG,WAAA,CAAAsG,WAAA;MACA;MACA,KAAAtG,WAAA,OAAAuG,cAAA,CAAA9H,OAAA,MAAA8H,cAAA,CAAA9H,OAAA,WAAAuB,WAAA,QAAAzB,gBAAA;MACA,SAAA6B,SAAA;QACA,KAAAJ,WAAA,CAAAwG,SAAA,OAAAC,gBAAA,OAAArG,SAAA;QACA,KAAAJ,WAAA,CAAA0G,OAAA,OAAAD,gBAAA,OAAArG,SAAA;MACA;QACA,KAAAJ,WAAA,CAAAwG,SAAA;QACA,KAAAxG,WAAA,CAAA0G,OAAA;MACA;MACA,KAAA1G,WAAA,CAAAC,OAAA;MACA,KAAAD,WAAA,CAAAE,QAAA;MAEA,UAAAF,WAAA,CAAAwG,SAAA;QACA,KAAAxG,WAAA,CAAAwG,SAAA,OAAAC,gBAAA,MAAAE,IAAA,GAAAC,QAAA;MACA;MACA,UAAA5G,WAAA,CAAA0G,OAAA;QACA,KAAA1G,WAAA,CAAA0G,OAAA,OAAAD,gBAAA,MAAAE,IAAA,GAAAC,QAAA;MACA;MACA,KAAAxG,SAAA,SAAAJ,WAAA,CAAAwG,SAAA,OAAAxG,WAAA,CAAA0G,OAAA;MACA,KAAAnG,KAAA;MACA,KAAAsG,OAAA;MACA,KAAAC,SAAA;QACA,IAAAlI,IAAA,GAAAmI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAZ,MAAA,CAAArG,WAAA;QACA,IAAApB,IAAA,CAAAoC,YAAA;UACApC,IAAA,CAAAoC,YAAA,GAAApC,IAAA,CAAAoC,YAAA,CAAAkG,IAAA;QACA;QACAb,MAAA,CAAAc,KAAA,CAAAC,MAAA,CAAAC,eAAA,CAAAzI,IAAA;MACA;IACA;IACA;IACAiH,eAAA,WAAAA,gBAAA;MAAA,IAAAyB,MAAA;MACA,IAAAC,oBAAA;QACAC,QAAA;MACA,GAAAtD,IAAA,WAAAiC,GAAA;QACAmB,MAAA,CAAAvH,YAAA,GAAAoG,GAAA,CAAAvH,IAAA;MACA;IACA;IACA;IACAmH,YAAA,WAAAA,aAAA;MAAA,IAAA0B,MAAA;MACA,IAAAC,gCAAA,IAAAxD,IAAA,WAAAiC,GAAA;QAAA,OAAAsB,MAAA,CAAAtH,WAAA,GAAAgG,GAAA,CAAAvH,IAAA;MAAA;IACA;IACA+I,YAAA,WAAAA,aAAA7C,GAAA;MACA;MACA,IAAAA,GAAA;QACA,KAAA9E,WAAA,CAAAyE,MAAA,GAAAK,GAAA,CAAAA,GAAA,CAAAV,MAAA;MACA;QACA,KAAApE,WAAA,CAAAyE,MAAA;MACA;IACA;IACAmD,UAAA,WAAAA,WAAA;MACA,KAAA5H,WAAA;QACAY,YAAA;QACAI,YAAA;QACAD,UAAA;QACAuF,WAAA;QACAjF,KAAA;QACAE,MAAA;QACA9B,WAAA;QACAoI,SAAA;QACAC,UAAA;QACAC,eAAA;QACA9H,OAAA;QACAC,QAAA;MACA;MACA,IAAAkH,MAAA,QAAAD,KAAA,CAAAC,MAAA;MACA,IAAAA,MAAA;QACAA,MAAA,CAAAY,mBAAA;MACA;MACA,KAAA5H,SAAA;MACA,KAAA0F,WAAA;IACA;IACA;IACAmC,SAAA,WAAAA,UAAA;MACA,KAAAxH,YAAA;MACA,KAAAC,IAAA;MACA,KAAA4B,QAAA;MACA,KAAA9B,KAAA;MACA,KAAA0H,IAAA,MAAAxH,IAAA;IACA;IACA;IACAyH,YAAA,WAAAA,aAAA;MACA,KAAAzF,YAAA;IACA;IACA0F,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,gCAAA9B,cAAA,CAAA9H,OAAA,MAEA,KAAAuB,WAAA,+BAAAsI,MAAA,CAEA,IAAA3B,IAAA,GAAA4B,OAAA,YACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAAC,GAAA,EAAAC,MAAA,EAAAC,KAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAH,GAAA,IAAAA,GAAA,CAAAlJ,EAAA;QACA,IAAAmJ,MAAA,CAAAG,QAAA;UACA,IAAAH,MAAA,CAAAG,QAAA;YACA,KAAA7I,WAAA,CAAA0I,MAAA,CAAAG,QAAA,KAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,SAAAlK,MAAA,CAAA8J,GAAA,CAAAC,MAAA,CAAAG,QAAA;YACA,IAAAC,uBAAA,MAAAC,gBAAA,CAAAtK,OAAA,MAAAsK,gBAAA,CAAAtK,OAAA,MAAAsK,gBAAA,CAAAtK,OAAA,MAAAsK,gBAAA,CAAAtK,OAAA,MAAAsK,gBAAA,CAAAtK,OAAA,MACAiK,MAAA,CAAAG,QAAA,GAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,SAAAlK,MAAA,CAAA8J,GAAA,CAAAC,MAAA,CAAAG,QAAA,gBACA,gBACA,kBACA,IAAApC,gBAAA,OAAArG,SAAA,kBACA,IAAAqG,gBAAA,OAAArG,SAAA,KACA,EAAA8D,IAAA,WAAAC,QAAA;cACAyE,MAAA,CAAAtI,gBAAA,GAAA6D,QAAA,CAAAiC,IAAA;cACAwC,MAAA,CAAAtI,gBAAA,CAAA+D,OAAA,WAAAC,IAAA;gBACAA,IAAA,CAAA0E,SAAA,GAAA1E,IAAA,CAAA2E,cAAA,SAAA3E,IAAA,CAAAE,aAAA;gBACA,IAAAF,IAAA,CAAA0E,SAAA;kBACA1E,IAAA,CAAA0E,SAAA;gBACA;cACA;cACAJ,MAAA,CAAArI,KAAA,GAAA4D,QAAA,CAAA5D,KAAA;cACAqI,MAAA,CAAAvI,OAAA;YACA;YACA;UACA,WAAAqI,MAAA,CAAAG,QAAA;YACA,KAAA7I,WAAA,CAAA0I,MAAA,CAAAG,QAAA,IAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,EAAA7D,KAAA;UACA,WAAA0D,MAAA,CAAAG,QAAA;YACA,KAAA7I,WAAA,CAAA0I,MAAA,CAAAG,QAAA,IAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,EAAAK,QAAA;UACA;YACA,KAAAlJ,WAAA,CAAA0I,MAAA,CAAAG,QAAA,IAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA;UACA;UACA,IAAAC,uBAAA,MAAAC,gBAAA,CAAAtK,OAAA,MAAAsK,gBAAA,CAAAtK,OAAA,MAAAsK,gBAAA,CAAAtK,OAAA,MAAAsK,gBAAA,CAAAtK,OAAA,MAAAsK,gBAAA,CAAAtK,OAAA,MACAiK,MAAA,CAAAG,QAAA,EAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,eACA,gBACA,kBACA,IAAApC,gBAAA,OAAArG,SAAA,kBACA,IAAAqG,gBAAA,OAAArG,SAAA,KACA,EAAA8D,IAAA,WAAAC,QAAA;YACAyE,MAAA,CAAAtI,gBAAA,GAAA6D,QAAA,CAAAiC,IAAA;YACAwC,MAAA,CAAAtI,gBAAA,CAAA+D,OAAA,WAAAC,IAAA;cACAA,IAAA,CAAA0E,SAAA,GAAA1E,IAAA,CAAA2E,cAAA,SAAA3E,IAAA,CAAAE,aAAA;cACA,IAAAF,IAAA,CAAA0E,SAAA;gBACA1E,IAAA,CAAA0E,SAAA;cACA;YACA;YACAJ,MAAA,CAAArI,KAAA,GAAA4D,QAAA,CAAA5D,KAAA;YACAqI,MAAA,CAAAvI,OAAA;UACA;QACA;MACA;IACA;IAEA;IACA8I,qBAAA,WAAAA,sBAAArE,GAAA;MACA,KAAArB,iBAAA,GAAAqB,GAAA;IACA;IAEAsE,kBAAA,WAAAA,mBAAAX,GAAA,EAAAC,MAAA,EAAAW,SAAA,EAAAC,KAAA;MACA,IAAAvM,IAAA;MACA,IAAAwM,KAAA,QAAApG,gBAAA,CAAAqG,IAAA,WAAAlF,IAAA;QAAA,OAAAA,IAAA,CAAAvF,KAAA,IAAAsK,SAAA;MAAA;MACA,IAAAE,KAAA;QACAxM,IAAA,GAAAwM,KAAA,CAAAnG,KAAA;MACA;MACA,OAAArG,IAAA;IACA;IACA0M,iBAAA,WAAAA,kBAAAhB,GAAA,EAAAC,MAAA,EAAAW,SAAA,EAAAC,KAAA;MACA,IAAAvM,IAAA;MACA,IAAAsM,SAAA;QACA,KAAAjK,QAAA,CAAAiF,OAAA,WAAAqF,CAAA;UACA,IAAAA,CAAA,CAAAC,MAAA,IAAAN,SAAA;YACAtM,IAAA,GAAA2M,CAAA,CAAAE,QAAA;UACA;QACA;QACA,OAAA7M,IAAA;MACA;MACA,OAAAA,IAAA;IACA;IAEA8M,oBAAA,WAAAA,qBAAApB,GAAA,EAAAC,MAAA,EAAAW,SAAA,EAAAC,KAAA;MACA,IAAAvM,IAAA;MACA,IAAAwM,KAAA,QAAAlG,kBAAA,CAAAmG,IAAA,WAAAlF,IAAA;QAAA,OAAAA,IAAA,CAAAvF,KAAA,IAAAsK,SAAA;MAAA;MACA,IAAAE,KAAA;QACAxM,IAAA,GAAAwM,KAAA,CAAAnG,KAAA;MACA;MACA,OAAArG,IAAA;IACA;IACA+M,YAAA,WAAAA,aAAArB,GAAA;MACA,KAAAhG,SAAA,OAAA8D,cAAA,CAAA9H,OAAA,MAAAgK,GAAA;MACA,KAAAjI,KAAA;MACA,KAAAuJ,UAAA;IACA;IACAC,UAAA,WAAAA,WAAAvB,GAAA;MACA;MACA,IAAAA,GAAA,CAAAhJ,WAAA,YAAAgJ,GAAA,CAAAhJ,WAAA;QACA,KAAAH,UAAA,CAAAG,WAAA,GAAAwK,QAAA,CAAAxB,GAAA,CAAAhJ,WAAA;QACA,KAAAH,UAAA,CAAAE,UAAA,GAAAiJ,GAAA,CAAAjJ,UAAA;MACA;QACA,KAAAF,UAAA;UACAE,UAAA;UACAC,WAAA;QACA;MACA;MACA,KAAAH,UAAA,CAAAC,EAAA,GAAAkJ,GAAA,CAAAlJ,EAAA;MACA,KAAAF,gBAAA;IACA;IACA6K,UAAA,WAAAA,WAAAzB,GAAA;MAAA,IAAA0B,MAAA;MACA,IAAAC,sBAAA,EAAA3B,GAAA,CAAAlJ,EAAA,EAAA2E,IAAA,WAAAiC,GAAA;QACAgE,MAAA,CAAAzJ,IAAA,OAAA6F,cAAA,CAAA9H,OAAA,MAAA0H,GAAA,CAAAvH,IAAA;QACA,IAAAuL,MAAA,CAAAzJ,IAAA,CAAAK,UAAA;UACAoJ,MAAA,CAAAzJ,IAAA,CAAAK,UAAA,GAAAoJ,MAAA,CAAAzJ,IAAA,CAAAK,UAAA,CAAAmI,QAAA;QACA;QACA,IAAAiB,MAAA,CAAAzJ,IAAA,CAAAM,YAAA;UACAmJ,MAAA,CAAAzJ,IAAA,CAAAM,YAAA,GAAAmJ,MAAA,CAAAzJ,IAAA,CAAAM,YAAA,CAAAgE,KAAA;QACA;QACA,IAAAmF,MAAA,CAAAzJ,IAAA,CAAA2J,SAAA;UACAF,MAAA,CAAAzJ,IAAA,CAAA2J,SAAA,GAAAF,MAAA,CAAAzJ,IAAA,CAAA2J,SAAA,CAAAnB,QAAA;QACA;QACA,IAAAiB,MAAA,CAAAzJ,IAAA,CAAAY,OAAA;UACA6I,MAAA,CAAAzJ,IAAA,CAAAY,OAAA,GAAA6I,MAAA,CAAAzJ,IAAA,CAAAY,OAAA,CAAA4H,QAAA;QACA;QACA,IAAAiB,MAAA,CAAAzJ,IAAA,CAAAc,QAAA;UACA2I,MAAA,CAAAzJ,IAAA,CAAAc,QAAA,GAAA2I,MAAA,CAAAzJ,IAAA,CAAAc,QAAA,CAAA0H,QAAA;QACA;QACAiB,MAAA,CAAA3J,KAAA;QACA2J,MAAA,CAAA1J,YAAA;MACA;IACA;IACA6J,YAAA,WAAAA,aAAA7B,GAAA;MAAA,IAAA8B,MAAA;MACA,IAAAC,GAAA,GAAA/B,GAAA,CAAAlJ,EAAA;MACA,IAAAiB,KAAA,GAAAiI,GAAA,CAAA7H,YAAA;MACA,KAAA6J,MAAA,CAAAC,OAAA,kBAAAlK,KAAA,aAAA0D,IAAA;QACA,WAAAyG,sBAAA,EAAAH,GAAA;MACA,GAAAtG,IAAA;QACAqG,MAAA,CAAA7F,QAAA,CAAAkG,OAAA;QACAL,MAAA,CAAA1D,OAAA;MACA,GAAAgE,KAAA,cAEA;IACA;IACAC,qBAAA,WAAAA,sBAAAvL,EAAA,EAAAsI,SAAA,EAAAY,GAAA;MAAA,IAAAsC,MAAA;MACA,IAAAnM,IAAA;QACAW,EAAA,EAAAA,EAAA;QACAyL,QAAA;QACAC,MAAA,EAAApD,SAAA;QACAqD,MAAA,EAAArD,SAAA;QACAY,GAAA,EAAAA,GAAA;QACA0C,MAAA;MACA;MACAvM,IAAA,CAAA6J,GAAA,CAAA2C,QAAA;MACAxM,IAAA,CAAA6J,GAAA,CAAA4C,SAAA;MACAzM,IAAA,CAAA0M,UAAA;MACA,KAAAC,eAAA,GAAA3M,IAAA;MACA,KAAAyB,OAAA;MACA,KAAAmL,YAAA,2BAAAtH,IAAA,WAAAiC,GAAA;QACA,IAAAsF,MAAA,GAAAtF,GAAA,CAAAuF,GAAA;QACA,IAAAD,MAAA;UACAV,MAAA,CAAAY,iBAAA,CAAAF,MAAA;QACA;UACAV,MAAA,CAAA7H,yBAAA;QACA;MACA,GAAA0I,OAAA;QACAb,MAAA,CAAA1K,OAAA;MACA;IACA;IACAsL,iBAAA,WAAAA,kBAAA7G,GAAA;MAAA,IAAA+G,OAAA;MACA,IAAAC,0BAAA,EAAAhH,GAAA,EAAAZ,IAAA,WAAAiC,GAAA;QACA,IAAAA,GAAA,CAAAvH,IAAA,IAAAuH,GAAA,CAAAvH,IAAA,CAAAmN,gBAAA;UACA,IAAAnN,IAAA,GAAAmI,IAAA,CAAAC,KAAA,CAAAb,GAAA,CAAAvH,IAAA,CAAAmN,gBAAA;UACA,KAAAnN,IAAA,IAAA6M,MAAA;YACAI,OAAA,CAAAnH,QAAA,CAAAsH,KAAA;UACA;YACAH,OAAA,CAAAN,eAAA,CAAAE,MAAA,GAAA7M,IAAA,IAAA6M,MAAA;YACAI,OAAA,CAAA5I,WAAA;YACA4I,OAAA,CAAA/E,SAAA;cACA+E,OAAA,CAAA1E,KAAA,CAAA5J,OAAA,CAAAqH,IAAA,CAAAiH,OAAA,CAAAN,eAAA;YACA;UACA;QACA;MACA,GAAAK,OAAA;QACAC,OAAA,CAAAxL,OAAA;MACA;IACA;IACAwG,OAAA,WAAAA,QAAA;MAAA,IAAAoF,OAAA;MACA,KAAA5L,OAAA;MACA,IAAAL,WAAA,OAAAuG,cAAA,CAAA9H,OAAA,MACA,KAAAuB,WAAA,CACA;MACA,IAAAA,WAAA,CAAAgB,YAAA;QACAhB,WAAA,CAAAgB,YAAA,GAAAhB,WAAA,CAAAgB,YAAA,CAAAkG,IAAA;MACA;MACA;MACA,KAAAgF,KAAA,gBAAA3F,cAAA,CAAA9H,OAAA,MAAAuB,WAAA;MACA,IAAA8I,uBAAA,EAAA9I,WAAA,EAAAkE,IAAA,WAAAC,QAAA;QACA8H,OAAA,CAAA3L,gBAAA,GAAA6D,QAAA,CAAAiC,IAAA;QACA6F,OAAA,CAAA3L,gBAAA,CAAA+D,OAAA,WAAAC,IAAA;UACAA,IAAA,CAAA0E,SAAA,GAAA1E,IAAA,CAAA2E,cAAA,SAAA3E,IAAA,CAAAE,aAAA;UACA,IAAAF,IAAA,CAAA0E,SAAA;YACA1E,IAAA,CAAA0E,SAAA;UACA;UACA,IAAA1E,IAAA,CAAA6H,QAAA;YACA,IAAAC,WAAA,OAAAC,gBAAA,EAAA/H,IAAA,CAAA6H,QAAA,CAAAnH,KAAA;YACAV,IAAA,CAAA6H,QAAA,GAAAC,WAAA,CAAAlF,IAAA;UACA;QACA;QACA+E,OAAA,CAAA1L,KAAA,GAAA4D,QAAA,CAAA5D,KAAA;QACA0L,OAAA,CAAA5L,OAAA;MACA;IACA;IACAiM,WAAA,WAAAA,YAAAC,IAAA;MACAA,IAAA;MACA,KAAA7L,IAAA;MACA,KAAAyG,KAAA,CAAAzG,IAAA,CAAA8L,WAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,KAAAvF,KAAA,oBAAAwF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,yBAAA,EAAAH,OAAA,CAAApN,UAAA,EAAA4E,IAAA,WAAAiC,GAAA;YACAuG,OAAA,CAAAhI,QAAA,CAAAkG,OAAA;YACA8B,OAAA,CAAApN,UAAA;YACAoN,OAAA,CAAArN,gBAAA;YACAqN,OAAA,CAAA7F,OAAA;UACA;QACA;MACA;IACA;IACAiG,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,SAAArM,IAAA,CAAAM,YAAA;QACA,KAAAN,IAAA,CAAAM,YAAA,QAAAN,IAAA,CAAAM,YAAA,CAAAkG,IAAA;MACA;MACA,KAAAC,KAAA,SAAAwF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAG,OAAA,CAAArM,IAAA,CAAAnB,EAAA;YACA,IAAAyN,sBAAA,EAAAD,OAAA,CAAArM,IAAA,EAAAwD,IAAA,WAAAiC,GAAA;cACA4G,OAAA,CAAArI,QAAA,CAAAkG,OAAA;cACAmC,OAAA,CAAArM,IAAA;cACAqM,OAAA,CAAAtM,YAAA;cACAsM,OAAA,CAAAlG,OAAA;YACA;UACA;YACA,IAAAgG,yBAAA,EAAAE,OAAA,CAAArM,IAAA,EAAAwD,IAAA,WAAAiC,GAAA;cACA4G,OAAA,CAAArI,QAAA,CAAAkG,OAAA;cACAmC,OAAA,CAAArM,IAAA;cACAqM,OAAA,CAAAtM,YAAA;cACAsM,OAAA,CAAAlG,OAAA;YACA;UACA;QACA;MACA;IACA;IACAoG,MAAA,WAAAA,OAAA;MACA,KAAAxM,YAAA;MACA,KAAA0G,KAAA,CAAAzG,IAAA,CAAA8L,WAAA;IACA;IACAzC,UAAA,WAAAA,WAAAjF,GAAA;MACA,KAAAtC,UAAA,GAAAsC,GAAA;IACA;IACAoI,WAAA,WAAAA,YAAA;MACA,KAAAxK,YAAA;MACA,KAAAoD,WAAA;IACA;IACAqH,gBAAA,WAAAA,iBAAA;MACA,KAAAxK,UAAA;MACA,KAAAE,QAAA;MACA,KAAAuK,WAAA;IACA;IACAC,SAAA,WAAAA,UAAAC,UAAA;MACA,KAAArK,WAAA;MACA,IAAAqK,UAAA,OAAAzG,OAAA;IACA;IACA0G,wBAAA,WAAAA,yBAAAzI,GAAA;MAAA,IAAA0I,OAAA;MACA,KAAAtK,yBAAA;MACA,KAAAD,WAAA;MACA,KAAAsI,eAAA,CAAAE,MAAA,GAAA3G,GAAA;MACA,KAAAgC,SAAA;QACA0G,OAAA,CAAArG,KAAA,CAAA5J,OAAA,CAAAqH,IAAA,CAAA4I,OAAA,CAAAjC,eAAA;MACA;IACA;IACAkC,iBAAA,WAAAA,kBAAArG,MAAA;MACA,KAAApH,WAAA,CAAA0N,SAAA,GAAAtG,MAAA;MACA,KAAAtB,WAAA;IACA;IACAf,gBAAA,WAAAA,iBAAAD,GAAA;MACA,IAAAA,GAAA,IAAAtG,MAAA,CAAAmH,IAAA,CAAAb,GAAA,EAAAV,MAAA;QACA,IAAAU,GAAA,CAAA4I,SAAA,SAAAvG,KAAA,CAAAC,MAAA;UACA,KAAAD,KAAA,CAAAC,MAAA,CAAAY,mBAAA,GAAAlD,GAAA,CAAA4I,SAAA;QACA;QACA,KAAA1N,WAAA,GAAA8E,GAAA;QACA,IAAAA,GAAA,CAAA0B,SAAA,IAAA1B,GAAA,CAAA4B,OAAA;UACA,KAAAtG,SAAA,IAAA0E,GAAA,CAAA0B,SAAA,EAAA1B,GAAA,CAAA4B,OAAA;QACA;QACA,IAAA5B,GAAA,CAAA6I,MAAA;UACA,KAAA3N,WAAA,CAAAP,WAAA;QACA,WAAAqF,GAAA,CAAA6I,MAAA;UACA,KAAA3N,WAAA,CAAAP,WAAA;QACA;QACA,IAAAqF,GAAA,CAAA8I,UAAA;UACA,KAAA5N,WAAA,CAAA6N,UAAA,GAAA5D,QAAA,CAAAnF,GAAA,CAAA8I,UAAA;QACA;QACA,KAAA/H,eAAA;QACA,KAAAE,YAAA;QACA,KAAAD,WAAA;MACA;IACA;IACAgI,wBAAA,WAAAA,yBAAAC,eAAA;MACA,KAAAA,eAAA,IAAAA,eAAA,CAAA3J,MAAA;QACA;MACA;MACA,IAAA4J,MAAA,GAAAD,eAAA,IAAAxJ,SAAA;MACA,IAAAwJ,eAAA,CAAA3J,MAAA;QACA4J,MAAA;MACA;MACA,OAAAA,MAAA;IACA;IAEAC,cAAA,WAAAA,eAAA;MACA,SAAAxK,iBAAA,CAAAW,MAAA,kBAAAM,QAAA,CAAAC,OAAA;MACA,KAAAtC,qBAAA;MACA,IAAA6L,GAAA,QAAAzK,iBAAA,CAAAwB,GAAA,WAAAX,IAAA;QAAA,OAAAA,IAAA,CAAAjD,KAAA;MAAA;MACA6M,GAAA,GAAAC,KAAA,CAAAC,IAAA,KAAAC,GAAA,CAAAH,GAAA;MACA,KAAAhG,IAAA,MAAApG,YAAA,cAAAoM,GAAA,CAAAhH,IAAA;IACA;IACAoH,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA,KAAApH,KAAA,iBAAAwF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAA4B,wBAAA,EAAAD,OAAA,CAAAzM,YAAA,EAAAoC,IAAA,WAAAiC,GAAA;YACAoI,OAAA,CAAA7J,QAAA,CAAAkG,OAAA;UACA,GAAAgB,OAAA;YACA2C,OAAA,CAAAlM,qBAAA;YACAkM,OAAA,CAAApH,KAAA,CAAAsH,aAAA,CAAAC,cAAA;YACAH,OAAA,CAAA9K,iBAAA;UACA;QACA;MACA;IACA;IACA8B,mBAAA,WAAAA,oBAAA;MAAA,IAAAoJ,OAAA;MACA,IAAAC,8BAAA;QAAAC,YAAA;MAAA,GAAA3K,IAAA,WAAAiC,GAAA;QACAwI,OAAA,CAAAjL,gBAAA,GAAAyC,GAAA,CAAAC,IAAA;MACA;IACA;IAEA;IACA0I,4BAAA,WAAAA,6BAAArG,GAAA;MAAA,IAAAsG,OAAA;MACA;MACA,IAAAtG,GAAA,CAAAuG,UAAA;QACA;MACA;MAEA,KAAA9G,IAAA,CAAAO,GAAA;MAEA,IAAAwG,oCAAA,GAAAxG,GAAA,CAAAlJ,EAAA,GAAA2E,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAA+K,IAAA,YAAA/K,QAAA,CAAAvF,IAAA,IAAAuF,QAAA,CAAAvF,IAAA,CAAAwF,MAAA;UACA;UACA,IAAA+K,aAAA,GAAAhL,QAAA,CAAAvF,IAAA;UACAmQ,OAAA,CAAA7G,IAAA,CAAAO,GAAA,qBAAA0G,aAAA,CAAApH,eAAA;UACAgH,OAAA,CAAArK,QAAA,CAAAkG,OAAA;QACA;UACAmE,OAAA,CAAArK,QAAA,CAAAsH,KAAA;QACA;MACA,GAAAnB,KAAA,WAAAmB,KAAA;QACAoD,OAAA,CAAApD,KAAA,cAAAA,KAAA;QACA+C,OAAA,CAAArK,QAAA,CAAAsH,KAAA,gBAAAA,KAAA,CAAApM,OAAA;MACA,GAAAgM,OAAA;QACAmD,OAAA,CAAA7G,IAAA,CAAAO,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}