<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblUploadfileMapper">
    
    <resultMap type="com.ruoyi.safe.domain.TblUploadfile" id="TblUploadfileResult">
        <result property="id"    column="id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileType"    column="file_type"    />
        <result property="exType"    column="ex_type"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="md5"    column="MD5"    />
        <result property="assetId"    column="asset_id"    />
        <result property="deleted"    column="deleted"    />
        <result property="userName"    column="user_name"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTblUploadfileVo">
        select id, file_name,file_type,ex_type ,file_url, MD5, asset_id,deleted, user_name, user_id, dept_id, create_time, update_time, create_by, update_by, remark from tbl_uploadfile
    </sql>

    <select id="selectTblUploadfileList" parameterType="TblUploadfile" resultMap="TblUploadfileResult">
        <include refid="selectTblUploadfileVo"/>
        <where>  
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="exType != null  and exType != ''"> and ex_type =#{exType}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="md5 != null  and md5 != ''"> and MD5 = #{md5}</if>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="1==1"> and deleted = '0'</if>
        </where>
    </select>
    
    <select id="selectTblUploadfileById" parameterType="Long" resultMap="TblUploadfileResult">
        <include refid="selectTblUploadfileVo"/>
        where id = #{id}
    </select>
    <select id="selectTblUploadfileByIds" parameterType="Long" resultMap="TblUploadfileResult">
        <include refid="selectTblUploadfileVo"/>
        where id in (
        <foreach collection="array" item="item" separator=",">
            #{item}
        </foreach>
        ) and deleted = '0'
    </select>
    <select id="selectTblUploadfileByAssetIds" parameterType="Long" resultMap="TblUploadfileResult">
        <include refid="selectTblUploadfileVo"/>
        where asset_id in (
            <foreach collection="array" item="item" separator=",">
                #{item}
            </foreach>
        ) and deleted = '0'
    </select>
        
    <insert id="insertTblUploadfile" parameterType="TblUploadfile" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_uploadfile
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="exType != null">ex_type,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="md5 != null">MD5,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="exType != null">#{exType},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="md5 != null">#{md5},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>
    <insert id="insertTblUploadfiles" parameterType="List" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_uploadfile
        (
            file_name,
            file_type,
            ex_type,
            file_url,
            MD5,
            asset_id,
            user_name,
            user_id,
            dept_id,
            create_time,
            update_time,
            create_by,
            update_by,
            remark
        )
        <foreach collection="list" item="item" separator="," open="values ">
            (
            #{item.fileName},
            #{item.fileType},
            #{item.exType},
            #{item.fileUrl},
            #{item.md5},
            #{item.assetId},
            #{item.userName},
            #{item.userId},
            #{item.deptId},
            #{item.createTime},
            #{item.updateTime},
            #{item.createBy},
            #{item.updateBy},
            #{item.remark}
            )
        </foreach>

    </insert>

    <update id="updateTblUploadfile" parameterType="TblUploadfile">
        update tbl_uploadfile
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="exType != null">ex_type = #{exType},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="md5 != null">MD5 = #{md5},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="setUpFileAssetRel" parameterType="string">
        update tbl_uploadfile set asset_id = #{assetId}
        where id = #{fileId} and asset_id is null
    </update>
    <update id="setUpFilesAssetRels" parameterType="string">
        update tbl_uploadfile set asset_id = #{assetId}
        where asset_id is null and id in
        <foreach collection="fileIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <delete id="deleteTblUploadfileById" parameterType="Long">
        delete from tbl_uploadfile where id = #{id}
    </delete>

    <delete id="deleteTblUploadfileByIds" parameterType="String">
        delete from tbl_uploadfile where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="updataTblUploadfileDeleted" parameterType="TblUploadfile">
        update tbl_uploadfile set
            deleted = #{deleted}
        where asset_id = #{assetId} and file_name = #{fileName}
    </update>
    <update id="deleteFilesAssetRelByFileIds" parameterType="string">
        update tbl_uploadfile set asset_id = null and deleted = 1
        where id in
        <foreach collection="fileIds" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </update>
</mapper>