use aqsoc;

-- 1. 插入攻击方向字典类型
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES
('攻击方向', 'attack_direction', '0', 'admin', NOW(), '威胁告警攻击方向字典');

-- 2. 插入攻击方向字典数据
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '内对内', '1', 'attack_direction', '', 'primary', 'N', '0', 'admin', NOW(), '内网到内网的攻击'),
(2, '内对外', '2', 'attack_direction', '', 'success', 'N', '0', 'admin', NOW(), '内网到外网的攻击'),
(3, '外对内', '3', 'attack_direction', '', 'warning', 'N', '0', 'admin', NOW(), '外网到内网的攻击'),
(4, '未知', '4', 'attack_direction', '', 'info', 'Y', '0', 'admin', NOW(), '无法判断攻击方向');
