{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1756369457274}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7bWFwR2V0dGVycywgbWFwU3RhdGV9IGZyb20gInZ1ZXgiOwppbXBvcnQgTG9nbyBmcm9tICIuL0xvZ28iOwppbXBvcnQgU2lkZWJhckl0ZW0gZnJvbSAiLi9TaWRlYmFySXRlbSI7CmltcG9ydCB2YXJpYWJsZXMgZnJvbSAiQC9hc3NldHMvc3R5bGVzL3ZhcmlhYmxlcy5zY3NzIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7U2lkZWJhckl0ZW0sIExvZ299LAogIG1vdW50ZWQoKSB7fSwKICBjb21wdXRlZDogewogICAgLi4ubWFwU3RhdGUoWyJzZXR0aW5ncyJdKSwKICAgIC4uLm1hcEdldHRlcnMoWyJzaWRlYmFyUm91dGVycyIsICJzaWRlYmFyIl0pLAogICAgYWN0aXZlTWVudSgpIHsKICAgICAgY29uc3Qgcm91dGUgPSB0aGlzLiRyb3V0ZTsKICAgICAgY29uc3Qge21ldGEsIHBhdGh9ID0gcm91dGU7CiAgICAgIC8vIGlmIHNldCBwYXRoLCB0aGUgc2lkZWJhciB3aWxsIGhpZ2hsaWdodCB0aGUgcGF0aCB5b3Ugc2V0CiAgICAgIGlmIChtZXRhLmFjdGl2ZU1lbnUpIHsKICAgICAgICByZXR1cm4gbWV0YS5hY3RpdmVNZW51OwogICAgICB9CiAgICAgIGxldCBhY3RpdmVNZW51CiAgICAgIGNvbnN0IHBhcnRzID0gcGF0aC5zcGxpdCgnLycpOwogICAgICBpZiAocGFydHMubGVuZ3RoID09PSA0ICYmIHBhcnRzWzFdICYmIHBhcnRzWzJdICYmIHBhcnRzWzNdKSB7CiAgICAgICAgYWN0aXZlTWVudSA9IGAvJHtwYXJ0c1sxXX0vJHtwYXJ0c1syXX1gOwogICAgICB9ZWxzZXsKICAgICAgICBhY3RpdmVNZW51ID0gIHBhdGg7CiAgICAgIH0KICAgICAgcmV0dXJuIGFjdGl2ZU1lbnUKICAgIH0sCiAgICBzaG93TG9nbygpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnNpZGViYXJMb2dvOwogICAgfSwKICAgIHZhcmlhYmxlcygpIHsKICAgICAgcmV0dXJuIHZhcmlhYmxlczsKICAgIH0sCiAgICAvLyDkvqfovrnmoI/mipjlj6DnirbmgIEKICAgIGlzQ29sbGFwc2UoKSB7CiAgICAgIHJldHVybiAhdGhpcy5zaWRlYmFyLm9wZW5lZDsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\n  <div :class=\"{'has-logo':showLogo}\">\n<!--       :style=\"{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground,-->\n<!--           background: settings.sideTheme === 'theme-dark' ? '' : variables.menuGradient }\">-->\n<!--    <logo v-if=\"showLogo\" :collapse=\"isCollapse\"/>-->\n    <el-scrollbar :class=\"settings.sideTheme\" wrap-class=\"scrollbar-wrapper\">\n      <!--          :background-color=\"settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground\"-->\n      <el-menu\n        :default-active=\"activeMenu\"\n        :collapse=\"isCollapse\"\n        :unique-opened=\"true\"\n        :collapse-transition=\"false\"\n        mode=\"vertical\"\n      >\n        <sidebar-item\n          v-for=\"(route, index) in sidebarRouters\"\n          :key=\"route.path  + index\"\n          :item=\"route\"\n          :base-path=\"route.path\"\n        />\n      </el-menu>\n    </el-scrollbar>\n    <div class=\"sidebar-bottom\" v-if=\"!isCollapse\">\n      <div class=\"text\">\n        <p><span>当前版本：2.5.3</span><span style=\"display: none;\">Date: 2025-08-12 17:24 SvnVersion: $Revision: 3115 $</span></p>\n        <p><span>技术支持：安盟科技</span></p>\n        <p><span>联系方式：4001-110-9963</span></p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {mapGetters, mapState} from \"vuex\";\nimport Logo from \"./Logo\";\nimport SidebarItem from \"./SidebarItem\";\nimport variables from \"@/assets/styles/variables.scss\";\n\nexport default {\n  components: {SidebarItem, Logo},\n  mounted() {},\n  computed: {\n    ...mapState([\"settings\"]),\n    ...mapGetters([\"sidebarRouters\", \"sidebar\"]),\n    activeMenu() {\n      const route = this.$route;\n      const {meta, path} = route;\n      // if set path, the sidebar will highlight the path you set\n      if (meta.activeMenu) {\n        return meta.activeMenu;\n      }\n      let activeMenu\n      const parts = path.split('/');\n      if (parts.length === 4 && parts[1] && parts[2] && parts[3]) {\n        activeMenu = `/${parts[1]}/${parts[2]}`;\n      }else{\n        activeMenu =  path;\n      }\n      return activeMenu\n    },\n    showLogo() {\n      return this.$store.state.settings.sidebarLogo;\n    },\n    variables() {\n      return variables;\n    },\n    // 侧边栏折叠状态\n    isCollapse() {\n      return !this.sidebar.opened;\n    }\n  }\n};\n</script>\n<style scoped lang=\"scss\">\n.el-menu {\n  background-color: unset;\n  ::v-deep .el-submenu__title i {\n    font-weight: 700;\n  }\n}\n.sidebar-bottom {\n  width: 190px;\n  height: 72px;\n  position: fixed;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  border-top: 1px solid #7f7f7f24;\n  .text{\n    position: absolute;\n    text-align: center;\n    padding: 2px 2px 2px 2px;\n    box-sizing: border-box;\n    width: 100%;\n    height: 54px;\n    color: rgba(127, 127, 127, 0.4980392156862745);\n    font-size: 12px;\n    word-wrap: break-word;\n    text-transform: none;\n  }\n}\n</style>\n"]}]}