{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\form.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\hw-work\\form.vue", "mtime": 1756369456012}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_crud", "require", "_vuex", "_userSelect", "_interopRequireDefault", "_deptSelect", "components", "DeptSelect", "UserSelect", "props", "data", "dataFormSubmitType", "continueBtnLoading", "index", "prevDis", "nextDis", "allList", "visible", "loading", "btnLoading", "currTableConf", "addTableConf", "ableAll", "tableRows", "currVmodel", "dataForm", "id", "templateId", "name", "year", "userId", "hwStart", "hwEnd", "supportOrgs", "supportUsers", "dataJson", "rules", "required", "message", "trigger", "deptId", "typeOptions", "typeProps", "childIndex", "yearList", "isEdit", "isCopy", "computed", "_objectSpread2", "default", "mapGetters", "watch", "created", "dataAll", "currentYear", "Date", "getFullYear", "mounted", "methods", "gettypeOptions", "init", "isDetail", "_this", "$nextTick", "$refs", "resetFields", "getInfo", "then", "res", "dataInfo", "clearData", "dataFormSubmit", "type", "_this2", "validate", "valid", "request", "_this3", "_data", "dataList", "copyWork", "$message", "msg", "duration", "onClose", "$emit", "addData", "catch", "userIdData", "split", "uniqueUserIdData", "_toConsumableArray2", "Set", "join", "JSON", "parse", "stringify", "_dataAll"], "sources": ["src/views/aqsoc/hw-work/form.vue"], "sourcesContent": ["<template>\n  <el-dialog :title=\"!dataForm.id ? '新增HW事务' :isCopy?'复制HW事务':'编辑HW事务'\"\n             :close-on-click-modal=\"false\" append-to-body\n             :visible.sync=\"visible\" class=\"JNPF-dialog JNPF-dialog_center\" lock-scroll\n             width=\"800px\">\n    <el-row :gutter=\"16\" class=\"\">\n      <el-form ref=\"elForm\" :model=\"dataForm\" :rules=\"rules\" size=\"small\" label-width=\"150px\" label-position=\"right\">\n        <template v-if=\"!loading\">\n          <el-col :span=\"12\">\n            <jnpf-form-tip-item label=\"年度\" prop=\"year\">\n              <el-select v-model=\"dataForm.year\" placeholder=\"请选择\">\n                <el-option\n                  v-for=\"item in yearList\"\n                  :key=\"item\"\n                  :label=\"item\"\n                  :value=\"item\">\n                </el-option>\n              </el-select>\n            </jnpf-form-tip-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <jnpf-form-tip-item label=\"所属单位\" prop=\"deptId\">\n              <dept-select\n                v-model=\"dataForm.deptId\"\n                is-current\n              />\n            </jnpf-form-tip-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <jnpf-form-tip-item label=\"联络人\" prop=\"userId\">\n              <user-select v-model=\"dataForm.userId\" placeholder=\"请选择\" multiple/>\n            </jnpf-form-tip-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <jnpf-form-tip-item label=\"HW开始时间\" prop=\"hwStart\">\n              <el-date-picker v-model=\"dataForm.hwStart\" type=\"datetime\"  style=\"width: 192px;\"value-format=\"yyyy-MM-dd HH:mm:ss\"\n                              placeholder=\"请选择HW开始时间\" clearable>\n              </el-date-picker>\n            </jnpf-form-tip-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <jnpf-form-tip-item label=\"HW结束时间\" prop=\"hwEnd\">\n              <el-date-picker v-model=\"dataForm.hwEnd\" type=\"datetime\" style=\"width: 192px;\" value-format=\"yyyy-MM-dd HH:mm:ss\"\n                              placeholder=\"请选择HW结束时间\" clearable>\n              </el-date-picker>\n            </jnpf-form-tip-item>\n          </el-col>\n        </template>\n      </el-form>\n    </el-row>\n    <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"visible = false\"> 取 消</el-button>\n          <el-button type=\"primary\" @click=\"dataFormSubmit()\" :loading=\"btnLoading\"> 确 定</el-button>\n    </span>\n  </el-dialog>\n</template>\n<script>\nimport {addData, getInfo,copyWork} from '@/api/aqsoc/work-hw/crud'\nimport {mapGetters} from \"vuex\";\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nimport DeptSelect from \"@/views/components/select/deptSelect.vue\";\n\nexport default {\n  components: {DeptSelect, UserSelect},\n  props: [],\n  data() {\n    return {\n      dataFormSubmitType: 0,\n      continueBtnLoading: false,\n      index: 0,\n      prevDis: false,\n      nextDis: false,\n      allList: [],\n      visible: false,\n      loading: false,\n      btnLoading: false,\n      currTableConf: {},\n      addTableConf: {},\n      //可选范围默认值\n      ableAll: {},\n      tableRows: {},\n      currVmodel: \"\",\n      dataForm: {\n        id: '',\n        templateId: '',\n        name: '',\n        year: '',\n        userId: '',\n        hwStart: '',\n        hwEnd: '',\n        supportOrgs: '',\n        supportUsers: '',\n        dataJson: '',\n      },\n      rules:\n        {\n          year: [{required: true, message: `年度不能为空`, trigger: 'change'}],\n          //userId: [{required: true, message: `责任人不能为空`, trigger: 'change'}],\n          hwStart: [{required: true, message: `HW开始时间不能为空`, trigger: 'change'}],\n          hwEnd: [{required: true, message: `HW结束时间不能为空`, trigger: 'change'}],\n          deptId: [{required: true, message: `所属单位不能为空`, trigger: 'change'}],\n        },\n      typeOptions: [],\n      typeProps: {\"label\": \"fullName\", \"value\": \"enCode\"},\n      childIndex: -1,\n      yearList:[],\n      isEdit: false,\n      isCopy: false,\n    }\n  },\n  computed: {\n    ...mapGetters(['userInfo']),\n  },\n  watch: {},\n  created() {\n    this.dataAll()\n    // 获取当前年份\n    const currentYear = new Date().getFullYear();\n    this.yearList= [currentYear - 1, currentYear, currentYear + 1];\n  },\n  mounted() {\n  },\n  methods: {\n    dataAll() {\n      this.gettypeOptions();\n    },\n    gettypeOptions() {\n\n    },\n    init(id, isDetail, allList,isCopy) {\n      this.isCopy = isCopy;\n      this.dataForm.id = id || 0;\n      this.visible = true;\n      this.$nextTick(() => {\n        this.$refs['elForm'].resetFields();\n        if (this.dataForm.id) {\n          this.loading = true\n          getInfo(this.dataForm.id).then(res => {\n            this.dataInfo(res.data)\n            this.loading = false\n          });\n        } else {\n          this.clearData()\n        }\n      });\n    },\n    // 表单提交\n    dataFormSubmit(type) {\n      this.dataFormSubmitType = type ? type : 0\n      this.$refs['elForm'].validate((valid) => {\n        if (valid) {\n          this.request()\n        }\n      })\n    },\n    request() {\n      this.btnLoading = true\n      let _data = this.dataList()\n      if(this.isCopy){\n        copyWork(_data).then(res => {\n          this.$message({\n            message: res.msg,\n            type: 'success',\n            duration: 1000,\n            onClose: () => {\n              this.visible = false\n              this.btnLoading = false\n              this.$emit('refresh', true)\n            }\n          })\n        })\n        return;\n      }\n      if (!this.dataForm.id) {\n        addData(_data).then((res) => {\n          this.$message({\n            message: res.msg,\n            type: 'success',\n            duration: 1000,\n            onClose: () => {\n              this.visible = false\n              this.btnLoading = false\n              this.$emit('refresh', true)\n            }\n          })\n        }).catch(() => {\n          this.btnLoading = false\n        })\n      } else {\n        addData(_data).then((res) => {\n          this.$message({\n            message: res.msg,\n            type: 'success',\n            duration: 1000,\n            onClose: () => {\n              this.visible = false\n              this.btnLoading = false\n              this.$emit('refresh', true)\n            }\n          })\n        }).catch(() => {\n          this.btnLoading = false\n          this.continueBtnLoading = false\n        })\n      }\n    },\n    clearData() {\n      this.dataForm = {\n        id: '',\n        templateId: '',\n        name: '',\n        year: '',\n        userId: '',\n        hwStart: '',\n        hwEnd: '',\n        supportOrgs: '',\n        supportUsers: '',\n        dataJson: '',\n      }\n    },\n    dataList() {\n      let userIdData = this.dataForm.userId.split(','); // 将字符串按逗号分割成数组\n      let uniqueUserIdData = [...new Set(userIdData)]; // 使用 Set 去重\n      this.dataForm.userId = uniqueUserIdData.join(','); // 将去重后的数组转换为字符串\n      var _data = JSON.parse(JSON.stringify(this.dataForm));\n      return _data;\n    },\n    dataInfo(dataAll) {\n      let _dataAll = dataAll\n      this.dataForm = _dataAll\n      this.isEdit = true\n      this.dataAll()\n      this.childIndex = -1\n    },\n  },\n}\n\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;AAyDA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,WAAA,GAAAD,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,UAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,kBAAA;MACAC,kBAAA;MACAC,KAAA;MACAC,OAAA;MACAC,OAAA;MACAC,OAAA;MACAC,OAAA;MACAC,OAAA;MACAC,UAAA;MACAC,aAAA;MACAC,YAAA;MACA;MACAC,OAAA;MACAC,SAAA;MACAC,UAAA;MACAC,QAAA;QACAC,EAAA;QACAC,UAAA;QACAC,IAAA;QACAC,IAAA;QACAC,MAAA;QACAC,OAAA;QACAC,KAAA;QACAC,WAAA;QACAC,YAAA;QACAC,QAAA;MACA;MACAC,KAAA,EACA;QACAP,IAAA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACA;QACAR,OAAA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAP,KAAA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAC,MAAA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAE,WAAA;MACAC,SAAA;QAAA;QAAA;MAAA;MACAC,UAAA;MACAC,QAAA;MACAC,MAAA;MACAC,MAAA;IACA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA,gBACA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA;IACA,IAAAC,WAAA,OAAAC,IAAA,GAAAC,WAAA;IACA,KAAAZ,QAAA,IAAAU,WAAA,MAAAA,WAAA,EAAAA,WAAA;EACA;EACAG,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACAL,OAAA,WAAAA,QAAA;MACA,KAAAM,cAAA;IACA;IACAA,cAAA,WAAAA,eAAA,GAEA;IACAC,IAAA,WAAAA,KAAAlC,EAAA,EAAAmC,QAAA,EAAA7C,OAAA,EAAA8B,MAAA;MAAA,IAAAgB,KAAA;MACA,KAAAhB,MAAA,GAAAA,MAAA;MACA,KAAArB,QAAA,CAAAC,EAAA,GAAAA,EAAA;MACA,KAAAT,OAAA;MACA,KAAA8C,SAAA;QACAD,KAAA,CAAAE,KAAA,WAAAC,WAAA;QACA,IAAAH,KAAA,CAAArC,QAAA,CAAAC,EAAA;UACAoC,KAAA,CAAA5C,OAAA;UACA,IAAAgD,aAAA,EAAAJ,KAAA,CAAArC,QAAA,CAAAC,EAAA,EAAAyC,IAAA,WAAAC,GAAA;YACAN,KAAA,CAAAO,QAAA,CAAAD,GAAA,CAAA1D,IAAA;YACAoD,KAAA,CAAA5C,OAAA;UACA;QACA;UACA4C,KAAA,CAAAQ,SAAA;QACA;MACA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAA9D,kBAAA,GAAA6D,IAAA,GAAAA,IAAA;MACA,KAAAR,KAAA,WAAAU,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAAG,OAAA;QACA;MACA;IACA;IACAA,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MACA,KAAA1D,UAAA;MACA,IAAA2D,KAAA,QAAAC,QAAA;MACA,SAAAjC,MAAA;QACA,IAAAkC,cAAA,EAAAF,KAAA,EAAAX,IAAA,WAAAC,GAAA;UACAS,MAAA,CAAAI,QAAA;YACA3C,OAAA,EAAA8B,GAAA,CAAAc,GAAA;YACAV,IAAA;YACAW,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAP,MAAA,CAAA5D,OAAA;cACA4D,MAAA,CAAA1D,UAAA;cACA0D,MAAA,CAAAQ,KAAA;YACA;UACA;QACA;QACA;MACA;MACA,UAAA5D,QAAA,CAAAC,EAAA;QACA,IAAA4D,aAAA,EAAAR,KAAA,EAAAX,IAAA,WAAAC,GAAA;UACAS,MAAA,CAAAI,QAAA;YACA3C,OAAA,EAAA8B,GAAA,CAAAc,GAAA;YACAV,IAAA;YACAW,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAP,MAAA,CAAA5D,OAAA;cACA4D,MAAA,CAAA1D,UAAA;cACA0D,MAAA,CAAAQ,KAAA;YACA;UACA;QACA,GAAAE,KAAA;UACAV,MAAA,CAAA1D,UAAA;QACA;MACA;QACA,IAAAmE,aAAA,EAAAR,KAAA,EAAAX,IAAA,WAAAC,GAAA;UACAS,MAAA,CAAAI,QAAA;YACA3C,OAAA,EAAA8B,GAAA,CAAAc,GAAA;YACAV,IAAA;YACAW,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAP,MAAA,CAAA5D,OAAA;cACA4D,MAAA,CAAA1D,UAAA;cACA0D,MAAA,CAAAQ,KAAA;YACA;UACA;QACA,GAAAE,KAAA;UACAV,MAAA,CAAA1D,UAAA;UACA0D,MAAA,CAAAjE,kBAAA;QACA;MACA;IACA;IACA0D,SAAA,WAAAA,UAAA;MACA,KAAA7C,QAAA;QACAC,EAAA;QACAC,UAAA;QACAC,IAAA;QACAC,IAAA;QACAC,MAAA;QACAC,OAAA;QACAC,KAAA;QACAC,WAAA;QACAC,YAAA;QACAC,QAAA;MACA;IACA;IACA4C,QAAA,WAAAA,SAAA;MACA,IAAAS,UAAA,QAAA/D,QAAA,CAAAK,MAAA,CAAA2D,KAAA;MACA,IAAAC,gBAAA,OAAAC,mBAAA,CAAA1C,OAAA,MAAA2C,GAAA,CAAAJ,UAAA;MACA,KAAA/D,QAAA,CAAAK,MAAA,GAAA4D,gBAAA,CAAAG,IAAA;MACA,IAAAf,KAAA,GAAAgB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAvE,QAAA;MACA,OAAAqD,KAAA;IACA;IACAT,QAAA,WAAAA,SAAAhB,OAAA;MACA,IAAA4C,QAAA,GAAA5C,OAAA;MACA,KAAA5B,QAAA,GAAAwE,QAAA;MACA,KAAApD,MAAA;MACA,KAAAQ,OAAA;MACA,KAAAV,UAAA;IACA;EACA;AACA", "ignoreList": []}]}