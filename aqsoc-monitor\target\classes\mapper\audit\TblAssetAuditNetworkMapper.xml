<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.audit.mapper.TblAssetAuditNetworkMapper">
    
    <resultMap type="TblAssetAuditNetwork" id="TblAssetAuditNetworkResult">
        <result property="id"    column="id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="networkIp"    column="network_ip"    />
        <result property="networkPost"    column="network_post"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <sql id="selectTblAssetAuditNetworkVo">
        select id, asset_id, network_ip, network_post, remark, create_time, create_by, update_time, update_by, user_id, dept_id from tbl_asset_audit_network
    </sql>

    <select id="selectTblAssetAuditNetworkList" parameterType="TblAssetAuditNetwork" resultMap="TblAssetAuditNetworkResult">
        <include refid="selectTblAssetAuditNetworkVo"/>
        <where>  
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="networkIp != null  and networkIp != ''"> and network_ip = #{networkIp}</if>
            <if test="networkPost != null  and networkPost != ''"> and network_post = #{networkPost}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
        </where>
    </select>
    
    <select id="selectTblAssetAuditNetworkById" parameterType="Long" resultMap="TblAssetAuditNetworkResult">
        <include refid="selectTblAssetAuditNetworkVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblAssetAuditNetwork" parameterType="TblAssetAuditNetwork">
        insert into tbl_asset_audit_network
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="networkIp != null">network_ip,</if>
            <if test="networkPost != null">network_post,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="networkIp != null">#{networkIp},</if>
            <if test="networkPost != null">#{networkPost},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>

    <update id="updateTblAssetAuditNetwork" parameterType="TblAssetAuditNetwork">
        update tbl_asset_audit_network
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="networkIp != null">network_ip = #{networkIp},</if>
            <if test="networkPost != null">network_post = #{networkPost},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblAssetAuditNetworkById" parameterType="Long">
        delete from tbl_asset_audit_network where id = #{id}
    </delete>

    <delete id="deleteTblAssetAuditNetworkByIds" parameterType="String">
        delete from tbl_asset_audit_network where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>