<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblMapperMapper">
    
    <resultMap type="TblMapper" id="TblMapperResult">
        <result property="moduleId"    column="module_id"    />
        <result property="moduleName"    column="module_name"    />
        <result property="moduleVersion"    column="module_version"    />
        <result property="moduleDesc"    column="module_desc"    />
        <result property="applicationId"    column="application_id"    />
        <result property="applicationName"    column="application_name"    />
        <result property="applicationVersion"    column="application_version"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <sql id="selectTblMapperVo">
        select module_id, module_name, module_version, module_desc, application_id, application_name, application_version from tbl_mapper
    </sql>

    <select id="selectTblMapperList" parameterType="TblMapper" resultMap="TblMapperResult">
        <include refid="selectTblMapperVo"/>
        <where>  
            <if test="moduleName != null  and moduleName != ''"> and module_name like concat('%', #{moduleName}, '%')</if>
            <if test="moduleVersion != null  and moduleVersion != ''"> and module_version = #{moduleVersion}</if>
            <if test="moduleDesc != null  and moduleDesc != ''"> and module_desc = #{moduleDesc}</if>
            <if test="applicationId != null "> and application_id = #{applicationId}</if>
            <if test="applicationName != null  and applicationName != ''"> and application_name like concat('%', #{applicationName}, '%')</if>
            <if test="applicationVersion != null  and applicationVersion != ''"> and application_version = #{applicationVersion}</if>
        </where>
    </select>
    
    <select id="selectTblMapperBymoduleId" parameterType="Long" resultMap="TblMapperResult">
        <include refid="selectTblMapperVo"/>
        where module_id = #{moduleId}
    </select>
    <select id="selectTblMapperBymoduleIds" parameterType="Long" resultMap="TblMapperResult">
        <include refid="selectTblMapperVo"/>
        where module_id in
        <foreach collection="moduleIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectTblMapperByApplicationId" parameterType="Long" resultMap="TblMapperResult">
        <include refid="selectTblMapperVo"/>
        where application_id = #{applicationId}
    </select>

    <select id="selectTblMapperByAssetId" parameterType="Long" resultMap="TblMapperResult">
        select module_id, module_name, module_desc from tbl_mapper where application_id=#{assetId}
    </select>


    <insert id="insertTblMapper" parameterType="TblMapper">
        insert into tbl_mapper
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="moduleId != null">module_id,</if>
            <if test="moduleName != null and moduleName != ''">module_name,</if>
            <if test="moduleVersion != null">module_version,</if>
            <if test="moduleDesc != null">module_desc,</if>
            <if test="applicationId != null">application_id,</if>
            <if test="applicationName != null">application_name,</if>
            <if test="applicationVersion != null">application_version,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="moduleId != null">#{moduleId},</if>
            <if test="moduleName != null and moduleName != ''">#{moduleName},</if>
            <if test="moduleVersion != null">#{moduleVersion},</if>
            <if test="moduleDesc != null">#{moduleDesc},</if>
            <if test="applicationId != null">#{applicationId},</if>
            <if test="applicationName != null">#{applicationName},</if>
            <if test="applicationVersion != null">#{applicationVersion},</if>
         </trim>
    </insert>

    <update id="updateTblMapper" parameterType="TblMapper">
        update tbl_mapper
        <trim prefix="SET" suffixOverrides=",">
            <if test="moduleName != null and moduleName != ''">module_name = #{moduleName},</if>
            <if test="moduleVersion != null">module_version = #{moduleVersion},</if>
            <if test="moduleDesc != null">module_desc = #{moduleDesc},</if>
            <if test="applicationId != null">application_id = #{applicationId},</if>
            <if test="applicationName != null">application_name = #{applicationName},</if>
            <if test="applicationVersion != null">application_version = #{applicationVersion},</if>
        </trim>
        where module_id = #{moduleId}
    </update>

    <update id="updateTblMapperByApplicationId" parameterType="TblMapper">
        update tbl_mapper
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationName != null">application_name = #{applicationName},</if>
            <if test="applicationVersion != null">application_version = #{applicationVersion},</if>
        </trim>
        where application_id = #{applicationId}
    </update>

    <delete id="deleteTblMapperBymoduleId" parameterType="Long">
        delete from tbl_mapper where module_id = #{moduleId}
    </delete>

    <delete id="deleteTblMapperBymoduleIds" parameterType="String">
        delete from tbl_mapper where module_id in 
        <foreach item="moduleId" collection="array" open="(" separator="," close=")">
            #{moduleId}
        </foreach>
    </delete>

    <delete id="deleteTblMapperByApplicationId" parameterType="Long">
        delete from tbl_mapper where application_id = #{applicationId}
    </delete>

    <delete id="deleteTblMapperByApplicationIds" parameterType="String">
        delete from tbl_mapper where application_id in
        <foreach item="applicationId" collection="array" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
    </delete>

    <select id="selectCountNum" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(0) from tbl_mapper
        <where>
            <if test="assetId != null "> application_id = #{applicationId}</if>
        </where>
    </select>


    <select id="selectMapperStatisticsList" parameterType="TblMapper" resultMap="TblMapperResult">
        SELECT m.module_id,m.module_name,m.application_id,m.application_name,m.module_desc,d.dept_id
        FROM tbl_mapper m
        LEFT JOIN tbl_business_application b ON m.application_id = b.asset_id
        LEFT JOIN sys_dept d ON b.dept_id = d.dept_id
        <where>
            <if test="moduleName != null  and moduleName != ''"> and m.module_name like concat('%', #{moduleName}, '%')</if>
            <if test="moduleVersion != null  and moduleVersion != ''"> and module_version = #{moduleVersion}</if>
            <if test="moduleDesc != null  and moduleDesc != ''"> and m.module_desc like concat('%', #{moduleDesc}, '%')</if>
            <if test="applicationId != null "> and m.application_id = #{applicationId}</if>
            <if test="applicationName != null  and applicationName != ''"> and m.application_name like concat('%', #{applicationName}, '%')</if>
            <if test="applicationVersion != null  and applicationVersion != ''"> and m.application_version = #{applicationVersion}</if>
            <if test="deptId != null and deptId != 0">
                and (b.dept_id = #{deptId} or b.dept_id in ( select t.dept_id from sys_dept t where
                find_in_set(#{deptId}, ancestors) ))
            </if>
            order by m.module_id desc
        </where>
    </select>

</mapper>