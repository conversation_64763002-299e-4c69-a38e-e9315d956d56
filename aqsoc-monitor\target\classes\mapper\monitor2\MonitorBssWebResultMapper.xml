<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.MonitorBssWebResultMapper">

    <resultMap type="MonitorBssWebResult" id="MonitorBssWebResultResult">
        <result property="id"    column="id"    />
        <result property="planid"    column="planId"    />
        <result property="xprocessid"    column="xprocessId"    />
        <result property="url"    column="url"    />
        <result property="title"    column="title"    />
        <result property="rawRequest"    column="raw_request"    />
        <result property="rawResponse"    column="raw_response"    />
        <result property="cert"    column="cert"    />
        <result property="waf"    column="waf"    />
        <result property="cdn"    column="cdn"    />
        <result property="programmingLanguage"    column="programming_language"    />
        <result property="framework"    column="framework"    />
    </resultMap>

    <sql id="selectMonitorBssWebResultVo">
        select id, planId, xprocessId, url, title, raw_request, raw_response, cert, waf, cdn, programming_language, framework from monitor_bss_web_result
    </sql>

    <select id="selectMonitorBssWebResultList" parameterType="MonitorBssWebResult" resultMap="MonitorBssWebResultResult">
        <include refid="selectMonitorBssWebResultVo"/>
        <where>
            <if test="planid != null "> and planId = #{planid}</if>
            <if test="xprocessid != null "> and xprocessId = #{xprocessid}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="rawRequest != null  and rawRequest != ''"> and raw_request = #{rawRequest}</if>
            <if test="rawResponse != null  and rawResponse != ''"> and raw_response = #{rawResponse}</if>
            <if test="cert != null  and cert != ''"> and cert = #{cert}</if>
            <if test="waf != null  and waf != ''"> and waf = #{waf}</if>
            <if test="cdn != null  and cdn != ''"> and cdn = #{cdn}</if>
            <if test="programmingLanguage != null  and programmingLanguage != ''"> and programming_language = #{programmingLanguage}</if>
            <if test="framework != null  and framework != ''"> and framework = #{framework}</if>
        </where>
    </select>

    <select id="selectMonitorBssWebResultById" parameterType="Long" resultMap="MonitorBssWebResultResult">
        <include refid="selectMonitorBssWebResultVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorBssWebResultByIds" parameterType="Long" resultMap="MonitorBssWebResultResult">
        <include refid="selectMonitorBssWebResultVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMonitorBssWebResult" parameterType="MonitorBssWebResult" useGeneratedKeys="true" keyProperty="id">
        insert into monitor_bss_web_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planid != null">planId,</if>
            <if test="xprocessid != null">xprocessId,</if>
            <if test="url != null">url,</if>
            <if test="title != null">title,</if>
            <if test="rawRequest != null">raw_request,</if>
            <if test="rawResponse != null">raw_response,</if>
            <if test="cert != null">cert,</if>
            <if test="waf != null">waf,</if>
            <if test="cdn != null">cdn,</if>
            <if test="programmingLanguage != null">programming_language,</if>
            <if test="framework != null">framework,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planid != null">#{planid},</if>
            <if test="xprocessid != null">#{xprocessid},</if>
            <if test="url != null">#{url},</if>
            <if test="title != null">#{title},</if>
            <if test="rawRequest != null">#{rawRequest},</if>
            <if test="rawResponse != null">#{rawResponse},</if>
            <if test="cert != null">#{cert},</if>
            <if test="waf != null">#{waf},</if>
            <if test="cdn != null">#{cdn},</if>
            <if test="programmingLanguage != null">#{programmingLanguage},</if>
            <if test="framework != null">#{framework},</if>
        </trim>
    </insert>

    <update id="updateMonitorBssWebResult" parameterType="MonitorBssWebResult">
        update monitor_bss_web_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="planid != null">planId = #{planid},</if>
            <if test="xprocessid != null">xprocessId = #{xprocessid},</if>
            <if test="url != null">url = #{url},</if>
            <if test="title != null">title = #{title},</if>
            <if test="rawRequest != null">raw_request = #{rawRequest},</if>
            <if test="rawResponse != null">raw_response = #{rawResponse},</if>
            <if test="cert != null">cert = #{cert},</if>
            <if test="waf != null">waf = #{waf},</if>
            <if test="cdn != null">cdn = #{cdn},</if>
            <if test="programmingLanguage != null">programming_language = #{programmingLanguage},</if>
            <if test="framework != null">framework = #{framework},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorBssWebResultById" parameterType="Long">
        delete from monitor_bss_web_result where id = #{id}
    </delete>

    <delete id="deleteMonitorBssWebResultByIds" parameterType="String">
        delete from monitor_bss_web_result where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <resultMap id="webResult" type="com.ruoyi.monitor2.domain.MonitorBssWebResult">
        <result property="planid"  column="planId"  />
        <result property="xprocessid"  column="xprocessId"  />
        <result property="url"  column="url"  />
        <result property="title"  column="title"  />
        <collection property="gapList" javaType="ArrayList" column="{planid=planId,xprocessid=xprocessId,url=url }" select="selectWebSunList" />
    </resultMap>

    <select id="getWebList" parameterType="com.ruoyi.monitor2.domain.MonitorBssWebResult" resultMap="webResult">
        SELECT id, planId, xprocessId, url, title FROM monitor_bss_web_result
        <where>
            <if test="planid != null">planId = #{planid}</if>
            <if test="xprocessid != null">AND xprocessId = #{xprocessid}</if>
        </where>
    </select>

    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssVulnResult" id="vulnResult">
        <result property="xprocessId" column="xprocess_id" />
        <result property="planId" column="plan_id" />
        <result property="countNum" column="countNum"/>
    </resultMap>

    <select id="selectWebSunList" parameterType="map" resultMap="vulnResult">
        SELECT plan_id, xprocess_id, web_url, severity, count(0) as countNum
            FROM monitor_bss_vuln_result
        <where>
            <if test="planid != null">plan_id = #{planid}</if>
            <if test="xprocessid != null">and xprocess_id = #{xprocessid}</if>
            <if test="url != null">and web_url = #{url}</if>
        </where>
        GROUP BY severity
    </select>
</mapper>