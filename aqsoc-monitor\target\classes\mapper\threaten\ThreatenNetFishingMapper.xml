<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenNetFishingMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenNetFishingListVO">
        select a.* from threaten_net_fishing a left join threaten_alarm_origin b on a.id = b.origin_id and (b.threaten_type = 'threaten_net_fishing' or b.threaten_type = '网络钓鱼')
        <where>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.baseId!=null">
                and a.base_id = #{query.baseId}
            </if>
            <if test="query.isEncry!=null">
                and a.is_encry = #{query.isEncry}
            </if>
            <if test="query.attackNum!=null">
                and a.attack_num = #{query.attackNum}
            </if>
            <if test="query.attackDirection!=null">
                and a.attack_direction = #{query.attackDirection}
            </if>
            <if test="query.attackStage!=null">
                and a.attack_stage = #{query.attackStage}
            </if>
            <if test="query.attackResult!=null">
                and a.attack_result = #{query.attackResult}
            </if>
            <if test="query.handleAction!=null">
                and a.handle_action = #{query.handleAction}
            </if>
            <if test="query.organization!=null and query.organization!=''">
                and a.organization like concat('%', #{query.organization}, '%')
            </if>
            <if test="query.domain!=null and query.domain!=''">
                and a.domain like concat('%', #{query.domain}, '%')
            </if>
            <if test="query.email!=null and query.email!=''">
                and a.email like concat('%', #{query.email}, '%')
            </if>
            <if test="query.emailContent!=null and query.emailContent!=''">
                and a.email_content like concat('%', #{query.emailContent}, '%')
            </if>
            <if test="query.emailExtra!=null and query.emailExtra!=''">
                and a.email_extra like concat('%', #{query.emailExtra}, '%')
            </if>
            <if test="query.attackTime!=null">
                and a.attack_time = #{query.attackTime}
            </if>
            <if test="query.alarmId!=null">
                and b.alarm_id = #{query.alarmId}
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenNetFishingListVO">
        select a.* from threaten_net_fishing a 
        where a.id=#{id}
    </select>
</mapper>
