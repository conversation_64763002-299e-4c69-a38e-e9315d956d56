<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.strategy.config.mapper.ThreatenStrategyClassMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.strategy.config.model.ThreatenStrategyClassListVO">
        select a.* from threaten_strategy_class a 
        <where>
                <if test="query.id!=null">
                    and a.id = #{query.id}
                </if>
                <if test="query.strategyName!=null and query.strategyName!=''">
                    and a.strategy_name like concat('%', #{query.strategyName}, '%')
                </if>
                <if test="query.strategyClass!=null and query.strategyClass!=''">
                    and a.strategy_class like concat('%', #{query.strategyClass}, '%')
                </if>
                <if test="query.createTime!=null">
                    and a.create_time = #{query.createTime}
                </if>
                <if test="query.paramsDefine!=null and query.paramsDefine!=''">
                    and a.params_define like concat('%', #{query.paramsDefine}, '%')
                </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.strategy.config.model.ThreatenStrategyClassListVO">
        select a.* from threaten_strategy_class a 
        where a.id=#{id}
    </select>
</mapper>
