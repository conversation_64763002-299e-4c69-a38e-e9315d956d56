{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\api\\aqsoc\\work-hw\\crud.js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\api\\aqsoc\\work-hw\\crud.js", "mtime": 1756369455933}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1751956515056}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getList", "data", "request", "url", "method", "getSelect", "getInfo", "id", "concat", "getCountNum", "countId", "addData", "_data", "editData", "delData", "getNum", "path", "getUserNames", "userIds", "updateStages", "copyWork"], "sources": ["E:/wsh/augment_workspace/aqsoc-main/ruoyi-ui/src/api/aqsoc/work-hw/crud.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取数据列表（分页）\r\nexport function getList(data) {\r\n    return request({\r\n        url: `/api/work-hw/getList`,\r\n        method: 'post',\r\n        data\r\n    })\r\n}\r\n// 获取下拉数据列表（不分页）\r\nexport function getSelect(data) {\r\n    return request({\r\n        url: `/api/work-hw/getList`,\r\n        method: 'post',\r\n        data\r\n    })\r\n}\r\n// 获取数据详情\r\nexport function getInfo(id) {\r\n    return request({\r\n        url: `/api/work-hw/${id}`,\r\n        method: 'get'\r\n    })\r\n}\r\nexport function getCountNum(countId) {\r\n  return request({\r\n    url: `/api/work-hw/getCountNum?countId=${countId}`,\r\n    method: 'get'\r\n  })\r\n}\r\n//新增数据\r\nexport function addData(_data){\r\n    return request({\r\n        url: '/api/work-hw',\r\n        method: 'POST',\r\n        data: _data\r\n    })\r\n}\r\n//修改数据\r\nexport function editData(_data){\r\n    return request({\r\n        url: '/api/work-hw',\r\n        method: 'PUT',\r\n        data: _data\r\n    })\r\n}\r\n// 删除数据\r\nexport function delData(id){\r\n    return request({\r\n        url: `/api/work-hw/${id}`,\r\n        method: 'DELETE'\r\n    })\r\n}\r\nexport function getNum(path,id) {\r\n  return request({\r\n    url: `/api/work-hw/${path}?id=${id}`,\r\n    method: 'get'\r\n  })\r\n}\r\nexport function getUserNames(userIds) {\r\n  return request({\r\n    url: `/api/work-hw/getUserNames?userIds=${userIds}`,\r\n    method: 'get'\r\n  })\r\n}\r\nexport function updateStages(_data){\r\n  return request({\r\n    url: '/api/work-hw/updateStages',\r\n    method: 'POST',\r\n    data: _data\r\n  })\r\n}\r\n\r\n//复制\r\nexport function copyWork(_data){\r\n  return request({\r\n    url: '/api/work-hw/copyWork',\r\n    method: 'POST',\r\n    data: _data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,OAAOA,CAACC,IAAI,EAAE;EAC1B,OAAO,IAAAC,gBAAO,EAAC;IACXC,GAAG,wBAAwB;IAC3BC,MAAM,EAAE,MAAM;IACdH,IAAI,EAAJA;EACJ,CAAC,CAAC;AACN;AACA;AACO,SAASI,SAASA,CAACJ,IAAI,EAAE;EAC5B,OAAO,IAAAC,gBAAO,EAAC;IACXC,GAAG,wBAAwB;IAC3BC,MAAM,EAAE,MAAM;IACdH,IAAI,EAAJA;EACJ,CAAC,CAAC;AACN;AACA;AACO,SAASK,OAAOA,CAACC,EAAE,EAAE;EACxB,OAAO,IAAAL,gBAAO,EAAC;IACXC,GAAG,kBAAAK,MAAA,CAAkBD,EAAE,CAAE;IACzBH,MAAM,EAAE;EACZ,CAAC,CAAC;AACN;AACO,SAASK,WAAWA,CAACC,OAAO,EAAE;EACnC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,sCAAAK,MAAA,CAAsCE,OAAO,CAAE;IAClDN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACA;AACO,SAASO,OAAOA,CAACC,KAAK,EAAC;EAC1B,OAAO,IAAAV,gBAAO,EAAC;IACXC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdH,IAAI,EAAEW;EACV,CAAC,CAAC;AACN;AACA;AACO,SAASC,QAAQA,CAACD,KAAK,EAAC;EAC3B,OAAO,IAAAV,gBAAO,EAAC;IACXC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbH,IAAI,EAAEW;EACV,CAAC,CAAC;AACN;AACA;AACO,SAASE,OAAOA,CAACP,EAAE,EAAC;EACvB,OAAO,IAAAL,gBAAO,EAAC;IACXC,GAAG,kBAAAK,MAAA,CAAkBD,EAAE,CAAE;IACzBH,MAAM,EAAE;EACZ,CAAC,CAAC;AACN;AACO,SAASW,MAAMA,CAACC,IAAI,EAACT,EAAE,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,kBAAAK,MAAA,CAAkBQ,IAAI,UAAAR,MAAA,CAAOD,EAAE,CAAE;IACpCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACO,SAASa,YAAYA,CAACC,OAAO,EAAE;EACpC,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,uCAAAK,MAAA,CAAuCU,OAAO,CAAE;IACnDd,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACO,SAASe,YAAYA,CAACP,KAAK,EAAC;EACjC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdH,IAAI,EAAEW;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,QAAQA,CAACR,KAAK,EAAC;EAC7B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdH,IAAI,EAAEW;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}