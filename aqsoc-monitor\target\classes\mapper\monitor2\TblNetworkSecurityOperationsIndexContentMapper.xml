<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblNetworkSecurityOperationsIndexContentMapper">

    <resultMap type="TblNetworkSecurityOperationsIndexContent" id="TblNetworkSecurityOperationsIndexContentResult">
        <result property="id"    column="id"    />
        <result property="content"    column="content"    />
        <result property="indexId"    column="index_id"    />
        <collection property="indexItems" column="id" select="com.ruoyi.monitor2.mapper.TblNetworkSecurityOperationsIndexItemMapper.selectTblNetworkSecurityOperationsIndexItemListByContentId" />
    </resultMap>

    <sql id="selectTblNetworkSecurityOperationsIndexContentVo">
        select id, content, index_id from tbl_network_security_operations_index_content
    </sql>

    <select id="selectTblNetworkSecurityOperationsIndexContentList" parameterType="TblNetworkSecurityOperationsIndexContent" resultMap="TblNetworkSecurityOperationsIndexContentResult">
        <include refid="selectTblNetworkSecurityOperationsIndexContentVo"/>
        <where>
            <if test="id != null  and id != ''"> and id = #{id}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="indexId != null  and indexId != ''"> and index_id = #{indexId}</if>
            <if test="indexIds != null and indexIds.size() > 0">
                and index_id in
                <foreach item="indexId" collection="indexIds" open="(" separator="," close=")">
                    #{indexId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectTblNetworkSecurityOperationsIndexContentById" parameterType="String" resultMap="TblNetworkSecurityOperationsIndexContentResult">
        <include refid="selectTblNetworkSecurityOperationsIndexContentVo"/>
        where id = #{id}
    </select>

    <select id="selectTblNetworkSecurityOperationsIndexContentByIds" parameterType="String" resultMap="TblNetworkSecurityOperationsIndexContentResult">
        <include refid="selectTblNetworkSecurityOperationsIndexContentVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblNetworkSecurityOperationsIndexContent" parameterType="TblNetworkSecurityOperationsIndexContent">
        insert into tbl_network_security_operations_index_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="indexId != null and indexId != ''">index_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="indexId != null and indexId != ''">#{indexId},</if>
         </trim>
    </insert>

    <update id="updateTblNetworkSecurityOperationsIndexContent" parameterType="TblNetworkSecurityOperationsIndexContent">
        update tbl_network_security_operations_index_content
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="indexId != null and indexId != ''">index_id = #{indexId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblNetworkSecurityOperationsIndexContentById" parameterType="String">
        delete from tbl_network_security_operations_index_content where id = #{id}
    </delete>

    <delete id="deleteTblNetworkSecurityOperationsIndexContentByIds" parameterType="String">
        delete from tbl_network_security_operations_index_content where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
