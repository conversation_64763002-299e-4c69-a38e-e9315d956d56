package com.ruoyi.threaten.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.safe.domain.ThreatTrendForm;
import com.ruoyi.safe.domain.dto.OverviewParams;
import com.ruoyi.threaten.domain.FfsafeHoneypotAttackdetail;
import com.ruoyi.threaten.domain.TblThreatenAlarm;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 威胁情报Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-16
 */
public interface TblThreatenAlarmMapper {

    /**
     * 查询威胁情报
     *
     * @param id 威胁情报主键
     * @return 威胁情报
     */
    public TblThreatenAlarm selectTblThreatenAlarmById(Long id);

    /**
     * 批量查询威胁情报
     *
     * @param ids 威胁情报主键集合
     * @return 威胁情报集合
     */
    public List<TblThreatenAlarm> selectTblThreatenAlarmByIds(Long[] ids);


    /**
     * 查询威胁情报列表
     *
     * @param tblThreatenAlarm 威胁情报
     * @return 威胁情报集合
     */
    public List<TblThreatenAlarm> selectTblThreatenAlarmList(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询威胁情报列表
     *
     * @param tblThreatenAlarm 威胁情报
     * @return 威胁情报集合
     */
    public List<TblThreatenAlarm> getTblThreatenAlarmList(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 获取威胁告警列表
     *
     * @param tblThreatenAlarm 威胁告警
     * @return 威胁告警集合
     */
    public List<TblThreatenAlarm> selectTblThreatenAlarmStateList(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 新增威胁情报
     *
     * @param tblThreatenAlarm 威胁情报
     * @return 结果
     */
    public int insertTblThreatenAlarm(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 新增威胁情报列表
     *
     * @param tblThreatenAlarmList 威胁情报列表
     * @return 结果
     */
    public int insertTblThreatenAlarmList(List<TblThreatenAlarm> tblThreatenAlarmList);


    /**
     * 修改威胁情报
     *
     * @param tblThreatenAlarm 威胁情报
     * @return 结果
     */
    public int updateTblThreatenAlarm(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 删除威胁情报
     *
     * @param id 威胁情报主键
     * @return 结果
     */
    public int deleteTblThreatenAlarmById(Long id);

    /**
     * 批量删除威胁情报
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTblThreatenAlarmByIds(Long[] ids);

    /**
     * 查询威胁情报数量
     */
    public int count(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询威胁情报种类
     */
    List<HashMap> getThreatenType(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询威胁情报名称
     */
    List<HashMap> getThreatenName(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询 威胁等级 数据分析
     */
    List<HashMap> getThreatenGrade(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询 攻击源列表
     */
    List<TblThreatenAlarm> getAttackIpList(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询 受害源列表
     */
    List<TblThreatenAlarm> getDestIpList(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 查询 威胁列表
     */
    List<TblThreatenAlarm> getAlertInfoList(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 威胁 等级 每日分析
     */
    List<Map<String, Object>> getThreatenDayList(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 威胁 等级 每月分析
     */
    List<Map<String, Object>> getThreatenMonthList(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 威胁 等级 每年分析
     */
    List<Map<String, Object>> getThreatenYearList(TblThreatenAlarm tblThreatenAlarm);

    int getAttackNum(TblThreatenAlarm tblThreatenAlarm);
    int getHighRiskHostNum(TblThreatenAlarm tblThreatenAlarm);
    int getRiskHostNum(TblThreatenAlarm tblThreatenAlarm);
    int getThreatenAlarmNum(TblThreatenAlarm tblThreatenAlarm);
    @MapKey("alarm_level")
    Map<Integer, Object> getThreatenLevelNum(TblThreatenAlarm tblThreatenAlarm);

    List<TblThreatenAlarm> selectAttackIpList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectVictimIpList(TblThreatenAlarm tblThreatenAlarm);

    List<TblThreatenAlarm> getAttackSegSummary(TblThreatenAlarm tblThreatenAlarm);

    List<TblThreatenAlarm> getAssetAttackSegSummary(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectIpAttackSegNum(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectAttackIpOverviewList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectVictimIpOverviewList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectAssetIpOverviewList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectAttackIpReportList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectVictimIpReportList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectOutConnectReportList(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectAttackIpVictimStatReport(TblThreatenAlarm tblThreatenAlarm);
    List<TblThreatenAlarm> selectVictimIpAttackStatReport(TblThreatenAlarm tblThreatenAlarm);

    List<TblThreatenAlarm> selectTimeAxisList(TblThreatenAlarm tblThreatenAlarm);


    /**
     * 攻击者视角 列表查询
     * <AUTHOR>
     * @Description //
     * @Date 2024/9/12 14:27
     * @param tblThreatenAlarm
     * @return java.util.List<com.ruoyi.threaten.domain.TblThreatenAlarm>
     **/
    List<TblThreatenAlarm> getEventAttackList(TblThreatenAlarm tblThreatenAlarm);

    /**
     * 事件 攻击阶段 查询
     * <AUTHOR>
     * @Description //
     * @Date 2024/9/13 16:08
     * @param tblThreatenAlarm
     * @return java.util.List<com.ruoyi.threaten.domain.TblThreatenAlarm>
     **/
    List<TblThreatenAlarm> getEventSegTypeList(TblThreatenAlarm tblThreatenAlarm);

    /** 攻击者/受害者视角 告警名称分组查询 **/
    List<TblThreatenAlarm> getGroupByName(TblThreatenAlarm tblThreatenAlarm);

    /**  攻击者/受害者视角 告警等级分组查询 **/
    List<TblThreatenAlarm> getGroupByLevel(TblThreatenAlarm tblThreatenAlarm);

    /** 攻击者 攻击者分组查询 **/
    List<TblThreatenAlarm> getGroupByAttack(TblThreatenAlarm tblThreatenAlarm);

    /** 攻击者 攻击者分组查询**/
    List<TblThreatenAlarm> getGroupByVictim(TblThreatenAlarm tblThreatenAlarm);

    /** 攻击者 攻击阶段 查询**/
    List<TblThreatenAlarm> exportAttackStage(TblThreatenAlarm tblThreatenAlarm);

    /** 受害者 攻击阶段 查询**/
    List<TblThreatenAlarm> exportSufferStage(TblThreatenAlarm tblThreatenAlarm);

    List<TblThreatenAlarm> selectThreatenByIpList(@Param("ipList") List<String> ipList);

    List<FfsafeHoneypotAttackdetail> selectFfsafeHoneypotAttackdetailList(FfsafeHoneypotAttackdetail honeypotAttackdetail);

    List<JSONObject> getThreatTrend(ThreatTrendForm threatTrendForm);

    List<JSONObject> getVulnTrend(ThreatTrendForm threatTrendForm);

    List<TblThreatenAlarm> getAttackIpListTop5(TblThreatenAlarm tblThreatenAlarm);

    List<TblThreatenAlarm> getDestIpListTop5(TblThreatenAlarm tblThreatenAlarm);

    void batchUpdateHandleState(@Param("list") List<TblThreatenAlarm> handleList);

    /**
     * 批量更新威胁告警攻击方向
     *
     * @param alarms 威胁告警列表
     */
    void batchUpdateAttackDirection(@Param("list") List<TblThreatenAlarm> alarms);

    List<JSONObject> getAlarmCountsByIps(@Param("ipList") List<String> ipList);

    List<JSONObject> getAlarmCountsByAssetIds(@Param("assetIds") List<Long> assetIds);

    List<String> selectThreatenTypeByAttackSeg(String attackSeg);

    List<TblThreatenAlarm> selectNotSyncList(Date startDate);

    int countThreatenAlarmNum(OverviewParams params);

    List<JSONObject> groupAlarmLevelStatistics(TblThreatenAlarm tblThreatenAlarm);

    int selectTblThreatenAlarmListDeputy(TblThreatenAlarm tblThreatenAlarm);

    int countAttackIpOverview(TblThreatenAlarm tblThreatenAlarm);

    List<JSONObject> selectTop10Alarm(TblThreatenAlarm tblThreatenAlarm);

    List<JSONObject> selectAlarmTypeNum(TblThreatenAlarm tblThreatenAlarm);


    List<JSONObject> selectAlarmLevelStatistics(TblThreatenAlarm tblThreatenAlarm);

    List<JSONObject> selectRecentlyBlockedList(TblThreatenAlarm tblThreatenAlarm);

    List<JSONObject> selectAttackIpOverviewTop10(TblThreatenAlarm tblThreatenAlarm);

    List<JSONObject> getRankingOfAlarmSystems(TblThreatenAlarm tblThreatenAlarm);

    JSONObject getHostOverview(TblThreatenAlarm tblThreatenAlarm);

    String selectThreatenTypeByThreatenType(String threatenType);

    List<TblThreatenAlarm> selectListByGreaterThanId(Long threatenAlarmId);
}
