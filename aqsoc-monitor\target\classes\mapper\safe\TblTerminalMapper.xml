<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblTerminalMapper">

    <resultMap type="TblTerminal" id="TblTerminalResult">
        <result property="assetId" column="asset_id"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="isVirtual" column="is_virtual"/>
        <result property="systemSoftware" column="system_software"/>
        <result property="typeUsage" column="type_usage"/>
        <result property="vendor" column="vendor"/>
        <result property="manger" column="manger"/>
        <result property="degreeImportance" column="degree_importance"/>
        <result property="assetType" column="asset_type"/>
        <result property="assetTypeDesc" column="asset_type_desc"/>
        <result property="assetClass" column="asset_class"/>
        <result property="assetClassDesc" column="asset_class_desc"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="orgnId" column="orgn_id"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="mangerName" column="manger_name"/>
        <result property="domainId" column="domain_id" />
        <result property="ipv4" column="ipv4" />
        <result property="mac" column="mac" />
        <result property="isLocalization" column="is_localization" />
        <result property="concatDeptName" column="concatDeptName" />
        <association property="optsystem" column="opt_id" javaType="TblDeploy" resultMap="optResult"/>
    </resultMap>
    <resultMap id="optResult" type="TblDeploy">
        <id property="deployId" column="opt_id"/>
        <result property="procName" column="opt_name"/>
        <result property="prdid" column="opt_prdid"/>
    </resultMap>

    <resultMap id="DeptTerminalCountMap" type="com.ruoyi.safe.vo.DeptTerminalCount">
        <result property="deptId" column="deptId"/>
        <result property="ancestors" column="ancestors"/>
        <result property="terminalCount" column="terminalCount"/>
    </resultMap>

    <sql id="selectTblTerminalVo">
        select a.asset_id,
               a.asset_code,
               a.asset_name,
               a.is_virtual,
               a.system_software,
               a.type_usage,
               a.manger,
               a.vendor,
               a.degree_importance,
               a.asset_type,
               a.asset_type_desc,
               a.asset_class,
               a.asset_class_desc,
               a.remark,
               a.create_by,
               a.create_time,
               a.update_by,
               a.update_time,
               a.user_id,
               a.dept_id,
               a.orgn_id,
               a.domain_id,
               a.ipv4,
               a.mac,
               a.is_localization,
               d.vendor_name,
               e.nick_name as manger_name,
               f.deploy_id as opt_id,
               f.prdid     as opt_prdid,
               f.proc_name as opt_name
        from tbl_terminal a
             left join tbl_vendor d on a.vendor = d.id
             left join sys_user e on a.user_id = e.user_id
             left join tbl_deploy f on a.asset_id = f.asset_id and f.application_id is null and f.moduel_id is null and f.softlx = '1'
             left join tbl_network_ip_mac net on net.asset_id = a.asset_id
    </sql>

    <select id="selectTblTerminalList" parameterType="TblTerminal" resultMap="TblTerminalResult">
        <include refid="selectTblTerminalVo"/>
        <where>
            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>
            <if test="assetCode != null  and assetCode != ''"> and a.asset_code like concat('%',#{assetCode},'%')</if>
            <if test="assetName != null  and assetName != ''"> and a.asset_name like concat('%', #{assetName}, '%')</if>
            <if test="isVirtual != null  and isVirtual != ''"> and is_virtual = #{isVirtual}</if>
            <if test="assetType != null  and assetType != ''"> and asset_type = #{assetType}</if>
            <if test="degreeImportance != null  and degreeImportance != ''"> and degree_importance = #{degreeImportance}</if>
            <if test="domainId != null">and a.domain_id = #{domainId}</if>
            <if test="deptId != null and deptId != 100">
                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="params.dataScope != null and params.dataScope != ''">
                ${params.dataScope}
            </if>
            <if test="ipv4 != null and ipv4 != ''"> and a.ipv4 like concat('%',#{ipv4},'%')</if>
            <if test="userName != null and userName != ''"> and (e.nick_name like concat('%',#{userName},'%') OR e.user_name like concat('%',#{userName},'%')) </if>
            <if test="isLocalization != null"> and a.is_localization = #{isLocalization}</if>
            <if test="mac != null and mac != ''"> and a.mac like concat('%',#{mac},'%') </if>
            <if test="optId != null and optId != ''"> and f.prdid = #{optId} </if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
        order by a.update_time desc
    </select>

    <select id="selectTblTerminalByAssetId" parameterType="Long" resultMap="TblTerminalResult">
        <include refid="selectTblTerminalVo"/>
        where a.asset_id = #{assetId}
    </select>

    <select id="selectTblTerminalByAssetIds" parameterType="Long" resultMap="TblTerminalResult">
        <include refid="selectTblTerminalVo"/>
        where a.asset_id in
        <foreach item="assetId" collection="array" open="(" close=")" separator=",">
            #{assetId}
        </foreach>
    </select>
    <select id="getDeptTerminalCount" resultType="com.ruoyi.safe.vo.DeptTerminalCount" resultMap="DeptTerminalCountMap">
        SELECT
        sd.dept_id AS deptId,sd.ancestors,
        <choose>
            <when test="params.userId != null">
                COALESCE(SUM(CASE WHEN a.user_id = #{params.userId} THEN 1 ELSE 0 END), 0)
            </when>
            <otherwise>
                COALESCE(COUNT(distinct a.asset_id), 0)
            </otherwise>
        </choose>
        AS terminalCount
        FROM sys_dept sd
        LEFT JOIN tbl_terminal a ON a.dept_id = sd.dept_id <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        WHERE sd.dept_id IN
        <foreach collection="deptIdList" item="deptId" separator="," close=")" open="(">
            #{deptId}
        </foreach>
        GROUP BY sd.dept_id
    </select>

    <select id="searchTblTermina" resultType="com.ruoyi.safe.domain.TblTerminal" resultMap="TblTerminalResult">
        select a.asset_id,
               a.asset_code,
               a.asset_name,
               a.is_virtual,
               a.system_software,
               a.type_usage,
               a.manger,
               a.vendor,
               a.degree_importance,
               a.asset_type,
               a.asset_type_desc,
               a.asset_class,
               a.asset_class_desc,
               a.remark,
               a.create_by,
               a.create_time,
               a.update_by,
               a.update_time,
               a.user_id,
               a.dept_id,
               IF(de.concatDeptName IS NOT NULL,CONCAT(de.concatDeptName,'/',de.dept_name),de.dept_name) concatDeptName,
               a.orgn_id,
               a.domain_id,
               a.ipv4,
               a.mac,
               a.is_localization,
               d.vendor_name,
               e.nick_name as manger_name,
               f.deploy_id as opt_id,
               f.prdid     as opt_prdid,
               f.proc_name as opt_name
        from tbl_terminal a
                 LEFT JOIN (
        SELECT
        d1.*,
        GROUP_CONCAT(d2.dept_name ORDER BY d2.parent_id SEPARATOR'/') concatDeptName
        FROM
        sys_dept d1
        LEFT JOIN sys_dept d2 ON LOCATE(d2.dept_id,d1.ancestors)
        GROUP BY
        d1.dept_id
        ) de ON de.dept_id = a.dept_id
                 LEFT JOIN tbl_deploy td ON td.asset_id = a.asset_id
                 left join tbl_vendor d on a.vendor = d.id
                 left join sys_user e on a.user_id = e.user_id
                 left join tbl_deploy f on a.asset_id = f.asset_id and f.application_id is null and f.moduel_id is null and f.softlx = '1'
                 left join tbl_network_ip_mac net on net.asset_id = a.asset_id
                 LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = a.domain_id
        <where>
            <if test="params.dataScope == null or params.dataScope != ''">${params.dataScope}</if>
            <if test="queryParam != null and queryParam != ''">
                and (de.dept_name like concat('%',#{queryParam},'%') or e.nick_name like concat('%',#{queryParam},'%') or a.asset_code like concat('%',#{queryParam},'%')
                    or a.ipv4 like concat('%',#{queryParam},'%') or a.mac like concat('%',#{queryParam},'%') or tnd.domain_name like concat('%',#{queryParam},'%')
                    or td.proc_name like concat('%',#{queryParam},'%'))
            </if>
        </where>
    </select>

    <insert id="insertTblTerminal" parameterType="TblTerminal">
        insert into tbl_terminal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="assetCode != null">asset_code,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="isVirtual != null">is_virtual,</if>
            <if test="systemSoftware != null">system_software,</if>
            <if test="typeUsage != null">type_usage,</if>
            <if test="vendor != null">vendor,</if>
            <if test="manger != null">manger,</if>
            <if test="degreeImportance != null">degree_importance,</if>
            <if test="assetType != null">asset_type,</if>
            <if test="assetTypeDesc != null">asset_type_desc,</if>
            <if test="assetClass != null and assetClass != ''">asset_class,</if>
            <if test="assetClassDesc != null">asset_class_desc,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="orgnId != null">orgn_id,</if>
            <if test="domainId != null">domain_id,</if>
            <if test="ipv4 != null">ipv4,</if>
            <if test="mac != null">`mac`,</if>
            <if test="isLocalization != null">is_localization,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="assetCode != null">#{assetCode},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="isVirtual != null">#{isVirtual},</if>
            <if test="systemSoftware != null">#{systemSoftware},</if>
            <if test="typeUsage != null">#{typeUsage},</if>
            <if test="vendor != null">#{vendor},</if>
            <if test="manger != null">#{manger},</if>
            <if test="degreeImportance != null">#{degreeImportance},</if>
            <if test="assetType != null">#{assetType},</if>
            <if test="assetTypeDesc != null">#{assetTypeDesc},</if>
            <if test="assetClass != null and assetClass != ''">#{assetClass},</if>
            <if test="assetClassDesc != null">#{assetClassDesc},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="orgnId != null">#{orgnId},</if>
            <if test="domainId != null">#{domainId},</if>
            <if test="ipv4 != null">#{ipv4},</if>
            <if test="mac != null">#{mac},</if>
            <if test="isLocalization != null">#{isLocalization},</if>
         </trim>
    </insert>

    <update id="updateTblTerminal" parameterType="TblTerminal">
        update tbl_terminal
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="isVirtual != null">is_virtual = #{isVirtual},</if>
            <if test="systemSoftware != null">system_software = #{systemSoftware},</if>
            <if test="typeUsage != null">type_usage = #{typeUsage},</if>
            <if test="vendor != null">vendor = #{vendor},</if>
            <if test="manger != null">manger = #{manger},</if>
            <if test="degreeImportance != null">degree_importance = #{degreeImportance},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="assetTypeDesc != null">asset_type_desc = #{assetTypeDesc},</if>
            <if test="assetClass != null and assetClass != ''">asset_class = #{assetClass},</if>
            <if test="assetClassDesc != null">asset_class_desc = #{assetClassDesc},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="orgnId != null">orgn_id = #{orgnId},</if>
            <if test="domainId != null">domain_id = #{domainId},</if>
            <if test="ipv4 != null and ipv4 != ''">ipv4 = #{ipv4},</if>
            <if test="mac != null and mac != ''">mac = #{mac},</if>
            <if test="isLocalization != null">is_localization = #{isLocalization},</if>
        </trim>
        where asset_id = #{assetId}
    </update>

    <delete id="deleteTblTerminalByAssetId" parameterType="Long">
        delete from tbl_terminal where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblTerminalByAssetIds" parameterType="String">
        delete from tbl_terminal where asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
</mapper>
