2025-08-29 10:48:34.417 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1194 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 10:48:31"]
2025-08-29 10:48:36.304 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1859 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 10:48:31"]
2025-08-29 10:48:40.482 [pool-5-thread-2] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getFirewall<PERSON>ist,458] - 图幻-----获取防火墙列表-----失败： SSLHandshakeException: Remote host terminated the handshake
2025-08-29 10:48:43.323 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8622 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:48:26"]
2025-08-29 10:48:46.418 [pool-5-thread-2] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getNatList,492] - 图幻-----获取防火墙NAT列表--失败：SSLHandshakeException: Remote host terminated the handshake
2025-08-29 10:48:46.491 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10181 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:48:31"]
2025-08-29 10:48:49.342 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16098 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 10:48:24"]
2025-08-29 10:48:57.168 [pool-14-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3144 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id,ta.attack_direction from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) ) and ta.handle_state=0["**************","***************","***************","**************","***************","***************"]
2025-08-29 10:48:57.435 [pool-14-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3399 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id,ta.attack_direction from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-29 10:48:57.445 [pool-14-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3421 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id,ta.attack_direction from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-29 10:48:57.745 [pool-14-thread-4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3703 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id,ta.attack_direction from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         ) ) and ta.handle_state=0["***************","***************","***************","***************"]
2025-08-29 10:48:58.688 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9338 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 10:48:24"]
2025-08-29 10:49:28.634 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 29822 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 10:48:24"]
2025-08-29 10:49:37.438 [async-task-pool74] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7657 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:48:56"]
2025-08-29 10:49:39.497 [async-task-pool77] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9110 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:48:42"]
2025-08-29 10:49:41.248 [async-task-pool73] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12227 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 10:48:59"]
2025-08-29 10:49:52.589 [async-task-pool73] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11044 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 10:48:59"]
2025-08-29 10:50:22.145 [async-task-pool73] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 29551 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 10:48:59"]
2025-08-29 10:50:24.311 [async-task-pool131] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1105 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 10:49:41"]
2025-08-29 10:50:30.169 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6825 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:49:56"]
2025-08-29 10:50:35.482 [async-task-pool131] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11164 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:49:41"]
2025-08-29 10:50:38.512 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16014 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 10:49:59"]
2025-08-29 10:50:46.601 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8084 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 10:49:59"]
2025-08-29 10:51:18.624 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 32018 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 10:49:59"]
2025-08-29 10:51:20.844 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1060 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 10:51:03"]
2025-08-29 10:51:26.962 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6853 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:50:56"]
2025-08-29 10:51:31.974 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11127 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:51:03"]
2025-08-29 10:51:34.198 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15244 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 10:50:58"]
2025-08-29 10:51:41.264 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7059 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 10:50:58"]
2025-08-29 10:52:14.892 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 33622 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 10:50:58"]
2025-08-29 10:52:16.973 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1118 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 10:52:06"]
2025-08-29 10:52:22.937 [async-task-pool58] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6835 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:51:56"]
2025-08-29 10:52:28.413 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11434 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:52:06"]
2025-08-29 10:52:31.227 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16036 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 10:52:04"]
2025-08-29 10:52:37.962 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6732 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 10:52:04"]
2025-08-29 10:53:14.348 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36379 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 10:52:04"]
2025-08-29 10:53:22.122 [async-task-pool124] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6568 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:52:56"]
2025-08-29 10:53:27.312 [async-task-pool121] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10728 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:53:04"]
2025-08-29 10:53:29.711 [async-task-pool122] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14929 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 10:53:00"]
2025-08-29 10:53:36.689 [async-task-pool122] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6974 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 10:53:00"]
2025-08-29 10:54:14.106 [async-task-pool122] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 37413 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 10:53:00"]
2025-08-29 10:54:22.269 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6693 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:53:56"]
2025-08-29 10:54:26.581 [async-task-pool190] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10305 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:54:09"]
2025-08-29 10:54:27.815 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13270 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 10:54:10"]
2025-08-29 10:54:37.330 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9509 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 10:54:10"]
2025-08-29 10:55:28.229 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 50892 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 10:54:10"]
2025-08-29 10:55:31.221 [async-task-pool76] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2226 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 10:55:09"]
2025-08-29 10:55:33.633 [async-task-pool76] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2405 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 10:55:09"]
2025-08-29 10:55:34.597 [async-task-pool80] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4736 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:54:56"]
2025-08-29 10:55:44.425 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15429 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 10:55:07"]
2025-08-29 10:56:29.118 [async-task-pool124] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3097 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",49846,"*********",53,"dns","[1600037] dnslog家族漏洞利用黑客工具（请求恶意域名: oastify.com）","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiNmY2MTczNzQ2OTY2NzkyZTYzNmY2ZCIsICJjb250ZW50X3N0cmluZyI6ICJvYXN0aWZ5LmNvb...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1600037,4,"请求","SloBAAABAAAAAAAAB3BvbGxpbmcHb2FzdGlmeQNjb20AABwAAQ==","[]","2025-08-29 10:56:22",1]
2025-08-29 10:56:29.126 [async-task-pool142] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3106 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",49846,"*********",53,"dns","[1600037] dnslog家族漏洞利用黑客工具（请求恶意域名: oastify.com）","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiNmY2MTczNzQ2OTY2NzkyZTYzNmY2ZCIsICJjb250ZW50X3N0cmluZyI6ICJvYXN0aWZ5LmNvb...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1600037,4,"请求","SloBAAABAAAAAAAAB3BvbGxpbmcHb2FzdGlmeQNjb20AABwAAQ==","[]","2025-08-29 10:56:22",4]
2025-08-29 10:56:47.233 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 62803 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 10:55:07"]
2025-08-29 10:57:23.065 [http-nio-8080-exec-27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1738 millis. SELECT
        count( t1.id )
        FROM
        monitor_bss_vuln_deal t1
         
         WHERE  t1.update_time between ? and ?["2025-08-29 00:00:00","2025-08-29 23:59:59"]
2025-08-29 10:57:38.829 [http-nio-8080-exec-44] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1027 millis. SELECT
            t1.*
        FROM
            tbl_business_application t1
        WHERE
            t1.asset_id in
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[1864886739466850304,1871137827866152960,1864886125852758016,1930512672974049280,1871813057550880768,1871459805881831424,1873545246391013376,1894661472097800192,1904466348155736064,1904464809680834560,1898971907412332544,1897841788165689344,1897925745276227584,1892047164184596480,1906636970101182464,1913413722722930688,1912773529762402304,1932314545070673920,1944291744459919360,1947940623533346816,1947941768246988800,1947942203330531328,1955109948098744320,1955154237205909504,1955173048390914048,1955182532454649856,1955182625857605632,1955182626167984128,1955215619972534272,1955215620106752000,1955528146631659520,1955528146799431680,1955537899923574784,1955537900045209600,1955541091663089664,1955541091742781440,1955546558099492864,1955546558242099200,1955546711648768000,1955546711741042688,1940342961300705280,1954916495247151104,1955109101855969280,1955110301527576576,1955144079239155712]
2025-08-29 10:57:38.831 [http-nio-8080-exec-43] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1038 millis. SELECT
            t1.*
        FROM
            tbl_business_application t1
        WHERE
            t1.asset_id in
         (  
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         , 
            ?
         )[1864886739466850304,1871137827866152960,1864886125852758016,1930512672974049280,1871813057550880768,1871459805881831424,1873545246391013376,1894661472097800192,1904466348155736064,1904464809680834560,1898971907412332544,1897841788165689344,1897925745276227584,1892047164184596480,1906636970101182464,1913413722722930688,1912773529762402304,1932314545070673920,1944291744459919360,1947940623533346816,1947941768246988800,1947942203330531328,1955109948098744320,1955154237205909504,1955173048390914048,1955182532454649856,1955182625857605632,1955182626167984128,1955215619972534272,1955215620106752000,1955528146631659520,1955528146799431680,1955537899923574784,1955537900045209600,1955541091663089664,1955541091742781440,1955546558099492864,1955546558242099200,1955546711648768000,1955546711741042688,1940342961300705280,1954916495247151104,1955109101855969280,1955110301527576576,1955144079239155712]
2025-08-29 10:57:38.916 [http-nio-8080-exec-12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1626 millis. SELECT 
            ? as notice_id,
            COUNT(*) as total_count,
            SUM(CASE WHEN read_status = '1' THEN 1 ELSE 0 END) as read_count,
            SUM(CASE WHEN read_status = '0' THEN 1 ELSE 0 END) as unread_count
        FROM sys_notice_read_status 
        WHERE notice_id = ?[414,414]
2025-08-29 10:57:39.324 [http-nio-8080-exec-38] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1153 millis. select count(0) from ( 
SELECT
		u.user_id,
		u.dept_id,
		u.nick_name,
		u.user_name,
		GROUP_CONCAT(r.role_name SEPARATOR ',') AS roleNameStr,
		u.email,
		u.avatar,
		u.phonenumber,
		u.sex,
		u.STATUS,
		u.del_flag,
		u.login_ip,
		u.login_date,
		u.passtime,
		u.create_by,
		u.create_time,
		u.remark,
		d.dept_name,
		fd.dept_name_full,
		d.leader
		FROM
		sys_user u
		LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
		LEFT JOIN sys_user_role ur ON ur.user_id = u.user_id
		LEFT JOIN sys_role r ON r.role_id = ur.role_id
		LEFT JOIN(
		SELECT
		d.dept_id,
		CONCAT(
		COALESCE ( CONCAT( p3.dept_name, '/' ), '' ),
		COALESCE ( CONCAT( p2.dept_name, '/' ), '' ),
		COALESCE ( CONCAT( p1.dept_name, '/' ), '' ),
		d.dept_name
		) AS dept_name_full
		FROM
		sys_dept d
		LEFT JOIN sys_dept p1 ON p1.dept_id = d.parent_id
		LEFT JOIN sys_dept p2 ON p2.dept_id = p1.parent_id
		LEFT JOIN sys_dept p3 ON p3.dept_id = p2.parent_id
		ORDER BY
		d.ancestors,
		d.order_num
		)fd ON d.dept_id = fd.dept_id
		WHERE
		u.del_flag = '0'
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		
		
		GROUP BY u.user_id
		order by u.create_time desc
 ) tmp_count[]
2025-08-29 10:57:40.336 [http-nio-8080-exec-46] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1176 millis. select
        t1.id,
        t1.work_no,
        t1.work_type,
        t1.event_ids,
        t1.work_name,
        t1.application_id,
        t1.login_url,
        t1.handle_dept,
        t1.handle_user,
        t1.handle_user_phone,
        t1.expect_complete_time,
        t1.event_source,
        t1.event_type,
        t1.event_level,
        t1.complete_time,
        t1.prod_Id,
        t1.complete_user,
        t1.update_time,
        t1.flow_state,
        t1.flow_state AS handle_state,
        t1.remark2 AS node_properties,
        t1.create_time,
        t1.urgency,
        t1.is_public,
        t1.severity_level,
        t1.report_date,
        t1.period,
        t1.signed,
        t1.proofread,
        t1.editor,
        t1.remark6,
        hd.dept_name AS handleDeptName,
        hu.nick_name AS handleUserName,
        tba.asset_name application_name,
        hu2.nick_name AS create_by
        from
            tbl_work_order t1
        left join tbl_work_backlog t2 ON t2.work_id = t1.id
        left join tbl_work_history t3 on t3.work_id = t1.id
        left join tbl_work_order_target wot on wot.work_order_id = t1.id
        LEFT JOIN sys_dept hd ON wot.handle_dept = hd.dept_id
        LEFT JOIN sys_user hu ON wot.handle_user = hu.user_id
        LEFT JOIN tbl_business_application tba ON tba.asset_id = t1.application_id
        LEFT JOIN sys_user hu2 on t1.remark5 = hu2.user_id
         WHERE (
                1=1
                
                
                
                     or(
                    1=2
                    
                     OR (t2.handle_user = ? OR t3.handle_user = ?)
                     OR 1=1
                    )
                
                )
            
            
            
            
                
                    and t1.work_type = ? 
        GROUP BY
        t1.id
        ORDER BY t1.update_time DESC["1","1","3"]
2025-08-29 10:57:59.865 [http-nio-8080-exec-46] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19520 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0"]
2025-08-29 10:57:59.867 [http-nio-8080-exec-43] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20593 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0"]
2025-08-29 10:57:59.875 [http-nio-8080-exec-44] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20541 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 10:58:00.254 [http-nio-8080-exec-51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17224 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 10:58:14.861 [async-task-pool41] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1085 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",43414,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","T5sBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-29 10:58:06",1]
2025-08-29 10:58:14.864 [async-task-pool45] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1183 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",43414,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","T5sBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-29 10:58:06",4]
2025-08-29 10:58:50.147 [http-nio-8080-exec-61] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6694 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 10:58:50.868 [http-nio-8080-exec-62] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6804 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.update_time BETWEEN ? AND ?["2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 10:58:50.913 [http-nio-8080-exec-63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6886 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.update_time between ? and ? 
    group by ttd.attack_seg["2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 10:59:01.955 [http-nio-8080-exec-76] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1034 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 10:59:12.450 [http-nio-8080-exec-83] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1036 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:00:11.341 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 204104 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 10:55:07"]
2025-08-29 11:01:07.680 [async-task-pool76] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 334043 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 10:55:09"]
2025-08-29 11:01:31.803 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1219 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:01:09"]
2025-08-29 11:01:43.167 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13212 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:01:20"]
2025-08-29 11:01:43.369 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12684 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:01:14"]
2025-08-29 11:01:46.764 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14958 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:01:09"]
2025-08-29 11:01:53.073 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9901 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:01:20"]
2025-08-29 11:03:50.481 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 117406 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:01:20"]
2025-08-29 11:03:55.519 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3352 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 09:49:03","2025-08-29 10:59:08"]
2025-08-29 11:03:57.650 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5455 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***********","2025-08-25 08:27:55","2025-08-29 10:55:12"]
2025-08-29 11:03:57.669 [async-task-pool160] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5340 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:03:53","2025-08-29 11:00:17"]
2025-08-29 11:03:57.917 [async-task-pool167] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5651 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["************","2025-08-25 08:12:47","2025-08-29 10:54:36"]
2025-08-29 11:03:58.352 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6168 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**********","2025-08-25 08:14:50","2025-08-29 11:00:02"]
2025-08-29 11:03:58.444 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6134 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:03:15","2025-08-29 11:00:40"]
2025-08-29 11:04:03.702 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2498 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:02:29","2025-08-29 11:02:46"]
2025-08-29 11:04:28.144 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7021 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:03:56"]
2025-08-29 11:04:31.470 [async-task-pool77] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9702 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:03:54"]
2025-08-29 11:04:34.514 [async-task-pool89] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14100 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:03:54"]
2025-08-29 11:04:40.283 [async-task-pool89] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5767 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:03:54"]
2025-08-29 11:05:31.692 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1016 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",53749,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","lUEBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-29 11:05:25",1]
2025-08-29 11:05:31.692 [async-task-pool165] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1046 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",53749,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","lUEBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-29 11:05:25",4]
2025-08-29 11:06:21.916 [async-task-pool186] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15928 millis. update tbl_threaten_alarm
         SET threaten_name = ?,
            threaten_type = ?,
            
            
            
            src_ip = ?,
            src_port = ?,
            dest_ip = ?,
            dest_port = ?,
            
            alarm_level = ?,
            
            
            create_time = ?,
            
            update_time = ?,
            
            alarm_num = ?,
            
            
            
            
            handle_state = ?,
            
            
            synchronization_status = ?,
            
            attack_direction = ? 
        where id = ?["[1621883] 僵尸木马事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","**************",60845,"*******",53,4,"2025-07-25 10:46:18","2025-08-29 11:06:01",1130370,"0","1","2",37515]
2025-08-29 11:06:21.946 [async-task-pool191] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11596 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-29 11:06:09","/v2/flow-bypass-filtering-log"]
2025-08-29 11:06:21.955 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15974 millis. update tbl_threaten_alarm
         SET threaten_name = ?,
            threaten_type = ?,
            
            
            
            src_ip = ?,
            src_port = ?,
            dest_ip = ?,
            dest_port = ?,
            
            alarm_level = ?,
            
            
            create_time = ?,
            
            update_time = ?,
            
            alarm_num = ?,
            
            
            
            
            handle_state = ?,
            
            
            
            
            attack_direction = ? 
        where id = ?["[1621883] 僵尸木马事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","**************",51487,"*******",53,4,"2025-07-30 15:47:24","2025-08-29 11:06:01",1971720,"0","2",43843]
2025-08-29 11:06:21.965 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11612 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-29 11:06:09","/v2/flow-bypass-filtering-log"]
2025-08-29 11:06:21.967 [async-task-pool183] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11670 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 11:04:09",1]
2025-08-29 11:06:21.971 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11632 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 11:04:09",4]
2025-08-29 11:06:21.983 [async-task-pool89] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 98425 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:03:54"]
2025-08-29 11:06:22.041 [async-task-pool4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5940 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",5,"sensitive_info",37,"weak_password",1,"['账号:ftpuser, 密码:******']",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",1,"********",5,"********",37,"********",1,"2024-10-31 11:52:33",5,"2024-10-30 14:22:32",37,"2025-07-02 10:49:00",1,"2025-08-29 11:01:56",5,"2025-08-29 10:17:56",37,"2025-08-29 09:09:24",1,4,5,4,37,4,1,5,37]
2025-08-29 11:06:22.056 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5940 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",5,"sensitive_info",37,"weak_password",1,"['账号:ftpuser, 密码:******']",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",1,"********",5,"********",37,"********",1,"2024-10-31 11:52:33",5,"2024-10-30 14:22:32",37,"2025-07-02 10:49:00",1,"2025-08-29 11:01:56",5,"2025-08-29 10:17:56",37,"2025-08-29 09:09:24",1,1,5,1,37,1,1,5,37]
2025-08-29 11:06:25.761 [async-task-pool5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1357 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:02:29","2025-08-29 11:02:46"]
2025-08-29 11:06:27.225 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2870 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 09:49:03","2025-08-29 10:59:08"]
2025-08-29 11:06:29.462 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5098 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***********","2025-08-25 08:27:55","2025-08-29 10:55:12"]
2025-08-29 11:06:29.506 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5117 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:03:53","2025-08-29 11:04:09"]
2025-08-29 11:06:29.544 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5144 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["************","2025-08-25 08:12:47","2025-08-29 10:54:36"]
2025-08-29 11:06:30.070 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5668 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**********","2025-08-25 08:14:50","2025-08-29 11:00:02"]
2025-08-29 11:06:30.693 [async-task-pool190] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6304 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:03:15","2025-08-29 11:03:29"]
2025-08-29 11:06:34.206 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1146 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:06:09"]
2025-08-29 11:06:35.302 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1093 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:06:09"]
2025-08-29 11:06:45.883 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10575 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:06:09"]
2025-08-29 11:06:57.688 [async-task-pool95] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8185 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:06:26"]
2025-08-29 11:07:05.829 [async-task-pool94] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15958 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:06:09"]
2025-08-29 11:07:11.045 [async-task-pool80] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22234 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:06:28"]
2025-08-29 11:07:18.896 [async-task-pool80] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7848 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:06:28"]
2025-08-29 11:07:53.016 [async-task-pool80] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 34116 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:06:28"]
2025-08-29 11:08:03.868 [async-task-pool183] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6888 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:07:26"]
2025-08-29 11:08:08.085 [async-task-pool191] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10468 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:07:11"]
2025-08-29 11:08:11.769 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15896 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:07:20"]
2025-08-29 11:08:18.854 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7078 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:07:20"]
2025-08-29 11:08:49.701 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 30841 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:07:20"]
2025-08-29 11:09:04.214 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10590 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:08:14"]
2025-08-29 11:09:14.554 [async-task-pool143] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5341 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:08:56"]
2025-08-29 11:09:24.039 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15533 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:09:02"]
2025-08-29 11:09:33.564 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9516 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:09:02"]
2025-08-29 11:09:46.403 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1260 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",52352,"***************",80,"http","[1799903] 尝试使用弱口令进行http basic认证","网络攻击/漏洞利用/弱口令","W3sicGNyZSI6ICJBdXRob3JpemF0aW9uOlxccypCYXNpY1xccyooWVdSdGFXNDZZV1J0YVc0PXxZV1J0YVc0NllXUnRhVzR4T...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1799903,2,"请求","R0VUIC9zZXJ2aWNlL2xvY2FsL2F1dGhlbnRpY2F0aW9uL2xvZ2luIEhUVFAvMS4xDQpIb3N0OiBzdW1tZXIyMzMudG9wDQpVc...","[]","2025-08-29 11:09:37",4]
2025-08-29 11:09:47.166 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1314 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",52352,"***************",80,"http","[1799903] 尝试使用弱口令进行http basic认证","网络攻击/漏洞利用/弱口令","W3sicGNyZSI6ICJBdXRob3JpemF0aW9uOlxccypCYXNpY1xccyooWVdSdGFXNDZZV1J0YVc0PXxZV1J0YVc0NllXUnRhVzR4T...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1799903,2,"请求","R0VUIC9zZXJ2aWNlL2xvY2FsL2F1dGhlbnRpY2F0aW9uL2xvZ2luIEhUVFAvMS4xDQpIb3N0OiBzdW1tZXIyMzMudG9wDQpVc...","[]","2025-08-29 11:09:37",1]
2025-08-29 11:09:51.075 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1838 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",51913,"**************",80,"http","[1704826] 金山V8系统默认账号密码尝试登录","网络攻击/漏洞利用/弱口令","W3sicGNyZSI6ICJhZG1pbiJ9LCB7ImNvbnRlbnRfaGV4IjogIjUwNGY1MzU0IiwgImNvbnRlbnRfc3RyaW5nIjogIlBPU1Qif...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8IWRvY3R5cGUgaHRtbD4KP...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1704826,3,"请求","UE9TVCAvaW50ZXIvYWpheC5waHA/Y21kPWdldF91c2VyX2xvZ2luX2NtZCBIVFRQLzEuMQ0KSG9zdDogbXl0eGNqLmNvbQ0KV...","[]","2025-08-29 11:09:30",4]
2025-08-29 11:10:16.148 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1207 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",53511,"**************",80,"http","[1780107] 疑似目录穿越攻击_URI","其他/web后门","W3sicGNyZSI6ICJcXC5cXC4vXFwuXFwuLyJ9XQ==",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1780107,3,"请求","R0VUIC93ZWJ1aS8/Zz1zeXNfZGlhX2RhdGFfZG93biZmaWxlX25hbWU9Li4vLi4vLi4vLi4vLi4vZXRjL3Bhc3N3ZCBIVFRQL...","[]","2025-08-29 11:10:08",4]
2025-08-29 11:10:16.417 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1342 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",53511,"**************",80,"http","[1780107] 疑似目录穿越攻击_URI","其他/web后门","W3sicGNyZSI6ICJcXC5cXC4vXFwuXFwuLyJ9XQ==",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1780107,3,"请求","R0VUIC93ZWJ1aS8/Zz1zeXNfZGlhX2RhdGFfZG93biZmaWxlX25hbWU9Li4vLi4vLi4vLi4vLi4vZXRjL3Bhc3N3ZCBIVFRQL...","[]","2025-08-29 11:10:08",1]
2025-08-29 11:10:18.017 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1524 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",53544,"**************",80,"http","[2025740] Apache CouchDB 安全漏洞攻击(CVE-2017-12635)","网络攻击/漏洞利用/恶意代码攻击","W3siY29udGVudF9oZXgiOiAiMmY1Zjc1NzM2NTcyNzMyZjZmNzI2NzJlNjM2Zjc1NjM2ODY0NjIyZTc1NzM2NTcyM2EiLCAiY...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2025740,4,"请求","UFVUIC9fdXNlcnMvb3JnLmNvdWNoZGIudXNlcjolN0IlN0IlN0JzdHIxJTdEJTdEJTdEIEhUVFAvMS4xDQpIb3N0OiB5YW5od...","[]","2025-08-29 11:10:04",4]
2025-08-29 11:10:18.037 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1433 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",53544,"**************",80,"http","[2025740] Apache CouchDB 安全漏洞攻击(CVE-2017-12635)","网络攻击/漏洞利用/恶意代码攻击","W3siY29udGVudF9oZXgiOiAiMmY1Zjc1NzM2NTcyNzMyZjZmNzI2NzJlNjM2Zjc1NjM2ODY0NjIyZTc1NzM2NTcyM2EiLCAiY...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2025740,4,"请求","UFVUIC9fdXNlcnMvb3JnLmNvdWNoZGIudXNlcjolN0IlN0IlN0JzdHIxJTdEJTdEJTdEIEhUVFAvMS4xDQpIb3N0OiB5YW5od...","[]","2025-08-29 11:10:04",1]
2025-08-29 11:10:23.933 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 50363 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:09:02"]
2025-08-29 11:10:33.696 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3243 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:10:03"]
2025-08-29 11:10:34.838 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1138 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:10:03"]
2025-08-29 11:10:39.896 [async-task-pool69] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8487 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:09:56"]
2025-08-29 11:10:42.593 [async-task-pool99] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1394 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54875,"***************",80,"http","[1700767] Hitachi Vantara Pentaho 访问控制错误漏洞(CVE-2021-31602)攻击","其他/web后门","W3siY29udGVudF9oZXgiOiAiNDc0NTU0IiwgImNvbnRlbnRfc3RyaW5nIjogIkdFVCJ9LCB7ImNvbnRlbnRfaGV4IjogIjJmN...",301,"SFRUUC8xLjEgMzAxCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1700767,4,"请求","R0VUIC9wZW50YWhvL2FwaS91c2Vycm9sZWxpc3Qvc3lzdGVtUm9sZXM/cmVxdWlyZS1jZmcuanMgSFRUUC8xLjENCkhvc3Q6I...","[]","2025-08-29 11:10:21",4]
2025-08-29 11:10:44.219 [async-task-pool99] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1563 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54857,"************",80,"http","[2034630] 漏洞攻击: Hikvision IP Camera RCE Attempt (CVE-2021-36260)","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICIoPzpbXFx4NjBcXHgzYlxceDdjXXwlNjB8JTNifCU3Y3wlMjZ8KD86W1xceDNjXFx4M2VcXHgyNF18JTNjf...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2034630,4,"请求","UFVUIC9TREsvd2ViTGFuZ3VhZ2UgSFRUUC8xLjENCkhvc3Q6IGp4cHhoLmNvbQ0KVXNlci1BZ2VudDogTW96aWxsYS81LjAgK...","[]","2025-08-29 11:10:21",4]
2025-08-29 11:10:44.264 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1581 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54805,"************",80,"http","[2033403] Apache SkyWalking GraphQL SQL Injection 攻击 (CVE-2020-13921)","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICJeXFxzPy57MCwxMDB9KD86U0VMRUNUfFVOSU9OfENIQVJ8TE9OR1ZBUkNIQVJ8U0NIRU1BfEZST018V0hFU...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgoKPCFET0NUWVBFIEhUTUw+C...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2033403,4,"请求","UE9TVCAvZ3JhcGhxbCBIVFRQLzEuMQ0KSG9zdDoganhweGguY29tDQpVc2VyLUFnZW50OiBNb3ppbGxhLzUuMCAoV2luZG93c...","[]","2025-08-29 11:10:21",1]
2025-08-29 11:10:46.207 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1678 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54714,"************",80,"http","[2031221] Citrix XenMobile Server 目录穿越攻击 攻击 (CVE-2020-8209)","网络攻击/漏洞利用/恶意代码攻击","W3siY29udGVudF9oZXgiOiAiNDc0NTU0IiwgImNvbnRlbnRfc3RyaW5nIjogIkdFVCJ9LCB7ImNvbnRlbnRfaGV4IjogIjNmN...",403,"SFRUUC8xLjEgNDAzCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPg0KC...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2031221,4,"请求","R0VUIC9qc3AvaGVscC1zYi1kb3dubG9hZC5qc3A/c2JGaWxlTmFtZT0uLi8uLi8uLi9ldGMvcGFzc3dkIEhUVFAvMS4xDQpIb...","[]","2025-08-29 11:10:18",1]
2025-08-29 11:10:46.403 [async-task-pool99] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1053 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54706,"************",80,"http","[2034125] 漏洞攻击: Apache HTTP Server 2.4.49 - Path Traversal Attempt (CVE-2021-41773) M2","网络攻击/漏洞利用/恶意代码攻击","W3sicGNyZSI6ICJeXFwvKD86aWNvbnN8Y2dpLWJpbikifSwgeyJjb250ZW50X2hleCI6ICIyZjJlMjUzMjY1MmYyZTI1MzI2N...",400,"SFRUUC8xLjEgNDAwCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPjx0a...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2034125,4,"请求","R0VUIC9pY29ucy8uJTJlLy4lMmUvLiUyZS8uJTJlLy4lMmUvLiUyZS8uJTJlL2V0Yy9wYXNzd2QgSFRUUC8xLjENCkhvc3Q6I...","[]","2025-08-29 11:10:20",4]
2025-08-29 11:10:46.529 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11687 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:10:03"]
2025-08-29 11:10:46.989 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16536 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:09:53"]
2025-08-29 11:10:47.707 [async-task-pool99] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1035 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",54714,"************",80,"http","[2031221] Citrix XenMobile Server 目录穿越攻击 攻击 (CVE-2020-8209)","网络攻击/漏洞利用/恶意代码攻击","W3siY29udGVudF9oZXgiOiAiNDc0NTU0IiwgImNvbnRlbnRfc3RyaW5nIjogIkdFVCJ9LCB7ImNvbnRlbnRfaGV4IjogIjNmN...",403,"SFRUUC8xLjEgNDAzCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8aHRtbD4NCjxoZWFkPg0KC...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2031221,4,"请求","R0VUIC9qc3AvaGVscC1zYi1kb3dubG9hZC5qc3A/c2JGaWxlTmFtZT0uLi8uLi8uLi9ldGMvcGFzc3dkIEhUVFAvMS4xDQpIb...","[]","2025-08-29 11:10:18",4]
2025-08-29 11:10:54.455 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7406 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:09:53"]
2025-08-29 11:11:33.732 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 39274 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:09:53"]
2025-08-29 11:11:46.801 [async-task-pool36] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4702 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:11:26"]
2025-08-29 11:11:56.042 [async-task-pool92] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6294 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:11:26"]
2025-08-29 11:12:01.053 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10384 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:11:37"]
2025-08-29 11:12:03.324 [async-task-pool90] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14419 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:11:36"]
2025-08-29 11:12:11.919 [async-task-pool90] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8592 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:11:36"]
2025-08-29 11:13:00.936 [async-task-pool90] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 49010 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:11:36"]
2025-08-29 11:13:05.115 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1154 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:12:38"]
2025-08-29 11:13:10.472 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6686 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:12:26"]
2025-08-29 11:13:15.430 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10295 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:12:38"]
2025-08-29 11:13:18.838 [async-task-pool197] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15782 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:12:27"]
2025-08-29 11:13:26.893 [async-task-pool197] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8051 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:12:27"]
2025-08-29 11:14:11.707 [async-task-pool197] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 44810 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:12:27"]
2025-08-29 11:14:20.496 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4179 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:13:56"]
2025-08-29 11:14:28.949 [async-task-pool121] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6181 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:13:56"]
2025-08-29 11:14:34.204 [async-task-pool150] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10695 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:14:10"]
2025-08-29 11:14:37.673 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15835 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:14:13"]
2025-08-29 11:14:46.766 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9089 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:14:13"]
2025-08-29 11:15:45.980 [hutool-cron-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2516 millis. update tbl_operate_work_record
         SET work_id = ?,
            work_class = ?,
            work_name = ?,
            work_type = ?,
            work_title = ?,
            dept_id = ?,
            create_by = ?,
            create_time = ?,
            f_flowtaskid = ?,
            
            f_flowstate = ?,
            f_handle_user = ? 
        where id = ?
         
            or f_flowtaskid = ?[62,2,"设备日常巡检12312312",1,"设备日常巡检12312312待办",100,"254","2025-08-29 11:15:43","731071078536839237",-1,"254",null,"731071078536839237"]
2025-08-29 11:15:46.093 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 59323 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:14:13"]
2025-08-29 11:15:51.489 [Thread-12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1417 millis. select id, dict_name, dict_array, dict_desc, dict_config from data_dict_change[]
2025-08-29 11:15:53.193 [pool-7-thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1261 millis. insert into tbl_attack_alarm_tags (attack_id, tag_name) values
          
            (?, ?)
         , 
            (?, ?)
         , 
            (?, ?)
         , 
            (?, ?)
         , 
            (?, ?)
         
        ON DUPLICATE KEY UPDATE id = VALUES(id)[1260,"内网端口扫描",1260,"暴力破解",1178,"内网恶意攻击",1260,"内网恶意攻击",1178,"弱口令登录成功"]
2025-08-29 11:15:58.117 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1127 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:15:46"]
2025-08-29 11:15:59.715 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1594 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:15:46"]
2025-08-29 11:16:00.703 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2676 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["************","2025-08-25 16:17:50","2025-08-29 11:15:26"]
2025-08-29 11:16:05.838 [async-task-pool91] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7967 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:15:26"]
2025-08-29 11:16:11.112 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11387 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:15:46"]
2025-08-29 11:16:15.388 [async-task-pool69] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 18399 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:15:44"]
2025-08-29 11:16:24.981 [async-task-pool69] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9590 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:15:44"]
2025-08-29 11:17:18.823 [async-task-pool69] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 53838 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:15:44"]
2025-08-29 11:17:26.678 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1031 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:17:09"]
2025-08-29 11:17:32.716 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6926 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:17:15"]
2025-08-29 11:17:38.639 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11957 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:17:09"]
2025-08-29 11:17:43.276 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 18586 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:17:15"]
2025-08-29 11:17:57.201 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13922 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:17:15"]
2025-08-29 11:18:40.257 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1431 millis. select id, risk_assets, risk_type, risk_info, engine_name, start_time, update_time,
               handle_state, handle_desc, disposer, create_time, create_by, update_by, device_config_id
        from ffsafe_flow_risk_assets
     
        where
         (  
            (risk_assets = ?
             AND risk_type = ?
             AND engine_name = ?
             AND start_time = ?)
          OR  
            (risk_assets = ?
             AND risk_type = ?
             AND engine_name = ?
             AND start_time = ?)
          OR  
            (risk_assets = ?
             AND risk_type = ?
             AND engine_name = ?
             AND start_time = ?)
         )["**************","weak_password","********","2024-10-31 11:52:33","http://twx.hyite.cn/h5_rest/v1/loginStep2","sensitive_info","********","2024-10-30 14:22:32","http://************:82/dp-mng-api/api/oauth/Login","weak_password","********","2025-07-02 10:49:00"]
2025-08-29 11:18:40.258 [async-task-pool118] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1433 millis. select id, risk_assets, risk_type, risk_info, engine_name, start_time, update_time,
               handle_state, handle_desc, disposer, create_time, create_by, update_by, device_config_id
        from ffsafe_flow_risk_assets
     
        where
         (  
            (risk_assets = ?
             AND risk_type = ?
             AND engine_name = ?
             AND start_time = ?)
          OR  
            (risk_assets = ?
             AND risk_type = ?
             AND engine_name = ?
             AND start_time = ?)
          OR  
            (risk_assets = ?
             AND risk_type = ?
             AND engine_name = ?
             AND start_time = ?)
         )["**************","weak_password","********","2024-10-31 11:52:33","http://twx.hyite.cn/h5_rest/v1/loginStep2","sensitive_info","********","2024-10-30 14:22:32","http://************:82/dp-mng-api/api/oauth/Login","weak_password","********","2025-07-02 10:49:00"]
2025-08-29 11:18:57.755 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 60551 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:17:15"]
2025-08-29 11:19:00.328 [async-task-pool154] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1156 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:03:15","2025-08-29 11:15:29"]
2025-08-29 11:19:00.353 [async-task-pool143] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1180 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["************","2025-08-25 08:12:47","2025-08-29 11:15:47"]
2025-08-29 11:19:00.355 [async-task-pool157] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1071 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",45296,"**************",5000,"http","[1780077] 通过curl下载远程文件并尝试授权/执行","异常行为/访问异常","W3sicGNyZSI6ICJjaG1vZFxccys/W3Vnb2Fyd3gwLTdcXCtcXC1dK1xccys/fGV4ZWNcXHMrP3xiYXNoXFxzKz98c2hcXHMrP...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1780077,4,"请求","UEFUQ0ggL3YyL2d6ZHAtbmdpbngvYmxvYnMvdXBsb2Fkcy85ODczZTFjYi1mNzRmLTQ2MWItYmQwNS05YzIyYWY2MmZiYmU/X...","[]","2025-08-29 11:18:55",1]
2025-08-29 11:19:00.356 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1070 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",45296,"**************",5000,"http","[1780077] 通过curl下载远程文件并尝试授权/执行","异常行为/访问异常","W3sicGNyZSI6ICJjaG1vZFxccys/W3Vnb2Fyd3gwLTdcXCtcXC1dK1xccys/fGV4ZWNcXHMrP3xiYXNoXFxzKz98c2hcXHMrP...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1780077,4,"请求","UEFUQ0ggL3YyL2d6ZHAtbmdpbngvYmxvYnMvdXBsb2Fkcy85ODczZTFjYi1mNzRmLTQ2MWItYmQwNS05YzIyYWY2MmZiYmU/X...","[]","2025-08-29 11:18:55",4]
2025-08-29 11:19:00.403 [async-task-pool151] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1001 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-28 10:20:30","2025-08-29 11:16:25"]
2025-08-29 11:19:04.806 [async-task-pool140] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5218 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:18:26"]
2025-08-29 11:19:13.026 [async-task-pool166] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6780 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:18:26"]
2025-08-29 11:19:17.992 [async-task-pool172] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10903 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:18:38"]
2025-08-29 11:19:21.767 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16435 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:18:33"]
2025-08-29 11:19:34.356 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12585 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:18:33"]
2025-08-29 11:20:28.453 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 54093 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:18:33"]
2025-08-29 11:20:44.485 [async-task-pool134] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7823 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:20:14"]
2025-08-29 11:20:48.676 [async-task-pool124] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11267 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:20:11"]
2025-08-29 11:20:53.692 [async-task-pool117] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17972 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:20:17"]
2025-08-29 11:21:03.820 [async-task-pool117] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10122 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:20:17"]
2025-08-29 11:22:00.010 [async-task-pool117] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 56183 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:20:17"]
2025-08-29 11:22:08.900 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1105 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:21:44"]
2025-08-29 11:22:14.033 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6354 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:21:26"]
2025-08-29 11:22:18.828 [async-task-pool119] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9923 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:21:44"]
2025-08-29 11:22:22.924 [async-task-pool127] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16037 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:21:55"]
2025-08-29 11:22:34.706 [async-task-pool127] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11779 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:21:55"]
2025-08-29 11:23:29.004 [async-task-pool127] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 54295 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:21:55"]
2025-08-29 11:23:37.551 [async-task-pool125] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1060 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:23:20"]
2025-08-29 11:23:42.139 [async-task-pool94] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5821 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:23:26"]
2025-08-29 11:23:47.628 [async-task-pool125] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10063 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:23:20"]
2025-08-29 11:23:52.039 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16505 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:23:24"]
2025-08-29 11:24:05.320 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13277 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:23:24"]
2025-08-29 11:24:50.179 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 44855 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:23:24"]
2025-08-29 11:24:56.824 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5163 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:24:15"]
2025-08-29 11:25:07.405 [async-task-pool0] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16484 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:24:15"]
2025-08-29 11:25:22.150 [async-task-pool0] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14738 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:24:15"]
2025-08-29 11:26:07.461 [async-task-pool0] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 45309 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:24:15"]
2025-08-29 11:26:19.516 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1127 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:25:53"]
2025-08-29 11:26:20.923 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1404 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:25:53"]
2025-08-29 11:26:30.118 [async-task-pool21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10425 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:25:56"]
2025-08-29 11:26:37.580 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16654 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:25:53"]
2025-08-29 11:26:41.770 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 23380 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:25:59"]
2025-08-29 11:26:52.903 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11125 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:25:59"]
2025-08-29 11:27:31.325 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38420 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:25:59"]
2025-08-29 11:27:35.333 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1169 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:27:10"]
2025-08-29 11:27:41.425 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6758 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:27:17"]
2025-08-29 11:27:46.921 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11579 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:27:10"]
2025-08-29 11:27:51.081 [async-task-pool73] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17568 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:27:11"]
2025-08-29 11:27:59.873 [async-task-pool73] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8790 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:27:11"]
2025-08-29 11:28:44.258 [async-task-pool73] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 44380 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:27:11"]
2025-08-29 11:28:56.053 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12345 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",5,"sensitive_info",37,"weak_password",1,"['账号:ftpuser, 密码:******']",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",1,"********",5,"********",37,"********",1,"2024-10-31 11:52:33",5,"2024-10-30 14:22:32",37,"2025-07-02 10:49:00",1,"2025-08-29 11:01:56",5,"2025-08-29 10:17:56",37,"2025-08-29 09:09:24",1,4,5,4,37,4,1,5,37]
2025-08-29 11:28:56.072 [taskScheduler-22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14434 millis. select id, device_name, device_ip, device_params, create_time, create_by, update_time, update_by, status,filter_log_last_time,alarm_detail_last_time,risk_asset_last_time,host_intrusion_last_time from tbl_device_config
     
         WHERE  `status` = ?[1]
2025-08-29 11:28:56.082 [pool-7-thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11819 millis. select distinct t1.id, t1.attack_ip, t1.risk_level, t1.location, t1.victim_ip_nums, t1.attack_type_nums, t1.attack_nums,t1.handle_state, t1.start_time, t1.update_time, t1.synchronization_status from tbl_attack_alarm t1
     
         
         WHERE  t1.attack_ip in
                (
                    ?
                ,
                    ?
                ,
                    ?
                ,
                    ?
                ,
                    ?
                ,
                    ?
                ,
                    ?
                ,
                    ?
                ,
                    ?
                ,
                    ?
                ,
                    ?
                ,
                    ?
                ) 
        order by t1.update_time desc["**************","**************","**************","************","*************","**************","***********","*************","************","************","***********","**************"]
2025-08-29 11:28:56.101 [async-task-pool8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14345 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-29 11:28:41","/v2/flow-bypass-filtering-log"]
2025-08-29 11:28:56.101 [async-task-pool89] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14346 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-29 11:28:41","/v2/flow-bypass-filtering-log"]
2025-08-29 11:28:56.107 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12381 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",5,"sensitive_info",37,"weak_password",1,"['账号:ftpuser, 密码:******']",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",1,"********",5,"********",37,"********",1,"2024-10-31 11:52:33",5,"2024-10-30 14:22:32",37,"2025-07-02 10:49:00",1,"2025-08-29 11:01:56",5,"2025-08-29 10:17:56",37,"2025-08-29 09:09:24",1,1,5,1,37,1,1,5,37]
2025-08-29 11:28:56.110 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13915 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 11:26:42",1]
2025-08-29 11:28:56.111 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13908 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 11:26:42",4]
2025-08-29 11:28:56.245 [async-task-pool170] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13536 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",39021,"**************",1521,"tcp","[1300006] 疑似 OracleDB 1521 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300006,2,"请求","","[]","2025-08-29 11:28:35",4]
2025-08-29 11:28:56.336 [async-task-pool172] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13535 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",39021,"**************",1521,"tcp","[1300006] 疑似 OracleDB 1521 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300006,2,"请求","","[]","2025-08-29 11:28:35",1]
2025-08-29 11:28:57.973 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1027 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:28:31"]
2025-08-29 11:28:59.392 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1416 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:28:31"]
2025-08-29 11:29:09.712 [async-task-pool183] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11327 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:28:26"]
2025-08-29 11:29:19.887 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20491 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:28:31"]
2025-08-29 11:29:24.057 [async-task-pool5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 27107 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:28:26"]
2025-08-29 11:29:32.728 [async-task-pool5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8666 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:28:26"]
2025-08-29 11:30:11.601 [async-task-pool5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38870 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:28:26"]
2025-08-29 11:30:16.557 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1364 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:30:04"]
2025-08-29 11:30:17.077 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1049 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:29:56"]
2025-08-29 11:30:18.669 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2108 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:30:04"]
2025-08-29 11:30:25.513 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8432 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:29:56"]
2025-08-29 11:30:31.954 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13279 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:30:04"]
2025-08-29 11:30:35.441 [async-task-pool97] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20247 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:30:02"]
2025-08-29 11:30:43.300 [async-task-pool97] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7856 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:30:02"]
2025-08-29 11:31:17.394 [async-task-pool97] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 34090 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:30:02"]
2025-08-29 11:31:19.669 [async-task-pool170] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1088 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:30:56"]
2025-08-29 11:31:20.371 [async-task-pool8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1793 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:31:04"]
2025-08-29 11:31:22.079 [async-task-pool8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1703 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:31:04"]
2025-08-29 11:31:28.895 [async-task-pool170] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8438 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:30:56"]
2025-08-29 11:31:36.053 [async-task-pool8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13970 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:31:04"]
2025-08-29 11:31:40.560 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 21976 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:31:02"]
2025-08-29 11:31:50.754 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10191 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:31:02"]
2025-08-29 11:32:26.392 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 35632 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:31:02"]
2025-08-29 11:32:30.971 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1228 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:32:09"]
2025-08-29 11:32:32.154 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1179 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:32:09"]
2025-08-29 11:32:36.057 [async-task-pool91] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4978 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:31:56"]
2025-08-29 11:32:47.469 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15311 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:32:09"]
2025-08-29 11:32:52.726 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22983 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:32:09"]
2025-08-29 11:33:09.128 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16271 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:32:09"]
2025-08-29 11:33:53.333 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 44202 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:32:09"]
2025-08-29 11:34:07.850 [async-task-pool41] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8986 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:33:26"]
2025-08-29 11:34:18.593 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20706 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:33:28"]
2025-08-29 11:34:27.406 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8802 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:33:28"]
2025-08-29 11:35:08.822 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 41413 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:33:28"]
2025-08-29 11:35:15.516 [async-task-pool148] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2518 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",40371,"**************",3306,"tcp","[1300005] 疑似 MySQL 3306 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300005,2,"请求","","[]","2025-08-29 11:35:09",4]
2025-08-29 11:35:15.516 [async-task-pool145] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2520 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",40371,"**************",3306,"tcp","[1300005] 疑似 MySQL 3306 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300005,2,"请求","","[]","2025-08-29 11:35:09",1]
2025-08-29 11:35:32.727 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1100 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:34:56"]
2025-08-29 11:35:34.174 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1445 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:34:56"]
2025-08-29 11:35:41.502 [http-nio-8080-exec-37] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8143 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0"]
2025-08-29 11:35:41.570 [http-nio-8080-exec-36] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8192 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:35:41.629 [http-nio-8080-exec-35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8278 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:35:41.884 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8941 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:34:56"]
2025-08-29 11:35:47.203 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13026 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:34:56"]
2025-08-29 11:35:50.709 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19082 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:34:58"]
2025-08-29 11:35:59.382 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8640 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:34:58"]
2025-08-29 11:36:10.682 [http-nio-8080-exec-50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2953 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0"]
2025-08-29 11:36:10.990 [http-nio-8080-exec-38] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3647 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:36:11.729 [http-nio-8080-exec-49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4267 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:36:41.596 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 42181 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:34:58"]
2025-08-29 11:36:52.989 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5449 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:36:26"]
2025-08-29 11:36:56.796 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1477 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:36:43"]
2025-08-29 11:37:03.900 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8406 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:36:26"]
2025-08-29 11:37:09.820 [http-nio-8080-exec-55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3123 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0"]
2025-08-29 11:37:11.042 [http-nio-8080-exec-51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3425 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:37:11.153 [http-nio-8080-exec-57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3914 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:37:11.925 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14722 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:36:43"]
2025-08-29 11:37:15.462 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 21298 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:36:44"]
2025-08-29 11:37:25.363 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9898 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:36:44"]
2025-08-29 11:37:48.518 [http-nio-8080-exec-71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1776 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0"]
2025-08-29 11:37:48.991 [http-nio-8080-exec-60] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2536 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:37:49.287 [http-nio-8080-exec-62] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2818 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:38:23.613 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 58247 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:36:44"]
2025-08-29 11:38:32.566 [async-task-pool141] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1113 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:38:16"]
2025-08-29 11:38:38.452 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6814 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:38:15"]
2025-08-29 11:38:45.697 [async-task-pool141] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13128 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:38:16"]
2025-08-29 11:38:51.112 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20578 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:38:10"]
2025-08-29 11:39:01.362 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10244 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:38:10"]
2025-08-29 11:39:41.775 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 40354 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:38:10"]
2025-08-29 11:39:49.049 [async-task-pool46] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1397 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:39:47"]
2025-08-29 11:39:50.410 [async-task-pool46] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1353 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:39:47"]
2025-08-29 11:39:56.692 [async-task-pool43] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7449 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:39:26"]
2025-08-29 11:40:01.209 [async-task-pool46] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10787 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:39:47"]
2025-08-29 11:40:07.021 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19368 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:39:42"]
2025-08-29 11:40:17.904 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10879 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:39:42"]
2025-08-29 11:41:01.516 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 43608 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:39:42"]
2025-08-29 11:41:06.831 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1675 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:40:50"]
2025-08-29 11:41:15.974 [async-task-pool124] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10816 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:40:26"]
2025-08-29 11:41:21.610 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14776 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:40:50"]
2025-08-29 11:41:25.452 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 21165 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:40:49"]
2025-08-29 11:41:37.223 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11768 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:40:49"]
2025-08-29 11:42:16.069 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38841 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:40:49"]
2025-08-29 11:42:18.959 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1111 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:41:43"]
2025-08-29 11:42:20.394 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1389 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:41:43"]
2025-08-29 11:42:25.795 [async-task-pool59] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6605 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:41:26"]
2025-08-29 11:42:33.451 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13054 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:41:43"]
2025-08-29 11:42:37.236 [async-task-pool40] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19388 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:41:51"]
2025-08-29 11:42:45.622 [async-task-pool40] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8381 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:41:51"]
2025-08-29 11:43:40.498 [async-task-pool40] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 54503 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:41:51"]
2025-08-29 11:43:52.751 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1099 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:43:20"]
2025-08-29 11:43:54.260 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1502 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:43:20"]
2025-08-29 11:44:00.071 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7420 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:43:17"]
2025-08-29 11:44:05.148 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10885 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:43:20"]
2025-08-29 11:44:09.062 [async-task-pool42] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17411 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:43:23"]
2025-08-29 11:44:21.116 [async-task-pool42] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12051 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:43:23"]
2025-08-29 11:45:04.915 [async-task-pool42] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 43792 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:43:23"]
2025-08-29 11:45:20.456 [async-task-pool4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6555 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:44:26"]
2025-08-29 11:45:29.545 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6292 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:44:26"]
2025-08-29 11:45:35.040 [pool-7-thread-1] ERROR c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,517] - 发送攻击告警同步消息失败: attackIp=***********, 错误: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1282)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1243)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at cn.hutool.extra.spring.SpringUtil.getBean(SpringUtil.java:122)
	at com.ruoyi.rabbitmq.domain.SyncMessage.setData(SyncMessage.java:72)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sendDataSyncMessageAsync(TblAttackAlarmServiceImpl.java:510)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sync(TblAttackAlarmServiceImpl.java:293)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$FastClassBySpringCGLIB$$e90ea721.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$EnhancerBySpringCGLIB$$4da08f73.sync(<generated>)
	at com.ruoyi.safe.task.FfSafeFlowAlarmTask.syncTask(FfSafeFlowAlarmTask.java:96)
	at com.ruoyi.ffsafe.api.service.impl.ApiResultSeviceImpl.lambda$dealFlowDetailResult$5(ApiResultSeviceImpl.java:202)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-29 11:46:31.579 [pool-5-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1089 millis. SELECT
        t1.id,
        t1.deduction_date,
        t1.deduction_type,
        t1.deduction_level,
        t1.deduction_score,
        t1.user_id,
        t1.department_id,
        t1.risk_type,
        t1.reference_id,
        t1.created_time,
        t1.created_by
        FROM
        (SELECT * FROM tbl_deduction_detail WHERE is_del = '1') t1
         
         
         
         
         WHERE  t1.deduction_type = ? 
        order by t1.deduction_date desc["主机漏洞"]
2025-08-29 11:46:40.455 [async-task-pool16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1174 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:46:08"]
2025-08-29 11:46:44.632 [pool-4-thread-2] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getFirewallList,458] - 图幻-----获取防火墙列表-----失败： SSLHandshakeException: Remote host terminated the handshake
2025-08-29 11:46:47.644 [async-task-pool13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8262 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:46:26"]
2025-08-29 11:46:50.860 [pool-4-thread-2] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getNatList,492] - 图幻-----获取防火墙NAT列表--失败：SSLHandshakeException: Remote host terminated the handshake
2025-08-29 11:46:55.233 [async-task-pool16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14751 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:46:08"]
2025-08-29 11:46:59.630 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 21173 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:46:27"]
2025-08-29 11:47:08.782 [pool-13-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6726 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id,ta.attack_direction from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-29 11:47:08.979 [pool-13-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6901 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id,ta.attack_direction from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         ) ) and ta.handle_state=0["***************","***************","***************","***************"]
2025-08-29 11:47:09.328 [pool-13-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7251 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id,ta.attack_direction from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         ) 
        or dest_ip in
         (  
            ?
         ) ) and ta.handle_state=0["***************","***************"]
2025-08-29 11:47:09.328 [pool-13-thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7279 millis. select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id,ta.attack_direction from tbl_threaten_alarm ta
     
        where (src_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) 
        or dest_ip in
         (  
            ?
         , 
            ?
         , 
            ?
         ) ) and ta.handle_state=0["**************","***************","***************","**************","***************","***************"]
2025-08-29 11:47:13.672 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14028 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:46:27"]
2025-08-29 11:47:25.210 [http-nio-8080-exec-16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1577 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0"]
2025-08-29 11:47:25.832 [http-nio-8080-exec-17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2131 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:47:25.981 [http-nio-8080-exec-99] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1449 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0"]
2025-08-29 11:47:28.955 [http-nio-8080-exec-94] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2508 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:47:44.233 [http-nio-8080-exec-86] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1355 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ?
            
            
            
            
            
            
            
            
                and ta.attack_direction = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0","1"]
2025-08-29 11:47:44.240 [http-nio-8080-exec-87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1397 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ?
        
        
        
        
        
        
        
        
        
        
        
        
        
            and ta.attack_direction = ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1"]
2025-08-29 11:47:44.244 [http-nio-8080-exec-88] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1295 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ? AND ta.attack_direction = ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1"]
2025-08-29 11:47:57.315 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 43588 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:46:27"]
2025-08-29 11:48:07.068 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8287 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:47:26"]
2025-08-29 11:48:13.267 [async-task-pool104] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13676 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:47:19"]
2025-08-29 11:48:16.980 [async-task-pool101] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19234 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:47:29"]
2025-08-29 11:48:27.384 [async-task-pool101] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10398 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:47:29"]
2025-08-29 11:49:15.139 [async-task-pool101] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 47752 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:47:29"]
2025-08-29 11:49:26.810 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8732 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:48:56"]
2025-08-29 11:49:33.134 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14244 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:48:59"]
2025-08-29 11:49:37.727 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20502 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:49:03"]
2025-08-29 11:49:47.623 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9647 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:49:03"]
2025-08-29 11:49:56.649 [async-task-pool17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3611 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",42092,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","s8wBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-29 11:49:37",1]
2025-08-29 11:49:58.535 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2378 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",42092,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","s8wBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-29 11:49:37",4]
2025-08-29 11:50:33.250 [http-nio-8080-exec-69] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2244 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0"]
2025-08-29 11:50:33.568 [http-nio-8080-exec-68] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2540 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:50:33.708 [http-nio-8080-exec-65] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2256 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0"]
2025-08-29 11:50:35.455 [http-nio-8080-exec-61] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2450 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59"]
2025-08-29 11:50:46.063 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 51642 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:49:03"]
2025-08-29 11:50:48.451 [async-task-pool61] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1099 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:50:28"]
2025-08-29 11:50:55.497 [http-nio-8080-exec-53] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3595 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ?
            
            
            
            
            
            
            
            
                and ta.attack_direction = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0","1"]
2025-08-29 11:50:55.502 [http-nio-8080-exec-58] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3000 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ? AND ta.attack_direction = ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1"]
2025-08-29 11:50:55.504 [http-nio-8080-exec-49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3081 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ?
        
        
        
        
        
        
        
        
        
        
        
        
        
            and ta.attack_direction = ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1"]
2025-08-29 11:50:57.294 [async-task-pool62] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9597 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:50:26"]
2025-08-29 11:50:58.995 [async-task-pool61] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10540 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:50:28"]
2025-08-29 11:51:03.182 [http-nio-8080-exec-50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1325 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ?
            
            
            
            
            
            
            
            
                and ta.attack_direction = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0","1"]
2025-08-29 11:51:03.188 [http-nio-8080-exec-54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1262 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ? AND ta.attack_direction = ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1"]
2025-08-29 11:51:04.336 [http-nio-8080-exec-54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1141 millis. SELECT
        ta.id,
        ta.threaten_name,
        ta.threaten_type,
        tyd.id AS threatenId,
        ta.label,
        ta.loss_state,
        ta.hand_suggest,
        ta.src_ip,
        ta.src_port,
        ta.dest_ip,
        ta.dest_port,
        ta.hit_intelligence,
        ta.alarm_level,
        ta.associa_device,
        ta.create_by,
        ta.create_time,
        ta.update_by,
        ta.update_time,
        ta.handle_state,
        ta.handle_desc,
        ta.data_source,
        ta.alarm_num,
        ta.file_url,
        ta.work_order_id,
        ta.disposer,
        ta.synchronization_status,
        ta.device_config_id
        FROM
        tbl_threaten_alarm ta
        LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type
         WHERE  ta.handle_state = ?
            
             and ta.update_time between ? and ?
            
            
            
            
            
            
            
            
            
            
            
            
                and ta.attack_direction = ? 
        ORDER BY
        ta.update_time DESC
 LIMIT ?, ? ["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1",30,10]
2025-08-29 11:51:04.960 [async-task-pool59] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 18385 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:50:29"]
2025-08-29 11:51:12.879 [http-nio-8080-exec-55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1864 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ?
            
            
            
            
            
            
            
            
                and ta.attack_direction = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0","1"]
2025-08-29 11:51:13.412 [http-nio-8080-exec-56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1724 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ? AND ta.attack_direction = ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1"]
2025-08-29 11:51:15.051 [http-nio-8080-exec-56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1631 millis. SELECT
        ta.id,
        ta.threaten_name,
        ta.threaten_type,
        tyd.id AS threatenId,
        ta.label,
        ta.loss_state,
        ta.hand_suggest,
        ta.src_ip,
        ta.src_port,
        ta.dest_ip,
        ta.dest_port,
        ta.hit_intelligence,
        ta.alarm_level,
        ta.associa_device,
        ta.create_by,
        ta.create_time,
        ta.update_by,
        ta.update_time,
        ta.handle_state,
        ta.handle_desc,
        ta.data_source,
        ta.alarm_num,
        ta.file_url,
        ta.work_order_id,
        ta.disposer,
        ta.synchronization_status,
        ta.device_config_id
        FROM
        tbl_threaten_alarm ta
        LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type
         WHERE  ta.handle_state = ?
            
             and ta.update_time between ? and ?
            
            
            
            
            
            
            
            
            
            
            
            
                and ta.attack_direction = ? 
        ORDER BY
        ta.update_time DESC
 LIMIT ? ["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1",50]
2025-08-29 11:51:16.446 [async-task-pool59] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11483 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:50:29"]
2025-08-29 11:51:30.218 [async-task-pool99] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12870 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",39,"sensitive_info",5,"sensitive_info",31,"sensitive_info",37,"weak_password",1,"['账号:ftpuser, 密码:******']",39,"['35**************56', '35**************37', '46**************1X', '52**************34', '35*****...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",31,"['36**************27', '36**************34', '36**************1X', '36**************24', '36*****...",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",1,"********",39,"********",5,"********",31,"********",37,"********",1,"2024-10-31 11:52:33",39,"2024-10-29 14:41:00",5,"2024-10-30 14:22:32",31,"2024-10-30 13:58:43",37,"2025-07-02 10:49:00",1,"2025-08-29 11:31:56",39,"2025-08-29 11:31:21",5,"2025-08-29 11:29:22",31,"2025-08-29 11:29:20",37,"2025-08-29 09:09:24",1,4,39,4,5,4,31,4,37,4,1,39,5,31,37]
2025-08-29 11:51:30.226 [async-task-pool108] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5878 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 11:49:23",1]
2025-08-29 11:51:30.232 [async-task-pool105] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5854 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-29 11:51:24","/v2/flow-bypass-filtering-log"]
2025-08-29 11:51:30.233 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12880 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",39,"sensitive_info",5,"sensitive_info",31,"sensitive_info",37,"weak_password",1,"['账号:ftpuser, 密码:******']",39,"['35**************56', '35**************37', '46**************1X', '52**************34', '35*****...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",31,"['36**************27', '36**************34', '36**************1X', '36**************24', '36*****...",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",1,"********",39,"********",5,"********",31,"********",37,"********",1,"2024-10-31 11:52:33",39,"2024-10-29 14:41:00",5,"2024-10-30 14:22:32",31,"2024-10-30 13:58:43",37,"2025-07-02 10:49:00",1,"2025-08-29 11:31:56",39,"2025-08-29 11:31:21",5,"2025-08-29 11:29:22",31,"2025-08-29 11:29:20",37,"2025-08-29 09:09:24",1,1,39,1,5,1,31,1,37,1,1,39,5,31,37]
2025-08-29 11:51:30.236 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5747 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-29 11:51:24","/v2/flow-bypass-filtering-log"]
2025-08-29 11:51:30.246 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5883 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 11:49:23",4]
2025-08-29 11:51:30.312 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7320 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",37381,"**************",22,"ssh","[1800502] SSH短时间多次登录失败(30秒5次登录失败)","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiNmY3MDY1NmU3MzczNjg1ZiIsICJjb250ZW50X3N0cmluZyI6ICJvcGVuc3NoXyJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1800502,2,"响应","U1NILTIuMC1PcGVuU1NIXzUuMw0K","[]","2025-08-29 11:51:14",1]
2025-08-29 11:51:30.375 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7359 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",37381,"**************",22,"ssh","[1800502] SSH短时间多次登录失败(30秒5次登录失败)","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiNmY3MDY1NmU3MzczNjg1ZiIsICJjb250ZW50X3N0cmluZyI6ICJvcGVuc3NoXyJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1800502,2,"响应","U1NILTIuMC1PcGVuU1NIXzUuMw0K","[]","2025-08-29 11:51:14",4]
2025-08-29 11:51:54.419 [async-task-pool59] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 37968 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:50:29"]
2025-08-29 11:51:56.000 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1010 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:51:30"]
2025-08-29 11:51:57.483 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1460 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:51:30"]
2025-08-29 11:52:04.843 [async-task-pool133] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8842 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:51:26"]
2025-08-29 11:52:10.748 [http-nio-8080-exec-43] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1849 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ?
            
            
            
            
            
            
            
            
                and ta.attack_direction = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0","1"]
2025-08-29 11:52:10.755 [http-nio-8080-exec-44] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2054 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ?
        
        
        
        
        
        
        
        
        
        
        
        
        
            and ta.attack_direction = ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1"]
2025-08-29 11:52:11.202 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13716 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:51:30"]
2025-08-29 11:52:15.251 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20261 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:51:27"]
2025-08-29 11:52:24.788 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9306 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:51:27"]
2025-08-29 11:53:04.086 [http-nio-8080-exec-47] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2168 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ? AND ta.attack_direction = ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1"]
2025-08-29 11:53:04.912 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 40119 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:51:27"]
2025-08-29 11:53:07.274 [async-task-pool8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1258 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:53:04"]
2025-08-29 11:53:08.802 [async-task-pool8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1524 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:53:04"]
2025-08-29 11:53:17.982 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10870 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:52:56"]
2025-08-29 11:53:25.153 [async-task-pool8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16344 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:53:04"]
2025-08-29 11:53:31.294 [async-task-pool11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 25302 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:53:04"]
2025-08-29 11:53:41.255 [http-nio-8080-exec-37] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1570 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ?
        
        
        
        
        
        
        
        
        
        
        
        
        
            and ta.attack_direction = ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1"]
2025-08-29 11:53:41.328 [http-nio-8080-exec-36] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1654 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ?
            
            
            
            
            
            
            
            
                and ta.attack_direction = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0","1"]
2025-08-29 11:53:43.866 [async-task-pool11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12568 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:53:04"]
2025-08-29 11:53:57.024 [http-nio-8080-exec-38] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1894 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ? AND ta.attack_direction = ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1"]
2025-08-29 11:54:22.114 [async-task-pool11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38244 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:53:04"]
2025-08-29 11:54:23.951 [async-task-pool91] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1098 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 11:54:09"]
2025-08-29 11:54:28.399 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4373 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:53:56"]
2025-08-29 11:54:36.568 [async-task-pool91] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11708 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:54:09"]
2025-08-29 11:54:40.190 [async-task-pool94] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17336 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:54:04"]
2025-08-29 11:54:54.851 [async-task-pool94] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14657 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:54:04"]
2025-08-29 11:55:40.409 [async-task-pool94] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 45554 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:54:04"]
2025-08-29 11:55:43.939 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1103 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:55:29"]
2025-08-29 11:55:51.008 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7851 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:55:26"]
2025-08-29 11:55:57.700 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13756 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:55:29"]
2025-08-29 11:56:02.336 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20116 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:55:35"]
2025-08-29 11:56:13.215 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10873 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:55:35"]
2025-08-29 11:56:24.978 [http-nio-8080-exec-34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3501 millis. select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
     WHERE  ta.handle_state = ?
        
         and ta.update_time between ? and ?
        
        
        
        
        
        
        
        
        
        
        
        
        
            and ta.attack_direction = ? 
    group by ttd.attack_seg["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1"]
2025-08-29 11:56:24.985 [http-nio-8080-exec-31] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3500 millis. select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
         WHERE  ta.update_time BETWEEN ? AND ?
            
            
            
            
            
            
            
            
            
            
            
            
            
            
             and ta.handle_state = ?
            
            
            
            
            
            
            
            
                and ta.attack_direction = ? 
        group by alarm_level["2025-08-22 00:00:00","2025-08-29 23:59:59","0","1"]
2025-08-29 11:56:28.743 [http-nio-8080-exec-35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2661 millis. SELECT count(0) FROM tbl_threaten_alarm ta LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type WHERE ta.handle_state = ? AND ta.update_time BETWEEN ? AND ? AND ta.attack_direction = ?["0","2025-08-22 00:00:00","2025-08-29 23:59:59","1"]
2025-08-29 11:57:01.544 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 48285 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:55:35"]
2025-08-29 11:57:13.111 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4579 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:56:26"]
2025-08-29 11:57:18.182 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1197 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:57:10"]
2025-08-29 11:57:24.453 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7246 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:56:56"]
2025-08-29 11:57:29.403 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11156 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:57:10"]
2025-08-29 11:57:33.693 [async-task-pool197] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17481 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:57:01"]
2025-08-29 11:57:43.546 [async-task-pool197] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9849 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:57:01"]
2025-08-29 11:58:19.646 [async-task-pool197] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36094 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:57:01"]
2025-08-29 11:58:24.927 [async-task-pool72] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1119 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 11:58:09"]
2025-08-29 11:58:28.411 [async-task-pool80] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4634 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:57:56"]
2025-08-29 11:58:38.290 [async-task-pool72] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13356 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:58:09"]
2025-08-29 11:58:47.272 [async-task-pool74] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 24279 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:58:09"]
2025-08-29 11:58:58.387 [async-task-pool74] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10970 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:58:09"]
2025-08-29 11:59:42.326 [async-task-pool74] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 43935 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:58:09"]
2025-08-29 11:59:54.470 [async-task-pool101] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7055 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 11:59:26"]
2025-08-29 12:00:01.878 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16189 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 11:59:36"]
2025-08-29 12:00:09.973 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8091 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 11:59:36"]
2025-08-29 12:00:47.856 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 37840 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 11:59:36"]
2025-08-29 12:01:09.853 [async-task-pool121] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5762 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 12:00:26"]
2025-08-29 12:01:13.141 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1001 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 12:00:44"]
