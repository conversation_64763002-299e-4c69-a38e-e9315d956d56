{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\application\\index.vue?vue&type=template&id=7fe2f36b&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\application\\index.vue", "mtime": 1756369455990}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}