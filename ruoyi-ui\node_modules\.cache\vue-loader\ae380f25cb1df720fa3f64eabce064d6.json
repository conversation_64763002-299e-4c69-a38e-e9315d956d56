{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\honeypotAlarmList.vue?vue&type=style&index=1&id=40160132&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\honeypotAlarmList.vue", "mtime": 1756381475129}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5hc3NldC10YWcgewogIG1heC13aWR0aDogMTAwJTsKICBvdmVyZmxvdzogaGlkZGVuOwogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsKfQoKLmVsLXRvb2x0aXBfX3BvcHBlciB7CiAgZm9udC1zaXplOiAxMnB4OwogIG1heC13aWR0aDogMzAwcHg7Cn0KCi5vdmVyZmxvdy10YWc6bm90KDpmaXJzdC1jaGlsZCkgewogIG1hcmdpbi10b3A6IDVweDsKfQouYmxvY2tpbmctZm9ybSB7CiAgOjp2LWRlZXAgLmVsLWZvcm0taXRlbV9fbGFiZWwgewogICAgZmxvYXQ6IG5vbmU7CiAgfQp9Cg=="}, {"version": 3, "sources": ["honeypotAlarmList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA25CA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "honeypotAlarmList.vue", "sourceRoot": "src/views/frailty/event/component", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"最近告警时间\" label-width=\"98px\">\n                <el-date-picker\n                  v-model=\"rangeTime\"\n                  type=\"datetimerange\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                  :default-time=\"['00:00:00', '23:59:59']\">\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"6\">\n              <el-form-item label=\"告警等级\" prop=\"alarmLevel\">\n                <el-select\n                  clearable\n                  v-model=\"queryParams.alarmLevel\"\n                  placeholder=\"请选择告警等级\"\n                >\n                  <el-option\n                    v-for=\"dict in dict.type.threaten_type\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置状态\" prop=\"\">\n                <el-select v-model=\"queryParams.handleState\" placeholder=\"请选择处置状态\" clearable>\n                  <el-option :key=\"item.value\" :label=\"item.label\" :value=\"item.value\"\n                             v-for=\"(item,index) in handleStateOptions\"/>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警类型\" prop=\"threatenType\">\n                <el-cascader v-model=\"queryParams.threatenType\" :options=\"threatenDict\" clearable\n                             :props=\"{ label: 'dictLabel', value: 'dictValue' }\" placeholder=\"请选择告警类型\">\n                  <template slot-scope=\"{ node, data }\">\n                    <span>{{ data.dictLabel }}</span>\n                    <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n                  </template>\n                </el-cascader>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\">查询\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警名称\" prop=\"threatenName\">\n                <el-input\n                  v-model=\"queryParams.threatenName\"\n                  placeholder=\"请输入告警名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"源IP\" prop=\"srcIp\">\n                <el-input\n                  v-model=\"queryParams.srcIp\"\n                  placeholder=\"请输入源IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"目标IP\" prop=\"destIp\">\n                <el-input\n                  v-model=\"queryParams.destIp\"\n                  placeholder=\"请输入目标IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"通报状态\" prop=\"flowState\">\n                <el-select v-model=\"queryParams.flowState\" placeholder=\"请选择通报状态\" clearable>\n                  <el-option :key=\"item.value\" :label=\"item.label\" :value=\"item.value\"\n                             v-for=\"(item,index) in flowStateOptions\"/>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置人\">\n                <el-input\n                  v-model=\"queryParams.disposer\"\n                  placeholder=\"请输入处置人\"\n                  clearable\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"同步状态\">\n                <el-select v-model=\"queryParams.synchronizationStatus\" placeholder=\"请选择同步状态\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.synchronization_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属部门\" prop=\"deptId\">\n                <dept-select v-model=\"queryParams.deptId\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属探针\">\n                <el-select v-model=\"queryParams.deviceConfigId\" filterable clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in deviceConfigList\"\n                    :key=\"item.id\"\n                    :label=\"item.deviceName\"\n                    :value=\"item.id\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n<!--      <div class=\"custom-content-search-chunk\" style=\"margin-bottom: 8px\">\n        <attack-stage ref=\"atcAge\" @handleClick=\"handleAtcAgeClick\"/>\n      </div>-->\n      <div class=\"custom-content-container\"\n           :style=\"showAll ? { height: 'calc(100% - 298px)' } :{ height: 'calc(100% - 208px)' }\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">蜜罐告警列表</span></div>\n          <div style=\"width: 60%; margin-left: 10%;\">\n<!--            <attack-stage-text ref=\"atcAge\" @handleClick=\"handleAtcAgeClick\" :dataSource=\"7\" />-->\n            <attack-stage-text ref=\"atcAge\" :dataSource=\"7\" />\n          </div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-col :span=\"1.5\">\n                  <el-button\n                    class=\"btn1\"\n                    size=\"small\"\n                    @click=\"handleBlocking\"\n                  >批量阻断</el-button>\n                </el-col>\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                  v-hasPermi=\"['system:threadten:export']\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          height=\"100%\"\n          v-loading=\"loading\"\n          ref=\"multipleTable\"\n          @row-click=\"handleRowClick\"\n          @selection-change=\"handleSelectionChange\"\n          :data=\"threatenWarnList\">\n          <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n<!--          <el-table-column type=\"index\" width=\"100\" label=\"序号\"/>-->\n          <el-table-column label=\"最近告警时间\" width=\"200\" prop=\"updateTime\"/>\n          <el-table-column label=\"告警名称\" prop=\"threatenName\" min-width=\"260\"/>\n          <el-table-column label=\"告警类型\" prop=\"threatenType\" width=\"150\"/>\n          <el-table-column label=\"告警等级\" prop=\"alarmLevel\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <dict-tag :options=\"dict.type.threaten_type\" :value=\"scope.row.alarmLevel\"/>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"源IP\" prop=\"srcIp\" width=\"180\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: flex-start\">\n                <span>{{ scope.row.srcIp }}</span>\n                <img v-if=\"scope.row.isBlocking\" style=\"width: 24px;margin-left: 10px\" src=\"@/assets/images/block.png\" alt=\"\">\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"目标IP/应用\" width=\"150\" prop=\"destIp\">\n          </el-table-column>\n          <el-table-column label=\"处置人\" prop=\"disposer\" width=\"150\" :formatter=\"disposerFormatter\">\n          </el-table-column>\n          <el-table-column label=\"关联业务系统\" prop=\"businessApplicationList\" width=\"200\">\n            <template slot-scope=\"scope\">\n              <el-tooltip placement=\"bottom-end\" effect=\"light\"\n                          v-if=\"scope.row.businessApplications && scope.row.businessApplications.length > 0\">\n                <div slot=\"content\">\n                  <div v-for=\"(item,tagIndex) in scope.row.businessApplications\" :key=\"item.assetId\"\n                       class=\"overflow-tag\" v-if=\"tagIndex <= 5\">\n                    <el-tag type=\"primary\"><span>{{ item.assetName }}</span></el-tag>\n                  </div>\n                  <div v-if=\"scope.row.businessApplications.length > 5\">\n                    <el-tag type=\"primary\"><span>...</span></el-tag>\n                  </div>\n                </div>\n                <el-tag type=\"primary\" class=\"asset-tag\">\n                  <span>{{ handleApplicationTagShow(scope.row.businessApplications) }}</span></el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"通报状态\" prop=\"flowState\" width=\"150\" :formatter=\"flowStateFormatter\"/>\n          <el-table-column label=\"处置状态\" prop=\"handleState\" width=\"150\" :formatter=\"handleStateFormatter\"/>\n          <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"150\"/>\n          <el-table-column label=\"发现次数\" prop=\"alarmNum\" width=\"150\"/>\n          <el-table-column label=\"同步状态\" prop=\"synchronizationStatus\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.synchronizationStatus === '0'\">未同步</span>\n              <span v-else-if=\"scope.row.synchronizationStatus === '1'\">已同步</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"250\" fixed=\"right\" :show-overflow-tooltip=\"false\"\n                           class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleDetail(scope.row)\"\n                v-hasPermi=\"['system:threadten:query']\"\n              >详情\n              </el-button>\n              <el-button v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                         size=\"mini\"\n                         type=\"text\"\n                         @click=\"handleEdit(scope.row)\"\n                         v-hasPermi=\"['system:threadten:edit']\"\n              >编辑\n              </el-button>\n              <el-button v-if=\"scope.row.workId==null && !(scope.row.handleState === '3')\"\n                         size=\"mini\"\n                         type=\"text\"\n                         class=\"table-delBtn\"\n                         @click=\"handleDelete(scope.row)\"\n                         v-hasPermi=\"['system:threadten:remove']\"\n              >删除\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"showHandle(scope.row)\"\n                v-hasPermi=\"['system:threadten:edit']\"\n              >处置\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                v-if=\"!(scope.row.handleState === '1' || scope.row.handleState === '3') && (scope.row.flowState == null || scope.row.flowState === '99')\"\n                @click=\"addOrUpdateFlowHandle(null,null,scope.row)\"\n              >创建通报\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n    <!-- 处置威胁情报对话框! -->\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"handleStateForm\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\n          <el-select v-model=\"handleForm.handleState\" clearable placeholder=\"请选择处置状态\">\n            <el-option v-for=\"dict in dict.type.handle_state\"\n                       :key=\"dict.value\" :label=\"dict.label\"\n                       :value=\"parseInt(dict.value)\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"handleForm.handleDesc\" placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleForm\">确 定</el-button>\n        <el-button @click=\"showHandleDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 添加或修改威胁情报对话框! -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"openThrenten\"\n      width=\"80%\"\n      append-to-body\n      :before-close=\"handleClose\"\n    >\n      <el-row>\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"106px\" :disabled=\"!editable\">\n          <el-col :span=\"24\" class=\"mb8\">\n            <el-divider direction=\"vertical\"></el-divider>\n            <div class=\"my-title\">基本信息</div>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"告警名称\" prop=\"threatenName\">\n              <el-input v-model=\"form.threatenName\" placeholder=\"请输入告警名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警等级\" prop=\"alarmLevel\">\n              <el-select v-model=\"form.alarmLevel\" placeholder=\"请选择告警等级\" clearable>\n                <el-option\n                  v-for=\"dict in dict.type.threaten_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警类型\" prop=\"threatenType\">\n              <el-cascader v-model=\"form.threatenType\" :options=\"threatenDict\" clearable placeholder=\"请选择告警类型\"\n                           :props=\"{ label: 'dictLabel', value: 'dictValue' }\" style=\"width: 100%\">\n                <template slot-scope=\"{ node, data }\">\n                  <span>{{ data.dictLabel }}</span>\n                  <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n                </template>\n              </el-cascader>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"原始报文\" prop=\"playload\">\n              <el-input v-model=\"form.playload\" :autosize=\"{minRows: 3, maxRows: 8}\" type=\"textarea\"\n                        placeholder=\"\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"处置建议\" prop=\"handSuggest\">\n              <el-input v-model=\"form.handSuggest\" :autosize=\"{minRows: 3, maxRows: 3}\" type=\"textarea\"\n                        placeholder=\"请输入告警建议\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警时间\" prop=\"createTime\">\n              <el-date-picker\n                v-model=\"form.createTime\"\n                type=\"date\"\n                placeholder=\"选择告警时间\"\n                format=\"yyyy 年 MM 月 dd 日\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"最近告警时间\" prop=\"updateTime\">\n              <el-date-picker\n                v-model=\"form.updateTime\"\n                type=\"date\"\n                placeholder=\"选择最近告警时间\"\n                format=\"yyyy 年 MM 月 dd 日\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"威胁标签\" prop=\"label\">\n              <DynamicTag v-model=\"form.label\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"关联设备\" prop=\"associaDevice\">\n              <el-input\n                v-model=\"form.associaDevice\"\n                placeholder=\"请输入关联设备\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\" class=\"mb8\">\n            <el-divider direction=\"vertical\"></el-divider>\n            <div class=\"my-title\">攻击关系</div>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"源IP\" prop=\"srcIp\">\n              <el-input\n                style=\"width: 50%\"\n                v-model=\"form.srcIp\"\n                placeholder=\"请输入源IP\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"源IP端口\" prop=\"srcPort\">\n              <el-input\n                style=\"width: 30%\"\n                v-model=\"form.srcPort\"\n                placeholder=\"请输入源IP端口\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"目标IP/应用\" prop=\"destIp\">\n              <el-input\n                style=\"width: 50%\"\n                v-model=\"form.destIp\"\n                placeholder=\"请输入目标IP\"\n              ></el-input>\n              <!--目标ip查询后有多个及显示资产数据，提交表单传(assetId： 资产id,deptId：部门id)-->\n              <el-select style=\"width: 50%;\" v-show=\"assetInfoList.length >= 2\" v-model=\"form.assetId\"\n                         placeholder=\"请确认疑似资产\">\n                <el-option v-for=\"item in assetInfoList\" :key=\"item.assetId\" :label=\"item.value\"\n                           :value=\"item.assetId\"></el-option>\n              </el-select>\n              <el-select style=\"width: 50%;\" v-show=\"false\" v-model=\"form.deptId\" placeholder=\"请选择资产\">\n                <el-option v-for=\"item in assetInfoList\" :label=\"item.value\" :value=\"item.deptId\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"目标IP端口\" prop=\"destPort\">\n              <el-input\n                style=\"width: 30%\"\n                v-model=\"form.destPort\"\n                placeholder=\"请输入目标IP端口\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col v-if=\"form.fileUrl!=null||editable\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\"></el-divider>\n              <div class=\"my-title\">文件上传</div>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"上传文件\" prop=\"fileUrl\">\n                <file-upload v-model=\"form.fileUrl\"\n                             :disUpload=\"!editable\"\n                             :limit=\"5\"\n                             :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button v-if=\"editable\" type=\"primary\" @click=\"submitForm\"\n        >确 定\n        </el-button\n        >\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      v-if=\"openDialog\"\n      :title=\"title\"\n      :visible.sync=\"openDialog\"\n      width=\"80%\"\n      class=\"my-dialog\"\n      append-to-body\n    >\n      <el-tabs type=\"border-card\" v-model=\"activeName\">\n        <el-tab-pane label=\"事件详情\" name=\"detail\">\n          <alarm-detail\n            v-if=\"openDialog\"\n            @openDetail=\"openDetail\"\n            :data-source=\"7\"\n            :asset-data=\"assetData\"\n          />\n        </el-tab-pane>\n        <el-tab-pane v-if=\"assetData.srcIp\" label=\"攻击IP关联事件\" name=\"attack\">\n          <attack-detail :detail-type=\"'attack'\" :host-ip=\"assetData.srcIp\" :current-asset-data=\"assetData\"/>\n        </el-tab-pane>\n        <el-tab-pane v-if=\"assetData.destIp\" label=\"受害IP关联事件\" name=\"suffer\">\n          <suffer-detail :detail-type=\"'suffer'\" :host-ip=\"assetData.destIp\" :current-asset-data=\"assetData\"/>\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n\n    <el-dialog title=\"导入威胁告警\" :visible.sync=\"importDialog\" width=\"800px\" append-to-body>\n      <import-threaten @closeDialog=\"closeDialog\" v-if=\"importDialog\"/>\n    </el-dialog>\n\n    <el-dialog title=\"查看服务器资产\" :visible.sync=\"serverOpen\" width=\"80%\" append-to-body>\n      <server-add :asset-id=\"assetId\" @cancel=\"closeAssetDialog()\" :editable=\"editable\" v-if=\"serverOpen\"/>\n    </el-dialog>\n\n    <el-dialog title=\"查看安全设备资产\" :visible.sync=\"safeOpen\" width=\"80%\" append-to-body>\n      <safe-add :asset-id=\"assetId\" @cancel=\"closeAssetDialog()\" :editable=\"editable\" v-if=\"safeOpen\"/>\n    </el-dialog>\n\n    <el-dialog title=\"查看告警策略\" :visible.sync=\"viewStrategy\" width=\"400\" append-to-body>\n      <view-strategy v-if=\"viewStrategy\" @close=\"viewStrategy=false\"/>\n    </el-dialog>\n\n\n    <el-dialog title=\"告警策略配置\" :visible.sync=\"threatenConfigFlag\" width=\"800\" append-to-body>\n      <threaten-config-list v-if=\"threatenConfigFlag\" @close=\"threatenConfigFlag=false\"/>\n    </el-dialog>\n\n    <el-dialog title=\"批量阻断\" :visible.sync=\"blockingDialogVisible\" width=\"400\">\n      <el-form :model=\"blockingForm\" :rules=\"blockingRules\" ref=\"blockingForm\" class=\"blocking-form\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"阻断ip\" prop=\"block_ip\">\n              <span slot=\"label\">\n                阻断ip\n                <template>\n                  <el-tooltip placement=\"top\">\n                    <div slot=\"content\">默认加载选择的事件的源IP，多个则以“;”隔开</div>\n                    <i class=\"el-icon-info\"></i>\n                  </el-tooltip>\n                </template>\n              </span>\n              <el-input v-model=\"blockingForm.block_ip\" placeholder=\"请输入ip\">\n                <el-popover\n                  slot=\"suffix\"\n                  placement=\"bottom\"\n                  width=\"100\"\n                  trigger=\"hover\"\n                >\n                  <ul>\n                    <li v-for=\"(ip, index) in blockingIpList\" :key=\"index\">{{ ip }}</li>\n                  </ul>\n                  <i slot=\"reference\" class=\"el-icon-more\"></i>\n                </el-popover>\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"阻断时长\" prop=\"duration_time\">\n              <el-select v-model=\"blockingForm.duration_time\" placeholder=\"请选择阻断时长\">\n                <el-option\n                  v-for=\"item in blockingDuration\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-form-item label=\"备注\" prop=\"remarks\">\n            <el-input v-model=\"blockingForm.remarks\" type=\"textarea\" maxlength=\"500\" show-word-limit placeholder=\"请输入阻断描述\"></el-input>\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"blockingSubmit\">确 定</el-button>\n        <el-button @click=\"blockingDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <FlowBox v-if=\"flowVisible\" ref=\"FlowBox\" @close=\"closeFlow\"/>\n    <flow-template-select :show.sync=\"flowTemplateSelectVisible\" @change=\"flowTemplateSelectChange\"/>\n\n    <publish-click-dialog\n      :publish-dialog-visible=\"publishDialogVisible\"\n      @updateVisible=\"(val) => { this.publishDialogVisible = val}\"\n      title=\"发布告警事件\"\n      width=\"30%\"/>\n  </div>\n</template>\n\n<script>\nimport {parseTime} from \"@/utils/ruoyi\";\nimport {getMulTypeDict} from \"@/api/system/dict/data\";\nimport {getDeptSystem} from \"@/api/monitor2/applicationAssets\";\nimport {getAlarm, delAlarm, listAlarm, addAlarm, updateAlarm,addBlockIp} from \"@/api/threaten/honeypotAlarm\";\nimport {getAssetInfoByIp} from \"@/api/safe/overview\";\nimport DynamicTag from \"../../../../components/DynamicTag\";\nimport AlarmDetail from \"../../../basis/securityWarn/alarmDetail\";\nimport importThreaten from \"@/views/basis/securityWarn/importThreaten.vue\"\nimport ThreatenConfigList from \"@/views/basis/securityWarn/threatenConfigList.vue\"\nimport ServerAdd from \"../../../hhlCode/component/application/adds/serverAdd\";\nimport SafeAdd from \"../../../hhlCode/component/application/adds/safeAdd\";\nimport ViewStrategy from \"../../../basis/securityWarn/viewStrategy\";\nimport PublishClickDialog from \"../../../basis/securityWarn/publishClickDialog\";\nimport FlowBox from \"../../../zeroCode/workFlow/components/FlowBox\";\nimport FlowTemplateSelect from \"../../../../components/FlowTemplateSelect\";\nimport AttackStage from \"../../../threat/overview/attackStage\";\nimport AttackViewList from \"./attackViewList\";\nimport SufferViewList from \"./sufferViewList\";\nimport attackDetail from \"./detail/index.vue\";\nimport sufferDetail from \"./detail/index.vue\";\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport {uniqueArr} from '@/utils'\nimport {FlowEngineInfo} from \"@/api/lowCode/FlowEngine\";\nimport {listUser} from \"@/api/system/user\";\nimport AttackStageText from '@/views/threat/overview/attackStageText.vue'\nimport {listDeviceConfig} from \"@/api/ffsafe/deviceConfig\";\n\nexport default {\n  name: \"eventList\",\n  components: {\n    AttackStageText,\n    DeptSelect,\n    SufferViewList,\n    AttackViewList,\n    AttackStage,\n    FlowTemplateSelect,\n    FlowBox,\n    PublishClickDialog,\n    attackDetail,\n    sufferDetail,\n    ThreatenConfigList, ViewStrategy, SafeAdd, ServerAdd, importThreaten, AlarmDetail, DynamicTag\n  },\n  dicts: ['threaten_type', 'attack_stage', 'attack_result','handle_state', 'synchronization_status'],\n  props: {\n    propsActiveName: {\n      type: String\n    },\n    propsQueryParams: {\n      type: Object,\n      default: function () {\n        return null\n      }\n    },\n    currentBtn: {\n      type: Number\n    }\n  },\n  data() {\n    let validateBlockIp = (rule, value, callback) => {\n      if (!value) {\n        return callback(new Error('IP不能为空'));\n      }\n      // let pattern = /^((1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2})\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0)\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0)\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0))$/;\n      let pattern = /^\\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\\s*;\\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))*\\s*$/;\n      if (!pattern.test(value)) {\n        return callback(new Error('请输入正确的IP'));\n      }\n      return callback();\n    };\n    return {\n      deviceConfigList: [],\n      userList: [],\n      showHandleDialog: false,\n      handleForm: {\n        id: '',\n        handleDesc: '',\n        handleState: ''\n      },\n      handleRules: {\n        handleState: [\n          {required: true, message: '请选择处理状态', trigger: 'blur'},\n        ]\n      },\n      showAll: false,\n      threatenDict: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        handleState: '0',\n      },\n      deptOptions: [],\n      rangeTime: [],\n      loading: false,\n      threatenWarnList: [],\n      total: 0,\n      title: '',\n      openThrenten: false,\n      form: {},\n      rules: {\n        threatenName: [\n          {required: false, min: 0, max: 500, message: '告警名称不能超过500字符', trigger: 'blur'},\n          {required: true, message: '请输入告警名称', trigger: 'blur'},\n          {\n            required: true,\n            pattern: /^[^\\s]+/,\n            message: '不能以空格开头！',\n            trigger: 'blur'\n          }\n        ],\n        alarmLevel: [\n          {required: true, message: '请输入告警等级', trigger: 'blur'},\n        ],\n        threatenType: [\n          {required: true, message: '请输入告警类型', trigger: 'blur'},\n        ],\n        reason: [\n          {required: false, min: 0, max: 2000, message: '告警原因不能超过2000字符', trigger: 'blur'},\n          {required: true, message: '请输入告警原因', trigger: 'blur'},\n        ],\n        handSuggest: [\n          {required: false, min: 0, max: 2000, message: '告警建议不能超2000字符', trigger: 'blur'},\n          {required: false, message: '请输入告警建议', trigger: 'blur'},\n        ],\n        logTime: [\n          {required: true, message: '请输入日志时间', trigger: 'blur'},\n        ],\n        createTime: [\n          {required: true, message: '请输入告警时间', trigger: 'blur'},\n        ],\n        srcIp: [\n          {required: false, min: 0, max: 30, message: '源IP不能超过30字符', trigger: 'blur'},\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: \"IP地址不能为空或格式不正确\",\n            trigger: \"blur\"\n          },\n        ],\n        srcPort: [\n          {required: false, min: 0, max: 11, message: '源IP端口不能超过11字符', trigger: 'blur'},\n          {required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '源IP端口不能为空或格式不正确', trigger: 'blur'},\n        ],\n        destIp: [\n          {required: false, min: 0, max: 30, message: '目标IP不能超过30字符', trigger: 'blur'},\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: \"IP地址不能为空或格式不正确\",\n            trigger: \"blur\"\n          },\n        ],\n        destPort: [\n          {required: false, min: 0, max: 11, message: '目标IP端口不能超过11字符', trigger: 'blur'},\n          {required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '目标IP端口不能为空或格式不正确', trigger: 'blur'},\n        ],\n        mateRule: [\n          {required: false, min: 0, max: 200, message: '分析规则不能超过200字符', trigger: 'blur'},\n        ],\n        associaDevice: [\n          {required: false, min: 0, max: 200, message: '关联设备不能超过200字符', trigger: 'blur'},\n        ],\n        attackType: [\n          {required: false, min: 0, max: 100, message: '攻击方式不能超过100字符', trigger: 'blur'},\n          {required: true, message: '请输入攻击方式', trigger: 'blur'},\n        ],\n        attackStage: [\n          {required: false, min: 0, max: 100, message: '攻击链阶段不能超过100字符', trigger: 'blur'},\n          {required: true, message: '请输入攻击链阶段', trigger: 'blur'},\n        ],\n        attackResult: [\n          {required: false, min: 0, max: 100, message: '攻击结果不能超过100字符', trigger: 'blur'},\n          {required: true, message: '请输入攻击结果', trigger: 'blur'},\n        ],\n      },\n      blockingForm: {},\n      blockingRules: {\n        block_ip: [\n          //可同时传多个，用\";\"隔开\n          { validator: validateBlockIp, trigger: 'blur' },\n        ],\n        duration_time: [\n          {required: true, message: '请选择阻断时长', trigger: 'blur'},\n        ],\n        remarks: [\n          {required: false, min: 0, max: 500, message: '备注不能超过500字符', trigger: 'blur'},\n        ]\n      },\n      blockingIpList: [],\n      blockingDialogVisible: false, // 批量阻断弹窗\n      editable: true,\n      assetInfoList: [],\n      openDialog: false,\n      assetData: {},\n      importDialog: false,\n      serverOpen: false,\n      assetId: null,\n      safeOpen: false,\n      threatenConfigFlag: false,\n      viewStrategy: false,\n      publishDialogVisible: false,\n      flowVisible: false,\n      flowTemplateSelectVisible: false,\n      flowStateOptions: [\n        {\n          label: '待审核',\n          value: 0\n        },\n        {\n          label: '待处置',\n          value: 1\n        },\n        {\n          label: \"待反馈审核\",\n          value: 2,\n        },\n        {\n          label: '待验证',\n          value: 3\n        },\n        {\n          label: '已完成',\n          value: 4\n        },\n        {\n          label: '待提交',\n          value: -1\n        },\n        {\n          label: '未分配',\n          value: 99\n        }\n      ],\n      handleStateOptions: [\n        {\n          label: '未处置',\n          value: '0'\n        },\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        },\n        {\n          label: '处置中',\n          value: '3'\n        }\n      ],\n      activeName: 'detail',\n      syncStateOptions: [\n        {\n          label: '未同步',\n          value: 0\n        },\n        {\n          label: '已同步',\n          value: 1\n        }\n      ],\n      blockingDuration: [\n        {\n          label: '30分钟',\n          value: '30m'\n        },\n        {\n          label: '24小时',\n          value: '24h'\n        },\n        {\n          label: '48小时',\n          value: '48h'\n        },\n        {\n          label: '7天',\n          value: '168h'\n        },\n        {\n          label: '永久',\n          value: '永久'\n        }\n      ],\n      multipleSelection: []\n    }\n  },\n  watch: {\n    // 监听目标ip\n    'form.destIp'(value, oldValue) {\n      var rg = /^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$/;\n      var reg = rg.test(value);\n      if (reg) {\n        // 根据ip获取资产数据\n        getAssetInfoByIp(value).then(response => {\n          if (response.data.length) {\n            let assetData = response.data;\n            assetData.forEach(item => item.value = item.assetName + '-' + item.assetTypeDesc);\n            if (value !== oldValue && oldValue) {\n              this.form.assetId = ''\n              this.form.deptId = ''\n            }\n            // 资产数据有多条显示下拉框，只有一条不显示\n            if (assetData.length === 1) {\n              this.form.assetId = assetData[0].assetId\n              this.form.deptId = assetData[0].deptId\n            }\n            if (assetData.length > 1 && !this.form.assetId) {\n              this.form.assetId = ''\n              this.form.deptId = ''\n            }\n            this.assetInfoList = assetData;\n          } else {\n            this.assetInfoList = [];\n            return this.$message.warning('未查询到资产数据');\n          }\n        })\n      } else {\n        this.assetInfoList = [];\n        this.form.assetId = '';\n        this.form.deptId = '';\n      }\n    },\n    propsActiveName() {\n      this.init()\n    },\n    propsQueryParams: {\n      handler(val) {\n        this.handlePropsQuery(val);\n      }\n    },\n    /*rangeTime(val) {\n      console.log(val)\n    },*/\n    'blockingForm.block_ip': {\n      handler(value) {\n        if (value) {\n          this.blockingIpList = value.split(';').map(ip => ip.trim()).filter(ip => ip);\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getDeviceConfigList();\n  },\n  mounted() {\n    if (!this.$route.query || Object.keys(this.$route.query).length < 1) {\n      this.init()\n    } else {\n      this.handlePropsQuery(this.$route.query);\n    }\n  },\n  methods: {\n    getDeviceConfigList(){\n      listDeviceConfig({queryAllData: true}).then(res => {\n        this.deviceConfigList = res.rows;\n      })\n    },\n    init() {\n      //this.resetQuery()\n      this.getThreatenDict()\n      this.handleQuery()\n      this.getDeptsData()\n      this.getUserList()\n    },\n    getUserList(){\n      listUser({pageNum:1,pageSize:1000}).then(res=>{\n        if (res.rows){\n          this.userList = res.rows\n        }\n      })\n    },\n    handleQuery() {\n      this.propsQueryParams.alarmLevel = this.queryParams.alarmLevel\n      this.$emit('update:currentBtn',this.queryParams.alarmLevel?parseInt(this.queryParams.alarmLevel) : null)\n      this.queryParams = {...this.queryParams,...this.propsQueryParams};\n      if (this.rangeTime != null) {\n        this.queryParams.startTime = parseTime(this.rangeTime[0]);\n        this.queryParams.endTime = parseTime(this.rangeTime[1]);\n      } else {\n        this.queryParams.startTime = null;\n        this.queryParams.endTime = null;\n      }\n      this.queryParams.pageNum = 1;\n      this.queryParams.pageSize = 10;\n\n      if(!this.queryParams.startTime){\n        this.queryParams.startTime = parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'); // 一周前，时间部分为 00:00:00\n      }\n      if(!this.queryParams.endTime){\n        this.queryParams.endTime = parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'); // 当前日期，时间部分为 23:59:59\n      }\n      this.rangeTime = [this.queryParams.startTime, this.queryParams.endTime];\n      this.total = 0;\n      this.getList();\n      this.$nextTick(() => {\n        const data = JSON.parse(JSON.stringify(this.queryParams))\n        if (data.threatenType != null) {\n          data.threatenType = data.threatenType.join('/');\n        }\n        this.$refs.atcAge.initAttackStage(data)\n      })\n    },\n    // 获取告警类型多级字典数据\n    getThreatenDict() {\n      getMulTypeDict({\n        dictType: 'threaten_alarm_type'\n      }).then(res => {\n        this.threatenDict = res.data;\n      })\n    },\n    // 获取部门数据\n    getDeptsData() {\n      getDeptSystem().then(res => this.deptOptions = res.data)\n    },\n    handleChange(val) {\n      // 获取所属部门最后id\n      if (val) {\n        this.queryParams.deptId = val[val.length - 1];\n      } else {\n        this.queryParams.deptId = '';\n      }\n    },\n    resetQuery() {\n      this.queryParams = {\n        threatenName: null,\n        threatenType: null,\n        alarmLevel: null,\n        srcIp: null,\n        destIp: null,\n        handleState: '0',\n        flowState: null,\n        updateTime: null,\n        pageNum: 1,\n        pageSize: 10\n      };\n      let atcAge = this.$refs.atcAge;\n      if (atcAge) {\n        atcAge.currentSelectedCard = null;\n      }\n      this.rangeTime = null;\n      this.handleQuery();\n    },\n    //新增威胁情报\n    handleAdd() {\n      this.openThrenten = true;\n      this.form = {};\n      this.editable = true;\n      this.title = \"新增威胁情报\";\n      this.$set(this.form, 'assetId', ''); // 解决el-select无法视图与数据的更新\n    },\n    // 导入功能\n    handleImport() {\n      this.importDialog = true;\n    },\n    handleExport() {\n      this.download(\n        \"/system/honeypotAlarm/export\",\n        {\n          ...this.queryParams,\n        },\n        `威胁告警_${new Date().getTime()}.xlsx`\n      );\n    },\n\n    // 获取列表数据查询\n    handleRowClick(row, column, event) {\n      // 获取告警详情单个单元格数据进行筛选\n      if (row && row.id) {\n        if (column.property) {\n          if (column.property === 'flowState') {\n            this.queryParams[column.property] = !row[column.property] ? 99 : Number(row[column.property]);\n            listAlarm({\n              [column.property]: !row[column.property] ? 99 : Number(row[column.property]),\n              pageNum: 1,\n              pageSize: 10,\n              startTime: parseTime(this.rangeTime[0]),\n              endTime: parseTime(this.rangeTime[1]),\n            }).then(response => {\n              this.threatenWarnList = response.rows;\n              this.threatenWarnList.forEach(item => {\n                item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc;\n                if (item.assetType == 'null-null') {\n                  item.assetType = null;\n                }\n              });\n              this.total = response.total;\n              this.loading = false;\n            });\n            return;\n          } else if (column.property === 'threatenType') {\n            this.queryParams[column.property] = row[column.property].split('/');\n          } else if (column.property === 'alarmLevel') {\n            this.queryParams[column.property] = row[column.property].toString();\n          } else {\n            this.queryParams[column.property] = row[column.property];\n          }\n          listAlarm({\n            [column.property]: row[column.property],\n            pageNum: 1,\n            pageSize: 10,\n            startTime: parseTime(this.rangeTime[0]),\n            endTime: parseTime(this.rangeTime[1]),\n          }).then(response => {\n            this.threatenWarnList = response.rows;\n            this.threatenWarnList.forEach(item => {\n              item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc;\n              if (item.assetType == 'null-null') {\n                item.assetType = null;\n              }\n            });\n            this.total = response.total;\n            this.loading = false;\n          });\n        }\n      }\n    },\n\n    // 多选\n    handleSelectionChange(val) {\n      this.multipleSelection = val;\n    },\n\n    flowStateFormatter(row, column, cellValue, index) {\n      let name = '未分配';\n      let match = this.flowStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    disposerFormatter(row, column, cellValue, index){\n      let name = '';\n      if (cellValue){\n        this.userList.forEach(e => {\n          if (e.userId == cellValue){\n            name = e.nickName\n          }\n        })\n        return name;\n      }\n      return name;\n    },\n\n\n    handleStateFormatter(row, column, cellValue, index) {\n      let name = '未处置';\n      let match = this.handleStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    handleDetail(row) {\n      this.assetData = {...row};\n      this.title = \"查看告警详情\";\n      this.openDetail(true);\n    },\n    showHandle(row) {\n      // 获取事件详情单个单元格数据进行筛选\n      if (row.handleState === '1' || row.handleState === '2' ) {\n        this.handleForm.handleState = parseInt(row.handleState);\n        this.handleForm.handleDesc = row.handleDesc;\n      }else {\n        this.handleForm = {\n          handleDesc: '',\n          handleState: ''\n        }\n      }\n      this.handleForm.id = row.id;\n      this.showHandleDialog = true;\n    },\n    handleEdit(row) {\n      getAlarm(row.id).then(res => {\n        this.form = {...res.data};\n        if (this.form.alarmLevel != null) {\n          this.form.alarmLevel = (this.form.alarmLevel).toString();\n        }\n        if (this.form.threatenType != null) {\n          this.form.threatenType = this.form.threatenType.split('/');\n        }\n        if (this.form.attackNum != null) {\n          this.form.attackNum = (this.form.attackNum).toString();\n        }\n        if (this.form.srcPort != null) {\n          this.form.srcPort = (this.form.srcPort).toString();\n        }\n        if (this.form.destPort != null) {\n          this.form.destPort = (this.form.destPort).toString();\n        }\n        this.title = \"修改威胁情报\";\n        this.openThrenten = true;\n      })\n    },\n    handleDelete(row) {\n      const ids = row.id;\n      const title = row.threatenName;\n      this.$modal.confirm('是否确认删除告警名称为【' + title + '】的数据项?').then(() => {\n        return delAlarm(ids);\n      }).then(() => {\n        this.$message.success(\"删除成功\");\n        this.getList();\n      }).catch(() => {\n\n      })\n    },\n    addOrUpdateFlowHandle(id, flowState, row) {\n      let data = {\n        id: id || '',\n        formType: 1,\n        opType: flowState ? 0 : '-1',\n        status: flowState,\n        row: row,\n        isWork: true\n      }\n      data.row.workType = '2';\n      data.row.eventType = 3;\n      data.originType = 'event';\n      this.currentFlowData = data;\n      this.loading = true;\n      this.getConfigKey(\"default.flowTemplateId\").then(res => {\n        let flowId = res.msg;\n        if (flowId) {\n          this.getFlowEngineInfo(flowId);\n        } else {\n          this.flowTemplateSelectVisible = true;\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    getFlowEngineInfo(val) {\n      FlowEngineInfo(val).then(res => {\n        if (res.data && res.data.flowTemplateJson) {\n          let data = JSON.parse(res.data.flowTemplateJson);\n          if (!data[0].flowId) {\n            this.$message.error('该流程模板异常,请重新选择');\n          } else {\n            this.currentFlowData.flowId = data[0].flowId;\n            this.flowVisible = true;\n            this.$nextTick(() => {\n              this.$refs.FlowBox.init(this.currentFlowData);\n            });\n          }\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    getList() {\n      this.loading = true;\n      let queryParams = {\n        ...this.queryParams\n      };\n      if (queryParams.threatenType != null) {\n        queryParams.threatenType = queryParams.threatenType.join('/');\n      }\n      //同步请求类型统计数据\n      this.$emit('getList',{...queryParams});\n      listAlarm(queryParams).then(response => {\n        this.threatenWarnList = response.rows;\n        this.threatenWarnList.forEach(item => {\n          item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc;\n          if (item.assetType == 'null-null') {\n            item.assetType = null;\n          }\n          if (item.deptName) {\n            let deptNameArr = uniqueArr(item.deptName.split(','));\n            item.deptName = deptNameArr.join(',');\n          }\n        });\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleClose(done) {\n      done();\n      this.form = {};\n      this.$refs.form.resetFields();\n    },\n    submitHandleForm() {\n      this.$refs[\"handleStateForm\"].validate(valid => {\n        if (valid) {\n          updateAlarm(this.handleForm).then(res => {\n            this.$message.success(\"处置成功\");\n            this.handleForm = {};\n            this.showHandleDialog = false;\n            this.getList();\n          })\n        }\n      });\n    },\n    submitForm() {\n      if (this.form.threatenType != null) {\n        this.form.threatenType = this.form.threatenType.join('/');\n      }\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id == null) {\n            addAlarm(this.form).then(res => {\n              this.$message.success(\"新增成功\");\n              this.form = {};\n              this.openThrenten = false;\n              this.getList();\n            })\n          } else {\n            updateAlarm(this.form).then(res => {\n              this.$message.success(\"修改成功\");\n              this.form = {};\n              this.openThrenten = false;\n              this.getList();\n            })\n          }\n        }\n      })\n    },\n    cancel() {\n      this.openThrenten = false;\n      this.$refs.form.resetFields();\n    },\n    openDetail(val) {\n      this.openDialog = val;\n    },\n    closeDialog() {\n      this.importDialog = false;\n      this.handleQuery();\n    },\n    closeAssetDialog() {\n      this.serverOpen = false;\n      this.safeOpen = false;\n      this.networkOpen = false;\n    },\n    closeFlow(isrRefresh) {\n      this.flowVisible = false\n      if (isrRefresh) this.getList();\n    },\n    flowTemplateSelectChange(val) {\n      this.flowTemplateSelectVisible = false;\n      this.flowVisible = true;\n      this.currentFlowData.flowId = val;\n      this.$nextTick(() => {\n        this.$refs.FlowBox.init(this.currentFlowData)\n      })\n    },\n    handleAtcAgeClick(atcAge) {\n      this.queryParams.attackSeg = atcAge;\n      this.handleQuery();\n    },\n    handlePropsQuery(val) {\n      if (val && Object.keys(val).length > 0) {\n        if (val.attackSeg && this.$refs.atcAge) {\n          this.$refs.atcAge.currentSelectedCard = val.attackSeg;\n        }\n        this.queryParams = val;\n        if (val.startTime && val.endTime) {\n          this.rangeTime = [val.startTime, val.endTime];\n        }\n        if (val.handle == '1') {\n          this.queryParams.handleState = '1'\n        } else if (val.handle == '0') {\n          this.queryParams.handleState = '0'\n        }\n        if (val.datasource) {\n          this.queryParams.dataSource = parseInt(val.datasource);\n        }\n        this.getThreatenDict()\n        this.getDeptsData()\n        this.handleQuery()\n      }\n    },\n    handleApplicationTagShow(applicationList) {\n      if (!applicationList || applicationList.length < 1) {\n        return '';\n      }\n      let result = applicationList[0].assetName;\n      if (applicationList.length > 1) {\n        result += '...';\n      }\n      return result;\n    },\n\n    handleBlocking() {\n      if (this.multipleSelection.length < 1) return this.$message.warning('请选择要阻断的ip');\n      this.blockingDialogVisible = true;\n      let arr = this.multipleSelection.map(item => item.srcIp);\n      arr = Array.from(new Set(arr));\n      this.$set(this.blockingForm,'block_ip',arr.join(';'));\n    },\n    blockingSubmit() {\n      this.$refs[\"blockingForm\"].validate(valid => {\n        if (valid) {\n          addBlockIp(this.blockingForm).then(res => {\n            this.$message.success('添加成功');\n          }).finally(() => {\n            this.blockingDialogVisible = false;\n            this.$refs.multipleTable.clearSelection();\n            this.multipleSelection = [];\n          })\n        }\n      })\n    },\n  }\n}\n</script>\n\n<style scoped>\n.el-divider {\n  background: #0E94EA;\n}\n\n.el-divider--vertical {\n  display: inline-block;\n  width: 5px;\n  height: 2em;\n  margin: 0 8px 0 0;\n  vertical-align: middle;\n  position: relative;\n}\n\n.my-title {\n  display: inline-block;\n  vertical-align: center;\n}\n\n\n.asset-tag {\n  margin-left: 5px;\n}\n</style>\n<style lang=\"scss\" scoped>\n.asset-tag {\n  max-width: 100%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n\n.el-tooltip__popper {\n  font-size: 12px;\n  max-width: 300px;\n}\n\n.overflow-tag:not(:first-child) {\n  margin-top: 5px;\n}\n.blocking-form {\n  ::v-deep .el-form-item__label {\n    float: none;\n  }\n}\n</style>\n"]}]}