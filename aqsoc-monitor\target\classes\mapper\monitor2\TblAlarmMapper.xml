<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblAlarmMapper">
    
    <resultMap type="TblAlarm" id="TblAlarmResult">
        <result property="id"    column="ID"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="type"    column="type"    />
        <result property="greed"    column="greed"    />
        <result property="curtime"    column="curtime"    />
        <result property="isread"    column="isread"    />
        <result property="userid"    column="userid"    />
        <result property="ptime"    column="ptime"    />
        <result property="flag"    column="flag"    />
    </resultMap>

    <sql id="selectTblAlarmVo">
        select ID, title, content, type, greed, curtime, isread, userid, ptime, flag from tbl_alarm
    </sql>

    <select id="selectTblAlarmList" parameterType="TblAlarm" resultMap="TblAlarmResult">
        <include refid="selectTblAlarmVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="greed != null  and greed != ''"> and greed = #{greed}</if>
            <if test="curtime != null "> and curtime = #{curtime}</if>
            <if test="isread != null  and isread != ''"> and isread = #{isread}</if>
            <if test="userid != null  and userid != ''"> and userid = #{userid}</if>
            <if test="ptime != null "> and ptime = #{ptime}</if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
        </where>
    </select>
    
    <select id="selectTblAlarmById" parameterType="Long" resultMap="TblAlarmResult">
        <include refid="selectTblAlarmVo"/>
        where ID = #{id}
    </select>

    <select id="selectTblAlarmByQuery" parameterType="TblAlarm" resultMap="TblAlarmResult">
        <include refid="selectTblAlarmVo"/>
        where flag=#{flag} and title =#{title} and type =#{type} order by curtime desc limit 1
    </select>

    <insert id="insertTblAlarm" parameterType="TblAlarm" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_alarm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="greed != null">greed,</if>
            <if test="curtime != null">curtime,</if>
            <if test="isread != null">isread,</if>
            <if test="userid != null">userid,</if>
            <if test="ptime != null">ptime,</if>
            <if test="flag != null">flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="greed != null">#{greed},</if>
            <if test="curtime != null">#{curtime},</if>
            <if test="isread != null">#{isread},</if>
            <if test="userid != null">#{userid},</if>
            <if test="ptime != null">#{ptime},</if>
            <if test="flag != null">#{flag},</if>
         </trim>
    </insert>

    <update id="updateTblAlarm" parameterType="TblAlarm">
        update tbl_alarm
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="greed != null">greed = #{greed},</if>
            <if test="curtime != null">curtime = #{curtime},</if>
            <if test="isread != null">isread = #{isread},</if>
            <if test="userid != null">userid = #{userid},</if>
            <if test="ptime != null">ptime = #{ptime},</if>
            <if test="flag != null">flag = #{flag},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTblAlarmById" parameterType="Long">
        delete from tbl_alarm where ID = #{id}
    </delete>


</mapper>