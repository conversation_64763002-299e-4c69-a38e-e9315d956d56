<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.BaseTopoConfigMapper">

    <resultMap type="com.ruoyi.monitor2.domain.BaseTopoConfig" id="BaseTopoConfigResult">
        <result property="fId"    column="F_Id"    />
        <result property="fToponame"    column="F_TopoName"    />
        <result property="fResolutionx"    column="F_ResolutionX"    />
        <result property="fResolutiony"    column="F_ResolutionY"    />
        <result property="fTopodesc"    column="F_TopoDesc"    />
        <result property="fPublishstate"    column="F_PublishState"    />
        <result property="fRefreshinterval"    column="F_RefreshInterval"    />
        <result property="fTopojson"    column="F_TopoJson"    />
        <result property="fCreateby"    column="F_CreateBy"    />
        <result property="fCreatetime"    column="F_CreateTime"    />
        <result property="fUpdateby"    column="F_UpdateBy"    />
        <result property="fUpdatetime"    column="F_UpdateTime"    />
        <result property="fTopouuid"    column="F_TopoUuid"    />
        <result property="fTopoimageid"    column="F_TopoImageId"    />
    </resultMap>

    <sql id="selectBaseTopoConfigVo">
        select F_Id, F_TopoName, F_ResolutionX, F_ResolutionY, F_TopoDesc, F_PublishState, F_RefreshInterval, F_TopoJson, F_CreateBy, F_CreateTime, F_UpdateBy, F_UpdateTime, F_TopoUuid, F_TopoImageId from base_topo_config
    </sql>

    <select id="selectBaseTopoConfigList" parameterType="BaseTopoConfig" resultMap="BaseTopoConfigResult">
        <include refid="selectBaseTopoConfigVo"/>
        <where>
            <if test="fToponame != null  and fToponame != ''"> and F_TopoName like concat('%', #{fToponame}, '%')</if>
            <if test="fResolutionx != null  and fResolutionx != ''"> and F_ResolutionX = #{fResolutionx}</if>
            <if test="fResolutiony != null  and fResolutiony != ''"> and F_ResolutionY = #{fResolutiony}</if>
            <if test="fTopodesc != null  and fTopodesc != ''"> and F_TopoDesc = #{fTopodesc}</if>
            <if test="fPublishstate != null "> and F_PublishState = #{fPublishstate}</if>
            <if test="fRefreshinterval != null "> and F_RefreshInterval = #{fRefreshinterval}</if>
            <if test="fTopojson != null  and fTopojson != ''"> and F_TopoJson = #{fTopojson}</if>
            <if test="fCreateby != null  and fCreateby != ''"> and F_CreateBy = #{fCreateby}</if>
            <if test="fCreatetime != null "> and F_CreateTime = #{fCreatetime}</if>
            <if test="fUpdateby != null  and fUpdateby != ''"> and F_UpdateBy = #{fUpdateby}</if>
            <if test="fUpdatetime != null "> and F_UpdateTime = #{fUpdatetime}</if>
            <if test="fTopouuid != null  and fTopouuid != ''"> and F_TopoUuid = #{fTopouuid}</if>
            <if test="fTopoimageid != null  and fTopoimageid != ''"> and F_TopoImageId = #{fTopoimageid}</if>
            <if test="updateTimeStart !=null and updateTimeEnd !=null"> and F_UpdateTime between #{updateTimeStart} and #{updateTimeEnd}</if>
        </where>
        ORDER BY F_CreateTime DESC
    </select>

    <select id="selectBaseTopoConfigByFId" parameterType="String" resultMap="BaseTopoConfigResult">
        <include refid="selectBaseTopoConfigVo"/>
        where F_Id = #{fId}
    </select>

    <select id="selectBaseTopoConfigByFIds" parameterType="String" resultMap="BaseTopoConfigResult">
        <include refid="selectBaseTopoConfigVo"/>
        where F_Id in
        <foreach item="fId" collection="array" open="(" separator="," close=")">
            #{fId}
        </foreach>
    </select>

    <select id="getAssetTotal" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            (
                SELECT
                    COUNT(*)
                FROM
                    tbl_business_application
            ) businessSystemCount,
            (
                SELECT
                    COUNT(*)
                FROM
                    tbl_network_devices
            ) networkDeviceCount,
            COUNT(*) safetyCount,
            (
                SELECT
                    COUNT(*)
                FROM
                    tbl_server
            ) serverCount
        FROM
            tbl_safety
    </select>

    <insert id="insertBaseTopoConfig" parameterType="BaseTopoConfig">
        insert into base_topo_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fId != null">F_Id,</if>
            <if test="fToponame != null">F_TopoName,</if>
            <if test="fResolutionx != null">F_ResolutionX,</if>
            <if test="fResolutiony != null">F_ResolutionY,</if>
            <if test="fTopodesc != null">F_TopoDesc,</if>
            <if test="fPublishstate != null">F_PublishState,</if>
            <if test="fRefreshinterval != null">F_RefreshInterval,</if>
            <if test="fTopojson != null">F_TopoJson,</if>
            <if test="fCreateby != null">F_CreateBy,</if>
            <if test="fCreatetime != null">F_CreateTime,</if>
            <if test="fUpdateby != null">F_UpdateBy,</if>
            <if test="fUpdatetime != null">F_UpdateTime,</if>
            <if test="fTopouuid != null">F_TopoUuid,</if>
            <if test="fTopoimageid != null">F_TopoImageId,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fId != null">#{fId},</if>
            <if test="fToponame != null">#{fToponame},</if>
            <if test="fResolutionx != null">#{fResolutionx},</if>
            <if test="fResolutiony != null">#{fResolutiony},</if>
            <if test="fTopodesc != null">#{fTopodesc},</if>
            <if test="fPublishstate != null">#{fPublishstate},</if>
            <if test="fRefreshinterval != null">#{fRefreshinterval},</if>
            <if test="fTopojson != null">#{fTopojson},</if>
            <if test="fCreateby != null">#{fCreateby},</if>
            <if test="fCreatetime != null">#{fCreatetime},</if>
            <if test="fUpdateby != null">#{fUpdateby},</if>
            <if test="fUpdatetime != null">#{fUpdatetime},</if>
            <if test="fTopouuid != null">#{fTopouuid},</if>
            <if test="fTopoimageid != null">#{fTopoimageid},</if>
         </trim>
    </insert>

    <update id="updateBaseTopoConfig" parameterType="BaseTopoConfig">
        update base_topo_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="fToponame != null">F_TopoName = #{fToponame},</if>
            <if test="fResolutionx != null">F_ResolutionX = #{fResolutionx},</if>
            <if test="fResolutiony != null">F_ResolutionY = #{fResolutiony},</if>
            <if test="fTopodesc != null">F_TopoDesc = #{fTopodesc},</if>
            <if test="fPublishstate != null">F_PublishState = #{fPublishstate},</if>
            <if test="fRefreshinterval != null">F_RefreshInterval = #{fRefreshinterval},</if>
            <if test="fTopojson != null">F_TopoJson = #{fTopojson},</if>
            <if test="fCreateby != null">F_CreateBy = #{fCreateby},</if>
            <if test="fCreatetime != null">F_CreateTime = #{fCreatetime},</if>
            <if test="fUpdateby != null">F_UpdateBy = #{fUpdateby},</if>
            <if test="fUpdatetime != null">F_UpdateTime = #{fUpdatetime},</if>
            <if test="fTopouuid != null">F_TopoUuid = #{fTopouuid},</if>
            <if test="fTopoimageid != null">F_TopoImageId = #{fTopoimageid},</if>
        </trim>
        where F_Id = #{fId}
    </update>

    <delete id="deleteBaseTopoConfigByFId" parameterType="String">
        delete from base_topo_config where F_Id = #{fId}
    </delete>

    <delete id="deleteBaseTopoConfigByFIds" parameterType="String">
        delete from base_topo_config where F_Id in
        <foreach item="fId" collection="array" open="(" separator="," close=")">
            #{fId}
        </foreach>
    </delete>
</mapper>
