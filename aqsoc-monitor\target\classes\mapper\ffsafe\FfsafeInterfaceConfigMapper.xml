<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeInterfaceConfigMapper">

    <resultMap type="FfsafeInterfaceConfig" id="FfsafeInterfaceConfigResult">
        <result property="id"    column="id"    />
        <result property="interfaceName"    column="interface_name"    />
        <result property="interfaceCnName"    column="interface_cn_name"    />
        <result property="interfacePath"    column="interface_path"    />
        <result property="type"    column="type"    />
        <result property="interfaceMethod"    column="interface_method"    />
        <result property="dataLastTime"    column="data_last_time"    />
    </resultMap>

    <sql id="selectFfsafeInterfaceConfigVo">
        select id, interface_name, interface_cn_name, interface_path, type, interface_method, data_last_time from ffsafe_interface_config
    </sql>

    <select id="selectFfsafeInterfaceConfigList" parameterType="FfsafeInterfaceConfig" resultMap="FfsafeInterfaceConfigResult">
        <include refid="selectFfsafeInterfaceConfigVo"/>
        <where>
            <if test="interfaceName != null  and interfaceName != ''"> and interface_name like concat('%', #{interfaceName}, '%')</if>
            <if test="interfaceCnName != null  and interfaceCnName != ''"> and interface_cn_name like concat('%', #{interfaceCnName}, '%')</if>
            <if test="interfacePath != null  and interfacePath != ''"> and interface_path = #{interfacePath}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="interfaceMethod != null "> and interface_method = #{interfaceMethod}</if>
            <if test="dataLastTime != null "> and data_last_time = #{dataLastTime}</if>
        </where>
    </select>

    <select id="selectFfsafeInterfaceConfigById" parameterType="Long" resultMap="FfsafeInterfaceConfigResult">
        <include refid="selectFfsafeInterfaceConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeInterfaceConfigByIds" parameterType="Long" resultMap="FfsafeInterfaceConfigResult">
        <include refid="selectFfsafeInterfaceConfigVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeInterfaceConfig" parameterType="FfsafeInterfaceConfig" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_interface_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="interfaceName != null">interface_name,</if>
            <if test="interfaceCnName != null">interface_cn_name,</if>
            <if test="interfacePath != null">interface_path,</if>
            <if test="type != null">type,</if>
            <if test="interfaceMethod != null">interface_method,</if>
            <if test="dataLastTime != null">data_last_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="interfaceName != null">#{interfaceName},</if>
            <if test="interfaceCnName != null">#{interfaceCnName},</if>
            <if test="interfacePath != null">#{interfacePath},</if>
            <if test="type != null">#{type},</if>
            <if test="interfaceMethod != null">#{interfaceMethod},</if>
            <if test="dataLastTime != null">#{dataLastTime},</if>
        </trim>
    </insert>

    <update id="updateFfsafeInterfaceConfig" parameterType="FfsafeInterfaceConfig">
        update ffsafe_interface_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="interfaceName != null">interface_name = #{interfaceName},</if>
            <if test="interfaceCnName != null">interface_cn_name = #{interfaceCnName},</if>
            <if test="interfacePath != null">interface_path = #{interfacePath},</if>
            <if test="type != null">type = #{type},</if>
            <if test="interfaceMethod != null">interface_method = #{interfaceMethod},</if>
            <if test="dataLastTime != null">data_last_time = #{dataLastTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateDataLastTime" parameterType="FfsafeInterfaceConfig">
        update ffsafe_interface_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataLastTime != null">data_last_time = #{dataLastTime},</if>
        </trim>
        where
        interface_path = #{interfacePath}
        <if test="type != null "> and type = #{type}</if>
    </update>

    <delete id="deleteFfsafeInterfaceConfigById" parameterType="Long">
        delete from ffsafe_interface_config where id = #{id}
    </delete>

    <delete id="deleteFfsafeInterfaceConfigByIds" parameterType="String">
        delete from ffsafe_interface_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>