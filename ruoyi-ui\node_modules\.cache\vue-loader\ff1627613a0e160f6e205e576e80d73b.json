{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\machroom\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\machroom\\index.vue", "mtime": 1756381475352}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7bGlzdE1hY2hyb29tLCBnZXRNYWNocm9vbSwgZGVsTWFjaHJvb20sIGFkZE1hY2hyb29tLCB1cGRhdGVNYWNocm9vbX0gZnJvbSAiQC9hcGkvc2FmZS9tYWNocm9vbSI7CmltcG9ydCB7bGlzdERlcHQsIGxpc3REZXB0RXhjbHVkZUNoaWxkfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGVwdCI7CmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsKaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7CmltcG9ydCB7Z2V0QWxsRGVwdFRyZWUsIGxpc3RVc2VyfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7CmltcG9ydCB7Z2V0QXNzZXRUeXBlQ2hpbGRyZW5CeWlkfSBmcm9tICJAL2FwaS9zYWZlL292ZXJ2aWV3IjsKaW1wb3J0IHVzZXJTZWxlY3QgZnJvbSAiQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdC91c2VyU2VsZWN0IjsKaW1wb3J0IHR5cGVUcmVlIGZyb20gIkAvdmlld3MvY29tcG9uZW50cy90eXBlVHJlZSI7CmltcG9ydCB2ZW5kb3JTZWxlY3QgZnJvbSAiQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdC92ZW5kb3JTZWxlY3QiOwppbXBvcnQgbWFuYWdlciBmcm9tICJAL3ZpZXdzL2NvbXBvbmVudHMvbWFuYWdlciI7CmltcG9ydCBMb2NhdGlvblNlbGVjdCBmcm9tICJAL3ZpZXdzL2NvbXBvbmVudHMvc2VsZWN0L2xvY2F0aW9uU2VsZWN0IjsKaW1wb3J0IERlcHRTZWxlY3QgZnJvbSAiQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdC9kZXB0U2VsZWN0IjsKaW1wb3J0IHtsaXN0TG9jYXRpb259IGZyb20gIkAvYXBpL2RpY3QvbG9jYXRpb24iOwppbXBvcnQgdXBsb2FkRmlsZVRhYmxlIGZyb20gIkAvdmlld3MvY29tcG9uZW50cy90YWJsZS91cGxvYWRGaWxlVGFibGUiOwppbXBvcnQgcHJvY3VyZW1lbnQgZnJvbSAiQC92aWV3cy9zYWZlL3Byb2N1cmVtZW50IjsKaW1wb3J0IGNvbXBhbnlFbXAgZnJvbSAiQC92aWV3cy9tb25pdG9yMi9jb21wYW55RW1wIjsKaW1wb3J0IE9wdGlvbkxhYiBmcm9tICJAL3ZpZXdzL2NvbXBvbmVudHMvb3B0aW9uTGFiIjsKaW1wb3J0IEFzc2V0RmlsZSBmcm9tICJAL3ZpZXdzL2RpbWVuc2lvbi9maWxlIjsKaW1wb3J0IER5bmFtaWNUYWcgZnJvbSAnQC9jb21wb25lbnRzL0R5bmFtaWNUYWcvaW5kZXgudnVlJzsKaW1wb3J0IE5ldHdvcmtEZXZpY2VEZXRhaWwgZnJvbSAiQC92aWV3cy9zYWZlL25ldHdvcmtkZXZpY2VzL25ldHdvcmtEZXZpY2VEZXRhaWwudnVlIjsKaW1wb3J0IHtkZXB0VHJlZVNlbGVjdH0gZnJvbSAiQC9hcGkvc3lzdGVtL3JvbGUiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJNYWNocm9vbSIsCiAgY29tcG9uZW50czogewogICAgTmV0d29ya0RldmljZURldGFpbCwKICAgIER5bmFtaWNUYWcsCiAgICBUcmVlc2VsZWN0LAogICAgdXNlclNlbGVjdCwKICAgIHR5cGVUcmVlLAogICAgdmVuZG9yU2VsZWN0LAogICAgbWFuYWdlciwKICAgIExvY2F0aW9uU2VsZWN0LAogICAgRGVwdFNlbGVjdCwKICAgIHVwbG9hZEZpbGVUYWJsZSwKICAgIHByb2N1cmVtZW50LAogICAgY29tcGFueUVtcCwKICAgIE9wdGlvbkxhYiwKICAgIEFzc2V0RmlsZQogIH0sCiAgZGljdHM6IFsiaW1wdF9ncmFkZSJdLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzaG93QWxsOiBmYWxzZSwKICAgICAgY2xhc3NJZDogMSwKICAgICAgY2xhc3NOYW1lOiAn54mp55CG5py65oi/JywKICAgICAgdHlwZUNsYXNzOiB7fSwKICAgICAgLy/liIbnsbvmoJEKICAgICAgdHlwZVRyZWVEYXRhOiBbXSwKICAgICAgdHlwZWxpc3Q6IFtdLAogICAgICBjaGlsZHJlbjogW10sCiAgICAgIC8vIOihqOagvOagkeaVsOaNrgogICAgICBkZXB0TGlzdDogW10sCiAgICAgIC8vIOmDqOmXqOagkemAiemhuQogICAgICBkZXB0T3B0aW9uczogW10sCiAgICAgIC8vIOS9jee9ruS/oeaBr+agkemAiemhuQogICAgICBsb2NhdGlvbk9wdGlvbnM6IFtdLAogICAgICAvL+eUqOaIt+mAieaLqQogICAgICB1c2VyRGlhbG9nOiBmYWxzZSwKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICBjdXJyZW50TmFtZXM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOacuuaIv+euoeeQhuihqOagvOaVsOaNrgogICAgICBtYWNocm9vbUxpc3Q6IFtdLAogICAgICBhc3NldElkOiAnJywKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDkuqfllYbpgInmi6kKICAgICAgdmVuZG9yRGlhbG9nOiBmYWxzZSwKICAgICAgLy/otKPku7vkuroKICAgICAgbWFuYWdlbWVudERpYWxvZzogZmFsc2UsCiAgICAgIC8v5paH5Lu2CiAgICAgIGZpbGVEaWFsb2c6IGZhbHNlLAogICAgICAvL+eUqOaItwogICAgICB1c2VyTGlzdDogW10sCiAgICAgIHVzZXJxdWVyeVBhcmFtczogewogICAgICAgIGlzQXNjOiB1bmRlZmluZWQsCiAgICAgICAgb3JkZXJCeUNvbHVtbjogdW5kZWZpbmVkLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDMwLAogICAgICB9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBpc0FzYzogdW5kZWZpbmVkLAogICAgICAgIG9yZGVyQnlDb2x1bW46IHVuZGVmaW5lZCwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBhc3NldENvZGU6IG51bGwsCiAgICAgICAgYXNzZXROYW1lOiBudWxsLAogICAgICAgIGRlZ3JlZUltcG9ydGFuY2U6IG51bGwsCiAgICAgICAgYXNzZXRDbGFzczogbnVsbCwKICAgICAgICBsb2NhdGlvbjogbnVsbCwKICAgICAgICB1c2VySWQ6IG51bGwsCiAgICAgICAgZGVwdElkOiBudWxsLAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIGFzc2V0TmFtZTogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5py65oi/5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICAgIHttaW46IDAsIG1heDogNjQsIG1lc3NhZ2U6ICfmnLrmiL/lkI3np7DkuI3og73otoXov4cgNjQg5Liq5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIGxvY2F0aW9uRGV0YWlsOiBbCiAgICAgICAgICB7bWluOiAwLCBtYXg6IDEyOCwgbWVzc2FnZTogJ+ivpue7huWcsOWdgOS4jeiDvei2hei/hyAxMjgg5Liq5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICB9LAogICAgICBjb2x1bW5zOiBbCiAgICAgICAge2tleTogMCwgbGFiZWw6ICfotYTkuqfnvJbnoIEnLCB2aXNpYmxlOiB0cnVlfSwKICAgICAgICB7a2V5OiAxLCBsYWJlbDogJ+acuuaIv+WQjeensCcsIHZpc2libGU6IHRydWV9LAogICAgICAgIHtrZXk6IDIsIGxhYmVsOiAn5omA5bGe6YOo6ZeoJywgdmlzaWJsZTogdHJ1ZX0sCiAgICAgICAge2tleTogMywgbGFiZWw6ICfph43opoHnqIvluqYnLCB2aXNpYmxlOiB0cnVlfSwKICAgICAgICB7a2V5OiA0LCBsYWJlbDogJ+eJqeeQhuS9jee9ricsIHZpc2libGU6IHRydWV9LAogICAgICAgIHtrZXk6IDUsIGxhYmVsOiAn6K+m57uG5Zyw5Z2AJywgdmlzaWJsZTogdHJ1ZX0sCiAgICAgICAgLy8ge2tleTogNCwgbGFiZWw6ICfotKPku7vkuronLCB2aXNpYmxlOiBmYWxzZX0sCiAgICAgICAgLy8ge2tleTogNSwgbGFiZWw6ICfogZTns7vnlLXor50nLCB2aXNpYmxlOiBmYWxzZX0sCiAgICAgICAge2tleTogNiwgbGFiZWw6ICfkvpvlupTllYYnLCB2aXNpYmxlOiB0cnVlfSwKICAgICAgICB7a2V5OiA3LCBsYWJlbDogJ+Wkh+azqCcsIHZpc2libGU6IGZhbHNlfSwKCiAgICAgIF0sCiAgICAgIGN1cnJlbnRBc3NldElkOiBudWxsLAogICAgICBlZGl0SXRlbTogJ2VkaXQnLAogICAgICBlZGl0YWJsZTogdHJ1ZSwKICAgICAgZGV2aWNlRGV0YWlsVmlzaWJsZTogZmFsc2UKICAgIH07CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5pbml0KCk7CiAgICB0aGlzLmdldERlcHRUcmVlKCk7CiAgICB0aGlzLmdldExvY2F0aW9uVHJlZXNlbGVjdCgpOwogIH0sCiAgd2F0Y2g6IHsKICAgICckcm91dGUnKHJvdXRlKSB7CiAgICAgIGlmIChyb3V0ZS5uYW1lID09PSAiTWFjaHJvb20iKSB7CiAgICAgICAgdGhpcy5pbml0KCk7CiAgICAgIH0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGluaXQoKSB7CiAgICAgIGdldEFzc2V0VHlwZUNoaWxkcmVuQnlpZCh0aGlzLmNsYXNzSWQpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnR5cGVsaXN0ID0gcmVzLnJvd3M7CiAgICAgICAgbGV0IGl0ZW0gPSB0aGlzLnR5cGVsaXN0LmZpbmQoaXRlbSA9PiB7CiAgICAgICAgICByZXR1cm4gaXRlbS50eXBlTmFtZSA9PSB0aGlzLmNsYXNzTmFtZQogICAgICAgIH0pCiAgICAgICAgaWYgKGl0ZW0pIHsKICAgICAgICAgIHRoaXMudHlwZUNsYXNzLmFzc2V0Q2xhc3MgPSBpdGVtLmlkOwogICAgICAgICAgdGhpcy50eXBlQ2xhc3MuYXNzZXRDbGFzc0Rlc2MgPSBpdGVtLnR5cGVOYW1lOwogICAgICAgIH0KICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMudHlwZWxpc3QubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIGlmICh0aGlzLnR5cGVsaXN0W2ldLnR5cGVHcmFkZWRQcm90ZWN0aW9uID09IDEpIHsKICAgICAgICAgICAgdGhpcy50eXBlbGlzdC5zcGxpY2UoTnVtYmVyKGkpLCAxKTsKICAgICAgICAgICAgaSA9IGkgLSAxOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICB0aGlzLnR5cGVUcmVlRGF0YSA9IHRoaXMuaGFuZGxlVHJlZSh0aGlzLnR5cGVsaXN0LCAnaWQnLCAncGlkJyk7CiAgICAgICAgdGhpcy5jaGlsZHJlbiA9IHRoaXMudHlwZVRyZWVEYXRhWzBdLmNoaWxkcmVuOwogICAgICB9KQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gey4uLnRoaXMucXVlcnlQYXJhbXMsIC4uLnRoaXMuJHJvdXRlLnBhcmFtc307CiAgICAgIC8vIGNvbnNvbGUubG9nKHRoaXMucXVlcnlQYXJhbXMpCiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDmn6Xor6LmiYDlsZ7pg6jpl6ggKi8KICAgIGdldERlcHRUcmVlKCkgewogICAgICBnZXRBbGxEZXB0VHJlZSgpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog6L2s5o2i6YOo6Zeo5pWw5o2u57uT5p6EICovCiAgICBub3JtYWxpemVyKG5vZGUpIHsKICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgIW5vZGUuY2hpbGRyZW4ubGVuZ3RoKSB7CiAgICAgICAgZGVsZXRlIG5vZGUuY2hpbGRyZW47CiAgICAgIH0KICAgICAgcmV0dXJuIHsKICAgICAgICBpZDogbm9kZS5kZXB0SWQsCiAgICAgICAgbGFiZWw6IG5vZGUuZGVwdE5hbWUsCiAgICAgICAgY2hpbGRyZW46IG5vZGUuY2hpbGRyZW4KICAgICAgfTsKICAgIH0sCiAgICAvKioKICAgICAqIOagkeiKgueCueivpeWPmOaXtgogICAgICovCiAgICBub2RlQ2hhbmdlKG5vZGUpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hc3NldFR5cGUgPSBudWxsOwogICAgICBpZiAobm9kZS5waWQgIT0gMCkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXNzZXRUeXBlID0gbm9kZS5pZDsKICAgICAgfQogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKioKICAgICAqIOi1hOS6p+exu+Wei+WPmOWMlgogICAgICovCiAgICBhc3NldFR5cGVDaGFuZ2UoZGF0YSkgewogICAgICBsZXQgaXRlbSA9IHRoaXMuY2hpbGRyZW4uZmluZChpdGVtID0+IHsKICAgICAgICByZXR1cm4gaXRlbS5pZCA9PSBkYXRhOwogICAgICB9KQogICAgICBpZiAoaXRlbSkgewogICAgICAgIHRoaXMuZm9ybS5hc3NldFR5cGVEZXNjID0gaXRlbS50eXBlTmFtZTsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICog54K55Ye76YCJ5oup55So5oi3CiAgICAgKi8KICAgIHVzZXJTZWxlY3QoKSB7CiAgICAgIHRoaXMudXNlckRpYWxvZyA9IHRydWU7CiAgICB9LAogICAgLyoqCiAgICAgKiDpgInmi6nkvpvlupTllYYKICAgICAqLwogICAgc2hvd1ZlbmRvckRpYWxvZygpIHsKICAgICAgaWYgKHRoaXMuZWRpdGFibGUpCiAgICAgICAgdGhpcy52ZW5kb3JEaWFsb2cgPSB0cnVlCiAgICB9LAogICAgLyoqCiAgICAgKiDpgInmi6nkvpvlupTllYYKICAgICAqLwogICAgc2VsZWN0Q29uZmlybSh2ZW5kb3IpIHsKCiAgICAgIHRoaXMuZm9ybS52ZW5kb3IgPSB2ZW5kb3IuaWQ7CiAgICAgIHRoaXMuZm9ybS52ZW5kb3JOYW1lID0gdmVuZG9yLnZlbmRvck5hbWU7CiAgICAgIHRoaXMudmVuZG9yRGlhbG9nID0gZmFsc2U7CiAgICB9LAogICAgLyoqCiAgICAgKiDlj5bmtojpgInmi6kKICAgICAqLwogICAgdmVuZG9yQ2FuY2VsKCkgewogICAgICB0aGlzLnZlbmRvckRpYWxvZyA9IGZhbHNlOwogICAgfSwKICAgIC8qKgogICAgICog5Y+W5raI6YCJ5oupCiAgICAgKi8KICAgIHVzZXJDYW5jZWwoKSB7CiAgICAgIHRoaXMudXNlckRpYWxvZyA9IGZhbHNlOwogICAgfSwKICAgIC8qKgogICAgICog6YCJ5oup6LSj5Lu75Lq6CiAgICAgKi8KICAgIHNob3dNYW5hZ2VtZW50KHJvdykgewogICAgICB0aGlzLmFzc2V0SWQgPSByb3cuYXNzZXRJZDsKICAgICAgdGhpcy5tYW5hZ2VtZW50RGlhbG9nID0gdHJ1ZTsKICAgIH0sCiAgICAvKioKICAgICAqIOWFs+mXremAieaLqei0o+S7u+S6ugogICAgICovCiAgICBjbG9zZU1hbmFnZW1lbnQoKSB7CiAgICAgIHRoaXMubWFuYWdlbWVudERpYWxvZyA9IGZhbHNlOwogICAgfSwKICAgIC8v6I635Y+W5Lq65ZGY5pWw5o2uCiAgICBnZXRUYWJsZURhdGEoKSB7CiAgICB9LAogICAgLy/lhbPpl63nlKjmiLfnqpflj6MKICAgIGNsb3NlVXNlckRpYWxvZygpIHsKICAgICAgdGhpcy51c2VyRGlhbG9nID0gZmFsc2U7CiAgICB9LAogICAgLy/miZPlvIDnlKjmiLfpgInmi6nnqpflj6MKICAgIHNob3dVc2VyRGlhbG9nKHZhbCkgewogICAgICB0aGlzLmRpYWxvZ05hbWUgPSB2YWwKICAgICAgdGhpcy51c2VyRGlhbG9nID0gdHJ1ZQogICAgfSwKICAgIC8v5o6S5bqPCiAgICBzb3J0Q2hhbmdlKGNvbHVtbiwgcHJvcCwgb3JkZXIpIHsKICAgICAgaWYgKGNvbHVtbi5vcmRlciAhPSBudWxsKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pc0FzYyA9ICdkZXNjJzsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzQXNjID0gJ2FzYyc7CiAgICAgIH0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5vcmRlckJ5Q29sdW1uID0gY29sdW1uLnByb3A7CiAgICAgIHRoaXMuZ2V0TGlzdCh0aGlzLnF1ZXJ5UGFyYW1zKTsKICAgIH0sCiAgICAvKiog5p+l6K+i5py65oi/566h55CG5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0TWFjaHJvb20odGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5tYWNocm9vbUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIHRoaXMudG90YWwgPSBOdW1iZXIocmVzcG9uc2UudG90YWwpOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgYXNzZXRJZDogbnVsbCwKICAgICAgICBhc3NldENvZGU6IG51bGwsCiAgICAgICAgYXNzZXROYW1lOiBudWxsLAogICAgICAgIGxvY2F0aW9uOiBudWxsLAogICAgICAgIGRlZ3JlZUltcG9ydGFuY2U6IG51bGwsCiAgICAgICAgbWFuZ2VyOiBudWxsLAogICAgICAgIHBob25lOiBudWxsLAogICAgICAgIHZlbmRvcjogbnVsbCwKICAgICAgICBhc3NldFR5cGU6IG51bGwsCiAgICAgICAgYXNzZXRUeXBlRGVzYzogbnVsbCwKICAgICAgICBhc3NldENsYXNzOiAxLAogICAgICAgIGFzc2V0Q2xhc3NEZXNjOiAi54mp55CG5py65oi/IiwKICAgICAgICBsb2NhdGlvbklkOiBudWxsLAogICAgICAgIHRhZ3M6IFtdLAogICAgICB9OwogICAgICB0aGlzLmVkaXRJdGVtID0gJ2VkaXQnOwogICAgICB0aGlzLmVkaXRhYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uYXNzZXRJZCkKICAgICAgdGhpcy5jdXJyZW50TmFtZXMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5hc3NldE5hbWUpOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICBPYmplY3QuYXNzaWduKHRoaXMuZm9ybSwgdGhpcy5xdWVyeVBhcmFtcyk7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOacuuaIvyI7CiAgICAgIH0pCiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVXBkYXRlKHJvdywgZWRpdCA9IHRydWUpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLmVkaXRhYmxlID0gZWRpdDsKICAgICAgY29uc3QgYXNzZXRJZCA9IHJvdy5hc3NldElkIHx8IHRoaXMuaWRzCiAgICAgIGdldE1hY2hyb29tKGFzc2V0SWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5mb3JtLmxvY2F0aW9uSWQgPSBOdW1iZXIocmVzcG9uc2UuZGF0YS5sb2NhdGlvbklkKTsKICAgICAgICB0aGlzLmdldExvY2F0aW9uVHJlZXNlbGVjdCgpOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9IChlZGl0ID8gIuS/ruaUuSIgOiAi5p+l55yLIikgKyAi5py65oi/IjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy5mb3JtID0gey4uLnRoaXMuZm9ybSwgLi4udGhpcy50eXBlQ2xhc3N9CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkICYmIHRoaXMuZWRpdGFibGUpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uYXNzZXRJZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZU1hY2hyb29tKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkTWFjaHJvb20odGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGFzc2V0SWRzID0gcm93LmFzc2V0SWQgfHwgdGhpcy5pZHM7CiAgICAgIGxldCBhc3NldHNOYW1lID0gIiI7CiAgICAgIGlmICghcm93LmFzc2V0SWQpIHsKICAgICAgICBhc3NldHNOYW1lID0gdGhpcy5jdXJyZW50TmFtZXMuam9pbigiLCIpOwogICAgICB9IGVsc2UgewogICAgICAgIGFzc2V0c05hbWUgPSByb3cuYXNzZXROYW1lOwogICAgICB9CgogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTjgJAnICsgYXNzZXRzTmFtZSArICfjgJHnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZGVsTWFjaHJvb20oYXNzZXRJZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoJ3NhZmUvbWFjaHJvb20vZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYG1hY2hyb29tXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfSwKICAgIC8qKiDovazmjaLkvY3nva7kv6Hmga/mlbDmja7nu5PmnoQgKi8KICAgIG5vcm1hbGl6ZXJMb2NhdGlvbihub2RlKSB7CiAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmICFub2RlLmNoaWxkcmVuLmxlbmd0aCkgewogICAgICAgIGRlbGV0ZSBub2RlLmNoaWxkcmVuOwogICAgICB9CiAgICAgIHJldHVybiB7CiAgICAgICAgaWQ6IG5vZGUubG9jYXRpb25JZCwKICAgICAgICBsYWJlbDogbm9kZS5sb2NhdGlvbk5hbWUsCiAgICAgICAgY2hpbGRyZW46IG5vZGUuY2hpbGRyZW4KICAgICAgfTsKICAgIH0sCiAgICAvKiog5p+l6K+i5L2N572u5L+h5oGv5LiL5ouJ5qCR57uT5p6EICovCiAgICBnZXRMb2NhdGlvblRyZWVzZWxlY3QoKSB7CiAgICAgIGxpc3RMb2NhdGlvbigpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubG9jYXRpb25PcHRpb25zID0gcmVzcG9uc2UuZGF0YS5maWx0ZXIoKGUpID0+IHsKICAgICAgICAgIHJldHVybiBlLmxvY2F0aW9uVHlwZSA9PT0gIjEiCiAgICAgICAgfSk7CiAgICAgICAgLy8gdGhpcy5sb2NhdGlvbk9wdGlvbnMgPSB0aGlzLmhhbmRsZVRyZWUocmVzcG9uc2UuZGF0YSwgImxvY2F0aW9uSWQiLCAicGFyZW50SWQiKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6LWE5Lqn5YWz57O75Zu+CiAgICBoYW5kbGVDaGFydChyb3cpIHsKICAgICAgLy8gY29uc3QgYXNzZXRJZCA9IHJvdy5hc3NldElkOwogICAgICAvLyBjb25zdCBhc3NldE5hbWUgPSByb3cuYXNzZXROYW1lOwogICAgICAvLyB0aGlzLiR0YWIub3BlblBhZ2UoIlsiICsgYXNzZXROYW1lICsgIl3otYTkuqfor6bmg4UiLCAnL3NhZmUvYXNzZXQvZGV0YWlscy9pbmRleC8nICsgYXNzZXRJZCk7CiAgICAgIC8vIHRoaXMuJHRhYi5vcGVuUGFnZSgiWyIgKyBhc3NldE5hbWUgKyAiXei1hOS6p+WFs+ezu+WbviIsICcvc2FmZS9hc3NldC9jaGFydC9pbmRleC8nICsgYXNzZXRJZCk7CiAgICAgIHRoaXMuYXNzZXRJZCA9IHJvdy5hc3NldElkOwogICAgICB0aGlzLmRldmljZURldGFpbFZpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8v6LWE5Lqn5paH5Lu2CiAgICBoYW5kbGVGaWxlKHJvdykgewogICAgICB0aGlzLmN1cnJlbnRBc3NldElkID0gcm93LmFzc2V0SWQ7CiAgICAgIHRoaXMuZmlsZURpYWxvZyA9IHRydWU7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk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file": "index.vue", "sourceRoot": "src/views/safe/machroom", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-tree-container\" v-if=\"children&&children.length\">\n      <type-tree :tree-date=\"typeTreeData\" @nodeChange=\"nodeChange\"></type-tree>\n    </div>\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form :model=\"queryParams\" ref=\"queryForm\" label-position=\"right\" :inline=\"true\" label-width=\"70px\">\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"资产编码\" prop=\"assetCode\">\n                <el-input\n                  v-model=\"queryParams.assetCode\"\n                  placeholder=\"请输入资产编码\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"机房名称\" prop=\"assetName\">\n                <el-input\n                  v-model=\"queryParams.assetName\"\n                  placeholder=\"请输入机房名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"重要程度\" prop=\"degreeImportance\">\n                <el-select v-model=\"queryParams.degreeImportance\" placeholder=\"请选择重要程度\" clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.impt_grade\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row v-if=\"showAll\" :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属部门\" prop=\"deptId\">\n                <dept-select v-model=\"queryParams.deptId\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">物理机房列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleAdd\"\n                  v-hasPermi=\"['safe:machroom:add']\"\n                >新增\n                </el-button>\n              </el-col>\n              <!--              <el-col :span=\"1.5\">-->\n              <!--                <el-button-->\n              <!--                  plain-->\n              <!--                  size=\"small\"-->\n              <!--                  :disabled=\"single\"-->\n              <!--                  @click=\"handleUpdate\"-->\n              <!--                  v-hasPermi=\"['safe:machroom:edit']\"-->\n              <!--                >修改-->\n              <!--                </el-button>-->\n              <!--              </el-col>-->\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"handleDelete\"\n                  v-hasPermi=\"['safe:machroom:remove']\"\n                >批量删除\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                  v-hasPermi=\"['safe:machroom:export']\"\n                >导出\n                </el-button>\n              </el-col>\n              <!--              <right-toolbar :showSearch.sync=\"showSearch\" :columns=\"columns\" @queryTable=\"getList\"></right-toolbar>-->\n            </el-row>\n          </div>\n        </div>\n        <el-table v-loading=\"loading\" :data=\"machroomList\" @selection-change=\"handleSelectionChange\"\n                  @sort-change=\"sortChange\">\n          <el-table-column type=\"selection\" width=\"55\"/>\n          <el-table-column label=\"资产编码\" align=\"left\" prop=\"assetCode\" v-if=\"columns[0].visible\"\n                           show-overflow-tooltip/>\n          <el-table-column label=\"机房名称\" align=\"left\" prop=\"assetName\" v-if=\"columns[1].visible\"\n                           show-overflow-tooltip/>\n          <el-table-column label=\"所属部门\" prop=\"deptName\" v-if=\"columns[2].visible\" show-overflow-tooltip/>\n          <el-table-column label=\"重要程度\" prop=\"degreeImportance\" v-if=\"columns[3].visible\">\n            <template slot-scope=\"scope\">\n              <dict-tag :options=\"dict.type.impt_grade\" :value=\"scope.row.degreeImportance\"/>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"物理位置\" prop=\"locationFullName\" v-if=\"columns[4].visible\" show-overflow-tooltip/>\n          <el-table-column label=\"详细地址\" prop=\"locationDetail\" v-if=\"columns[5].visible\" show-overflow-tooltip/>\n          <!--<el-table-column label=\"责任人\"  prop=\"mangerName\" v-if=\"columns[4].visible\" />-->\n          <!--<el-table-column label=\"联系电话\"  prop=\"phone\" v-if=\"columns[5].visible\" />-->\n          <el-table-column label=\"供应商\" prop=\"vendorName\" v-if=\"columns[6].visible\" show-overflow-tooltip/>\n          <el-table-column label=\"备注\" prop=\"remark\" v-if=\"columns[7].visible\" show-overflow-tooltip/>\n          <el-table-column label=\"操作\" width=\"200\" fixed=\"right\" :show-overflow-tooltip=\"false\"\n                           class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleUpdate(scope.row)\"\n                v-hasPermi=\"['safe:machroom:edit']\"\n              >编辑\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleUpdate(scope.row,false)\"\n                v-hasPermi=\"['safe:machroom:list']\"\n              >查看\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                class=\"table-delBtn\"\n                @click=\"handleDelete(scope.row)\"\n                v-hasPermi=\"['safe:machroom:remove']\"\n              >删除\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleChart(scope.row)\"\n              >详情\n              </el-button>\n              <!--              <option-lab>\n                              <template>\n                                <el-button\n                                  size=\"mini\"\n                                  type=\"text\"\n                                  @click=\"handleUpdate(scope.row,false)\"\n                                  v-hasPermi=\"['safe:machroom:list']\"\n                                >查看\n                                </el-button>\n                                <el-button\n                                  size=\"mini\"\n                                  type=\"text\"\n                                  @click=\"handleUpdate(scope.row)\"\n                                  v-hasPermi=\"['safe:machroom:edit']\"\n                                >编辑\n                                </el-button>\n                                <el-button\n                                  size=\"mini\"\n                                  type=\"text\"\n                                  @click=\"handleDelete(scope.row)\"\n                                  v-hasPermi=\"['safe:machroom:remove']\"\n                                >删除\n                                </el-button>\n                                <el-button\n                                  size=\"mini\"\n                                  type=\"text\"\n                                  @click=\"handleChart(scope.row)\"\n                                >详情\n                                </el-button>\n                              </template>\n                            </option-lab>-->\n\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <!-- 添加或修改机房管理对话框! -->\n    <el-dialog v-if=\"open\" title=\"机房管理\" :visible.sync=\"open\" width=\"900px\" append-to-body>\n      <el-tabs type=\"card\" v-model=\"editItem\">\n        <el-tab-pane :lazy=\"true\" :label=\"title\" name=\"edit\">\n          <el-row v-if=\"title != '查看机房'\" :gutter=\"10\">\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"资产编码\" prop=\"assetCode\">\n                  <el-input :disabled=\"!editable\" v-model=\"form.assetCode\" placeholder=\"请输入资产编码\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"机房名称\" prop=\"assetName\">\n                  <el-input :disabled=\"!editable\" v-model=\"form.assetName\" placeholder=\"请输入机房名称\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"重要程度\" prop=\"degreeImportance\">\n                  <el-select :disabled=\"!editable\" v-model=\"form.degreeImportance\" placeholder=\"请选择重要程度\"\n                             clearable>\n                    <el-option\n                      v-for=\"dict in dict.type.impt_grade\"\n                      :key=\"dict.value\"\n                      :label=\"dict.label\"\n                      :value=\"dict.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"物理位置\" prop=\"locationId\">\n                  <!--<el-input v-model=\"form.location\" placeholder=\"请输入物理位置\"/>-->\n                  <!--<location-select :disabled=\"!editable\" v-model=\"form.locationId\"/>-->\n                  <el-select :disabled=\"!editable\" v-model=\"form.locationId\" placeholder=\"请选择所属位置\">\n                    <el-option\n                      v-for=\"dict in locationOptions\"\n                      :key=\"dict.locationId\"\n                      :label=\"dict.locationFullName\"\n                      :value=\"dict.locationId\"\n                    ></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"24\">\n                <el-form-item label=\"详细地址\" prop=\"locationDetail\">\n                  <el-input :disabled=\"!editable\" v-model=\"form.locationDetail\" placeholder=\"请输入详细地址\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"所属部门\" prop=\"deptId\">\n                  <dept-select v-model=\"form.deptId\" :is-disabled=\"!editable\" is-current/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item v-if=\"children&&children.length\" label=\"资产类型\" prop=\"assetClass\">\n                  <el-select :disabled=\"!editable\" v-model=\"form.assetType\" placeholder=\"请选择资产类型\"\n                             @change=\"assetTypeChange\">\n                    <el-option\n                      v-for=\"children in children\"\n                      :key=\"children.id\"\n                      :label=\"children.typeName\"\n                      :value=\"children.id\"\n                    ></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"供应商\" prop=\"vendor\">\n                  <el-input :disabled=\"!editable\" :value=\"form.vendorName\" @focus=\"showVendorDialog\"\n                            placeholder=\"请输入供应商\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"24\">\n                <el-form-item label=\"资产标签\" prop=\"tags\">\n                  <dynamic-tag :disabled=\"!editable\" v-model=\"form.tags\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"remark\">\n                  <el-input :disabled=\"!editable\" v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"/>\n                </el-form-item>\n              </el-col>\n            </el-form>\n          </el-row>\n          <div v-else class=\"customForm-container\">\n            <el-descriptions\n              class=\"custom-column\"\n              direction=\"vertical\"\n              size=\"medium\"\n              :colon=\"false\"\n              label-class-name=\"custom-label-style\"\n              content-class-name=\"custom-content-style\"\n              :column=\"2\">\n              <el-descriptions-item label=\"资产编号\">\n                {{ form.assetCode }}\n              </el-descriptions-item>\n              <el-descriptions-item label=\"机房名称\">\n                {{ form.assetName }}\n              </el-descriptions-item>\n              <el-descriptions-item label=\"重要程度\">\n                <span\n                  v-for=\"(item, index) in dict.type.impt_grade\"\n                  :key=\"index\"\n                  v-if=\"item.value === form.degreeImportance\"\n                >{{ item.label }}\n          </span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"物理位置\">\n                {{ form.locationFullName }}\n                <span\n                  v-for=\"(item, index) in locationOptions\"\n                  :key=\"index\"\n                  v-if=\"item.locationId === form.locationId\"\n                >{{ item.locationFullName }}\n          </span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"详细地址\">\n                {{ form.locationDetail }}\n              </el-descriptions-item>\n              <el-descriptions-item label=\"所属部门\">\n                <span\n                  v-for=\"(item, index) in deptOptions\"\n                  :key=\"item.id\"\n                  v-if=\"item.id === form.deptId\"\n                >{{ item.label }}\n          </span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"资产类型\">\n                <span\n                  v-for=\"(item, index) in children\"\n                  :key=\"item.id\"\n                  v-if=\"item.id === form.assetType\"\n                >{{ item.typeName }}\n          </span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"供应商\">\n                {{ form.vendorName }}\n              </el-descriptions-item>\n              <el-descriptions-item label=\"资产标签\">\n                <span\n                  v-for=\"(item, index) in form.tags\"\n                  :key=\"index\"\n                >{{ item }}\n          </span>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"备注\">\n                {{ form.remark }}\n              </el-descriptions-item>\n            </el-descriptions>\n          </div>\n        </el-tab-pane>\n        <el-tab-pane v-if=\"form.assetId\" :lazy=\"true\" v-hasPermi=\"['safe:management:list']\" label=\"管理信息\"\n                     name=\"manager\">\n          <manager :disabled=\"!editable\" :assetId=\"form.assetId\" @closeManagement=\"closeManagement\"></manager>\n        </el-tab-pane>\n        <!--<el-tab-pane v-if=\"form.assetId\" :lazy=\"true\" v-hasPermi=\"['safe:uploadfile:list']\" label=\"资产图片\" name=\"file\">-->\n        <!--  <upload-file-table :disabled=\"!editable\" :asset-id=\"form.assetId\"/>-->\n        <!--</el-tab-pane>-->\n        <el-tab-pane v-if=\"form.assetId\" :lazy=\"true\" label=\"相关文件\" name=\"file\">\n          <asset-file :disabled=\"!editable\" :asset-id=\"form.assetId\"/>\n        </el-tab-pane>\n        <el-tab-pane v-if=\"form.assetId\" :lazy=\"true\" label=\"财务信息\" name=\"procurement\">\n          <procurement :disabled=\"!editable\" :asset-id=\"form.assetId\"></procurement>\n        </el-tab-pane>\n        <el-tab-pane v-if=\"form.assetId\" :lazy=\"true\" label=\"驻场信息\" name=\"companyEmp\">\n          <company-emp :disabled=\"!editable\" :asset-id=\"form.assetId\"></company-emp>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button v-if=\"editable && editItem === 'edit'\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <!--    <el-dialog title=\"选择用户\" :visible.sync=\"userDialog\" width=\"900px\" append-to-body>-->\n    <!--      <user-select @confirm=\"selectConfirm\" @cancel=\"userCancel\"></user-select>-->\n    <!--    </el-dialog>-->\n    <el-dialog title=\"选择供应商\" :visible.sync=\"vendorDialog\" width=\"900px\" append-to-body>\n      <vendor-select v-if=\"vendorDialog\" @confirm=\"selectConfirm\" @cancel=\"vendorCancel\"></vendor-select>\n    </el-dialog>\n    <!--    <el-dialog title=\"责任人列表\" width=\"900px\" :visible.sync=\"managementDialog\" append-to-body>-->\n    <!--      <manager :assetId=\"assetId\" @closeManagement=\"closeManagement\"></manager>-->\n    <!--    </el-dialog>-->\n    <!--    <el-dialog title=\"资产文件\" width=\"900px\" :visible.sync=\"fileDialog\" append-to-body>-->\n    <!--      <upload-file-table :asset-id=\"currentAssetId\"/>-->\n    <!--      <div slot=\"footer\" class=\"dialog-footer\">-->\n    <!--        <el-button type=\"primary\" @click=\"fileDialog=false;\">关 闭</el-button>-->\n    <!--      </div>-->\n    <!--    </el-dialog>-->\n\n    <networkDeviceDetail\n      :asset-id=\"assetId\"\n      asset-name=\"物理机房详情\"\n      asset-allocation-type=\"5\"\n      :device-detail-visible.sync=\"deviceDetailVisible\"/>\n  </div>\n</template>\n\n<script>\nimport {listMachroom, getMachroom, delMachroom, addMachroom, updateMachroom} from \"@/api/safe/machroom\";\nimport {listDept, listDeptExcludeChild} from \"@/api/system/dept\";\nimport Treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport {getAllDeptTree, listUser} from \"@/api/system/user\";\nimport {getAssetTypeChildrenByid} from \"@/api/safe/overview\";\nimport userSelect from \"@/views/components/select/userSelect\";\nimport typeTree from \"@/views/components/typeTree\";\nimport vendorSelect from \"@/views/components/select/vendorSelect\";\nimport manager from \"@/views/components/manager\";\nimport LocationSelect from \"@/views/components/select/locationSelect\";\nimport DeptSelect from \"@/views/components/select/deptSelect\";\nimport {listLocation} from \"@/api/dict/location\";\nimport uploadFileTable from \"@/views/components/table/uploadFileTable\";\nimport procurement from \"@/views/safe/procurement\";\nimport companyEmp from \"@/views/monitor2/companyEmp\";\nimport OptionLab from \"@/views/components/optionLab\";\nimport AssetFile from \"@/views/dimension/file\";\nimport DynamicTag from '@/components/DynamicTag/index.vue';\nimport NetworkDeviceDetail from \"@/views/safe/networkdevices/networkDeviceDetail.vue\";\nimport {deptTreeSelect} from \"@/api/system/role\";\n\nexport default {\n  name: \"Machroom\",\n  components: {\n    NetworkDeviceDetail,\n    DynamicTag,\n    Treeselect,\n    userSelect,\n    typeTree,\n    vendorSelect,\n    manager,\n    LocationSelect,\n    DeptSelect,\n    uploadFileTable,\n    procurement,\n    companyEmp,\n    OptionLab,\n    AssetFile\n  },\n  dicts: [\"impt_grade\"],\n  data() {\n    return {\n      showAll: false,\n      classId: 1,\n      className: '物理机房',\n      typeClass: {},\n      //分类树\n      typeTreeData: [],\n      typelist: [],\n      children: [],\n      // 表格树数据\n      deptList: [],\n      // 部门树选项\n      deptOptions: [],\n      // 位置信息树选项\n      locationOptions: [],\n      //用户选择\n      userDialog: false,\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      currentNames: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 机房管理表格数据\n      machroomList: [],\n      assetId: '',\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 产商选择\n      vendorDialog: false,\n      //责任人\n      managementDialog: false,\n      //文件\n      fileDialog: false,\n      //用户\n      userList: [],\n      userqueryParams: {\n        isAsc: undefined,\n        orderByColumn: undefined,\n        pageNum: 1,\n        pageSize: 30,\n      },\n      // 查询参数\n      queryParams: {\n        isAsc: undefined,\n        orderByColumn: undefined,\n        pageNum: 1,\n        pageSize: 10,\n        assetCode: null,\n        assetName: null,\n        degreeImportance: null,\n        assetClass: null,\n        location: null,\n        userId: null,\n        deptId: null,\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        assetName: [\n          {required: true, message: \"机房名称不能为空\", trigger: \"blur\"},\n          {min: 0, max: 64, message: '机房名称不能超过 64 个字符', trigger: 'blur'},\n        ],\n        locationDetail: [\n          {min: 0, max: 128, message: '详细地址不能超过 128 个字符', trigger: 'blur'},\n        ],\n      },\n      columns: [\n        {key: 0, label: '资产编码', visible: true},\n        {key: 1, label: '机房名称', visible: true},\n        {key: 2, label: '所属部门', visible: true},\n        {key: 3, label: '重要程度', visible: true},\n        {key: 4, label: '物理位置', visible: true},\n        {key: 5, label: '详细地址', visible: true},\n        // {key: 4, label: '责任人', visible: false},\n        // {key: 5, label: '联系电话', visible: false},\n        {key: 6, label: '供应商', visible: true},\n        {key: 7, label: '备注', visible: false},\n\n      ],\n      currentAssetId: null,\n      editItem: 'edit',\n      editable: true,\n      deviceDetailVisible: false\n    };\n  },\n  mounted() {\n    this.init();\n    this.getDeptTree();\n    this.getLocationTreeselect();\n  },\n  watch: {\n    '$route'(route) {\n      if (route.name === \"Machroom\") {\n        this.init();\n      }\n    }\n  },\n  methods: {\n    init() {\n      getAssetTypeChildrenByid(this.classId).then(res => {\n        this.typelist = res.rows;\n        let item = this.typelist.find(item => {\n          return item.typeName == this.className\n        })\n        if (item) {\n          this.typeClass.assetClass = item.id;\n          this.typeClass.assetClassDesc = item.typeName;\n        }\n        for (let i = 0; i < this.typelist.length; i++) {\n          if (this.typelist[i].typeGradedProtection == 1) {\n            this.typelist.splice(Number(i), 1);\n            i = i - 1;\n          }\n        }\n        this.typeTreeData = this.handleTree(this.typelist, 'id', 'pid');\n        this.children = this.typeTreeData[0].children;\n      })\n      this.queryParams = {...this.queryParams, ...this.$route.params};\n      // console.log(this.queryParams)\n      this.getList();\n    },\n    /** 查询所属部门 */\n    getDeptTree() {\n      getAllDeptTree().then(response => {\n        this.deptOptions = response.data;\n      });\n    },\n    /** 转换部门数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.deptId,\n        label: node.deptName,\n        children: node.children\n      };\n    },\n    /**\n     * 树节点该变时\n     */\n    nodeChange(node) {\n      this.queryParams.assetType = null;\n      if (node.pid != 0) {\n        this.queryParams.assetType = node.id;\n      }\n      this.getList();\n    },\n    /**\n     * 资产类型变化\n     */\n    assetTypeChange(data) {\n      let item = this.children.find(item => {\n        return item.id == data;\n      })\n      if (item) {\n        this.form.assetTypeDesc = item.typeName;\n      }\n    },\n    /**\n     * 点击选择用户\n     */\n    userSelect() {\n      this.userDialog = true;\n    },\n    /**\n     * 选择供应商\n     */\n    showVendorDialog() {\n      if (this.editable)\n        this.vendorDialog = true\n    },\n    /**\n     * 选择供应商\n     */\n    selectConfirm(vendor) {\n\n      this.form.vendor = vendor.id;\n      this.form.vendorName = vendor.vendorName;\n      this.vendorDialog = false;\n    },\n    /**\n     * 取消选择\n     */\n    vendorCancel() {\n      this.vendorDialog = false;\n    },\n    /**\n     * 取消选择\n     */\n    userCancel() {\n      this.userDialog = false;\n    },\n    /**\n     * 选择责任人\n     */\n    showManagement(row) {\n      this.assetId = row.assetId;\n      this.managementDialog = true;\n    },\n    /**\n     * 关闭选择责任人\n     */\n    closeManagement() {\n      this.managementDialog = false;\n    },\n    //获取人员数据\n    getTableData() {\n    },\n    //关闭用户窗口\n    closeUserDialog() {\n      this.userDialog = false;\n    },\n    //打开用户选择窗口\n    showUserDialog(val) {\n      this.dialogName = val\n      this.userDialog = true\n    },\n    //排序\n    sortChange(column, prop, order) {\n      if (column.order != null) {\n        this.queryParams.isAsc = 'desc';\n      } else {\n        this.queryParams.isAsc = 'asc';\n      }\n      this.queryParams.orderByColumn = column.prop;\n      this.getList(this.queryParams);\n    },\n    /** 查询机房管理列表 */\n    getList() {\n      this.loading = true;\n      listMachroom(this.queryParams).then(response => {\n        this.machroomList = response.rows;\n        this.total = Number(response.total);\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        assetId: null,\n        assetCode: null,\n        assetName: null,\n        location: null,\n        degreeImportance: null,\n        manger: null,\n        phone: null,\n        vendor: null,\n        assetType: null,\n        assetTypeDesc: null,\n        assetClass: 1,\n        assetClassDesc: \"物理机房\",\n        locationId: null,\n        tags: [],\n      };\n      this.editItem = 'edit';\n      this.editable = true;\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.assetId)\n      this.currentNames = selection.map(item => item.assetName);\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.$nextTick(() => {\n        Object.assign(this.form, this.queryParams);\n        this.open = true;\n        this.title = \"添加机房\";\n      })\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row, edit = true) {\n      this.reset();\n      this.editable = edit;\n      const assetId = row.assetId || this.ids\n      getMachroom(assetId).then(response => {\n        this.form = response.data;\n        this.form.locationId = Number(response.data.locationId);\n        this.getLocationTreeselect();\n        this.open = true;\n        this.title = (edit ? \"修改\" : \"查看\") + \"机房\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.form = {...this.form, ...this.typeClass}\n      this.$refs[\"form\"].validate(valid => {\n        if (valid && this.editable) {\n          if (this.form.assetId != null) {\n            updateMachroom(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addMachroom(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const assetIds = row.assetId || this.ids;\n      let assetsName = \"\";\n      if (!row.assetId) {\n        assetsName = this.currentNames.join(\",\");\n      } else {\n        assetsName = row.assetName;\n      }\n\n      this.$modal.confirm('是否确认删除【' + assetsName + '】的数据项？').then(function () {\n        return delMachroom(assetIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {\n      });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('safe/machroom/export', {\n        ...this.queryParams\n      }, `machroom_${new Date().getTime()}.xlsx`)\n    },\n    /** 转换位置信息数据结构 */\n    normalizerLocation(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.locationId,\n        label: node.locationName,\n        children: node.children\n      };\n    },\n    /** 查询位置信息下拉树结构 */\n    getLocationTreeselect() {\n      listLocation().then(response => {\n        this.locationOptions = response.data.filter((e) => {\n          return e.locationType === \"1\"\n        });\n        // this.locationOptions = this.handleTree(response.data, \"locationId\", \"parentId\");\n      });\n    },\n    // 资产关系图\n    handleChart(row) {\n      // const assetId = row.assetId;\n      // const assetName = row.assetName;\n      // this.$tab.openPage(\"[\" + assetName + \"]资产详情\", '/safe/asset/details/index/' + assetId);\n      // this.$tab.openPage(\"[\" + assetName + \"]资产关系图\", '/safe/asset/chart/index/' + assetId);\n      this.assetId = row.assetId;\n      this.deviceDetailVisible = true;\n    },\n    //资产文件\n    handleFile(row) {\n      this.currentAssetId = row.assetId;\n      this.fileDialog = true;\n    }\n  }\n};\n</script>\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n\n.small-padding {\n  padding-left: 0;\n  padding-right: 0;\n  width: 150px;\n}\n\n::v-deep .small-padding .cell {\n  overflow: visible !important;\n}\n\n.operate {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 30px;\n}\n\n::v-deep .el-table {\n  display: flex;\n  flex-direction: column;\n}\n\n::v-deep .el-table__body-wrapper {\n  overflow-y: auto;\n  flex: 1;\n}\n\n::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\n  background: rgba(48, 111, 229, 0.8);\n  color: #FFFFFF;\n}\n\n::v-deep .el-tabs__content {\n  overflow-x: hidden;\n}\n</style>\n"]}]}