<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeWebmonitorUsableMapper">

    <resultMap type="FfsafeWebmonitorUsable" id="FfsafeWebmonitorUsableResult">
        <result property="id"    column="id"    />
        <result property="url"    column="url"    />
        <result property="systemName"    column="system_name"    />
        <result property="status"    column="status"    />
        <result property="responseTime"    column="response_time"    />
        <result property="reason"    column="reason"    />
        <result property="handleStatus"    column="handle_status"    />
        <result property="startTime"    column="start_time"    />
        <result property="recentlyTime"    column="recently_time"    />
    </resultMap>

    <sql id="selectFfsafeWebmonitorUsableVo">
        select id, url, system_name, status, response_time, reason, handle_status, start_time, recently_time from ffsafe_webmonitor_usable
    </sql>

    <select id="selectFfsafeWebmonitorUsableList" parameterType="FfsafeWebmonitorUsable" resultMap="FfsafeWebmonitorUsableResult">
        <include refid="selectFfsafeWebmonitorUsableVo"/>
        <where>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="responseTime != null "> and response_time = #{responseTime}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="handleStatus != null  and handleStatus != ''"> and handle_status = #{handleStatus}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="recentlyTime != null "> and recently_time = #{recentlyTime}</if>
        </where>
    </select>

    <select id="selectFfsafeWebmonitorUsableById" parameterType="Long" resultMap="FfsafeWebmonitorUsableResult">
        <include refid="selectFfsafeWebmonitorUsableVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeWebmonitorUsableByIds" parameterType="Long" resultMap="FfsafeWebmonitorUsableResult">
        <include refid="selectFfsafeWebmonitorUsableVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeWebmonitorUsable" parameterType="FfsafeWebmonitorUsable" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_webmonitor_usable
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="url != null">url,</if>
            <if test="systemName != null">system_name,</if>
            <if test="status != null">status,</if>
            <if test="responseTime != null">response_time,</if>
            <if test="reason != null">reason,</if>
            <if test="handleStatus != null">handle_status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="recentlyTime != null">recently_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="url != null">#{url},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="status != null">#{status},</if>
            <if test="responseTime != null">#{responseTime},</if>
            <if test="reason != null">#{reason},</if>
            <if test="handleStatus != null">#{handleStatus},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="recentlyTime != null">#{recentlyTime},</if>
        </trim>
    </insert>

    <update id="updateFfsafeWebmonitorUsable" parameterType="FfsafeWebmonitorUsable">
        update ffsafe_webmonitor_usable
        <trim prefix="SET" suffixOverrides=",">
            <if test="url != null">url = #{url},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="responseTime != null">response_time = #{responseTime},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="recentlyTime != null">recently_time = #{recentlyTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeWebmonitorUsableById" parameterType="Long">
        delete from ffsafe_webmonitor_usable where id = #{id}
    </delete>

    <delete id="deleteFfsafeWebmonitorUsableByIds" parameterType="String">
        delete from ffsafe_webmonitor_usable where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>