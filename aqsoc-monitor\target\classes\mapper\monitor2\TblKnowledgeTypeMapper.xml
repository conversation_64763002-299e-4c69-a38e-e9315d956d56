<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblKnowledgeTypeMapper">

    <resultMap type="com.ruoyi.monitor2.domain.TblKnowledgeType" id="TblKnowledgeTypeResult">
        <result property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="name" column="name"/>
        <result property="ancestors" column="ancestors"/>
        <result property="type" column="type"/>
        <result property="exParams" column="ex_params"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="remark" column="remark"/>
        <result property="sortNum"    column="sort_num"    />
    </resultMap>

    <sql id="selectTblKnowledgeTypeVo">
        select id, pid, name, ancestors,type,ex_params,user_id,dept_id,remark,sort_num
        from tbl_knowledge_type
    </sql>

    <select id="selectTblKnowledgeTypeList" parameterType="TblKnowledgeType" resultMap="TblKnowledgeTypeResult">
        <include refid="selectTblKnowledgeTypeVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="pid != null  and pid != ''">and find_in_set(#{pid},ancestors)</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="exParams != null  and exParams != ''">and ex_params = #{exParams}</if>
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
            <if test="deptId != null  and deptId != ''">and dept_id = #{deptId}</if>
        </where>
        order by  sort_num asc
    </select>
    <select id="selectTblKnowledgeTypeListChildById" parameterType="TblKnowledgeType" resultMap="TblKnowledgeTypeResult">
        <include refid="selectTblKnowledgeTypeVo"/>
        <where>
            <if test="id != null and id != ''">and find_in_set(#{id},ancestors)</if>
            <if test="type != null and type != ''">and type = #{type}</if>
        </where>
    </select>

    <select id="selectTblKnowledgeTypeById" parameterType="Long" resultMap="TblKnowledgeTypeResult">
        <include refid="selectTblKnowledgeTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertTblKnowledgeType" parameterType="TblKnowledgeType">
        insert into tbl_knowledge_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pid != null">pid,</if>
            <if test="name != null">name,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="type != null">type,</if>
            <if test="exParams != null">ex_params,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="remark != null">remark,</if>
            <if test="sortNum != null">sort_num,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pid != null">#{pid},</if>
            <if test="name != null">#{name},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="type != null">#{type},</if>
            <if test="exParams != null">#{exParams},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="sortNum != null">#{sortNum},</if>
        </trim>
    </insert>

    <update id="updateTblKnowledgeType" parameterType="TblKnowledgeType">
        update tbl_knowledge_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="pid != null">pid = #{pid},</if>
            <if test="name != null">name = #{name},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="type != null">type = #{type},</if>
            <if test="exParams != null">ex_params = #{exParams},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="sortNum != null">sort_num = #{sortNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblKnowledgeTypeById" parameterType="Long">
        delete from tbl_knowledge_type where id = #{id}
    </delete>

    <delete id="deleteTblKnowledgeTypeByIds" parameterType="String">
        delete from tbl_knowledge_type where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>