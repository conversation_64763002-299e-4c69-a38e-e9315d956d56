<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.work.mapper.TblWorkHistoryMapper">

    <resultMap type="TblWorkHistory" id="TblWorkHistoryResult">
        <result property="id"    column="id"    />
        <result property="workId"    column="work_id"    />
        <result property="handleUser"    column="handle_user"    />
        <result property="handleState" column="handle_state" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
        <result property="nodeProperties" column="node_properties" />
        <result property="nodeCode" column="node_code" />
        <result property="type" column="type" />
    </resultMap>

    <sql id="selectTblWorkHistoryVo">
        select id,work_id, handle_user, handle_state, create_by, create_time, update_by, update_time, is_del, node_properties, node_code,`type` from tbl_work_history
    </sql>

    <select id="selectTblWorkHistoryList" parameterType="TblWorkHistory" resultMap="TblWorkHistoryResult">
        <include refid="selectTblWorkHistoryVo"/>
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="workId != null"> and work_id = #{workId}</if>
            <if test="handleUser != null "> and handle_user = #{handleUser}</if>
            <if test="handleState != null  and handleState != ''"> and handle_state = #{handleState}</if>
            <if test="isDel != null  and isDel != ''"> and is_del = #{isDel}</if>
            <if test="type != null"> and `type` = #{type}</if>
        </where>
        ORDER BY create_time ASC
    </select>

    <select id="selectTblWorkHistoryByWorkId" parameterType="Long" resultMap="TblWorkHistoryResult">
        <include refid="selectTblWorkHistoryVo"/>
        where id = #{id}
    </select>

    <select id="selectTblWorkHistoryByWorkIds" parameterType="Long" resultMap="TblWorkHistoryResult">
        <include refid="selectTblWorkHistoryVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectWorkHistoryByWorkId" resultType="com.ruoyi.work.domain.TblWorkHistory" resultMap="TblWorkHistoryResult">
        <include refid="selectTblWorkHistoryVo"/>
        where work_id = #{workId} and handle_state != '-1' and handle_user &lt;> ''
    </select>

    <select id="selectWorkHistoryNodePropByWorkId" resultType="String">
        select node_properties from tbl_work_history
        where work_id = #{workId}
    </select>

    <insert id="insertTblWorkHistory" parameterType="TblWorkHistory">
        insert into tbl_work_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="workId != null">work_id,</if>
            <if test="handleUser != null">handle_user,</if>
            <if test="handleState != null">handle_state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="nodeProperties != null">node_properties,</if>
            <if test="nodeCode != null">node_code,</if>
            <if test="type != null">`type`,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="workId != null">#{workId},</if>
            <if test="handleUser != null">#{handleUser},</if>
            <if test="handleState != null">#{handleState},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="nodeProperties != null">#{nodeProperties},</if>
            <if test="nodeCode != null">#{nodeCode},</if>
            <if test="type != null">#{type},</if>
         </trim>
    </insert>

    <update id="updateTblWorkHistory" parameterType="TblWorkHistory">
        update tbl_work_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="handleUser != null">handle_user = #{handleUser},</if>
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="nodeProperties != null">node_properties = #{nodeProperties},</if>
            <if test="nodeCode != null">node_code = #{nodeCode},</if>
            <if test="type != null">`type` = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblWorkHistoryByWorkId" parameterType="Long">
        delete from tbl_work_history where id = #{id}
    </delete>

    <delete id="deleteTblWorkHistoryByWorkIds" parameterType="String">
        delete from tbl_work_history where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
