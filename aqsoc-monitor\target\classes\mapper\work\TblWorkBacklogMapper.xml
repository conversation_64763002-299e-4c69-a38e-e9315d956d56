<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.work.mapper.TblWorkBacklogMapper">

    <resultMap type="TblWorkBacklog" id="TblWorkBacklogResult">
        <id property="id" column="id" />
        <result property="workId"    column="work_id"    />
        <result property="handleUser"    column="handle_user"    />
        <result property="isCompletion" column="is_completion" />
        <result property="completionTime" column="completion_time" />
        <result property="nodeId" column="node_id" />
        <result property="flowState" column="flow_state" />
    </resultMap>

    <sql id="selectTblWorkBacklogVo">
        select id,work_id, handle_user,is_completion, completion_time, node_id, flow_state from tbl_work_backlog
    </sql>

    <select id="selectTblWorkBacklogList" parameterType="TblWorkBacklog" resultMap="TblWorkBacklogResult">
        <include refid="selectTblWorkBacklogVo"/>
        <where>
            <if test="workId != null "> and work_id = #{workId}</if>
            <if test="handleUser != null "> and handle_user = #{handleUser}</if>
            <if test="isCompletion != null "> and is_completion = #{isCompletion}</if>
            <if test="completionTime != null "> and completion_time = #{completionTime}</if>
            <if test="nodeId != null "> and node_id = #{nodeId}</if>
            <if test="nodeIds != null "> and node_id in
                <foreach item="nodeId" collection="nodeIds" open="(" separator="," close=")">
                    #{nodeId}
                </foreach>
            </if>
            <if test="flowState != null "> and flow_state = #{flowState}</if>
        </where>
    </select>

    <select id="selectTblWorkBacklogByWorkId" parameterType="Long" resultMap="TblWorkBacklogResult">
        <include refid="selectTblWorkBacklogVo"/>
        where work_id = #{workId}
    </select>

    <select id="selectTblWorkBacklogByWorkIds" parameterType="Long" resultMap="TblWorkBacklogResult">
        <include refid="selectTblWorkBacklogVo"/>
        where work_id in
        <foreach item="workId" collection="array" open="(" separator="," close=")">
            #{workId}
        </foreach>
    </select>

    <insert id="insertTblWorkBacklog" parameterType="TblWorkBacklog" keyProperty="id" useGeneratedKeys="true">
        insert IGNORE into tbl_work_backlog
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workId != null">work_id,</if>
            <if test="handleUser != null">handle_user,</if>
            <if test="isCompletion != null">is_completion,</if>
            <if test="completionTime != null">completion_time,</if>
            <if test="nodeId != null">node_id,</if>
            <if test="flowState != null">flow_state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workId != null">#{workId},</if>
            <if test="handleUser != null">#{handleUser},</if>
            <if test="isCompletion != null">#{isCompletion},</if>
            <if test="completionTime != null">#{completionTime},</if>
            <if test="nodeId != null">#{nodeId},</if>
            <if test="flowState != null">#{flowState},</if>
         </trim>
    </insert>

    <insert id="batchInsertTblWorkBacklog" useGeneratedKeys="true" keyProperty="id">
        insert IGNORE into tbl_work_backlog (work_id,handle_user,is_completion,completion_time,node_id,flow_state) values
        <foreach collection="workBacklogs" item="workBacklog" separator=",">
            (#{workBacklog.workId},#{workBacklog.handleUser},#{workBacklog.isCompletion},#{workBacklog.completionTime},#{workBacklog.nodeId},#{workBacklog.flowState})
        </foreach>
    </insert>

    <update id="updateTblWorkBacklog" parameterType="TblWorkBacklog">
        update tbl_work_backlog
        <trim prefix="SET" suffixOverrides=",">
            <if test="isCompletion != null">is_completion = #{isCompletion},</if>
            <if test="completionTime != null">completion_time = #{completionTime},</if>
            <if test="nodeId != null">node_id = #{nodeId},</if>
            <if test="flowState != null">flow_state = #{flowState},</if>
        </trim>
        where work_id = #{workId}
    </update>

    <delete id="deleteTblWorkBacklogByWorkId" parameterType="Long">
        delete from tbl_work_backlog where work_id = #{workId}
    </delete>

    <delete id="deleteTblWorkBacklogByWorkIds" parameterType="String">
        delete from tbl_work_backlog where work_id in
        <foreach item="workId" collection="array" open="(" separator="," close=")">
            #{workId}
        </foreach>
    </delete>
    <delete id="deleteTblWorkBacklogByWorkAndUser">
        delete from tbl_work_backlog where work_id = #{workId} and handle_user = #{handleUser}
    </delete>
    <delete id="deleteTblWorkBacklogById">
        delete from tbl_work_backlog where id = #{id}
    </delete>
</mapper>
