<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblCompanyDocMapper">
    
    <resultMap type="TblCompanyDoc" id="TblCompanyDocResult">
        <result property="id"    column="id"    />
        <result property="dwid"    column="dwid"    />
        <result property="title"    column="title"    />
        <result property="tm"    column="tm"    />
        <result property="type"    column="type"    />
        <result property="attach"    column="attach"    />
        <result property="content"    column="content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblCompanyDocVo">
        select id, dwid, title, tm, type, attach, content, create_by,
               create_time, update_by, update_time
        from tbl_company_doc a 
    </sql>

    <select id="selectTblCompanyDocList" parameterType="TblCompanyDoc" resultMap="TblCompanyDocResult">
        <include refid="selectTblCompanyDocVo"/>
        <where>  
            <if test="dwid != null "> and dwid = #{dwid}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="tm != null "> and tm = #{tm}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="attach != null  and attach != ''"> and attach = #{attach}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
        </where>
    </select>
    
    <select id="selectTblCompanyDocById" parameterType="Long" resultMap="TblCompanyDocResult">
        <include refid="selectTblCompanyDocVo"/>
        where a.id = #{id}
    </select>
        
    <insert id="insertTblCompanyDoc" parameterType="TblCompanyDoc">
        insert into tbl_company_doc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="dwid != null">dwid,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="tm != null">tm,</if>
            <if test="type != null">type,</if>
            <if test="attach != null">attach,</if>
            <if test="content != null">content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="dwid != null">#{dwid},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="tm != null">#{tm},</if>
            <if test="type != null">#{type},</if>
            <if test="attach != null">#{attach},</if>
            <if test="content != null">#{content},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblCompanyDoc" parameterType="TblCompanyDoc">
        update tbl_company_doc
        <trim prefix="SET" suffixOverrides=",">
            <if test="dwid != null">dwid = #{dwid},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="tm != null">tm = #{tm},</if>
            <if test="type != null">type = #{type},</if>
            <if test="attach != null">attach = #{attach},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblCompanyDocById" parameterType="Long">
        delete from tbl_company_doc where id = #{id}
    </delete>

    <delete id="deleteTblCompanyDocByIds" parameterType="String">
        delete from tbl_company_doc where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>