<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblInstitutionTypeMapper">

    <resultMap type="TblInstitutionType" id="TblInstitutionTypeResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="necessary"    column="necessary"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="sortNum"    column="sort_num"    />
    </resultMap>

    <sql id="selectTblInstitutionTypeVo">
        select id, name, type, parent_id, ancestors, necessary, create_by, create_time, update_by, update_time,sort_num from tbl_institution_type
    </sql>

    <select id="selectTblInstitutionTypeList" parameterType="TblInstitutionType" resultMap="TblInstitutionTypeResult">
        <include refid="selectTblInstitutionTypeVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="necessary != null  and necessary != ''"> and necessary = #{necessary}</if>
        </where>
        order by  sort_num asc,update_time desc,type desc
    </select>

    <select id="selectTblInstitutionTypeById" parameterType="Long" resultMap="TblInstitutionTypeResult">
        <include refid="selectTblInstitutionTypeVo"/>
        where id = #{id}
    </select>

    <select id="selectTblInstitutionTypeByIds" parameterType="Long" resultMap="TblInstitutionTypeResult">
        <include refid="selectTblInstitutionTypeVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblInstitutionType" parameterType="TblInstitutionType" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_institution_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="type != null">type,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="necessary != null">necessary,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="sortNum != null">sort_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="necessary != null">#{necessary},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sortNum != null">#{sortNum},</if>
         </trim>
    </insert>

    <update id="updateTblInstitutionType" parameterType="TblInstitutionType">
        update tbl_institution_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="necessary != null">necessary = #{necessary},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="sortNum != null">sort_num = #{sortNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblInstitutionTypeById" parameterType="Long">
        delete from tbl_institution_type where id = #{id}
    </delete>

    <delete id="deleteTblInstitutionTypeByIds" parameterType="String">
        delete from tbl_institution_type where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectChildrenCount" resultType="java.lang.Integer">
        select count(1)
        from tbl_institution_type
        WHERE FIND_IN_SET(#{id}, ancestors)
    </select>
    <select id="selectTypeChildren" resultType="com.ruoyi.monitor2.domain.TblInstitutionType">
        <include refid="selectTblInstitutionTypeVo"/>
        WHERE FIND_IN_SET(#{id}, ancestors)
    </select>

    <select id="queryInstitutionCountInt" resultType="java.lang.Integer">
        select COUNT(1) from tbl_institution_type where type='1'
    </select>

    <select id="queryInstitutionCountInfo" resultType="com.ruoyi.monitor2.domain.TblInstitutionType">
        select COUNT(1) as institutionNums,necessary from tbl_institution_type where type='1'
        group by necessary
    </select>

    <select id="queryInstitutionInfo" resultType="com.ruoyi.monitor2.domain.TblInstitutionType">
        select t.necessary,a.* from tbl_institution_type t  JOIN tbl_institution a ON t.id=a.type_id
        <where>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
        GROUP BY a.type_id
    </select>
</mapper>
