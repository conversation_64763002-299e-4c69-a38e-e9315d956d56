<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblNetworkDevicesMapper">

    <resultMap type="TblNetworkDevices" id="TblNetworkDevicesResult">
        <result property="assetId" column="asset_id"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="isVirtual" column="is_virtual"/>
        <result property="systemVersion" column="system_version"/>
        <result property="brandModel" column="brand_model"/>
        <result property="ipv4" column="ipv4"/>
        <result property="ipv6" column="ipv6"/>
        <result property="mac" column="mac"/>
        <result property="port" column="port"/>
        <result property="purpose" column="purpose"/>
        <result property="manger" column="manger"/>
        <result property="vendor" column="vendor"/>
        <result property="degreeImportance" column="degree_importance"/>
        <result property="assetType" column="asset_type"/>
        <result property="assetTypeDesc" column="asset_type_desc"/>
        <result property="assetClass" column="asset_class"/>
        <result property="assetClassDesc" column="asset_class_desc"/>
        <result property="netType" column="net_type"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="ver" column="ver"/>
        <result property="tags" column="tags"/>
        <result property="locationId" column="location_id"/>
        <result property="locationDetail" column="location_detail"/>
        <result property="domainId" column="domain_id"/>
        <result property="domainName" column="domain_name"/>
        <result property="laseScanState" column="last_scan_state"/>
        <result property="lastScanTime" column="last_scan_time"/>
        <result property="vulnNum" column="vuln_num"/>
        <result property="vnlnUpdateTime" column="vuln_update_time"/>
        <result property="state" column="state"/>
        <result property="uodTime" column="uod_time"/>
        <result property="manageDeptId" column="manage_dept_id"/>
        <result property="manageAddress" column="manage_address"/>
        <result property="interconnectVlan" column="interconnect_vlan"/>
        <result property="interconnectManage" column="interconnect_manage"/>
        <result property="switchInterconnectManage" column="switch_interconnect_manage"/>
        <result property="isSparing" column="is_sparing"/>
        <result property="usageTime" column="usage_time"/>
    </resultMap>

    <resultMap id="DeptNetworkDevicesCountMap" type="com.ruoyi.safe.vo.DeptNetworkDevicesCount">
        <result property="deptId" column="deptId"/>
        <result property="ancestors" column="ancestors"/>
        <result property="networkDevicesCount" column="networkDevicesCount"/>
    </resultMap>

    <sql id="selectTblNetworkDevicesVo">
        select a.asset_id, a.asset_code, a.asset_name, a.is_virtual, a.system_version, a.brand_model,
               a.purpose, a.degree_importance, a.manger, a.asset_type, tac.type_name asset_type_desc,
               a.asset_class, a.asset_class_desc, a.net_type, a.remark, a.create_by, a.create_time,
               a.update_by, a.update_time, a.user_id, a.dept_id, a.client_id, a.vendor,
           b.user_name, c.dept_name, d.vendor_name,a.ver,
        a.manage_dept_id,a.manage_address,a.interconnect_vlan,a.interconnect_manage,a.switch_interconnect_manage,a.is_sparing,a.usage_time
        from tbl_network_devices a
        left join sys_user b on b.user_id = a.user_id
        left join sys_dept c on a.dept_id = c.dept_id
        left join tbl_vendor d on a.vendor = d.id
        left join tbl_asset_class tac on a.asset_type = tac.id
    </sql>

<!--    <select id="selectTblNetworkDevicesList" parameterType="TblNetworkDevices" resultMap="TblNetworkDevicesResult">-->
<!--        <include refid="selectTblNetworkDevicesVo"/>-->
<!--        <where>-->
<!--            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>-->
<!--            <if test="assetCode != null  and assetCode != ''"> and a.asset_code = #{assetCode}</if>-->
<!--            <if test="assetName != null  and assetName != ''"> and a.asset_name like concat('%', #{assetName}, '%')</if>-->
<!--            <if test="degreeImportance != null  and degreeImportance != ''"> and a.degree_importance = #{degreeImportance}</if>-->
<!--            <if test="deptId != null "> and a.dept_id = #{deptId}</if>-->
<!--            <if test="assetType != null "> and a.asset_type = #{assetType}</if>-->
<!--            <if test="assetClass != null "> and a.asset_class = #{assetClass}</if>-->
<!--            <if test="netType != null "> and a.net_type = #{netType}</if>-->
<!--            <if test="deptId != null and deptId != 0">-->
<!--                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))-->
<!--            </if>-->
<!--            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>-->
<!--        </where>-->
<!--    </select>-->
    <select id="selectTblNetworkDevicesList" parameterType="TblNetworkDevices" resultMap="TblNetworkDevicesResult">
        select a.asset_id, a.asset_code, a.asset_name, a.is_virtual, a.system_version, a.brand_model,
        a.purpose, a.degree_importance, a.manger, a.asset_type, a.asset_type_desc,
        a.asset_class, a.asset_class_desc, a.net_type, a.remark, a.create_by, a.create_time,
        a.update_by, a.update_time, a.user_id, a.dept_id, a.client_id, a.vendor,
        b.user_name, c.dept_name, d.vendor_name,a.ver,
        e.tags, e.location_id, e.location_detail,e.uod_time, e.state,
        f.location_full_name,
        g.ipv4 as ip, g.mac, g.domain_id,g.last_scan_state,g.last_scan_time,g.vuln_num,g.vuln_update_time,n.domain_full_name domain_name,
        a.manage_dept_id,a.manage_address,a.interconnect_vlan,a.interconnect_manage,a.switch_interconnect_manage,a.is_sparing,a.usage_time
        from tbl_network_devices a
        left join sys_user b on b.user_id = a.user_id
        left join sys_dept c on a.dept_id = c.dept_id
        left join tbl_vendor d on a.vendor = d.id
        left join  tbl_asset_overview as e on a.asset_id = e.asset_id
        left join tbl_location as f on f.location_id = e.location_id
        left join tbl_network_ip_mac as g on g.asset_id = e.asset_id and g.main_ip='1'
        left join tbl_network_domain as n on g.domain_id is not null and g.domain_id = n.domain_id
        <where>
            <if test="userId != null and userId != ''">and a.user_id = #{userId}</if>
            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>
            <if test="assetCode != null  and assetCode != ''"> and a.asset_code = #{assetCode}</if>
            <if test="assetName != null  and assetName != ''"> and a.asset_name like concat('%', #{assetName}, '%')</if>
            <if test="degreeImportance != null  and degreeImportance != ''"> and a.degree_importance = #{degreeImportance}</if>
            <if test="assetType != null "> and a.asset_type = #{assetType}</if>
            <if test="assetClass != null "> and a.asset_class = #{assetClass}</if>
            <if test="netType != null "> and a.net_type = #{netType}</if>
            <if test="deptId != null and deptId != 0 and deptId != 100">
                and (a.dept_id = #{deptId} or FIND_IN_SET(#{deptId}, c.ancestors))
            </if>
            <if test="params.dataScope != null and params.dataScope != ''">
                ${params.dataScope}
            </if>
            <if test="ip != null "> and g.ipv4 = #{ip}</if>
            <if test="domainName != null "> and n.domain_name = #{domainName}</if>
            <if test="state != null "> and ((g.last_scan_state is not null And g.last_scan_state = #{state}) or (g.last_scan_state is null And e.state = #{state}))</if>
            <!--<if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>-->
        </where>
        order by a.update_time desc
    </select>

    <select id="selectTblNetworkDevicesByAssetId" parameterType="Long" resultMap="TblNetworkDevicesResult">
        <include refid="selectTblNetworkDevicesVo"/>
        where a.asset_id = #{assetId}
    </select>

    <select id="selectTblNetworkDevicesByAssetIds" parameterType="Long" resultMap="TblNetworkDevicesResult">
        <include refid="selectTblNetworkDevicesVo"/>
        where a.asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </select>

    <insert id="insertTblNetworkDevices" parameterType="TblNetworkDevices">
        insert into tbl_network_devices
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="assetCode != null">asset_code,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="isVirtual != null">is_virtual,</if>
            <if test="systemVersion != null">system_version,</if>
            <if test="brandModel != null">brand_model,</if>
            <if test="ipv4 != null">ipv4,</if>
            <if test="ipv6 != null">ipv6,</if>
            <if test="mac != null">mac,</if>
            <if test="port != null">port,</if>
            <if test="purpose != null">purpose,</if>
            <if test="vendor != null">vendor,</if>
            <if test="degreeImportance != null">degree_importance,</if>
            <if test="assetType != null">asset_type,</if>
            <if test="assetTypeDesc != null">asset_type_desc,</if>
            <if test="assetClass != null">asset_class,</if>
            <if test="assetClassDesc != null">asset_class_desc,</if>
            <if test="netType != null">net_type,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="ver != null">ver,</if>
            <if test="manageDeptId != null">manage_dept_id,</if>
            <if test="manageAddress != null">manage_address,</if>
            <if test="interconnectVlan != null">interconnect_vlan,</if>
            <if test="interconnectManage != null">interconnect_manage,</if>
            <if test="switchInterconnectManage != null">switch_interconnect_manage,</if>
            <if test="isSparing != null">is_sparing,</if>
            <if test="usageTime != null">usage_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="assetCode != null">#{assetCode},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="isVirtual != null">#{isVirtual},</if>
            <if test="systemVersion != null">#{systemVersion},</if>
            <if test="brandModel != null">#{brandModel},</if>
            <if test="ipv4 != null">#{ipv4},</if>
            <if test="ipv6 != null">#{ipv6},</if>
            <if test="mac != null">#{mac},</if>
            <if test="port != null">#{port},</if>
            <if test="purpose != null">#{purpose},</if>
            <if test="vendor != null">#{vendor},</if>
            <if test="degreeImportance != null">#{degreeImportance},</if>
            <if test="assetType != null">#{assetType},</if>
            <if test="assetTypeDesc != null">#{assetTypeDesc},</if>
            <if test="assetClass != null">#{assetClass},</if>
            <if test="assetClassDesc != null">#{assetClassDesc},</if>
            <if test="netType != null">#{netType},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="ver != null">#{ver},</if>
            <if test="manageDeptId != null">#{manageDeptId},</if>
            <if test="manageAddress != null">#{manageAddress},</if>
            <if test="interconnectVlan != null">#{interconnectVlan},</if>
            <if test="interconnectManage != null">#{interconnectManage},</if>
            <if test="switchInterconnectManage != null">#{switchInterconnectManage},</if>
            <if test="isSparing != null">#{isSparing},</if>
            <if test="usageTime != null">#{usageTime},</if>
         </trim>
    </insert>

    <update id="updateTblNetworkDevices" parameterType="TblNetworkDevices">
        update tbl_network_devices
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="isVirtual != null">is_virtual = #{isVirtual},</if>
            <if test="systemVersion != null">system_version = #{systemVersion},</if>
            <if test="brandModel != null">brand_model = #{brandModel},</if>
            <if test="ipv4 != null">ipv4 = #{ipv4},</if>
            <if test="ipv6 != null">ipv6 = #{ipv6},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="port != null">port = #{port},</if>
            <if test="purpose != null">purpose = #{purpose},</if>
            <if test="1==1">vendor = #{vendor},</if>
            <if test="degreeImportance != null">degree_importance = #{degreeImportance},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="assetTypeDesc != null">asset_type_desc = #{assetTypeDesc},</if>
            <if test="assetClass != null">asset_class = #{assetClass},</if>
            <if test="assetClassDesc != null">asset_class_desc = #{assetClassDesc},</if>
            <if test="netType != null">net_type = #{netType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="ver != null">ver = #{ver},</if>
            <if test="manageDeptId != null">manage_dept_id = #{manageDeptId},</if>
            <if test="manageAddress != null">manage_address = #{manageAddress},</if>
            <if test="interconnectVlan != null">interconnect_vlan = #{interconnectVlan},</if>
            <if test="interconnectManage != null">interconnect_manage = #{interconnectManage},</if>
            <if test="switchInterconnectManage != null">switch_interconnect_manage = #{switchInterconnectManage},</if>
            <if test="isSparing != null">is_sparing = #{isSparing},</if>
            usage_time = #{usageTime}
        </trim>
        where asset_id = #{assetId}
    </update>

    <delete id="deleteTblNetworkDevicesByAssetId" parameterType="Long">
        delete from tbl_network_devices where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblNetworkDevicesByAssetIds" parameterType="String">
        delete from tbl_network_devices where asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>

    <select id="assetSelectByNetworkDevices" parameterType="hashmap" resultMap="TblNetworkDevicesResult">
        select tnd.*,tba.asset_name as applicationName,tnim.ipv4 as ip
        from tbl_network_devices tnd
        left join tbl_application_netware tas on tnd.asset_id = tas.netware_id
        left join tbl_business_application tba on tas.asset_id = tba.asset_id
        left join tbl_network_ip_mac tnim on tnd.asset_id = tnim.asset_id and tnim.main_ip = '1'
        <where>
            <if test="assetId!= null">
                and tnd.asset_id = #{assetId}
            </if>
            <if test="deptId != null and deptId != 0">
                and (tnd.dept_id = #{deptId} or tnd.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="assetName != null and assetName != ''">
                and tnd.asset_name like concat('%', #{assetName}, '%')
            </if>
            <if test="ip != null and ip != ''">
                and tnim.ipv4 = #{ip}
            </if>
            <if test="applicationId!= null">
                and tba.asset_id = #{applicationId}
            </if>
        </where>
    </select>


    <select id="assetSelectByNetworkDevices2" parameterType="hashmap" resultMap="TblNetworkDevicesResult">
        select tnd.*,group_concat(tba.asset_name) as applicationName,tnim.ipv4 as ip
        from tbl_network_devices tnd
        left join tbl_application_netware tas on tnd.asset_id = tas.netware_id
        left join tbl_business_application tba on tas.asset_id = tba.asset_id
        left join tbl_network_ip_mac tnim on tnd.asset_id = tnim.asset_id
        <where>
            <if test="assetId!= null">
                and tnd.asset_id = #{assetId}
            </if>
            <if test="deptId != null and deptId != 0">
                and (tnd.dept_id = #{deptId} or tnd.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="assetName != null and assetName != ''">
                and tnd.asset_name like concat('%', #{assetName}, '%')
            </if>
            <if test="ip != null and ip != ''">
                and tnim.ipv4 = #{ip}
            </if>
        </where>
        group by tnd.asset_id
    </select>

    <select id="selectTblNetworkDevicesListLocationIdIsNotNull" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            a.asset_id assetId,
            a.asset_name assetName,
            a.asset_type_desc deviceType,
            d.vendor_phone vendorPhone,
            c.dept_name deptName,
            IF(d.vendor_name != '',d.vendor_name,'') vendorName,
            e.location_id locationId,
            IF(e.state = 0,'离线','在线') state,
            g.ipv4 AS ipAddr,
            g.last_scan_state lastScanState
        FROM
            tbl_network_devices a
                LEFT JOIN sys_user b ON b.user_id = a.user_id
                LEFT JOIN sys_dept c ON a.dept_id = c.dept_id
                LEFT JOIN tbl_vendor d ON a.vendor = d.id
                LEFT JOIN tbl_asset_overview AS e ON a.asset_id = e.asset_id
                LEFT JOIN tbl_location AS f ON f.location_id = e.location_id
                LEFT JOIN tbl_network_ip_mac AS g ON g.asset_id = e.asset_id
                AND g.main_ip = '1'
        WHERE
            e.location_id != ''
    </select>
    <select id="countNum" resultType="java.lang.Integer">
        select count(1) from tbl_network_devices
    </select>

    <select id="getDeptNetworkDevicesCount" parameterType="QueryDeptNetworkDevicesCountDto" resultMap="DeptNetworkDevicesCountMap">
        SELECT
        sd.dept_id AS deptId,sd.ancestors,
        <choose>
            <when test="params.userId != null">
                COALESCE(SUM(CASE WHEN a.user_id = #{params.userId} THEN 1 ELSE 0 END), 0)
            </when>
            <otherwise>
                COALESCE(COUNT(a.asset_id), 0)
            </otherwise>
        </choose>
        AS networkDevicesCount
        FROM sys_dept sd
        LEFT JOIN tbl_network_devices a ON a.dept_id = sd.dept_id <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        left join sys_user b on b.user_id = a.user_id
        left join tbl_vendor d on a.vendor = d.id
        left join  tbl_asset_overview as e on a.asset_id = e.asset_id
        left join tbl_location as f on f.location_id = e.location_id
        left join tbl_network_ip_mac as g on g.asset_id = e.asset_id and g.main_ip='1'
        left join tbl_network_domain as n on g.domain_id is not null and g.domain_id = n.domain_id
        WHERE sd.dept_id IN
        <foreach collection="deptIdList" item="deptId" separator="," close=")" open="(">
            #{deptId}
        </foreach>
        GROUP BY sd.dept_id
    </select>
</mapper>
