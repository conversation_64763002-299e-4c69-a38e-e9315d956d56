<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 事务统计Mapper -->
<mapper namespace="com.ruoyi.safe.mapper.OperationOverviewMapper">

    <!-- 数据权限过滤 -->
    <sql id="dataScope">
        left join sys_user su on su.user_id = t1.create_by
        left join sys_dept sd on sd.dept_id = su.dept_id
        <where>
            <if test="startTime != null and endTime != null">
                and t1.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="queryAll == null or queryAll == false">
                <if test="startTime != null and endTime != null">
                    and t1.create_time BETWEEN #{startTime} AND #{endTime}
                </if>
                <if test="onlySelf == true">
                    and (
                    1 = 2
                    <if test="createBy != null and createBy != ''">
                        OR t1.create_by = #{createBy}
                    </if>
                    <if test="flowHandleUser != null and flowHandleUser != ''">
                        OR FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user)
                    </if>
                    )
                </if>
                <if test="onlySelf == null or onlySelf == false">
                    and (
                    1 = 2
                    <if test="createBy != null and createBy != ''">
                        OR t1.create_by = #{createBy}
                    </if>
                    <if test="flowHandleUser != null and flowHandleUser != ''">
                        OR FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user)
                    </if>
                    <if test="handleDeptIds != null and handleDeptIds.size() > 0">
                        OR sd.dept_id in
                        <foreach item="handleDeptId" collection="handleDeptIds" open="(" separator="," close=")">
                            #{handleDeptId}
                        </foreach>
                    </if>
                    )
                </if>
            </if>
        </where>
    </sql>

    <!-- 总体统计 -->
    <select id="selectOverallStats" resultType="com.ruoyi.safe.vo.operationOverview.WorkClassStatVO">
        SELECT
            t1.work_class,
            COUNT(1) as total
        FROM tbl_operate_work_record t1
        left join sys_user su on su.user_id = t1.create_by
        left join sys_dept sd on sd.dept_id = su.dept_id
        <where>
            <if test="startTime != null and endTime != null">
                and t1.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="queryFlowState != null">
               and t1.work_class is not null
                <if test="queryFlowState == 1">
                    and (t1.f_flowstate != 100 and FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user))
                </if>
                <if test="queryFlowState == 2 and flowHandleUser == 1">
                    <!-- 已办 -->
                    and (t1.f_flowstate = 100 or NOT FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user))
                </if>
                <if test="queryFlowState == 2 and flowHandleUser != 1">
                    and (t1.f_flowstate = 100 and FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user))
                </if>
            </if>
            <if test="createBy != null and createBy != ''">
                OR t1.create_by = #{createBy}
            </if>
            <if test="queryAll == null or queryAll == false">
                <if test="onlySelf == true">
                    and (
                    1 = 2
                    <if test="createBy != null and createBy != ''">
                        OR t1.create_by = #{createBy}
                    </if>
                    <if test="flowHandleUser != null and flowHandleUser != ''">
                        OR FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user)
                    </if>
                    )
                </if>
                <if test="onlySelf == null or onlySelf == false">
                    and (
                    1 = 2
                    <if test="createBy != null and createBy != ''">
                        OR t1.create_by = #{createBy}
                    </if>
                    <if test="flowHandleUser != null and flowHandleUser != ''">
                        OR FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user)
                    </if>
                    <if test="handleDeptIds != null and handleDeptIds.size() > 0">
                        OR sd.dept_id in
                        <foreach item="handleDeptId" collection="handleDeptIds" open="(" separator="," close=")">
                            #{handleDeptId}
                        </foreach>
                    </if>
                    )
                </if>
            </if>
        </where>
        GROUP BY t1.work_class
        HAVING t1.work_class IS NOT NULL
    </select>

    <!-- 分类详情统计 -->
    <select id="selectClassDetails" resultType="com.ruoyi.safe.vo.operationOverview.WorkClassDetailVO">
        SELECT
            t1.work_class,
            SUM(CASE WHEN t1.f_flowstate != 100 and FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user) THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN t1.f_flowstate = 100 or t1.f_handle_user IS NULL or NOT FIND_IN_SET(#{flowHandleUser}, t1.f_handle_user) THEN 1 ELSE 0 END) as finished_count
        FROM tbl_operate_work_record t1
        <include refid="dataScope"/>
        GROUP BY t1.work_class
        HAVING t1.work_class IS NOT NULL
    </select>

    <!-- 具体事务统计 -->
    <select id="selectWorkItemStats" resultType="com.ruoyi.safe.vo.operationOverview.WorkItemStatVO">
        SELECT
            t1.work_class,
            t1.work_name,
            SUM(CASE WHEN t1.f_flowstate != 100 THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN t1.f_flowstate = 100 THEN 1 ELSE 0 END) as finished_count,
            MAX(t1.create_time) as latest_time
        FROM tbl_operate_work_record t1
        <include refid="dataScope"/>
        GROUP BY t1.work_class, t1.work_name
        HAVING t1.work_class IS NOT NULL
        ORDER BY latest_time desc
    </select>

</mapper>
