# 威胁告警攻击方向刷新功能开发任务

## 任务背景
为威胁告警模块添加攻击方向刷新功能，允许用户手动刷新单个或多个威胁告警的攻击方向，以应对网络拓扑变化或规则更新的情况。

## 需求分析
**核心功能**：
- 后端新增刷新攻击方向接口
- 前端在攻击方向列添加刷新图标
- 支持单个威胁告警的攻击方向重新计算和更新
- 实时更新前端显示，无需重新查询列表

**技术要求**：
- 复用现有AttackDirectionService服务
- 使用批量更新提高性能
- 零破坏性，不影响现有功能
- 遵循项目现有代码规范

## 技术方案

### 数据流设计
```
前端点击刷新图标 → 调用API → Controller接收ID列表 → 
Service查询告警记录 → AttackDirectionService重新计算 → 
批量更新数据库 → 返回结果 → 前端更新显示
```

### 核心算法
```java
String newDirection = attackDirectionService.determineAttackDirection(
    alarm.getSrcIp(), 
    alarm.getDestIp()
);
```

## 实施计划

### ✅ 阶段1：后端开发
**文件修改清单**：
1. `AttackDirectionRefreshVO.java` - 新建VO类
2. `TblThreatenAlarmController.java` - 新增刷新接口
3. `ITblThreatenAlarmService.java` - 新增Service方法
4. `TblThreatenAlarmServiceImpl.java` - 实现Service方法
5. `TblThreatenAlarmMapper.java` - 新增Mapper方法
6. `TblThreatenAlarmMapper.xml` - 新增批量更新SQL

### ⏳ 阶段2：前端开发
**文件修改清单**：
1. `threatenWarn.js` - 新增API方法
2. `eventList.vue` - 添加刷新图标和处理方法

### ⏳ 阶段3：测试验证
**测试内容**：
- 单元测试：AttackDirectionService方法测试
- 集成测试：前后端接口对接测试
- 功能测试：刷新功能完整流程测试

## 技术细节

### 接口设计
- **URL**: `/system/threadten/refreshAttackDirection`
- **方法**: POST
- **入参**: `List<Long> alarmIds`
- **出参**: `AjaxResult<List<AttackDirectionRefreshVO>>`

### 数据结构
```java
public class AttackDirectionRefreshVO {
    private Long alarmId;           // 威胁告警ID
    private String attackDirection; // 刷新后的攻击方向
}
```

### 批量更新SQL
```xml
<update id="batchUpdateAttackDirection">
    <foreach collection="list" item="item" separator=";">
        UPDATE tbl_threaten_alarm 
        SET attack_direction = #{item.attackDirection}
        WHERE id = #{item.id}
    </foreach>
</update>
```

## 风险控制
- **参数校验**：ID列表不能为空，ID必须存在
- **异常处理**：数据库更新失败、服务调用异常
- **性能考虑**：批量操作，避免N+1查询
- **并发控制**：防止重复点击

## 验收标准
1. 后端接口正常响应，返回正确的刷新结果
2. 前端图标正常显示，点击后能正确调用接口
3. 攻击方向能正确重新计算并更新到数据库
4. 前端显示能实时更新，无需刷新页面
5. 不影响现有的威胁告警查询和展示功能

## 开发进度
- [x] 需求分析和技术方案设计
- [x] 详细执行计划制定
- [ ] 后端开发
- [ ] 前端开发  
- [ ] 测试验证
- [ ] 部署上线
