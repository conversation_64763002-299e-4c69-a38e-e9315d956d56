<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblDeductionDetailMapper">

    <resultMap type="TblDeductionDetail" id="TblDeductionDetailResult">
        <result property="id"    column="id"    />
        <result property="deductionDate"    column="deduction_date"    />
        <result property="deductionType"    column="deduction_type"    />
        <result property="deductionLevel"    column="deduction_level"    />
        <result property="deductionScore"    column="deduction_score"    />
        <result property="userId"    column="user_id"    />
        <result property="departmentId"    column="department_id"    />
        <result property="riskType"    column="risk_type"    />
        <result property="referenceId"    column="reference_id"    />
        <result property="createdTime"    column="created_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectTblDeductionDetailVo">
        select id, deduction_date, deduction_type, deduction_level, deduction_score, user_id, department_id, risk_type, reference_id, created_time, created_by from tbl_deduction_detail
    </sql>

    <select id="selectTblDeductionDetailList" parameterType="TblDeductionDetail" resultMap="TblDeductionDetailResult">
        SELECT
        t1.id,
        t1.deduction_date,
        t1.deduction_type,
        t1.deduction_level,
        t1.deduction_score,
        t1.user_id,
        t1.department_id,
        t1.risk_type,
        t1.reference_id,
        t1.created_time,
        t1.created_by
        FROM
        (SELECT * FROM tbl_deduction_detail WHERE is_del = '1') t1
        <if test="hotsIpv4List != null and hotsIpv4List.size() > 0">
            LEFT JOIN monitor_bss_vuln_deal t2 ON t2.id = t1.reference_id
        </if>
        <if test="weakPasswordsIdList != null and weakPasswordsIdList.size() > 0">
            LEFT JOIN monitor_bss_wp_deal t3 ON t3.id = t1.reference_id
        </if>
        <if test="webLeakIdList != null and webLeakIdList.size() > 0">
            LEFT JOIN monitor_bss_webvuln_deal t4 ON t4.id = t1.reference_id
        </if>
        <if test="threatIpv4List != null and threatIpv4List.size() > 0">
            LEFT JOIN tbl_threaten_alarm t5 ON t5.id = t1.reference_id
        </if>
        <where>
            <if test="hotsIpv4List != null and hotsIpv4List.size() > 0">
                <trim prefix=" and (" suffix=" ) " prefixOverrides="or">
                    <if test="hotsIpv4List != null and hotsIpv4List.size() > 0">
                        or (t1.deduction_type='主机漏洞' AND t2.host_ip in
                        <foreach collection="hotsIpv4List" item="ipItem" open="(" separator="," close=")">
                            #{ipItem}
                        </foreach>
                            )
                    </if>
                    <if test="weakPasswordsIdList != null and weakPasswordsIdList.size() > 0">
                        or (t1.deduction_type='弱口令' AND t3.id in
                        <foreach collection="weakPasswordsIdList" item="wpItem" open="(" separator="," close=")">
                            #{wpItem}
                        </foreach>
                            )
                    </if>
                    <if test="webLeakIdList != null and webLeakIdList.size() > 0">
                        or (t1.deduction_type = 'WEB漏洞' AND t4.id in
                        <foreach collection="webLeakIdList" item="wpItem" open="(" separator="," close=")">
                            #{wpItem}
                        </foreach>
                            )
                    </if>
                    <if test="threatIpv4List != null and threatIpv4List.size() > 0">
                        or (t1.risk_type = '外部威胁' AND t5.src_ip in
                        <foreach collection="threatIpv4List" item="ipItem" open="(" separator="," close=")">
                            #{ipItem}
                        </foreach>
                        OR
                        t5.dest_ip in
                        <foreach collection="threatIpv4List" item="ipItem" open="(" separator="," close=")">
                            #{ipItem}
                        </foreach>
                        )
                    </if>
                </trim>
            </if>
            <if test="deductionDate != null "> and t1.deduction_date = #{deductionDate}</if>
            <if test="deductionType != null  and deductionType != ''"> and t1.deduction_type = #{deductionType}</if>
            <if test="deductionLevel != null  and deductionLevel != ''"> and t1.deduction_level = #{deductionLevel}</if>
            <if test="deductionScore != null "> and t1.deduction_score = #{deductionScore}</if>
            <if test="userId != null "> and t1.user_id = #{userId}</if>
            <if test="riskType != null  and riskType != ''"> and t1.risk_type = #{riskType}</if>
            <if test="referenceId != null  and referenceId != ''"> and t1.reference_id = #{referenceId}</if>
            <if test="createdTime != null "> and t1.created_time = #{createdTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and t1.created_by = #{createdBy}</if>
            <if test="isDel != null  and isDel != ''"> and t1.is_del = #{isDel}</if>
        </where>
        order by t1.deduction_date desc
    </select>

    <select id="selectTblDeductionDetailById" parameterType="Long" resultMap="TblDeductionDetailResult">
        <include refid="selectTblDeductionDetailVo"/>
        where id = #{id}
    </select>

    <select id="selectTblDeductionDetailByIds" parameterType="Long" resultMap="TblDeductionDetailResult">
        <include refid="selectTblDeductionDetailVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getDeductionTotalList" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            temp1.deductionType,
            temp1.deductionScore,
            temp2.total_score totalScore,
            IF(temp1.deductionScore >= temp2.total_score,temp2.total_score,temp1.deductionScore) itemTotal
        FROM(
                SELECT
                    t2.type deductionType,
                    IF(SUM(deduction_score) IS NULL,0,SUM(deduction_score)) deductionScore
                FROM
                    (SELECT * FROM tbl_deduction_detail WHERE is_del = '1') t1
                    RIGHT JOIN tbl_threat_deduction_standard t2 ON t2.type = t1.deduction_type
                    <if test="hotsIpv4List != null and hotsIpv4List.size() > 0">
                        LEFT JOIN monitor_bss_vuln_deal t4 ON t4.id = t1.reference_id
                    </if>
                    <if test="weakPasswordsIdList != null and weakPasswordsIdList.size() > 0">
                        LEFT JOIN monitor_bss_wp_deal t5 ON t5.id = t1.reference_id
                    </if>
                    <if test="webLeakIdList != null and webLeakIdList.size() > 0">
                        LEFT JOIN monitor_bss_webvuln_deal t6 ON t6.id = t1.reference_id
                    </if>
                    <if test="threatIpv4List != null and threatIpv4List.size() > 0">
                        LEFT JOIN tbl_threaten_alarm t7 ON t7.id = t1.reference_id
                    </if>
                <where>
                    <if test="hotsIpv4List != null and hotsIpv4List.size() > 0">
                        <trim prefix=" and (" suffix=" ) " prefixOverrides="or">
                            <if test="hotsIpv4List != null and hotsIpv4List.size() > 0">
                                or (t1.deduction_type='主机漏洞' AND t4.host_ip in
                                <foreach collection="hotsIpv4List" item="ipItem" open="(" separator="," close=")">
                                    #{ipItem}
                                </foreach>
                                    )
                            </if>
                            <if test="weakPasswordsIdList != null and weakPasswordsIdList.size() > 0">
                                or (t1.deduction_type='弱口令' AND t5.id in
                                <foreach collection="weakPasswordsIdList" item="wpItem" open="(" separator=","
                                         close=")">
                                    #{wpItem}
                                </foreach>
                                    )
                            </if>
                            <if test="webLeakIdList != null and webLeakIdList.size() > 0">
                                or (t1.deduction_type='WEB漏洞' AND t6.id in
                                <foreach collection="webLeakIdList" item="wpItem" open="(" separator="," close=")">
                                    #{wpItem}
                                </foreach>
                                    )
                            </if>
                            <if test="threatIpv4List != null and threatIpv4List.size() > 0">
                                or (t1.risk_type = '外部威胁' AND t7.src_ip in
                                <foreach collection="threatIpv4List" item="ipItem" open="(" separator="," close=")">
                                    #{ipItem}
                                </foreach>
                                OR
                                t7.dest_ip in
                                <foreach collection="threatIpv4List" item="ipItem" open="(" separator="," close=")">
                                    #{ipItem}
                                </foreach>
                                )
                            </if>
                        </trim>
                    </if>
                </where>
                GROUP BY
                    t2.type
            ) temp1 LEFT JOIN tbl_threat_deduction_standard temp2 ON temp2.type = temp1.deductionType
    </select>
    <select id="selectLastThreatenDeductionDetail" resultType="com.ruoyi.safe.domain.TblDeductionDetail" resultMap="TblDeductionDetailResult">
        <include refid="selectTblDeductionDetailVo"/>
        where risk_type = '外部威胁'
        ORDER BY id DESC
        LIMIT 1
    </select>

    <insert id="insertTblDeductionDetail" parameterType="TblDeductionDetail" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_deduction_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deductionDate != null">deduction_date,</if>
            <if test="deductionType != null">deduction_type,</if>
            <if test="deductionLevel != null">deduction_level,</if>
            <if test="deductionScore != null">deduction_score,</if>
            <if test="userId != null">user_id,</if>
            <if test="departmentId != null">department_id,</if>
            <if test="riskType != null">risk_type,</if>
            <if test="referenceId != null">reference_id,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="createdBy != null and createdBy != ''">created_by,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deductionDate != null">#{deductionDate},</if>
            <if test="deductionType != null">#{deductionType},</if>
            <if test="deductionLevel != null">#{deductionLevel},</if>
            <if test="deductionScore != null">#{deductionScore},</if>
            <if test="userId != null">#{userId},</if>
            <if test="departmentId != null">#{departmentId},</if>
            <if test="riskType != null">#{riskType},</if>
            <if test="referenceId != null">#{referenceId},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="createdBy != null and createdBy != ''">#{createdBy},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <insert id="batchInsert">
        insert into tbl_deduction_detail (deduction_date, deduction_type, deduction_level, deduction_score, user_id, department_id, risk_type, reference_id, created_time, created_by, delete_time, is_del) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.deductionDate}, #{item.deductionType}, #{item.deductionLevel}, #{item.deductionScore}, #{item.userId}, #{item.departmentId}, #{item.riskType}, #{item.referenceId},#{item.createdTime},#{item.createdBy},#{item.deleteTime},#{item.isDel})
        </foreach>
    </insert>

    <update id="updateTblDeductionDetail" parameterType="TblDeductionDetail">
        update tbl_deduction_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="deductionDate != null">deduction_date = #{deductionDate},</if>
            <if test="deductionType != null">deduction_type = #{deductionType},</if>
            <if test="deductionLevel != null">deduction_level = #{deductionLevel},</if>
            <if test="deductionScore != null">deduction_score = #{deductionScore},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="riskType != null">risk_type = #{riskType},</if>
            <if test="referenceId != null">reference_id = #{referenceId},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="createdBy != null and createdBy != ''">created_by = #{createdBy},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblDeductionDetailById" parameterType="Long">
        delete from tbl_deduction_detail where id = #{id}
    </delete>

    <delete id="deleteTblDeductionDetailByIds" parameterType="String">
        delete from tbl_deduction_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTblDeductionDetailByDeductionDetail">
            delete from tbl_deduction_detail
            <where>
                <if test="id != null">
                    and id = #{id}
                </if>
                <if test="deductionType != null">
                    and deduction_type = #{deductionType}
                </if>
                <if test="riskType != null">
                    and risk_type = #{riskType}
                </if>
                <if test="referenceId != null">
                    and reference_id = #{referenceId}
                </if>
            </where>
    </delete>
</mapper>
