<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblNetworkSecurityOperationsIndexItemMapper">

    <resultMap type="TblNetworkSecurityOperationsIndexItem" id="TblNetworkSecurityOperationsIndexItemResult">
        <result property="id"    column="id"    />
        <result property="contentId"    column="content_id"    />
        <result property="evaluationMethod"    column="evaluation_method"    />
        <result property="score"    column="score"    />
        <result property="realityScore"    column="reality_score"    />
        <result property="weight"    column="weight"    />
        <result property="type"    column="type"    />
        <result property="sort"    column="sort"    />
        <result property="dataSource"    column="data_source"    />
    </resultMap>
    <resultMap id="TblNetworkSecurityOperationsIndexItemVoResult" type="com.ruoyi.monitor2.vo.TblNetworkSecurityOperationsIndexItemVo" extends="TblNetworkSecurityOperationsIndexItemResult">
        <result property="content" column="content" />
        <result property="lastIndexName" column="last_index_name" />
    </resultMap>

    <sql id="selectTblNetworkSecurityOperationsIndexItemVo">
        SELECT
            t1.id,
            t1.content_id,
            t1.evaluation_method,
            t1.score,
            t1.reality_score,
            t1.weight,
            t1.type,
            t1.sort,
            t1.data_source,
            t2.content,
            t3.`name` last_index_name
        FROM
            tbl_network_security_operations_index_item t1
            LEFT JOIN tbl_network_security_operations_index_content t2 ON t2.id = t1.content_id
            LEFT JOIN tbl_network_security_operations_index t3 ON t3.id = t2.index_id
    </sql>

    <select id="selectTblNetworkSecurityOperationsIndexItemList" parameterType="TblNetworkSecurityOperationsIndexItemDto" resultMap="TblNetworkSecurityOperationsIndexItemVoResult">
        <include refid="selectTblNetworkSecurityOperationsIndexItemVo"/>
        <where>
            <if test="id != null  and id != ''"> and t1.id = #{id}</if>
            <if test="contentId != null  and contentId != ''"> and t1.content_id = #{contentId}</if>
            <if test="content != null and content != ''"> and t2.content LIKE concat('%',#{content},'%') </if>
            <if test="evaluationMethod != null  and evaluationMethod != ''"> and t1.evaluation_method LIKE concat('%',#{evaluationMethod},'%')</if>
            <if test="score != null "> and t1.score = #{score}</if>
            <if test="realityScore != null "> and t1.reality_score = #{realityScore}</if>
            <if test="weight != null "> and t1.weight = #{weight}</if>
            <if test="type != null">and t1.type = #{type}</if>
            <if test="sort != null "> and t1.sort = #{sort}</if>
            <if test="dataSource != null  and dataSource != ''"> and t1.data_source = #{dataSource}</if>
            <if test="indexId != null  and indexId != ''">
                and (FIND_IN_SET(#{indexId},t3.ancestors) OR t3.id = #{indexId})
            </if>
        </where>
    </select>

    <select id="selectTblNetworkSecurityOperationsIndexItemById" parameterType="String" resultMap="TblNetworkSecurityOperationsIndexItemVoResult">
        <include refid="selectTblNetworkSecurityOperationsIndexItemVo"/>
        where t1.id = #{id}
    </select>

    <select id="selectTblNetworkSecurityOperationsIndexItemByIds" parameterType="String" resultMap="TblNetworkSecurityOperationsIndexItemResult">
        <include refid="selectTblNetworkSecurityOperationsIndexItemVo"/>
        where t1.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectIndexItemListByContentIds"
            resultType="com.ruoyi.monitor2.domain.TblNetworkSecurityOperationsIndexItem" resultMap="TblNetworkSecurityOperationsIndexItemResult">
        <include refid="selectTblNetworkSecurityOperationsIndexItemVo"/>
        where t1.content_id in
        <foreach item="contentId" collection="contentIds" open="(" separator="," close=")">
            #{contentId}
        </foreach>
    </select>

    <select id="selectTblNetworkSecurityOperationsIndexItemListByContentId"
            resultType="com.ruoyi.monitor2.domain.TblNetworkSecurityOperationsIndexItem" resultMap="TblNetworkSecurityOperationsIndexItemResult">
        <include refid="selectTblNetworkSecurityOperationsIndexItemVo"/>
        where t1.content_id = #{contentId}
    </select>

    <insert id="insertTblNetworkSecurityOperationsIndexItem" parameterType="TblNetworkSecurityOperationsIndexItem">
        insert into tbl_network_security_operations_index_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="contentId != null and contentId != ''">content_id,</if>
            <if test="evaluationMethod != null and evaluationMethod != ''">evaluation_method,</if>
            <if test="score != null">score,</if>
            <if test="realityScore != null">reality_score,</if>
            <if test="weight != null">weight,</if>
            <if test="type != null">type,</if>
            <if test="sort != null">sort,</if>
            <if test="dataSource != null and dataSource != ''">data_source,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="contentId != null and contentId != ''">#{contentId},</if>
            <if test="evaluationMethod != null and evaluationMethod != ''">#{evaluationMethod},</if>
            <if test="score != null">#{score},</if>
            <if test="realityScore != null">#{realityScore},</if>
            <if test="weight != null">#{weight},</if>
            <if test="type != null">#{type},</if>
            <if test="sort != null">#{sort},</if>
            <if test="dataSource != null and dataSource != ''">#{dataSource},</if>
         </trim>
    </insert>

    <insert id="batchInsert">
        insert into tbl_network_security_operations_index_item (id, content_id, evaluation_method, score, reality_score, weight,type, sort, data_source)
        values
        <foreach collection="indexItems" item="indexItem" separator=",">
            (#{indexItem.id}, #{indexItem.contentId}, #{indexItem.evaluationMethod}, #{indexItem.score}, #{indexItem.realityScore}, #{indexItem.weight},#{indexItem.type}, #{indexItem.sort}, #{indexItem.dataSource})
        </foreach>
    </insert>

    <insert id="batchInsertOrUpdate">
        insert into tbl_network_security_operations_index_item (id, content_id, evaluation_method, score, reality_score, weight,type, sort, data_source)
        values
        <foreach collection="indexItems" item="indexItem" separator=",">
            (#{indexItem.id}, #{indexItem.contentId}, #{indexItem.evaluationMethod}, #{indexItem.score}, #{indexItem.realityScore}, #{indexItem.weight},#{indexItem.type}, #{indexItem.sort}, #{indexItem.dataSource})
        </foreach>
        ON DUPLICATE KEY UPDATE evaluation_method = VALUES(evaluation_method), score = VALUES(score), reality_score = VALUES(reality_score), weight = VALUES(weight), type = VALUES(type),sort = VALUES(sort), data_source = VALUES(data_source)
    </insert>

    <update id="updateTblNetworkSecurityOperationsIndexItem" parameterType="TblNetworkSecurityOperationsIndexItem">
        update tbl_network_security_operations_index_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="contentId != null and contentId != ''">content_id = #{contentId},</if>
            <if test="evaluationMethod != null and evaluationMethod != ''">evaluation_method = #{evaluationMethod},</if>
            <if test="score != null">score = #{score},</if>
            <if test="realityScore != null">reality_score = #{realityScore},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="type != null">type = #{type},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="dataSource != null and dataSource != ''">data_source = #{dataSource},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblNetworkSecurityOperationsIndexItemById" parameterType="String">
        delete from tbl_network_security_operations_index_item where id = #{id}
    </delete>

    <delete id="deleteTblNetworkSecurityOperationsIndexItemByIds" parameterType="String">
        delete from tbl_network_security_operations_index_item where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
