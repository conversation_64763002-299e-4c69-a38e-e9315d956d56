<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.work.mapper.FfsafeIpfilterLogPlusMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.work.model.FfsafeIpfilterLogListVO">
        select a.* from ffsafe_ipfilter_log a
        <where>
                <if test="query.id!=null">
                    and a.id = #{query.id}
                </if>
                <if test="query.ip!=null and query.ip!=''">
                    and a.ip like concat('%', #{query.ip}, '%')
                </if>
                <if test="query.location!=null and query.location!=''">
                    and a.location like concat('%', #{query.location}, '%')
                </if>
                <if test="query.action!=null and query.action!=''">
                    and a.action like concat('%', #{query.action}, '%')
                </if>
                <if test="query.conditions!=null and query.conditions!=''">
                    and a.conditions like concat('%', #{query.conditions}, '%')
                </if>
                <if test="query.filterSrc!=null and query.filterSrc!=''">
                    and a.filter_src like concat('%', #{query.filterSrc}, '%')
                </if>
                <if test="query.createTime!=null">
                    and a.create_time = #{query.createTime}
                </if>
                <if test="query.releaseTime!=null">
                    and a.release_time = #{query.releaseTime}
                </if>
                <if test="query.remark!=null and query.remark!=''">
                    and a.remark like concat('%', #{query.remark}, '%')
                </if>
        </where>
        order by a.create_time desc
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.work.model.FfsafeIpfilterLogListVO">
        select a.* from ffsafe_ipfilter_log a
        where a.id=#{id}
    </select>
</mapper>
