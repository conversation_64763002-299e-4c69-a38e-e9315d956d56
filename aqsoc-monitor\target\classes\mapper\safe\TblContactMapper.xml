<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblContactMapper">

    <resultMap type="TblContact" id="TblContactResult">
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="userType"    column="user_type"    />
        <result property="email"    column="email"    />
        <result property="phonenumber"    column="phonenumber"    />
        <result property="sex"    column="sex"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTblContactVo">
        select user_id, dept_id, nick_name, user_type, email, phonenumber, sex, create_by, create_time, update_by, update_time, remark from tbl_contact
    </sql>

    <select id="selectTblContactList" parameterType="TblContact" resultMap="TblContactResult">
        <include refid="selectTblContactVo"/>
        <where>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="email != null  and email != ''"> and email like concat('%', #{email}, '%')</if>
            <if test="phonenumber != null  and phonenumber != ''"> and phonenumber = #{phonenumber}</if>
        </where>
    </select>

    <select id="selectTblContactByUserId" parameterType="Long" resultMap="TblContactResult">
        <include refid="selectTblContactVo"/>
        where user_id = #{userId}
    </select>

    <select id="selectTblContactByUserIds" parameterType="Long" resultMap="TblContactResult">
        <include refid="selectTblContactVo"/>
        where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <insert id="insertTblContact" parameterType="TblContact" useGeneratedKeys="true" keyProperty="userId">
        insert into tbl_contact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="userType != null">user_type,</if>
            <if test="email != null">email,</if>
            <if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
            <if test="sex != null">sex,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="userType != null">#{userType},</if>
            <if test="email != null">#{email},</if>
            <if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
            <if test="sex != null">#{sex},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTblContact" parameterType="TblContact">
        update tbl_contact
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phonenumber != null and phonenumber != ''">phonenumber = #{phonenumber},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteTblContactByUserId" parameterType="Long">
        delete from tbl_contact where user_id = #{userId}
    </delete>

    <delete id="deleteTblContactByUserIds" parameterType="String">
        delete from tbl_contact where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>
