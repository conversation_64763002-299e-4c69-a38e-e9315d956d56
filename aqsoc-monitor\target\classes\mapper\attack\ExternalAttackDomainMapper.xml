<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.external.mapper.ExternalAttackDomainMapper">

    <resultMap type="ExternalAttackDomain" id="ExternalAttackDomainResult">
        <result property="id"    column="id"    />
        <result property="systemName"    column="system_name"    />
        <result property="domain"    column="domain"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="deptId"    column="dept_id"    />
        <result property="discoveryTime"    column="discovery_time"    />
        <result property="responsiblePerson"    column="responsible_person"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deptName" column="dept_name"/>
        <result property="responsiblePersonName" column="responsible_person_name"/>
    </resultMap>

    <sql id="selectExternalAttackDomainVo">
        select a.id, a.system_name, a.domain, a.ip_address, a.dept_id, a.discovery_time, a.responsible_person, a.create_by, a.create_time, a.update_by, a.update_time,
               GROUP_CONCAT(CONCAT(m.nick_name, '-', m.phonenumber) ORDER BY FIND_IN_SET(m.user_id, a.responsible_person) SEPARATOR ',') AS responsible_person_name, d.dept_name
        from external_attack_domain a
        left join sys_user m ON FIND_IN_SET(m.user_id, a.responsible_person) > 0
        left join sys_dept d on a.dept_id=d.dept_id
    </sql>

    <select id="selectExternalAttackDomainList" parameterType="ExternalAttackDomain" resultMap="ExternalAttackDomainResult">
        <include refid="selectExternalAttackDomainVo"/>
        <where>
            <if test="systemName != null  and systemName != ''"> and a.system_name like concat('%', #{systemName}, '%')</if>
            <if test="domain != null  and domain != ''"> and a.domain like concat('%', #{domain}, '%')</if>
            <if test="ipAddress != null  and ipAddress != ''"> and a.ip_address like concat('%', #{ipAddress}, '%')</if>
            <if test="deptId != null and deptId != 0">
                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
        </where>
        GROUP BY a.id
        <if test="params != null">
            <if test="params.orderByColumn != null and params.isAsc != null">
                ORDER BY ${params.orderByColumn} ${params.isAsc}
            </if>
        </if>
    </select>

    <select id="selectExternalAttackDomainById" parameterType="Long" resultMap="ExternalAttackDomainResult">
        <include refid="selectExternalAttackDomainVo"/>
        where a.id = #{id}
    </select>

    <select id="selectExternalAttackDomainByIds" parameterType="Long" resultMap="ExternalAttackDomainResult">
        <include refid="selectExternalAttackDomainVo"/>
        where a.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertExternalAttackDomain" parameterType="ExternalAttackDomain" useGeneratedKeys="true" keyProperty="id">
        insert into external_attack_domain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="systemName != null">system_name,</if>
            <if test="domain != null">domain,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="discoveryTime != null">discovery_time,</if>
            <if test="responsiblePerson != null">responsible_person,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="systemName != null">#{systemName},</if>
            <if test="domain != null">#{domain},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="discoveryTime != null">#{discoveryTime},</if>
            <if test="responsiblePerson != null">#{responsiblePerson},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateExternalAttackDomain" parameterType="ExternalAttackDomain">
        update external_attack_domain
        <trim prefix="SET" suffixOverrides=",">
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="domain != null">domain = #{domain},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="discoveryTime != null">discovery_time = #{discoveryTime},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExternalAttackDomainById" parameterType="Long">
        delete from external_attack_domain where id = #{id}
    </delete>

    <delete id="deleteExternalAttackDomainByIds" parameterType="String">
        delete from external_attack_domain where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据 domain 查询单条数据 -->
    <select id="selectByDomain" resultMap="ExternalAttackDomainResult">
        SELECT * FROM external_attack_domain WHERE domain = #{domain}
    </select>

    <!-- 检查 domain 是否已存在（排除指定 id） -->
    <select id="checkDomainExistence" resultType="int">
        SELECT COUNT(*) FROM external_attack_domain
        <where>
            and domain = #{domain}
            <!-- 修改时排除指定 id -->
            <if test="id != null "> AND id != #{id} </if>
        </where>
    </select>
    <select id="countNum" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM external_attack_domain
    </select>
</mapper>
