<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenVulnExploitMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenVulnExploitListVO">
        select a.* from threaten_vuln_exploit a left join threaten_alarm_origin b on a.id = b.origin_id and (b.threaten_type = 'threaten_vuln_exploit' or b.threaten_type = '漏洞利用')
        <where>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.baseId!=null">
                and a.base_id = #{query.baseId}
            </if>
            <if test="query.isEncry!=null">
                and a.is_encry = #{query.isEncry}
            </if>
            <if test="query.attackDirection!=null">
                and a.attack_direction = #{query.attackDirection}
            </if>
            <if test="query.attackStage!=null">
                and a.attack_stage = #{query.attackStage}
            </if>
            <if test="query.attackResult!=null">
                and a.attack_result = #{query.attackResult}
            </if>
            <if test="query.handleAction!=null">
                and a.handle_action = #{query.handleAction}
            </if>
            <if test="query.vulnName!=null and query.vulnName!=''">
                and a.vuln_name like concat('%', #{query.vulnName}, '%')
            </if>
            <if test="query.softVendors!=null and query.softVendors!=''">
                and a.soft_vendors like concat('%', #{query.softVendors}, '%')
            </if>
            <if test="query.vulnLevel!=null">
                and a.vuln_level = #{query.vulnLevel}
            </if>
            <if test="query.vulnType!=null and query.vulnType!=''">
                and a.vuln_type like concat('%', #{query.vulnType}, '%')
            </if>
            <if test="query.vulnCode!=null and query.vulnCode!=''">
                and a.vuln_code like concat('%', #{query.vulnCode}, '%')
            </if>
            <if test="query.vulnPublisher!=null and query.vulnPublisher!=''">
                and a.vuln_publisher like concat('%', #{query.vulnPublisher}, '%')
            </if>
            <if test="query.vulnLink!=null and query.vulnLink!=''">
                and a.vuln_link like concat('%', #{query.vulnLink}, '%')
            </if>
            <if test="query.vulnUrl!=null and query.vulnUrl!=''">
                and a.vuln_url like concat('%', #{query.vulnUrl}, '%')
            </if>
            <if test="query.vulnKeypos!=null and query.vulnKeypos!=''">
                and a.vuln_keypos like concat('%', #{query.vulnKeypos}, '%')
            </if>
            <if test="query.serviceType!=null and query.serviceType!=''">
                and a.service_type like concat('%', #{query.serviceType}, '%')
            </if>
            <if test="query.taskInfo!=null and query.taskInfo!=''">
                and a.task_info like concat('%', #{query.taskInfo}, '%')
            </if>
            <if test="query.userName!=null and query.userName!=''">
                and a.user_name like concat('%', #{query.userName}, '%')
            </if>
            <if test="query.password!=null and query.password!=''">
                and a.password like concat('%', #{query.password}, '%')
            </if>
            <if test="query.vulnPath!=null and query.vulnPath!=''">
                and a.vuln_path like concat('%', #{query.vulnPath}, '%')
            </if>
            <if test="query.isLogin!=null">
                and a.is_login = #{query.isLogin}
            </if>
            <if test="query.filePath!=null and query.filePath!=''">
                and a.file_path like concat('%', #{query.filePath}, '%')
            </if>
            <if test="query.action!=null and query.action!=''">
                and a.action like concat('%', #{query.action}, '%')
            </if>
            <if test="query.fileUser!=null and query.fileUser!=''">
                and a.file_user like concat('%', #{query.fileUser}, '%')
            </if>
            <if test="query.fileSize!=null">
                and a.file_size = #{query.fileSize}
            </if>
            <if test="query.fileAuthority!=null and query.fileAuthority!=''">
                and a.file_authority like concat('%', #{query.fileAuthority}, '%')
            </if>
            <if test="query.authorityModifyTime!=null">
                and a.authority_modify_time = #{query.authorityModifyTime}
            </if>
            <if test="query.contentModifyTime!=null">
                and a.content_modify_time = #{query.contentModifyTime}
            </if>
            <if test="query.attackTime!=null">
                and a.attack_time = #{query.attackTime}
            </if>
            <if test="query.alarmId!=null">
                and b.alarm_id = #{query.alarmId}
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenVulnExploitListVO">
        select a.* from threaten_vuln_exploit a 
        where a.id=#{id}
    </select>
</mapper>
