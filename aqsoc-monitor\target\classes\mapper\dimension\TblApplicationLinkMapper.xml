<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dimension.mapper.TblApplicationLinkMapper">

    <resultMap type="TblApplicationLink" id="TblApplicationLinkResult">
        <result property="linkId"    column="link_id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="assetName" column="asset_name" />
        <result property="linkName"    column="link_name"    />
        <result property="linkIp"    column="link_ip"    />
        <result property="linkPort"    column="link_port"    />
        <result property="linkType"    column="link_type"    />
        <result property="linkModel"    column="link_model"    />
        <result property="leaderCheck" column="leader_check"/>
        <result property="boundaryId" column="boundary_id"/>
    </resultMap>

    <sql id="selectTblApplicationLinkVo">
        select link_id, asset_id, link_name, link_ip, link_port, link_type, link_model,leader_check,boundary_id from tbl_application_link
    </sql>

    <select id="selectTblApplicationLinkList" parameterType="TblApplicationLink" resultMap="TblApplicationLinkResult">
        select t1.link_id, t1.asset_id, t1.link_name, t1.link_ip, t1.link_port, t1.link_type, t1.link_model,t1.leader_check,t1.boundary_id,t2.asset_name from tbl_application_link t1
        left join tbl_business_application t2 on t2.asset_id=t1.asset_id
        left join sys_dept sd on sd.dept_id=t2.dept_id
        <where>
            <if test="assetId != null "> and t1.asset_id = #{assetId}</if>
            <if test="linkName != null  and linkName != ''"> and t1.link_name like concat('%', #{linkName}, '%')</if>
            <if test="linkIp != null  and linkIp != ''"> and t1.link_ip = #{linkIp}</if>
            <if test="linkPort != null  and linkPort != ''"> and t1.link_port = #{linkPort}</if>
            <if test="linkType != null  and linkType != ''"> and t1.link_type = #{linkType}</if>
            <if test="linkModel != null  and linkModel != ''"> and t1.link_model = #{linkModel}</if>
            <if test="deptId != null">
                and (t2.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
            </if>
        </where>
    </select>

    <select id="selectTblApplicationLinkByLinkId" parameterType="Long" resultMap="TblApplicationLinkResult">
        select t1.link_id, t1.asset_id, t1.link_name, t1.link_ip, t1.link_port, t1.link_type, t1.link_model,t1.leader_check,t1.boundary_id,t2.asset_name from tbl_application_link t1
               left join tbl_business_application t2 on t2.asset_id=t1.asset_id
        where t1.link_id = #{linkId}
    </select>

    <select id="selectTblApplicationLinkByLinkIds" parameterType="Long" resultMap="TblApplicationLinkResult">
        <include refid="selectTblApplicationLinkVo"/>
        where link_id in
        <foreach item="linkId" collection="array" open="(" separator="," close=")">
            #{linkId}
        </foreach>
    </select>

    <insert id="insertTblApplicationLink" parameterType="TblApplicationLink">
        insert into tbl_application_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="linkId != null">link_id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="linkName != null">link_name,</if>
            <if test="linkIp != null">link_ip,</if>
            <if test="linkPort != null">link_port,</if>
            <if test="linkType != null">link_type,</if>
            <if test="linkModel != null">link_model,</if>
            <if test="boundaryId != null">boundary_id,</if>
            <if test="leaderCheck != null">leader_check,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="linkId != null">#{linkId},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="linkName != null">#{linkName},</if>
            <if test="linkIp != null">#{linkIp},</if>
            <if test="linkPort != null">#{linkPort},</if>
            <if test="linkType != null">#{linkType},</if>
            <if test="linkModel != null">#{linkModel},</if>
            <if test="boundaryId != null">#{boundaryId},</if>
            <if test="leaderCheck != null">#{leaderCheck},</if>
        </trim>
    </insert>

    <update id="updateTblApplicationLink" parameterType="TblApplicationLink">
        update tbl_application_link
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="linkName != null">link_name = #{linkName},</if>
            <if test="linkIp != null">link_ip = #{linkIp},</if>
            <if test="linkPort != null">link_port = #{linkPort},</if>
            <if test="linkType != null">link_type = #{linkType},</if>
            <if test="linkModel != null">link_model = #{linkModel},</if>
            <if test="boundaryId != null">boundary_id = #{boundaryId},</if>
            <if test="leaderCheck != null">leader_check = #{leaderCheck},</if>
        </trim>
        where link_id = #{linkId}
    </update>

    <delete id="deleteTblApplicationLinkByLinkId" parameterType="Long">
        delete from tbl_application_link where link_id = #{linkId}
    </delete>

    <delete id="deleteTblApplicationLinkByLinkIds" parameterType="String">
        delete from tbl_application_link where link_id in
        <foreach item="linkId" collection="array" open="(" separator="," close=")">
            #{linkId}
        </foreach>
    </delete>

    <delete id="deleteTblApplicationLinkByAssetId" parameterType="Long">
        delete from tbl_application_link where asset_id =#{assetId}
    </delete>

    <select id="selectCountNum" parameterType="Long" resultType="java.lang.Integer">
        select count(0) from tbl_application_link
        <where>
            <if test="assetId != null "> asset_id = #{assetId}</if>
        </where>
    </select>
</mapper>
