<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.external.mapper.ExternalAttackAppMapper">

    <resultMap type="ExternalAttackApp" id="ExternalAttackAppResult">
        <result property="id"    column="id"    />
        <result property="appName"    column="app_name"    />
        <result property="appLatestVersion"    column="app_latest_version"    />
        <result property="appDownloadLink"    column="app_download_link"    />
        <result property="appPackageName"    column="app_package_name"    />
        <result property="deptId"    column="dept_id"    />
        <result property="appDeveloper"    column="app_developer"    />
        <result property="appStoreId"    column="app_store_id"    />
        <result property="appId"    column="app_id"    />
        <result property="appUpdateTime"    column="app_update_time"    />
        <result property="responsiblePerson"    column="responsible_person"    />
        <result property="discoveryTime"    column="discovery_time"    />
        <result property="entryType"    column="entry_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="platformType"    column="platform_type"    />
        <result property="serverAddress"    column="server_address"    />
        <result property="remark"    column="remark"    />
        <result property="deptName" column="dept_name"/>
        <result property="responsiblePersonName" column="responsible_person_name"/>
    </resultMap>

    <sql id="selectExternalAttackAppVo">
        select a.id, a.app_name, a.app_latest_version, a.app_download_link, a.app_package_name, a.dept_id, a.app_developer, a.app_store_id, a.app_id, a.app_update_time, a.responsible_person, a.discovery_time, a.entry_type,a.create_by, a.create_time, a.update_by, a.update_time, a.platform_type, a.server_address, a.remark,
        GROUP_CONCAT(CONCAT(m.nick_name, '-', m.phonenumber) ORDER BY FIND_IN_SET(m.user_id, a.responsible_person) SEPARATOR ',') AS responsible_person_name, d.dept_name
        from external_attack_app a
        left join sys_user m ON FIND_IN_SET(m.user_id, a.responsible_person) > 0
        left join sys_dept d on a.dept_id=d.dept_id
    </sql>

    <select id="selectExternalAttackAppList" parameterType="ExternalAttackApp" resultMap="ExternalAttackAppResult">
        <include refid="selectExternalAttackAppVo"/>
        <where>
            <if test="appName != null  and appName != ''"> and a.app_name like concat('%', #{appName}, '%')</if>
            <if test="appLatestVersion != null  and appLatestVersion != ''"> and a.app_latest_version like concat('%', #{appLatestVersion}, '%')</if>
            <if test="appDownloadLink != null  and appDownloadLink != ''"> and a.app_download_link like concat('%', #{appDownloadLink}, '%')</if>
            <if test="appPackageName != null  and appPackageName != ''"> and a.app_package_name like concat('%', #{appPackageName}, '%')</if>
            <if test="deptId != null and deptId != 0">
                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="appId != null  and appId != ''"> and a.app_id like concat('%', #{appId}, '%')</if>
        </where>
        GROUP BY a.id
        <if test="params != null">
            <if test="params.orderByColumn != null and params.isAsc != null">
                ORDER BY ${params.orderByColumn} ${params.isAsc}
            </if>
        </if>
    </select>

    <select id="selectExternalAttackAppById" parameterType="Long" resultMap="ExternalAttackAppResult">
        <include refid="selectExternalAttackAppVo"/>
        where id = #{id}
    </select>

    <select id="selectExternalAttackAppByIds" parameterType="Long" resultMap="ExternalAttackAppResult">
        <include refid="selectExternalAttackAppVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertExternalAttackApp" parameterType="ExternalAttackApp" useGeneratedKeys="true" keyProperty="id">
        insert into external_attack_app
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appName != null">app_name,</if>
            <if test="appLatestVersion != null">app_latest_version,</if>
            <if test="appDownloadLink != null">app_download_link,</if>
            <if test="appPackageName != null">app_package_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="appDeveloper != null">app_developer,</if>
            <if test="appStoreId != null">app_store_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="appUpdateTime != null">app_update_time,</if>
            <if test="responsiblePerson != null">responsible_person,</if>
            <if test="discoveryTime != null">discovery_time,</if>
            <if test="entryType != null">entry_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="platformType != null">platform_type,</if>
            <if test="serverAddress != null">server_address,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appName != null">#{appName},</if>
            <if test="appLatestVersion != null">#{appLatestVersion},</if>
            <if test="appDownloadLink != null">#{appDownloadLink},</if>
            <if test="appPackageName != null">#{appPackageName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="appDeveloper != null">#{appDeveloper},</if>
            <if test="appStoreId != null">#{appStoreId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="appUpdateTime != null">#{appUpdateTime},</if>
            <if test="responsiblePerson != null">#{responsiblePerson},</if>
            <if test="discoveryTime != null">#{discoveryTime},</if>
            <if test="entryType != null">#{entryType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="platformType != null">#{platformType},</if>
            <if test="serverAddress != null">#{serverAddress},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateExternalAttackApp" parameterType="ExternalAttackApp">
        update external_attack_app
        <trim prefix="SET" suffixOverrides=",">
            <if test="appName != null">app_name = #{appName},</if>
            <if test="appLatestVersion != null">app_latest_version = #{appLatestVersion},</if>
            <if test="appDownloadLink != null">app_download_link = #{appDownloadLink},</if>
            <if test="appPackageName != null">app_package_name = #{appPackageName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="appDeveloper != null">app_developer = #{appDeveloper},</if>
            <if test="appStoreId != null">app_store_id = #{appStoreId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="appUpdateTime != null">app_update_time = #{appUpdateTime},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
            <if test="discoveryTime != null">discovery_time = #{discoveryTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="platformType != null">platform_type = #{platformType},</if>
            <if test="serverAddress != null">server_address = #{serverAddress},</if>
            <if test="remark != null">remark = #{remark},</if>

        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExternalAttackAppById" parameterType="Long">
        delete from external_attack_app where id = #{id}
    </delete>

    <delete id="deleteExternalAttackAppByIds" parameterType="String">
        delete from external_attack_app where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 检查 app 是否已存在（排除指定 id） -->
    <select id="checkAppExistence" resultType="int">
        SELECT COUNT(*) FROM external_attack_app
        <where>
            <if test="appId != null and appId != ''">
                AND app_id = #{appId}
            </if>
            and app_name = #{appName}
            <!-- 修改时排除指定 id -->
            <if test="id != null "> AND id != #{id} </if>
        </where>
    </select>

    <select id="selectByAppIds" parameterType="java.util.List" resultMap="ExternalAttackAppResult">
        SELECT * FROM external_attack_app WHERE app_id IN
        <foreach item="id" index="index" collection="appIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="countNum" resultType="java.lang.Integer">
        select count(1) from external_attack_app
    </select>


    <delete id="deleteByEntryTypeAndAppId" parameterType="java.util.Set">
        DELETE FROM external_attack_app
        WHERE CONCAT(IFNULL(app_name,'null'), '-', IFNULL(app_id,'null')) IN
        <foreach item="item" index="index" collection="uniqueKeys" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 选择性更新：只更新第三方接口返回的原有字段，保护用户维护的新增字段 -->
    <update id="selectiveUpdateByCondition" parameterType="ExternalAttackApp">
        UPDATE external_attack_app
        <trim prefix="SET" suffixOverrides=",">
            <!-- 只更新第三方接口返回的原有字段 -->
            <if test="appName != null">app_name = #{appName},</if>
            <if test="appLatestVersion != null">app_latest_version = #{appLatestVersion},</if>
            <if test="appDownloadLink != null">app_download_link = #{appDownloadLink},</if>
            <if test="appPackageName != null">app_package_name = #{appPackageName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="appDeveloper != null">app_developer = #{appDeveloper},</if>
            <if test="appStoreId != null">app_store_id = #{appStoreId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="appUpdateTime != null">app_update_time = #{appUpdateTime},</if>
            <if test="responsiblePerson != null">responsible_person = #{responsiblePerson},</if>
            <if test="discoveryTime != null">discovery_time = #{discoveryTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <!-- 注意：不更新新增字段 platform_type, server_address, remark -->
        </trim>
        WHERE CONCAT(IFNULL(app_name,'null'), '-', IFNULL(app_id,'null')) = CONCAT(IFNULL(#{appName},'null'), '-', IFNULL(#{appId},'null'))
          AND entry_type = 1
    </update>

    <!-- 根据唯一键查询现有记录 -->
    <select id="selectByUniqueKey" parameterType="String" resultMap="ExternalAttackAppResult">
        <include refid="selectExternalAttackAppVo"/>
        WHERE CONCAT(IFNULL(a.app_name,'null'), '-', IFNULL(a.app_id,'null')) = #{uniqueKey}
          AND a.entry_type = 1
        GROUP BY a.id
    </select>

    <!-- 批量查询现有记录 -->
    <select id="selectByUniqueKeys" parameterType="java.util.Set" resultMap="ExternalAttackAppResult">
        <include refid="selectExternalAttackAppVo"/>
        WHERE CONCAT(IFNULL(a.app_name,'null'), '-', IFNULL(a.app_id,'null')) IN
        <foreach item="item" index="index" collection="uniqueKeys" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND a.entry_type = 1
        GROUP BY a.id
    </select>

    <!-- 批量插入APP应用程序 -->
    <insert id="batchInsertExternalAttackApp" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into external_attack_app (
            app_name, app_latest_version, app_download_link, app_package_name,
            dept_id, app_developer, app_store_id, app_id, app_update_time,
            responsible_person, discovery_time, entry_type, create_by, create_time,
            update_by, update_time, platform_type, server_address, remark
        ) values
        <foreach collection="entityList" item="item" separator=",">
            (
                #{item.appName}, #{item.appLatestVersion}, #{item.appDownloadLink}, #{item.appPackageName},
                #{item.deptId}, #{item.appDeveloper}, #{item.appStoreId}, #{item.appId}, #{item.appUpdateTime},
                #{item.responsiblePerson}, #{item.discoveryTime}, #{item.entryType}, #{item.createBy}, #{item.createTime},
                #{item.updateBy}, #{item.updateTime}, #{item.platformType}, #{item.serverAddress}, #{item.remark}
            )
        </foreach>
    </insert>

</mapper>
