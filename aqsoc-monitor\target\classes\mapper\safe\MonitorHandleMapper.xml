<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.MonitorHandleMapper">

    <resultMap type="com.ruoyi.safe.domain.MonitorHandle" id="MonitorHandleResult" autoMapping="true">
        <result property="id"    column="id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="domainId"    column="domain_id"    />
        <result property="jobId"    column="job_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="name"    column="name"    />
        <result property="osVer"    column="os_ver"    />
        <result property="ip"    column="ip"    />
        <result property="mac"    column="mac"    />
        <result property="memo"    column="memo"    />
        <result property="state"    column="state"    />
        <result property="isVir"    column="is_vir"    />
        <result property="exceptionState"    column="exception_state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="enterMac"    column="enter_mac"    />
        <result property="enterName"    column="enter_name"    />
        <result property="enterOsName"    column="enter_os_name"    />
        <result property="enterState"    column="enter_state"    />
        <result property="domainNetwork"    column="domain_network"    />
        <result property="assetType"    column="assetType"    />
        <result property="deptName"    column="dept_name"    />
        <result property="assetClassDesc"    column="asset_class_desc"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <resultMap id="AssetTypeMap" type="com.ruoyi.safe.domain.AssetClass">
        <id column="id" property="id"></id>
        <result column="type_name"  property="typeName"></result>
        <result column="pid"  property="pid"></result>
        <result column="type_code"  property="typeCode"></result>
        <result column="type_desc"  property="typeDesc"></result>
        <result column="sequence"  property="sequence"></result>
        <result column="type_graded_protection"  property="typeGradedProtection"></result>
    </resultMap>

    <sql id="selectMonitorHandleVo">
        select id, asset_id, domain_id, job_id, dept_id, name, os_ver, ip, mac, memo, state, is_vir, create_by, create_time, update_by, update_time, remark from monitor_handle
    </sql>
    <sql id="selectMonitorHandleVo2">
        select mh.id, mh.asset_id, mh.job_id, mh.name, mh.os_ver, mh.domain_id, mh.dept_id, mh.ip, mh.mac, mh.state, mh.is_vir, mh.exception_state,mh.memo,
        mh.create_time,sd.dept_name, mh.update_time,tni.mac as enter_mac, tao.state as enter_state, tao.asset_name as enter_name, tao.asset_class_desc,
        tao.asset_type_desc as assetType, tnd.domain_full_name as domain_network, td.proc_name as enter_os_name, mh.remark,sj.job_type
        from monitor_handle mh
        left join tbl_asset_overview tao on tao.asset_id = mh.asset_id
        left join sys_dept sd on mh.dept_id = sd.dept_id
        left join tbl_deploy td on td.asset_id = mh.asset_id and td.softlx = '1'
        left join tbl_network_ip_mac tni on tni.asset_id = mh.asset_id and tni.ipv4 = mh.ip
        left join tbl_network_domain tnd on tnd.domain_id = mh.domain_id
        left join sys_job sj on mh.job_id=sj.job_id
    </sql>

    <select id="selectMonitorHandleList" parameterType="com.ruoyi.safe.domain.MonitorHandle" resultMap="MonitorHandleResult">
        <include refid="selectMonitorHandleVo2"/>
        <where>
            <if test="isAll == null or isAll == false"> and mh.is_del = 0</if>
            <if test="assetId != null "> and mh.asset_id = #{assetId}</if>
            <if test="domainId != null"> and mh.domain_id = #{domainId}</if>
            <if test="jobId != null "> and mh.job_id = #{jobId}</if>
            <if test="name != null  and name != ''"> and mh.name like concat('%', #{name}, '%')</if>
            <if test="osVer != null  and osVer != ''"> and mh.os_ver = #{osVer}</if>
            <if test="ip != null  and ip != ''"> and mh.ip like concat('%',#{ip},'%')</if>
            <if test="ipEqual != null  and ipEqual != ''"> and mh.ip #{ipEqual}</if>
            <if test="mac != null  and mac != ''"> and mh.mac = #{mac}</if>
            <if test="memo != null  and memo != ''"> and mh.memo = #{memo}</if>
            <if test="state != null  and state != ''"> and mh.state = #{state}</if>
            <if test="isVir != null "> and mh.is_vir = #{isVir}</if>
            <if test="exceptionState != null">and mh.exception_state = #{exceptionState}</if>
            <if test="assetTypeCode != null">and tao.asset_type = #{assetTypeCode}</if>
            <if test="deptId != null">and mh.dept_id = #{deptId}</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
            <if test="ips != null and ips.size() > 0">
                and mh.ip in
                <foreach collection="ips" item="ip" open="(" separator="," close=")">
                    #{ip}
                </foreach>
            </if>
            <if test="startTime != null"> and mh.create_time >= #{startTime}</if>
            <if test="endTime != null"> and mh.create_time &lt;= #{endTime}</if>
        </where>
        order by mh.update_time desc
    </select>

    <select id="selectMonitorHandleById" parameterType="java.lang.Long" resultMap="MonitorHandleResult">
        <include refid="selectMonitorHandleVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorHandleByIds" parameterType="java.lang.Long" resultMap="MonitorHandleResult">
        <include refid="selectMonitorHandleVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMonitorHandle" parameterType="com.ruoyi.safe.domain.MonitorHandle">
        insert into monitor_handle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="domainId != null">domain_id,</if>
            <if test="jobId != null">job_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="name != null">name,</if>
            <if test="osVer != null">os_ver,</if>
            <if test="ip != null">ip,</if>
            <if test="mac != null">mac,</if>
            <if test="memo != null">memo,</if>
            <if test="state != null">state,</if>
            <if test="isVir != null">is_vir,</if>
            <if test="exceptionState != null">exception_state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="domainId != null">#{domainId},</if>
            <if test="jobId != null">#{jobId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="name != null">#{name},</if>
            <if test="osVer != null">#{osVer},</if>
            <if test="ip != null">#{ip},</if>
            <if test="mac != null">#{mac},</if>
            <if test="memo != null">#{memo},</if>
            <if test="state != null">#{state},</if>
            <if test="isVir != null">#{isVir},</if>
            <if test="exceptionState != null">#{exceptionState},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="insertOrUpdateMonitorHandle" parameterType="com.ruoyi.safe.domain.MonitorHandle">
        <selectKey keyProperty="countData" resultType="java.lang.Integer" order="BEFORE">
            select count(1) from monitor_handle where ip = #{ip}
            <if test="assetId != null">and asset_id = #{assetId}</if>
            <if test="assetId == null">and asset_id is null</if>
        </selectKey>
        <if test="countData > 0">
            update monitor_handle
            <trim prefix="SET" suffixOverrides=",">
                <if test="assetId != null">asset_id = #{assetId},</if>
                <if test="domainId != null">domain_id = #{domainId},</if>
                <if test="jobId != null">job_id = #{jobId},</if>
                <if test="deptId != null">dept_id = #{deptId},</if>
                <if test="name != null">name = #{name},</if>
                <if test="osVer != null">os_ver = #{osVer},</if>
                <if test="ip != null">ip = #{ip},</if>
                <if test="mac != null">mac = #{mac},</if>
                <if test="memo != null">memo = #{memo},</if>
                <if test="state != null">state = #{state},</if>
                <if test="isVir != null">is_vir = #{isVir},</if>
                <if test="exceptionState != null">exception_state = #{exceptionState},</if>
                <if test="updateTime != null">update_time = #{updateTime},</if>
                <if test="remark != null and remark != ''">remark = #{remark},</if>
                <if test="isDel != null">is_del = #{isDel},</if>
            </trim>
            <where>
                ip = #{ip}
                <if test="assetId != null "> and asset_id = #{assetId}</if>
            </where>
        </if>
        <if test="countData == 0">
            insert into monitor_handle
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="assetId != null">asset_id,</if>
                <if test="domainId != null">domain_id,</if>
                <if test="jobId != null">job_id,</if>
                <if test="deptId != null">dept_id,</if>
                <if test="name != null">name,</if>
                <if test="osVer != null">os_ver,</if>
                <if test="ip != null">ip,</if>
                <if test="mac != null">mac,</if>
                <if test="memo != null">memo,</if>
                <if test="state != null">state,</if>
                <if test="isVir != null">is_vir,</if>
                <if test="exceptionState != null">exception_state,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="remark != null and remark != ''">remark,</if>
                <if test="isDel != null">is_del,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id},</if>
                <if test="assetId != null">#{assetId},</if>
                <if test="domainId != null">#{domainId},</if>
                <if test="jobId != null">#{jobId},</if>
                <if test="deptId != null">#{deptId},</if>
                <if test="name != null">#{name},</if>
                <if test="osVer != null">#{osVer},</if>
                <if test="ip != null">#{ip},</if>
                <if test="mac != null">#{mac},</if>
                <if test="memo != null">#{memo},</if>
                <if test="state != null">#{state},</if>
                <if test="isVir != null">#{isVir},</if>
                <if test="exceptionState != null">#{exceptionState},</if>
                <if test="createTime != null">#{createTime},</if>
                <if test="updateTime != null">#{updateTime},</if>
                <if test="remark != null and remark != ''">#{remark},</if>
                <if test="isDel != null">#{isDel},</if>
            </trim>
        </if>
    </update>

    <update id="updateMonitorHandle" parameterType="com.ruoyi.safe.domain.MonitorHandle">
        update monitor_handle
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="domainId != null">domain_id = #{domainId},</if>
            <if test="jobId != null">job_id = #{jobId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="osVer != null">os_ver = #{osVer},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="state != null">state = #{state},</if>
            <if test="isVir != null">is_vir = #{isVir},</if>
            <if test="exceptionState != null">exception_state = #{exceptionState},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorHandleById" parameterType="java.lang.Long">
        delete from monitor_handle where id = #{id}
    </delete>

    <delete id="deleteMonitorHandleByIds" parameterType="java.lang.String">
        delete from monitor_handle where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByIp">
        update monitor_handle set is_del = 1
        where ip in
        <foreach item="ip" collection="list" open="(" separator="," close=")">
            #{ip}
        </foreach>
    </delete>

    <select id="selectNeedDel" resultType="java.lang.Long">
        select mh.id from monitor_handle mh
        inner join tbl_deploy td on td.asset_id = mh.asset_id and td.softlx = '1'
        inner join tbl_product_finger tpf on tpf.prdid = td.prdid and mh.name = tpf.os_name
        union all
        select mh.id from monitor_handle mh
        inner join tbl_deploy td on td.asset_id = mh.asset_id and td.softlx = '1'
        inner join tbl_product tp on tp.prdid = td.prdid and mh.name = tp.proc_name
    </select>
    <select id="count" resultType="java.lang.Integer">
        select count(1)
        from monitor_handle mh
        <where>
            and mh.is_del='0'
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="domainId != null"> and domain_id = #{domainId}</if>
            <if test="jobId != null "> and job_id = #{jobId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="osVer != null  and osVer != ''"> and os_ver = #{osVer}</if>
            <if test="ip != null  and ip != ''"> and ip like concat('%',#{ip},'%')</if>
            <if test="mac != null  and mac != ''"> and mac = #{mac}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="isVir != null "> and is_vir = #{isVir}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
            <if test="exceptionState != null">and exception_state = #{exceptionState}</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
    </select>

    <select id="selectAssetClass" resultMap="AssetTypeMap">
        select t.id, t.type_name from tbl_asset_class t
        <where>
          (t.pid = #{pid} and t.type_graded_protection = #{typeGradedProtection}) or t.id = #{pid}
        </where>
    </select>
</mapper>
