<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dimension.mapper.TblAssetFileMapper">

    <resultMap type="TblAssetFile" id="TblAssetFileResult">
        <result property="id" column="id"/>
        <result property="assetId" column="asset_id"/>
        <result property="fileName" column="file_name"/>
        <result property="fileType" column="file_type"/>
        <result property="content" column="content"/>
        <result property="url" column="url"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTblAssetFileVo">
        select id, asset_id, file_name, file_type, content, url, remark, create_by, create_time, update_by, update_time from tbl_asset_file
    </sql>

    <select id="selectTblAssetFileList" parameterType="TblAssetFile" resultMap="TblAssetFileResult">
        <include refid="selectTblAssetFileVo"/>
        <where>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
        </where>
    </select>

    <select id="selectTblAssetFileById" parameterType="Long" resultMap="TblAssetFileResult">
        <include refid="selectTblAssetFileVo"/>
        where id = #{id}
    </select>

    <select id="selectTblAssetFileByIds" parameterType="Long" resultMap="TblAssetFileResult">
        <include refid="selectTblAssetFileVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblAssetFile" parameterType="TblAssetFile" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_asset_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="content != null">content,</if>
            <if test="url != null">url,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="content != null">#{content},</if>
            <if test="url != null">#{url},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTblAssetFile" parameterType="TblAssetFile">
        update tbl_asset_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="content != null">content = #{content},</if>
            <if test="url != null">url = #{url},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblAssetFileById" parameterType="Long">
        delete from tbl_asset_file where id = #{id}
    </delete>

    <delete id="deleteTblAssetFileByIds" parameterType="String">
        delete from tbl_asset_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
