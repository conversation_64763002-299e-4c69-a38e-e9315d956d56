<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblSystemLawRelMapper">

    <resultMap type="TblSystemLawRel" id="TblSystemLawRelResult">
        <result property="institutionId"    column="institution_id"    />
        <result property="lawId"    column="law_id"    />
        <result property="lawDesc"    column="law_desc"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblSystemLawRelVo">
        select institution_id, law_id, law_desc, create_by, create_time, update_by, update_time from tbl_system_law_rel
    </sql>

    <select id="selectTblSystemLawRelList" parameterType="TblSystemLawRel" resultMap="TblSystemLawRelResult">
        <include refid="selectTblSystemLawRelVo"/>
        <where>
            <if test="institutionId != null "> and institution_id = #{institutionId}</if>
            <if test="lawId != null "> and law_id = #{lawId}</if>
            <if test="lawDesc != null  and lawDesc != ''"> and law_desc = #{lawDesc}</if>
        </where>
    </select>

    <select id="selectTblSystemLawRelByInstitutionId" parameterType="Long" resultMap="TblSystemLawRelResult">
        <include refid="selectTblSystemLawRelVo"/>
        where institution_id = #{institutionId}
    </select>

    <select id="selectTblSystemLawRelByInstitutionIds" parameterType="Long" resultMap="TblSystemLawRelResult">
        <include refid="selectTblSystemLawRelVo"/>
        where institution_id in
        <foreach item="institutionId" collection="array" open="(" separator="," close=")">
            #{institutionId}
        </foreach>
    </select>

    <select id="selectTblSystemLawRelByLawIds" parameterType="Long" resultMap="TblSystemLawRelResult">
        <include refid="selectTblSystemLawRelVo"/>
        where law_id in
        <foreach item="lawId" collection="array" open="(" separator="," close=")">
            #{lawId}
        </foreach>
    </select>

    <insert id="insertTblSystemLawRel" parameterType="TblSystemLawRel">
        insert into tbl_system_law_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="institutionId != null">institution_id,</if>
            <if test="lawId != null">law_id,</if>
            <if test="lawDesc != null">law_desc,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="institutionId != null">#{institutionId},</if>
            <if test="lawId != null">#{lawId},</if>
            <if test="lawDesc != null">#{lawDesc},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblSystemLawRel" parameterType="TblSystemLawRel">
        update tbl_system_law_rel
        <trim prefix="SET" suffixOverrides=",">
            <if test="lawId != null">law_id = #{lawId},</if>
            <if test="lawDesc != null">law_desc = #{lawDesc},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where institution_id = #{institutionId}
    </update>

    <delete id="deleteTblSystemLawRelByInstitutionId" parameterType="Long">
        delete from tbl_system_law_rel where institution_id = #{institutionId}
    </delete>

    <delete id="deleteTblSystemLawRelByInstitutionIds" parameterType="String">
        delete from tbl_system_law_rel where institution_id in
        <foreach item="institutionId" collection="array" open="(" separator="," close=")">
            #{institutionId}
        </foreach>
    </delete>
</mapper>
