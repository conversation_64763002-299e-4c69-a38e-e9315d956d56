<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblApplicationDeployMapper">
    
    <resultMap type="TblApplicationDeploy" id="TblApplicationDeployResult">
        <result property="rid"    column="rid"    />
        <result property="aid"    column="aid"    />
        <result property="tid"    column="tid"    />
        <result property="sid"    column="sid"    />
        <result property="ip"       column="ip" />
        <result property="port"    column="port"    />
        <result property="func"    column="func"    />
        <result property="name"    column="name"    />
        <result property="ver"    column="ver"    />
        <result property="location"    column="location"    />
        <result property="logpos"    column="logpos"    />
        <result property="createTime"    column="create_time"    />
        <result property="pubtime"    column="pubtime"    />
    </resultMap>
    <resultMap id="ApplicationLeakResult" type="com.ruoyi.monitor2.vo.ApplicationLeak">
        <result property="appid"    column="appid"    />
        <result property="appname"    column="appname"    />
        <result property="appver"    column="appver"    />
        <result property="dwid"    column="dwid"    />
        <result property="deptid"    column="deptid"    />
        <result property="aid"    column="aid"    />
        <result property="pid"    column="pid"    />
        <result property="modulename" column="modulename"/>
        <result property="tid"    column="tid"    />
        <result property="assetname"    column="assetname"    />
        <result property="ip"       column="ip" />
        <result property="port"    column="port"    />
        <result property="softname"    column="softname"    />
        <result property="softver"    column="softver"    />
        <result property="prdid"    column="prdid"    />
        <result property="cnnvd"    column="cnnvd"    />
        <result property="cve"    column="cve"    />
        <result property="leakname"    column="leakname"    />
        <result property="grade"    column="grade"    />
        <result property="type"    column="type"    />
        <result property="source"    column="source"    />
        <result property="addr"    column="addr"    />
        <result property="createTime"    column="create_time"    />

        <result property="rid"    column="rid"    />
        <result property="func"    column="func"    />
        <result property="location"    column="location"    />
        <result property="logpos"    column="logpos"    />
        <result property="pubtime"    column="pubtime"    />
    </resultMap>


    <sql id="selectTblApplicationDeployVo">
        select rid, aid, tid, sid, ip, port, func, name, ver, location, logpos, create_time, pubtime
        from tbl_application_deploy
    </sql>

    <select id="selectTblApplicationDeployList" parameterType="TblApplicationDeploy" resultMap="TblApplicationDeployResult">
        <include refid="selectTblApplicationDeployVo"/>
        <where>  
            <if test="aid != null  and aid != ''"> and aid in (select tid from tbl_application where tid=#{aid} or pid=#{aid})</if>
            <if test="tid != null  and tid != ''"> and tid = #{tid}</if>
            <if test="sid != null  and sid != ''"> and sid = #{sid}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="port != null  and port != ''"> and port = #{port}</if>
            <if test="func != null  and func != ''"> and func = #{func}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="ver != null  and ver != ''"> and ver = #{ver}</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
            <if test="logpos != null  and logpos != ''"> and logpos = #{logpos}</if>
            <if test="pubtime != null "> and pubtime = #{pubtime}</if>
        </where>
    </select>
    
    <select id="selectTblApplicationDeployByRid" parameterType="Long" resultMap="TblApplicationDeployResult">
        <include refid="selectTblApplicationDeployVo"/>
        where rid = #{rid}
    </select>

    <!-- 查询所有有漏洞的应用部署列表 -->
    <select id="selectHasLeakDeployList" parameterType="com.ruoyi.monitor2.vo.ApplicationLeak" resultMap="ApplicationLeakResult">
        select * from(
            select case when (b.pid='0') then a.aid else b.pid end as appid, b.ver as appver,
                case when (b.dwid='') then b.dwid else f.dwid end as dwid, b.deptid, f.name as appname, a.aid, b.pid
                ,a.tid, a.ip, b.name as modulename
                ,a.name as softname, a.ver as softver, a.port
                ,a.rid,a.func, a.location,a.logpos, a.pubtime
                from tbl_application_deploy a
                left join tbl_application b on a.aid=b.tid
                left join tbl_application f on f.tid= (case when (b.pid='0') then a.aid else b.pid end)
        ) a where (a.pubtime &lt; a.create_time or a.pubtime is null)
        <if test="appname != null "> and a.appname like concat('%', #{appname}, '%')</if>
        <if test="assetname != null "> and a.assetname like concat('%', #{assetname}, '%')</if>
        <if test="softname != null "> and a.softname like concat('%',#{softname},'%')</if>
        <if test="leakname != null "> and a.leakname like concat('%', #{leakname}, '%')</if>
        <if test="appid != null "> and a.appid = #{appid}</if>
        <if test="tid != null "> and a.tid = #{tid}</if>
        <if test="aid != null "> and a.aid = #{aid}</if>
        <if test="prdid != null "> and a.prdid = #{prdid}</if>
        <if test="ip != null "> and a.ip = #{ip}</if>
        <if test="grade != null "> and a.grade = #{grade}</if>
        <if test="type != null "> and a.type = #{type}</if>
        <if test="source != null "> and a.source = #{source}</if>
    </select>

    <insert id="insertTblApplicationDeploy" parameterType="TblApplicationDeploy">
        insert into tbl_application_deploy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rid != null">rid,</if>
            <if test="aid != null">aid,</if>
            <if test="tid != null">tid,</if>
            <if test="sid != null">sid,</if>
            <if test="ip != null">ip,</if>
            <if test="port != null">port,</if>
            <if test="func != null">func,</if>
            <if test="name != null">name,</if>
            <if test="ver != null">ver,</if>
            <if test="location != null">location,</if>
            <if test="logpos != null">logpos,</if>
            <if test="createTime != null">create_time,</if>
            <if test="pubtime != null">pubtime,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rid != null">#{rid},</if>
            <if test="aid != null">#{aid},</if>
            <if test="tid != null">#{tid},</if>
            <if test="sid != null">#{sid},</if>
            <if test="ip != null">#{ip},</if>
            <if test="port != null">#{port},</if>
            <if test="func != null">#{func},</if>
            <if test="name != null">#{name},</if>
            <if test="ver != null">#{ver},</if>
            <if test="location != null">#{location},</if>
            <if test="logpos != null">#{logpos},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="pubtime != null">#{pubtime},</if>
         </trim>
    </insert>

    <update id="updateTblApplicationDeploy" parameterType="TblApplicationDeploy">
        update tbl_application_deploy
        <trim prefix="SET" suffixOverrides=",">
            <if test="aid != null">aid = #{aid},</if>
            <if test="tid != null">tid = #{tid},</if>
            <if test="sid != null">sid = #{sid},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="port != null">port = #{port},</if>
            <if test="func != null">func = #{func},</if>
            <if test="name != null">name = #{name},</if>
            <if test="ver != null">ver = #{ver},</if>
            <if test="location != null">location = #{location},</if>
            <if test="logpos != null">logpos = #{logpos},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="pubtime != null">pubtime = #{pubtime},</if>
        </trim>
        where rid = #{rid}
    </update>

    <delete id="deleteTblApplicationDeployByRid" parameterType="Long">
        delete from tbl_application_deploy where rid = #{rid}
    </delete>

    <delete id="deleteTblApplicationDeployByRids" parameterType="String">
        delete from tbl_application_deploy where rid in
        <foreach item="rid" collection="array" open="(" separator="," close=")">
            #{rid}
        </foreach>
    </delete>
</mapper>