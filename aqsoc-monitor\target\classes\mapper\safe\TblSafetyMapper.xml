<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblSafetyMapper">

    <resultMap type="TblSafety" id="TblSafetyResult">
        <result property="assetId" column="asset_id"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="isVirtual" column="is_virtual"/>
        <result property="systemVersion" column="system_version"/>
        <result property="brandModel" column="brand_model"/>
        <result property="purpose" column="purpose"/>
        <result property="ipAddr" column="ip_addr"/>
        <result property="vendor" column="vendor"/>
        <result property="degreeImportance" column="degree_importance"/>
        <result property="assetType" column="asset_type"/>
        <result property="assetTypeDesc" column="asset_type_desc"/>
        <result property="assetClass" column="asset_class"/>
        <result property="assetClassDesc" column="asset_class_desc"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="orgnId" column="orgn_id"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="ver" column="ver"/>
        <result property="tags" column="tags"/>
        <result property="locationId" column="location_id"/>
        <result property="locationDetail" column="location_detail"/>
        <result property="domainId" column="domain_id"/>
        <result property="domainName" column="domain_name"/>
        <result property="laseScanState" column="last_scan_state"/>
        <result property="lastScanTime" column="last_scan_time"/>
        <result property="vulnNum" column="vuln_num"/>
        <result property="vnlnUpdateTime" column="vuln_update_time"/>
        <result property="state" column="state"/>
        <result property="uodTime" column="uod_time"/>
        <result property="buyTime" column="buy_time"/>
        <result property="mExpirationDate" column="m_expiration_date"/>
        <result property="isSparing" column="is_sparing"/>
        <result property="mangerAddress" column="manage_address"/>
    </resultMap>

    <resultMap id="DeptSafetyCountMap" type="com.ruoyi.safe.vo.DeptSafetyCount">
        <result property="deptId" column="deptId"/>
        <result property="ancestors" column="ancestors"/>
        <result property="safetyCount" column="safetyCount"/>
    </resultMap>

    <sql id="selectTblSafetyVo">
        select a.asset_id,
               a.asset_code,
               a.asset_name,
               a.is_virtual,
               a.system_version,
               a.brand_model,
               a.purpose,
               a.ip_addr,
               a.vendor,
               a.degree_importance,
               a.asset_type,
               a.asset_type_desc,
               a.asset_class,
               a.asset_class_desc,
               a.remark,
               a.create_by,
               a.create_time,
               a.update_by,
               a.update_time,
               a.user_id,
               a.dept_id,
               a.orgn_id,
               d.vendor_name,
               a.ver,
               a.buy_time,a.m_expiration_date,a.is_sparing,a.manage_address
        from tbl_safety a
                 left join tbl_vendor d on a.vendor = d.id
    </sql>

<!--    <select id="selectTblSafetyList" parameterType="TblSafety" resultMap="TblSafetyResult">-->
<!--        <include refid="selectTblSafetyVo"/>-->
<!--        <where>-->
<!--            <if test="1==1"> and 1=1</if>-->
<!--            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>-->
<!--            <if test="assetCode != null  and assetCode != ''"> and a.asset_code = #{assetCode}</if>-->
<!--            <if test="assetName != null  and assetName != ''"> and a.asset_name like concat('%', #{assetName}, '%')</if>-->
<!--            <if test="degreeImportance != null  and degreeImportance != ''"> and a.degree_importance = #{degreeImportance}</if>-->
<!--            <if test="assetType != null  and assetType != ''"> and a.asset_type = #{assetType}</if>-->
<!--            <if test="assetClass != null  and assetClass != ''"> and a.asset_class = #{assetClass}</if>-->
<!--            <if test="deptId != null and deptId != 0">-->
<!--                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))-->
<!--            </if>-->
<!--            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>-->
<!--        </where>-->
<!--    </select>-->
    <select id="selectTblSafetyList" parameterType="TblSafety" resultMap="TblSafetyResult">
        select a.asset_id,a.asset_code,a.asset_name,a.is_virtual,a.system_version,a.brand_model,a.purpose,a.ip_addr,a.vendor,a.degree_importance,a.asset_type,a.asset_type_desc,a.asset_class,
               a.asset_class_desc,a.remark,a.create_by,a.create_time,a.update_by,a.update_time,a.user_id,a.dept_id,a.orgn_id,d.vendor_name,a.ver,a.ver,
               s.dept_name,
               e.tags, e.location_id, e.location_detail,e.uod_time,e.state,
               f.location_full_name,
               g.ipv4 as ip, g.mac, g.domain_id,g.last_scan_state,g.last_scan_time,g.vuln_num,g.vuln_update_time,n.domain_full_name domain_name,
               a.buy_time,a.m_expiration_date,a.is_sparing,a.manage_address
        from tbl_safety a
        left join sys_dept s on a.dept_id = s.dept_id
        left join tbl_vendor d on a.vendor = d.id
        left join  tbl_asset_overview as e on a.asset_id = e.asset_id
        left join tbl_location as f on f.location_id = e.location_id
        left join tbl_network_ip_mac as g on g.asset_id = e.asset_id and g.main_ip='1'
        left join tbl_network_domain as n on g.domain_id is not null and g.domain_id = n.domain_id
        <where>
            <if test="userId != null and userId != ''">and a.user_id = #{userId}</if>
            <if test="domainId != null and domainId != ''">and g.domain_id = #{domainId}</if>
            <if test="assetId != null and assetId != ''">and a.asset_id = #{assetId}</if>
            <if test="assetCode != null  and assetCode != ''"> and a.asset_code = #{assetCode}</if>
            <if test="assetName != null  and assetName != ''"> and a.asset_name like concat('%', #{assetName}, '%')</if>
            <if test="degreeImportance != null  and degreeImportance != ''"> and a.degree_importance = #{degreeImportance}</if>
            <if test="assetType != null  and assetType != ''"> and a.asset_type = #{assetType}</if>
            <if test="assetClass != null  and assetClass != ''"> and a.asset_class = #{assetClass}</if>
            <if test="deptId != null and deptId != 0">
                and (a.dept_id = #{deptId} or a.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="ip != null "> and g.ipv4 = #{ip}</if>
            <if test="domainName != null "> and n.domain_name = #{domainName}</if>
            <if test="state != null "> and ((g.last_scan_state is not null And g.last_scan_state = #{state}) or (g.last_scan_state is null And e.state = #{state}))</if>
            <!--<if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>-->
        </where>
        order by a.update_time desc
    </select>

    <select id="selectTblSafetyByAssetId" parameterType="Long" resultMap="TblSafetyResult">
        <include refid="selectTblSafetyVo"/>
        where a.asset_id = #{assetId}
    </select>

    <select id="selectTblSafetyByAssetIds" parameterType="Long" resultMap="TblSafetyResult">
        <include refid="selectTblSafetyVo"/>
        where a.asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </select>

    <insert id="insertTblSafety" parameterType="TblSafety">
        insert into tbl_safety
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="assetCode != null">asset_code,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="isVirtual != null">is_virtual,</if>
            <if test="systemVersion != null">system_version,</if>
            <if test="brandModel != null">brand_model,</if>
            <if test="purpose != null">purpose,</if>
            <if test="ipAddr != null">ip_addr,</if>
            <if test="vendor != null">vendor,</if>
            <if test="degreeImportance != null">degree_importance,</if>
            <if test="assetType != null">asset_type,</if>
            <if test="assetTypeDesc != null">asset_type_desc,</if>
            <if test="assetClass != null and assetClass != ''">asset_class,</if>
            <if test="assetClassDesc != null">asset_class_desc,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="orgnId != null">orgn_id,</if>
            <if test="ver != null">ver,</if>
            <if test="buyTime != null">buy_time,</if>
            <if test="mExpirationDate != null">m_expiration_date,</if>
            <if test="isSparing != null">is_sparing,</if>
            <if test="mangerAddress != null">manage_address,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="assetCode != null">#{assetCode},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="isVirtual != null">#{isVirtual},</if>
            <if test="systemVersion != null">#{systemVersion},</if>
            <if test="brandModel != null">#{brandModel},</if>
            <if test="purpose != null">#{purpose},</if>
            <if test="ipAddr != null">#{ipAddr},</if>
            <if test="vendor != null">#{vendor},</if>
            <if test="degreeImportance != null">#{degreeImportance},</if>
            <if test="assetType != null">#{assetType},</if>
            <if test="assetTypeDesc != null">#{assetTypeDesc},</if>
            <if test="assetClass != null and assetClass != ''">#{assetClass},</if>
            <if test="assetClassDesc != null">#{assetClassDesc},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="orgnId != null">#{orgnId},</if>
            <if test="ver != null">#{ver},</if>
            <if test="buyTime != null">#{buyTime},</if>
            <if test="mExpirationDate != null">#{mExpirationDate},</if>
            <if test="isSparing != null">#{isSparing},</if>
            <if test="mangerAddress != null">#{mangerAddress},</if>
         </trim>
    </insert>

    <update id="updateTblSafety" parameterType="TblSafety">
        update tbl_safety
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="isVirtual != null">is_virtual = #{isVirtual},</if>
            <if test="systemVersion != null">system_version = #{systemVersion},</if>
            <if test="brandModel != null">brand_model = #{brandModel},</if>
            <if test="purpose != null">purpose = #{purpose},</if>
            <if test="ipAddr != null">ip_addr = #{ipAddr},</if>
            <if test="vendor != null">vendor = #{vendor},</if>
            <if test="degreeImportance != null">degree_importance = #{degreeImportance},</if>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="assetTypeDesc != null">asset_type_desc = #{assetTypeDesc},</if>
            <if test="assetClass != null and assetClass != ''">asset_class = #{assetClass},</if>
            <if test="assetClassDesc != null">asset_class_desc = #{assetClassDesc},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="orgnId != null">orgn_id = #{orgnId},</if>
            <if test="ver != null">ver = #{ver},</if>
            <if test="isSparing != null">is_sparing = #{isSparing},</if>
            <if test="mangerAddress != null">manage_address = #{mangerAddress},</if>
            buy_time = #{buyTime},
            m_expiration_date = #{mExpirationDate},
        </trim>
        where asset_id = #{assetId}
    </update>

    <delete id="deleteTblSafetyByAssetId" parameterType="Long">
        delete from tbl_safety where asset_id = #{assetId}
    </delete>

    <delete id="deleteTblSafetyByAssetIds" parameterType="String">
        delete from tbl_safety where asset_id in
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>

    <select id="assetSelectBySafety" parameterType="hashmap" resultMap="TblSafetyResult">
        select safety.*,tba.asset_name as applicationName,tnim.ipv4 as ip
        from tbl_safety safety
        left join tbl_application_safe tas on safety.asset_id = tas.safe_id
        left join tbl_business_application tba on tas.asset_id = tba.asset_id
        left join tbl_network_ip_mac tnim on safety.asset_id = tnim.asset_id and tnim.main_ip = '1'
        <where>
            <if test="assetId!= null">
                and safety.asset_id = #{assetId}
            </if>
            <if test="deptId != null and deptId != 0">
                and (safety.dept_id = #{deptId} or safety.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="assetName != null and assetName != ''">
                and safety.asset_name like concat('%', #{assetName}, '%')
            </if>
            <if test="ip != null and ip != ''">
                and tnim.ipv4 = #{ip}
            </if>
            <if test="applicationId!= null">
                and tba.asset_id = #{applicationId}
            </if>
        </where>
    </select>
    <select id="assetSelectBySafety2" parameterType="hashmap" resultMap="TblSafetyResult">
        select safety.*,group_concat(tba.asset_name) as applicationName,tnim.ipv4 as ip
        from tbl_safety safety
        left join tbl_application_safe tas on safety.asset_id = tas.safe_id
        left join tbl_business_application tba on tas.asset_id = tba.asset_id
        left join tbl_network_ip_mac tnim on safety.asset_id = tnim.asset_id
        <where>
            <if test="assetId!= null">
                and safety.asset_id = #{assetId}
            </if>
            <if test="deptId != null and deptId != 0">
                and (safety.dept_id = #{deptId} or safety.dept_id in ( select t.dept_id from sys_dept t where find_in_set(#{deptId}, ancestors) ))
            </if>
            <if test="assetName != null and assetName != ''">
                and safety.asset_name like concat('%', #{assetName}, '%')
            </if>
            <if test="ip != null and ip != ''">
                and tnim.ipv4 = #{ip}
            </if>
        </where>
        group by safety.asset_id
    </select>

    <select id="selectTblSafetyLocationIdIsNotNull" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            a.asset_id assetId,
            a.asset_name assetName,
            g.ipv4 as ipAddr,
            a.asset_type_desc deviceType,
            d.vendor_name vendorName,
            d.vendor_phone vendorPhone,
            s.dept_name deptName,
            e.location_id locationId,
            IF(e.state = 0,'离线','在线') state,
            g.last_scan_state lastScanState
        FROM
            tbl_safety a
                LEFT JOIN sys_dept s ON a.dept_id = s.dept_id
                LEFT JOIN tbl_vendor d ON a.vendor = d.id
                LEFT JOIN tbl_asset_overview AS e ON a.asset_id = e.asset_id
                LEFT JOIN tbl_location AS f ON f.location_id = e.location_id
                LEFT JOIN tbl_network_ip_mac AS g ON g.asset_id = e.asset_id
                AND g.main_ip = '1'
        WHERE
            e.location_id != ''
    </select>

    <select id="selectFirewallListByIps" resultType="com.ruoyi.safe.domain.TblSafety" resultMap="TblSafetyResult">
        select t1.*,t2.ipv4 as ip from tbl_safety t1 left join tbl_network_ip_mac t2 on t2.asset_id = t1.asset_id where t2.ipv4 in
        <foreach item="ip" collection="ips" open="(" separator="," close=")">
            #{ip}
        </foreach>
    </select>
    <select id="countNum" resultType="java.lang.Integer">
        select count(1) from tbl_safety
    </select>

    <select id="getDeptSafetyCount" parameterType="QueryDeptSafetyCountDto" resultMap="DeptSafetyCountMap">
        SELECT
        sd.dept_id AS deptId,sd.ancestors,
        <choose>
            <when test="params.userId != null">
                COALESCE(SUM(CASE WHEN a.user_id = #{params.userId} THEN 1 ELSE 0 END), 0)
            </when>
            <otherwise>
                COALESCE(COUNT(a.asset_id), 0)
            </otherwise>
        </choose>
        AS safetyCount
        FROM sys_dept sd
        LEFT JOIN tbl_safety a ON a.dept_id = sd.dept_id <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        left join tbl_vendor d on a.vendor = d.id
        left join  tbl_asset_overview as e on a.asset_id = e.asset_id
        left join tbl_location as f on f.location_id = e.location_id
        left join tbl_network_ip_mac as g on g.asset_id = e.asset_id and g.main_ip='1'
        left join tbl_network_domain as n on g.domain_id is not null and g.domain_id = n.domain_id
        WHERE sd.dept_id IN
        <foreach collection="deptIdList" item="deptId" separator="," close=")" open="(">
            #{deptId}
        </foreach>
        GROUP BY sd.dept_id
    </select>
</mapper>
