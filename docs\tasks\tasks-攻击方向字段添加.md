# 威胁告警攻击方向字段添加任务

## 任务背景
在`tbl_threaten_alarm`表中添加攻击方向字段（attack_direction），该字段存储数据字典值，用于区分内网攻击、外网入侵等不同威胁类型。

## 需求分析
基于源IP（srcIp）和目标IP（destIp）与网络区域的匹配关系判断攻击方向：
1. **内对内（1）**：srcIp和destIp都能匹配到网络区域
2. **内对外（2）**：srcIp能匹配到网络区域，destIp不能匹配
3. **外对内（3）**：srcIp不能匹配到网络区域，destIp能匹配
4. **未知（4）**：其他所有情况（默认值）

## 数据字典设计
### 字典类型
- dict_type: "attack_direction"
- dict_name: "攻击方向"

### 字典数据
| dict_sort | dict_label | dict_value | is_default | remark |
|-----------|------------|------------|------------|---------|
| 1 | 内对内 | 1 | N | 内网到内网的攻击 |
| 2 | 内对外 | 2 | N | 内网到外网的攻击 |
| 3 | 外对内 | 3 | N | 外网到内网的攻击 |
| 4 | 未知 | 4 | Y | 无法判断攻击方向 |

## 需要修改的调用点（9个）
### TblThreatenAlarmServiceImpl.java (5个)
- insertTblThreatenAlarm() - 第222行
- insertTblThreatenAlarm() - 第202行 (重复数据更新)
- insertTblThreatenAlarmList() - 第280行 (重复数据更新)
- insertTblThreatenAlarmList() - 第302行
- updateTblThreatenAlarm() - 第347行

### ThreatenAlarmEngine.java (2个)
- 第96行：策略引擎插入
- 第110行：策略引擎更新

### ApiResultSeviceImpl.java (2个)
- 第124行：API接口插入
- 第132行：API接口更新

## 技术方案
采用集中式攻击方向判断服务，创建独立的AttackDirectionService，在所有调用点统一调用。

## 实施计划
1. 数据库层修改（字段+数据字典）
2. 实体类修改（添加属性）
3. MyBatis映射文件修改
4. 攻击方向判断服务实现
5. 调用点修改
6. 测试验证

## 核心判断逻辑
```java
boolean srcIsInternal = getDomainIdByIp(srcIp, domains) != null;
boolean destIsInternal = getDomainIdByIp(destIp, domains) != null;

if (srcIsInternal && destIsInternal) return "1"; // 内对内
if (srcIsInternal && !destIsInternal) return "2"; // 内对外
if (!srcIsInternal && destIsInternal) return "3"; // 外对内
return "4"; // 未知
```

## 零破坏性保证
- 新字段有默认值'4'
- 所有现有查询保持兼容
- 渐进式部署
