<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblProductFingerMapper">

    <resultMap type="com.ruoyi.monitor2.domain.TblProductFinger" id="TblProductFingerResult">
        <result property="id"    column="id"    />
        <result property="prdid"    column="prdid"    />
        <result property="osName"    column="os_name"    />
        <result property="osVer"    column="os_ver"    />
        <result property="osCpe"    column="os_cpe"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTblProductFingerVo">
        select id, prdid, os_name, os_ver, os_cpe, create_by, create_time, update_by, update_time, remark from tbl_product_finger
    </sql>

    <select id="selectTblProductFingerList" parameterType="com.ruoyi.monitor2.domain.TblProductFinger" resultMap="TblProductFingerResult">
        <include refid="selectTblProductFingerVo"/>
        <where>
            <if test="prdid != null  and prdid != ''"> and prdid = #{prdid}</if>
            <if test="osName != null  and osName != ''"> and os_name like concat('%', #{osName}, '%')</if>
            <if test="osVer != null  and osVer != ''"> and os_ver = #{osVer}</if>
            <if test="osCpe != null  and osCpe != ''"> and os_cpe = #{osCpe}</if>
        </where>
    </select>

    <select id="selectTblProductFingerById" parameterType="java.lang.Long" resultMap="TblProductFingerResult">
        <include refid="selectTblProductFingerVo"/>
        where id = #{id}
    </select>

    <select id="selectTblProductFingerByIds" parameterType="java.lang.Long" resultMap="TblProductFingerResult">
        <include refid="selectTblProductFingerVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    
    <select id="selectFingersByAssetId" parameterType="java.lang.Long" resultMap="TblProductFingerResult">
        select tpf.id, tpf.prdid, tpf.os_name, tpf.os_ver, tpf.os_cpe
        from tbl_product_finger tpf
        inner join tbl_deploy td on td.prdid = tpf.prdid and td.softlx = '1'
        where td.asset_id = #{assetId}
    </select>

    <insert id="insertTblProductFinger" parameterType="com.ruoyi.monitor2.domain.TblProductFinger" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_product_finger
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="prdid != null and prdid != ''">prdid,</if>
            <if test="osName != null">os_name,</if>
            <if test="osVer != null">os_ver,</if>
            <if test="osCpe != null">os_cpe,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="prdid != null and prdid != ''">#{prdid},</if>
            <if test="osName != null">#{osName},</if>
            <if test="osVer != null">#{osVer},</if>
            <if test="osCpe != null">#{osCpe},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTblProductFinger" parameterType="com.ruoyi.monitor2.domain.TblProductFinger">
        update tbl_product_finger
        <trim prefix="SET" suffixOverrides=",">
            <if test="prdid != null and prdid != ''">prdid = #{prdid},</if>
            <if test="osName != null">os_name = #{osName},</if>
            <if test="osVer != null">os_ver = #{osVer},</if>
            <if test="osCpe != null">os_cpe = #{osCpe},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblProductFingerById" parameterType="java.lang.Long">
        delete from tbl_product_finger where id = #{id}
    </delete>

    <delete id="deleteTblProductFingerByPrdId" parameterType="java.lang.String">
        delete from tbl_product_finger where prdid = #{prdid}
    </delete>

    <delete id="deleteTblProductFingerByIds" parameterType="java.lang.String">
        delete from tbl_product_finger where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
