<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenDdosAlarmMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenDdosAlarmListVO">
        select a.* from threaten_ddos_alarm a left join threaten_alarm_origin b on a.id = b.origin_id and (b.threaten_type = 'threaten_ddos_alarm' or b.threaten_type = '拒绝服务攻击')
        <where>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.baseId!=null">
                and a.base_id = #{query.baseId}
            </if>
            <if test="query.isEncry!=null">
                and a.is_encry = #{query.isEncry}
            </if>
            <if test="query.attackNum!=null">
                and a.attack_num = #{query.attackNum}
            </if>
            <if test="query.attackDirection!=null">
                and a.attack_direction = #{query.attackDirection}
            </if>
            <if test="query.attackStage!=null">
                and a.attack_stage = #{query.attackStage}
            </if>
            <if test="query.attackResult!=null">
                and a.attack_result = #{query.attackResult}
            </if>
            <if test="query.handleAction!=null">
                and a.handle_action = #{query.handleAction}
            </if>
            <if test="query.organization!=null and query.organization!=''">
                and a.organization like concat('%', #{query.organization}, '%')
            </if>
            <if test="query.totalPacketNum!=null">
                and a.total_packet_num = #{query.totalPacketNum}
            </if>
            <if test="query.totalByteNum!=null">
                and a.total_byte_num = #{query.totalByteNum}
            </if>
            <if test="query.peakPacketRate!=null">
                and a.peak_packet_rate = #{query.peakPacketRate}
            </if>
            <if test="query.peakByteRate!=null">
                and a.peak_byte_rate = #{query.peakByteRate}
            </if>
            <if test="query.peakStreamRate!=null">
                and a.peak_stream_rate = #{query.peakStreamRate}
            </if>
            <if test="query.attackTime!=null">
                and a.attack_time = #{query.attackTime}
            </if>
            <if test="query.alarmId!=null">
                and b.alarm_id = #{query.alarmId}
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenDdosAlarmListVO">
        select a.* from threaten_ddos_alarm a 
        where a.id=#{id}
    </select>
</mapper>
