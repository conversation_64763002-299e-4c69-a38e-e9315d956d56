<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.MonitorBssProcessMapper">

    <resultMap type="com.ruoyi.monitor2.domain.MonitorBssProcess" id="MonitorBssProcessResult">
        <result property="id"    column="id"    />
        <result property="jobId"    column="job_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="xprocessId"    column="xprocess_id"    />
        <result property="xprocessType"    column="xprocess_type"    />
        <result property="status"    column="status"    />
        <result property="hostNum"    column="host_num"    />
        <result property="vulnNum"    column="vuln_num"    />
        <result property="serviceNum"    column="service_num"    />
        <result property="webNum"    column="web_num"    />
        <result property="xprocessLog"    column="xprocess_log"    />
        <result property="reportId"    column="report_id"    />
        <result property="reportStatus"    column="report_status"    />
        <result property="reportDetail"    column="report_detail"    />
        <result property="minioFileName"    column="minio_file_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMonitorBssProcessVo">
        select id, job_id, plan_id, xprocess_id, xprocess_type, status, host_num, vuln_num, service_num, web_num, xprocess_log, report_id, report_status, report_detail, minio_file_name,  create_by, create_time, update_by, update_time from monitor_bss_process
    </sql>

    <select id="selectMonitorBssProcessList" parameterType="com.ruoyi.monitor2.domain.MonitorBssProcess" resultMap="MonitorBssProcessResult">
        <include refid="selectMonitorBssProcessVo"/>
        <where>
            <if test="jobId != null "> and job_id = #{jobId}</if>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="xprocessId != null "> and xprocess_id = #{xprocessId}</if>
            <!--<if test="planId != null "> and plan_id = #{planId}</if>
            <if test="xprocessId != null "> and xprocess_id = #{xprocessId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="hostNum != null "> and host_num = #{hostNum}</if>
            <if test="vulnNum != null "> and vuln_num = #{vulnNum}</if>
            <if test="serviceNum != null "> and service_num = #{serviceNum}</if>
            <if test="xprocessLog != null  and xprocessLog != ''"> and xprocess_log = #{xprocessLog}</if>-->
        </where>
        order by create_time desc
    </select>

    <select id="selectMonitorBssProcessById" parameterType="java.lang.Long" resultMap="MonitorBssProcessResult">
        <include refid="selectMonitorBssProcessVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorBssProcessByIds" parameterType="java.lang.Long" resultMap="MonitorBssProcessResult">
        <include refid="selectMonitorBssProcessVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectMonitorBssProcessByStatuss" parameterType="java.lang.Integer" resultMap="MonitorBssProcessResult">
        <include refid="selectMonitorBssProcessVo"/>
        where status in
        <foreach item="status" collection="array" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>

    <insert id="insertMonitorBssProcess" parameterType="com.ruoyi.monitor2.domain.MonitorBssProcess" useGeneratedKeys="true" keyProperty="id">
        insert into monitor_bss_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jobId != null">job_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="xprocessId != null">xprocess_id,</if>
            <if test="xprocessType != null">xprocess_type,</if>
            <if test="status != null">status,</if>
            <if test="hostNum != null">host_num,</if>
            <if test="vulnNum != null">vuln_num,</if>
            <if test="serviceNum != null">service_num,</if>
            <if test="webNum != null">web_num,</if>
            <if test="xprocessLog != null">xprocess_log,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobId != null">#{jobId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="xprocessId != null">#{xprocessId},</if>
            <if test="xprocessType != null">#{xprocessType},</if>
            <if test="status != null">#{status},</if>
            <if test="hostNum != null">#{hostNum},</if>
            <if test="vulnNum != null">#{vulnNum},</if>
            <if test="serviceNum != null">#{serviceNum},</if>
            <if test="webNum != null">#{webNum},</if>
            <if test="xprocessLog != null">#{xprocessLog},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateMonitorBssProcess" parameterType="com.ruoyi.monitor2.domain.MonitorBssProcess">
        update monitor_bss_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobId != null">job_id = #{jobId},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="xprocessId != null">xprocess_id = #{xprocessId},</if>
            <if test="xprocessType != null">xprocess_type = #{xprocessType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="hostNum != null">host_num = #{hostNum},</if>
            <if test="vulnNum != null">vuln_num = #{vulnNum},</if>
            <if test="serviceNum != null">service_num = #{serviceNum},</if>
            <if test="webNum != null">web_num = #{webNum},</if>
            <if test="xprocessLog != null">xprocess_log = #{xprocessLog},</if>
            <if test="reportId != null">report_id = #{reportId},</if>
            <if test="reportStatus != null">report_status = #{reportStatus},</if>
            <if test="reportDetail != null">report_detail = #{reportDetail},</if>
            <if test="minioFileName != null">minio_file_name = #{minioFileName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        <where>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="xprocessId != null "> and xprocess_id = #{xprocessId}</if>
        </where>
    </update>

    <delete id="deleteMonitorBssProcessById" parameterType="java.lang.Long">
        delete from monitor_bss_process where id = #{id}
    </delete>

    <delete id="deleteMonitorBssProcessByIds" parameterType="java.lang.String">
        delete from monitor_bss_process where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectJobInfo" parameterType="com.ruoyi.monitor2.domain.MonitorBssProcess"
            resultType="com.ruoyi.monitor2.domain.MonitorBssProcess">
        SELECT
            mbp.plan_id as planId,
            mbp.xprocess_id as xprocessId,
            mbp.STATUS,
            job_type as jobType,
            mbp.create_time as createTime,
            mbp.host_num as hostNum,
            cron_transfer as cronTransfer,
            mbp.xprocess_log as xprocessLog
        FROM
            monitor_bss_process mbp
                LEFT JOIN sys_job sy ON mbp.job_id = sy.job_id
        <where>
            <if test="jobId != null">
                mbp.job_id = #{jobId}
            </if>
            <if test="xprocessId != null">
                AND xprocess_id = #{xprocessId}
            </if>
        </where>
    </select>
</mapper>