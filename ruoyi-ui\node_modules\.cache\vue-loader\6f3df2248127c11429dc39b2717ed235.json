{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\attackViewList.vue?vue&type=style&index=0&id=d5d9d844&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\attackViewList.vue", "mtime": 1756369455986}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5hc3NldC10YWd7CiAgbWF4LXdpZHRoOiAxMDAlOwogIG92ZXJmbG93OiBoaWRkZW47CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOwp9Ci5kaXAtZGlhbG9newogIDo6di1kZWVwIC5lbC1kaWFsb2d7CgogIH0KICA6OnYtZGVlcCAuZWwtZGlhbG9nX19ib2R5ewogICAgcGFkZGluZzogMjBweDsKICAgIG1hcmdpbi10b3A6IDIwcHg7CiAgfQp9Ci5hdHRhY2stdGFnOm5vdCg6bnRoLWNoaWxkKC1uKzIpKSB7CiAgbWFyZ2luLXRvcDogNXB4Owp9Ci5vdmVyZmxvdy10YWc6bm90KDpmaXJzdC1jaGlsZCl7CiAgbWFyZ2luLXRvcDogNXB4Owp9Ci50aHJlYXRlbk5hbWUtZXJyb3J7CiAgY29sb3I6ICNGNTZDNkM7Cn0KLnRocmVhdGVuTmFtZS1zdWNjZXNzewogIGNvbG9yOiAjNjdDMjNBOwp9Ci50aHJlYXRlbk5hbWUtd2FybnsKICBjb2xvcjogI0U2QTIzQzsKfQoKLnRhZy1uYW1lIHsKICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgcGFkZGluZzogMnB4IDEycHg7CiAgbWFyZ2luLWJvdHRvbTogNXB4OwogIGZvbnQtc2l6ZTogMTRweDsKICBmb250LXdlaWdodDogbm9ybWFsOwogIGxpbmUtaGVpZ2h0OiAxLjQyODU3MTQzOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwogIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7CiAgY3Vyc29yOiBwb2ludGVyOwogIC13ZWJraXQtdXNlci1zZWxlY3Q6IG5vbmU7CiAgLW1vei11c2VyLXNlbGVjdDogbm9uZTsKICAtbXMtdXNlci1zZWxlY3Q6IG5vbmU7CiAgdXNlci1zZWxlY3Q6IG5vbmU7CiAgYmFja2dyb3VuZC1pbWFnZTogbm9uZTsKICBib3JkZXI6IDFweCBzb2xpZCB0cmFuc3BhcmVudDsKICBib3JkZXItcmFkaXVzOiA0cHg7Cn0KCjo6di1kZWVwIC5lbC1kaWFsb2dfX2JvZHkgewogIG1heC1oZWlnaHQ6IDgwdmg7CiAgcGFkZGluZzogMCAzMHB4IDMwcHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKfQo="}, {"version": 3, "sources": ["attackViewList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+0BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "attackViewList.vue", "sourceRoot": "src/views/frailty/event/component", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label-width=\"100px\" label=\"最近告警时间\">\n                <el-date-picker\n                  v-model=\"rangeTime\"\n                  type=\"datetimerange\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                  :default-time=\"['00:00:00', '23:59:59']\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"攻击者IP\" prop=\"destIp\">\n                <el-input\n                  v-model=\"queryParams.attackIp\"\n                  placeholder=\"请输入完整的攻击者IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"地理位置\" prop=\"location\">\n                <el-select v-model=\"queryParams.location\" clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in locationOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\"\n                >查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\"\n                >重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"阻断状态\" prop=\"blockStatus\">\n                <el-select v-model=\"queryParams.blockStatus\" clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in blockStatusOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"6\">\n              <el-form-item label=\"告警等级\" prop=\"riskLevel\">\n                <el-select v-model=\"queryParams.riskLevel\" clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in riskLevelOptions\"\n                    :key=\"item.dictValue\"\n                    :label=\"item.dictLabel\"\n                    :value=\"item.dictValue\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"custom-content-container\" :style=\"showAll ? { height: 'calc(100% - 248px)' } :{ height: 'calc(100% - 208px)' }\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">攻击者视角列表</span></div>\n          <div style=\"width: 50%; margin-left: 8%\">\n<!--            <attack-stage-text ref=\"stage\"/>-->\n          </div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"showHandleBatch\"\n                >批量处置\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleBlocking\"\n                >批量阻断</el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <div class=\"tableContainer\">\n        <el-table\n          height=\"100%\"\n          v-loading=\"loading\"\n          :data=\"attackAlarmList\"\n          @selection-change=\"handleSelectionChange\"\n          @expand-change=\"expandChange\" >\n          <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n          <el-table-column label=\"攻击者IP\" min-width=\"150\" prop=\"attackIp\" :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: flex-start\">\n                <span>{{ scope.row.attackIp }}</span>\n                <img v-if=\"scope.row.isBlocking\" style=\"width: 24px;margin-left: 10px\" src=\"@/assets/images/block.png\" alt=\"\">\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"关联业务系统\" prop=\"businessApplicationList\" width=\"130\">\n            <template slot-scope=\"scope\">\n              <el-tooltip placement=\"bottom-end\" effect=\"light\" v-if=\"scope.row.businessApplications && scope.row.businessApplications.length > 0\">\n                <div slot=\"content\">\n                  <div v-for=\"(item,tagIndex) in scope.row.businessApplications\" :key=\"item.assetId\" class=\"overflow-tag\" v-if=\"tagIndex <= 5\">\n                    <el-tag type=\"primary\"><span>{{item.assetName}}</span></el-tag>\n                  </div>\n                  <div v-if=\"scope.row.businessApplications.length > 5\">\n                    <el-tag type=\"primary\"><span>...</span></el-tag>\n                  </div>\n                </div>\n                <el-tag type=\"primary\" class=\"asset-tag\"><span>{{handleApplicationTagShow(scope.row.businessApplications)}}</span></el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"告警等级\" prop=\"riskLevel\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <dict-tag :options=\"dict.type.alarm_attack_risk_level\" :value=\"scope.row.riskLevel\"/>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"攻击者标签\"\n            prop=\"tags\"\n            :width=\"flexColumnWidth\"\n            :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: flex-start\">\n                <span class=\"tag-name\" v-for=\"(tag,index) in scope.row.tags\" :style=\"{ backgroundColor: tagBackgroundColor[index], color: tagColor[index], marginRight: scope.row.tags.length > 0 ? '5px' : '0'}\">{{ tag.tagName }}</span>\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"地理位置\" min-width=\"120\" prop=\"location\"  />\n          <el-table-column label=\"攻击目标IP数\" min-width=\"120\" prop=\"victimIpNums\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"text\" @click=\"openDipDetails(scope.row)\">{{scope.row.victimIpNums}}</el-button>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"命中规则数\" min-width=\"120\" prop=\"attackTypeNums\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"text\" @click=\"openThreatenNameDetails(scope.row)\">{{scope.row.attackTypeNums}}</el-button>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"告警数量\" min-width=\"120\" prop=\"attackNums\"  />\n          <el-table-column label=\"最早告警时间\" min-width=\"120\" prop=\"startTime\"  />\n          <el-table-column label=\"最近告警时间\" min-width=\"120\" prop=\"updateTime\"  />\n          <el-table-column label=\"同步状态\" prop=\"synchronizationStatus\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.synchronizationStatus === '0'\">未同步</span>\n              <span v-else-if=\"scope.row.synchronizationStatus === '1'\">已同步</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"处置状态\" prop=\"handleState\" width=\"120\" :formatter=\"handleStateFormatter\"/>\n          <el-table-column label=\"操作\" width=\"150\" fixed=\"right\" :show-overflow-tooltip=\"false\" class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n              <el-button v-if=\"!(scope.row.handleState == 1 || scope.row.handleState == 3)\"\n                         size=\"mini\"\n                         type=\"text\"\n                         @click=\"showHandle(scope.row)\"\n                         v-hasPermi=\"['frailty:loophole:edit']\"\n              >处置\n              </el-button>\n              <el-button\n                :disabled=\"!scope.row.attackIp\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleDetail(scope.row)\"\n              >详情\n              </el-button>\n              <el-button\n                :disabled=\"!scope.row.attackIp\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleBlocking(scope.row)\"\n              >阻断\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n\n        </div>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"category\">\n          <el-select\n            v-model=\"handleForm.handleState\"\n            placeholder=\"请选择处置状态\"\n          >\n            <el-option\n              v-for=\"dict in handleStateOption\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"handleForm.handleDesc\" maxlength=\"120\" show-word-limit\n                    placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleForm\">确 定</el-button>\n        <el-button @click=\"showHandleDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      title=\"批量处置\"\n      :visible.sync=\"showHandleBatchDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"category\">\n          <el-select\n            v-model=\"handleForm.handleState\"\n            placeholder=\"请选择处置状态\"\n          >\n            <el-option\n              v-for=\"dict in handleStateOption\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"handleForm.handleDesc\" maxlength=\"120\" show-word-limit\n                    placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleBatchForm\">确 定</el-button>\n        <el-button @click=\"showHandleBatchDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog title=\"详情\" :visible.sync=\"detailDialog\" width=\"70%\" append-to-body>\n      <detail-info v-if=\"detailDialog\" :host-ip=\"hostIp\" :detail-type=\"detailType\" :is-asset=\"isAsset\" :current-asset-data=\"currentAssetData\" />\n    </el-dialog>\n\n<!--    <el-drawer\n      :visible.sync=\"dipDrawerVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      <el-table :data=\"dipDetailsData\" v-loading=\"dipDrawerLoading\">\n        <el-table-column type=\"index\" label=\"序号\" width=\"80\"></el-table-column>\n        <el-table-column property=\"sip\" label=\"攻击IP\"></el-table-column>\n        <el-table-column property=\"dip\" label=\"目标IP\" :render-header=\"dipRenderHeader\">\n          <template slot-scope=\"scope\">\n            <span>{{scope.row.dip}}</span>\n            <span style=\"color: #ADADAD;margin-left: 5px;\" v-if=\"scope.row.serverName\">[{{scope.row.serverName}}]</span>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-drawer>-->\n\n    <el-dialog\n      :visible.sync=\"dipDrawerVisible\"\n      @close=\"victimIpNumTotal = 0\"\n      width=\"60%\" height=\"50%\" class=\"dip-dialog\">\n      <el-table :data=\"dipDetailsData\" v-loading=\"dipDrawerLoading\">\n<!--         <el-table-column type=\"index\" label=\"序号\" width=\"80\"></el-table-column>-->\n         <el-table-column property=\"sip\" label=\"攻击IP\"></el-table-column>\n         <el-table-column property=\"dip\" label=\"目标IP\" :render-header=\"dipRenderHeader\">\n           <template slot-scope=\"scope\">\n             <span>{{scope.row.dip}}</span>\n             <span style=\"color: #ADADAD;margin-left: 5px;\" v-if=\"scope.row.serverName\">[{{scope.row.serverName}}]</span>\n           </template>\n         </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"victimIpNumTotal>0\"\n        :total=\"victimIpNumTotal\"\n        :page.sync=\"victimIpNumQueryParams.pageNum\"\n        :limit.sync=\"victimIpNumQueryParams.pageSize\"\n        @pagination=\"openDipDetails(victimIpNumData)\"\n      />\n    </el-dialog>\n\n    <el-dialog\n      :visible.sync=\"threatenNameDrawerVisible\"\n      @close=\"hitRulesTotal = 0\"\n      width=\"60%\" height=\"50%\" class=\"dip-dialog\">\n      <el-table :data=\"threatenNameDetailsData\" v-loading=\"threatenNameDrawerLoading\">\n<!--        <el-table-column type=\"index\" label=\"序号\" width=\"80\"></el-table-column>-->\n        <el-table-column property=\"sip\" label=\"攻击IP\"></el-table-column>\n        <el-table-column property=\"threatenName\" label=\"[规则ID] 告警名称\">\n          <template slot-scope=\"scope\">\n            <span :class=\"scope.row.alarmLevel?scope.row.alarmLevel===4?'threatenName-error':scope.row.alarmLevel===3?'threatenName-warn':scope.row.alarmLevel===2?'threatenName-success':'':''\">{{scope.row.threatenName}}</span>\n          </template>\n        </el-table-column>\n        <el-table-column property=\"threatenType\" label=\"攻击类型\"></el-table-column>\n        <el-table-column property=\"count\" label=\"告警数量\"></el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"hitRulesTotal>0\"\n        :total=\"hitRulesTotal\"\n        :page.sync=\"hitRulesQueryParams.pageNum\"\n        :limit.sync=\"hitRulesQueryParams.pageSize\"\n        @pagination=\"openThreatenNameDetails(threatenNameDetailsQuery)\"\n      />\n    </el-dialog>\n\n<!--    <el-drawer\n      :visible.sync=\"threatenNameDrawerVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      <el-table :data=\"threatenNameDetailsData\" v-loading=\"threatenNameDrawerLoading\">\n        <el-table-column type=\"index\" label=\"序号\" width=\"80\"></el-table-column>\n        <el-table-column property=\"sip\" label=\"攻击IP\"></el-table-column>\n        <el-table-column property=\"threatenName\" label=\"[规则ID] 告警名称\">\n          <template slot-scope=\"scope\">\n            <span :class=\"scope.row.alarmLevel?scope.row.alarmLevel===4?'threatenName-error':scope.row.alarmLevel===3?'threatenName-warn':scope.row.alarmLevel===2?'threatenName-success':'':''\">{{scope.row.threatenName}}</span>\n          </template>\n        </el-table-column>\n        <el-table-column property=\"threatenType\" label=\"攻击类型\"></el-table-column>\n        <el-table-column property=\"count\" label=\"告警数量\"></el-table-column>\n      </el-table>\n    </el-drawer>-->\n\n    <batch-block :visible.sync=\"blockingDialogVisible\" ref=\"batchBlock\" />\n  </div>\n</template>\n\n<script>\nimport { listAttackAlarm,getDipDetails,getThreatenNameDetails,handleVuln,handleBatchVuln } from \"@/api/threaten/AttackAlarm\";\nimport { parseTime } from \"../../../../utils/ruoyi\";\nimport AttackStage from \"../../../threat/overview/attackStage\";\nimport DetailInfo from \"./attack_detail/index.vue\"\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport AttackStageText from '@/views/threat/overview/attackStageText.vue'\nimport {addBlockIp} from \"@/api/threaten/threatenWarn\";\nimport BatchBlock from \"@/views/frailty/event/component/batchBlock.vue\";\nimport {getMulTypeDict} from \"@/api/system/dict/data\";\n\nexport default {\n  name: \"attackViewList\",\n  components: { AttackStageText, DeptSelect, AttackStage, DetailInfo,BatchBlock },\n  dicts: ['alarm_attack_risk_level'],\n  props: {\n    propsActiveName: {\n      type: String\n    },\n    propsQueryParams: {\n      type: Object,\n      default: function () {\n        return null\n      }\n    },\n    currentBtn: {\n      type: Number,\n      default: null\n    }\n  },\n  data() {\n    return {\n      showHandleBatchDialog: false,\n      multiple: true,\n      handleRules: {},\n      showHandleDialog: false,\n      victimIpNumData: {},\n      threatenNameDetailsQuery:{},\n      count: 10,\n      showAll: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      victimIpNumQueryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      hitRulesQueryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      rangeTime: null,\n      loading: false,\n      descLoading: false,\n      attackAlarmList: [],\n      total: 0,\n      victimIpNumTotal: 0,\n      hitRulesTotal: 0,\n      detailDialog: false,\n      descKey: 0,\n      hostIp: '',\n      detailType: 'attack',\n      isAsset: false,\n      currentAssetData: {},\n      dipDrawerVisible: false,\n      dipDetailsData: [],\n      ids: [],\n      dipDrawerLoading: false,\n      threatenName: 1,\n      threatenNameDrawerVisible: false,\n      threatenNameDetailsData: [],\n      threatenNameDrawerLoading: false,\n      blockingDialogVisible: false,\n      multipleSelection: [],\n      locationOptions: [\n        {value: '1',label: '内网'},\n        {value: '2',label: '外网'},\n        {value: '3',label: '内/外网'},\n      ],\n      blockStatusOptions: [\n        {value: 1,label: '正在阻断'},\n        {value: 2,label: '曾经阻断'},\n      ],\n      riskLevelOptions: [],\n      rows: [],\n      handleStateOptions: [\n        {\n          label: '未处置',\n          value: '0'\n        },\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        },\n        {\n          label: '处置中',\n          value: '3'\n        }\n      ],\n      handleStateOption: [\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        }\n      ],\n      handleForm: {\n        id: '',\n        handleDesc: ''\n      },\n      tagColor: ['#c86c00','#bf1a1a','#1a2bbf','#901abf','#1a2bbf'],\n      tagBackgroundColor: ['#ff9f1933','#fd828233','#7899e033','#c278e033','#7899e033'],\n    }\n  },\n  watch: {\n    propsActiveName() {\n      this.init()\n    },\n    propsQueryParams(val){\n      this.handlePropsQuery(val);\n    },\n  },\n  computed: {\n    noMore () {\n      return this.count >= 20\n    },\n    disabled () {\n      return this.loading || this.noMore\n    },\n    flexColumnWidth() {\n      return this.columnWidth({prop: 'tags', label: '攻击者标签'}, this.attackAlarmList);\n    }\n  },\n  mounted() {\n    let query = this.$route.query;\n    if(!query || Object.keys(query).length < 1){\n      this.init();\n    }else {\n      this.handlePropsQuery(query);\n    }\n    this.getRiskLevelOptions();\n  },\n  methods: {\n    load () {\n      this.loading = true\n      setTimeout(() => {\n        this.count += 2\n        this.loading = false\n      }, 2000)\n    },\n    handlePropsQuery(val) {\n      if(val && Object.keys(val).length > 0){\n        this.queryParams = val;\n        if(val.startTime && val.endTime){\n          this.rangeTime = [val.startTime, val.endTime];\n        }\n        if(val.alarmLevel){\n          if(val.alarmLevel === '2'){\n            this.queryParams.riskLevel = '1';\n          }else if(val.alarmLevel === '3'){\n            this.queryParams.riskLevel = '2';\n          }else if(val.alarmLevel === '4'){\n            this.queryParams.riskLevel = '3';\n          }else {\n            this.queryParams.riskLevel = '-99';\n          }\n        }else {\n          this.queryParams.riskLevel = null;\n        }\n        this.handleQuery();\n      }\n    },\n    showHandle(row) {\n      this.handleForm = {};\n      this.handleForm = {...row};\n      this.$nextTick(() => {\n        if (this.handleForm.handleState == 0) {\n          this.handleForm.handleState = null\n        }\n        this.handleForm.assetName = ''\n        this.showHandleDialog = true;\n      })\n    },\n    init() {\n      this.handleQuery()\n      //this.getList()\n    },\n    handleQuery() {\n      this.queryParams = {...this.queryParams,...this.propsQueryParams};\n      if(this.queryParams.alarmLevel){\n        if(this.queryParams.alarmLevel === '2'){\n          this.queryParams.riskLevel = '1';\n        }else if(this.queryParams.alarmLevel === '3'){\n          this.queryParams.riskLevel = '2';\n        }else if(this.queryParams.alarmLevel === '4'){\n          this.queryParams.riskLevel = '3';\n        }else {\n          this.queryParams.riskLevel = '-99';\n        }\n      }else {\n        this.queryParams.riskLevel = null;\n      }\n      this.queryParams.pageNum = 1\n      this.queryParams.pageSize = 10\n      if (this.rangeTime !== null) {\n        this.queryParams.startUpdateTime = parseTime(this.rangeTime[0]);\n        this.queryParams.endUpdateTime = parseTime(this.rangeTime[1]);\n      } else {\n        this.queryParams.startUpdateTime = null;\n        this.queryParams.endUpdateTime = null;\n      }\n\n      if(!this.queryParams.startUpdateTime){\n        this.queryParams.startUpdateTime = parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'); // 一周前，时间部分为 00:00:00\n      }\n      if(!this.queryParams.endUpdateTime){\n        this.queryParams.endUpdateTime = parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'); // 当前日期，时间部分为 23:59:59\n      }\n      this.rangeTime = [this.queryParams.startUpdateTime, this.queryParams.endUpdateTime];\n      this.getList()\n      this.$nextTick(() => {\n        this.$refs.stage && this.$refs.stage.initAttackStage({\n          ...this.queryParams,\n          startTime: this.queryParams.startUpdateTime,\n          endTime: this.queryParams.endUpdateTime\n        })\n      })\n    },\n    resetQuery() {\n      this.queryParams = {\n        srcIp: '',\n        alarmLevel: ''\n      }\n      this.propsQueryParams.riskLevel = this.queryParams.riskLevel;\n      this.propsQueryParams.alarmLevel = this.queryParams.alarmLevel;\n      if(this.$refs.stage){\n        this.$refs.stage.currentSelectedCard = null;\n      }\n      this.rangeTime = []\n      this.handleQuery();\n    },\n    getList() {\n      this.loading = true;\n      //同步请求类型统计数据\n      let params = {...this.queryParams};\n      params.srcIp = this.queryParams.attackIp;\n      params.startTime = this.queryParams.startUpdateTime;\n      params.endTime = this.queryParams.endUpdateTime;\n      if(params.riskLevel){\n        if(params.riskLevel === '1'){\n          params.alarmLevel = '2';\n        }else if(params.riskLevel === '2'){\n          params.alarmLevel = '3';\n        }else if(params.riskLevel === '3'){\n          params.alarmLevel = '4';\n        }\n      }\n      this.$emit('getList',params);\n      listAttackAlarm(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.attackAlarmList = res.rows\n          this.attackAlarmList.forEach(e => {\n            e.childrenData = {}\n          })\n          this.total = res.total\n        }\n      }).finally(()=>{\n        this.loading = false;\n      })\n    },\n    async expandChange(row, expandRowKeys) {\n      if (expandRowKeys.length > 0) {\n        getEventSegTypeList({\n          attackIp: row.attackIp,\n          startTime: this.queryParams.startUpdateTime,\n          endTime: this.queryParams.endUpdateTime\n        }).then(res => {\n          if (res.code === 200) {\n            const childrenData = res.data\n            this.$set(row, 'childrenData', childrenData)\n            this.descKey = new Date().getTime()\n          }\n        })\n      }\n    },\n    handleExport() {\n      this.download('/threaten/AttackAlarm/export', {\n        ...this.queryParams\n      }, `攻击者视角数据_${new Date().getTime()}.xlsx`)\n    },\n    handleDetail(row) {\n      if (row.attackIp) {\n        this.detailDialog = true\n        this.hostIp = row.attackIp;\n        this.isAsset = row.assetId;\n        this.currentAssetData = row;\n      }\n    },\n    handleAtcClick(attackSeg){\n      this.queryParams.attackSeg = attackSeg;\n      this.handleQuery();\n    },\n    handleApplicationTagShow(applicationList){\n      if(!applicationList || applicationList.length < 1){\n        return '';\n      }\n      let result = applicationList[0].assetName;\n      if(applicationList.length > 1){\n        result += '...';\n      }\n      return result;\n    },\n    openDipDetails(row){\n      // this.victimIpNumTotal = 0 ;\n        this.dipDetailsData = [];\n      this.dipDrawerVisible = true;\n      this.dipDrawerLoading = true;\n      this.victimIpNumData = row;\n      getDipDetails({\n        attackIp: row.attackIp,\n        startTime: row.startTime,\n        updateTime: row.updateTime,\n        id: row.id,\n        pageSize: this.victimIpNumQueryParams.pageSize,\n        pageNum: this.victimIpNumQueryParams.pageNum\n      }).then(res => {\n        this.dipDetailsData = res.data.rows;\n        this.victimIpNumTotal = res.data.total;\n      }).finally(() => {\n        this.dipDrawerLoading = false;\n      })\n    },\n    dipRenderHeader(h, { column, $index }){\n      return h('div', [\n        h('span', column.label),\n        h('span', {\n          style: {\n            marginLeft: '5px',\n            color: '#ADADAD'\n          }\n        }, '[主机名称]')\n      ])\n    },\n    openThreatenNameDetails(row){\n      this.threatenNameDetailsData = [];\n      this.threatenNameDrawerVisible = true;\n      this.threatenNameDrawerLoading = true;\n      this.threatenNameDetailsQuery = row\n      getThreatenNameDetails({\n        attackIp: row.attackIp,\n        startTime: row.startTime,\n        updateTime: row.updateTime,\n        id: row.id,\n        pageSize: this.hitRulesQueryParams.pageSize,\n        pageNum: this.hitRulesQueryParams.pageNum\n      }).then(res => {\n        this.threatenNameDetailsData = res.data.rows;\n        this.hitRulesTotal = res.data.total;\n      }).finally(() => {\n        this.threatenNameDrawerLoading = false;\n      })\n    },\n    handleBlocking(row) {\n      console.log( row)\n      let arr = [];\n      if(row && row.attackIp){\n        arr.push(row.attackIp);\n      }else {\n        if (this.multipleSelection.length < 1) return this.$message.warning('请选择要阻断的ip');\n        arr = this.multipleSelection.map(item => item.attackIp);\n        arr = Array.from(new Set(arr));\n      }\n      this.blockingDialogVisible = true;\n      this.$nextTick(() => {\n        this.$refs.batchBlock && this.$refs.batchBlock.init({\n          block_ip: arr.join(';')\n        })\n      })\n    },\n    // 多选\n    handleSelectionChange(val) {\n      this.ids = val.map(item => item.id);\n      this.multiple = !val.length;\n      this.multipleSelection = val;\n      this.rows = val;\n    },\n    getRiskLevelOptions(){\n      getMulTypeDict({\n        dictType: 'alarm_attack_risk_level'\n      }).then(res => {\n        this.riskLevelOptions = res.data;\n      })\n    },\n    columnWidth(col, list) {\n      // 获取数组长度作为基础\n      let arrayLength = 0;\n\n      // 遍历数据列表，找出 tags 数组的最大长度\n      for (let info of list) {\n        if (info[col.prop] && Array.isArray(info[col.prop])) {\n          arrayLength = Math.max(arrayLength, info[col.prop].length);\n        }\n      }\n\n      // 设置最小宽度为140px\n      return Math.max(arrayLength * 140, 140);\n    },\n    handleStateFormatter(row, column, cellValue, index) {\n      let name = '未处置';\n      let match = this.handleStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    submitHandleForm() {\n      handleVuln(this.handleForm).then(res => {\n        this.$message.success(\"处置成功\");\n        this.handleForm = {};\n        this.showHandleDialog = false;\n        this.getList();\n        this.initData();\n      })\n    },\n    showHandleBatch() {\n      let rows = [...this.rows];\n      rows = rows.filter(item => item.handleState == 0 || item.handleState == 2 || item.handleState === null);\n      if (rows.length < this.rows.length) {\n        this.$message.error('选择中有已处置或处置中事件，无法批量处置');\n        return false;\n      }\n      this.handleForm = {};\n      if (rows.length === 1) {\n        if (rows[0].handleState == 2) {\n          this.handleForm = rows[0]\n        }\n      }\n      this.handleForm.ids = this.ids;\n      this.showHandleBatchDialog = true;\n    },\n\n    submitHandleBatchForm() {\n      handleBatchVuln(this.handleForm).then(res => {\n        this.$message.success(\"处置成功\");\n        this.handleForm = {};\n        this.showHandleBatchDialog = false;\n        this.getList();\n        this.initData();\n      })\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.asset-tag{\n  max-width: 100%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n.dip-dialog{\n  ::v-deep .el-dialog{\n\n  }\n  ::v-deep .el-dialog__body{\n    padding: 20px;\n    margin-top: 20px;\n  }\n}\n.attack-tag:not(:nth-child(-n+2)) {\n  margin-top: 5px;\n}\n.overflow-tag:not(:first-child){\n  margin-top: 5px;\n}\n.threatenName-error{\n  color: #F56C6C;\n}\n.threatenName-success{\n  color: #67C23A;\n}\n.threatenName-warn{\n  color: #E6A23C;\n}\n\n.tag-name {\n  display: inline-block;\n  padding: 2px 12px;\n  margin-bottom: 5px;\n  font-size: 14px;\n  font-weight: normal;\n  line-height: 1.42857143;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  background-image: none;\n  border: 1px solid transparent;\n  border-radius: 4px;\n}\n\n::v-deep .el-dialog__body {\n  max-height: 80vh;\n  padding: 0 30px 30px;\n  overflow-y: auto;\n}\n</style>\n"]}]}