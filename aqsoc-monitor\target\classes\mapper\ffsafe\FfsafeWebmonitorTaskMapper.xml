<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeWebmonitorTaskMapper">

    <resultMap type="FfsafeWebmonitorTask" id="FfsafeWebmonitorTaskResult">
        <result property="id"    column="id"    />
        <result property="periodId"    column="period_id"    />
        <result property="url"    column="url"    />
        <result property="domain"    column="domain"    />
        <result property="systemName"    column="system_name"    />
        <result property="ip"    column="ip"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectFfsafeWebmonitorTaskVo">
        select id, period_id, url, domain, system_name, ip, status, create_time from ffsafe_webmonitor_task
    </sql>

    <select id="selectFfsafeWebmonitorTaskList" parameterType="FfsafeWebmonitorTask" resultMap="FfsafeWebmonitorTaskResult">
        <include refid="selectFfsafeWebmonitorTaskVo"/>
        <where>
            <if test="periodId != null "> and period_id = #{periodId}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="domain != null  and domain != ''"> and domain = #{domain}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectFfsafeWebmonitorTaskById" parameterType="Long" resultMap="FfsafeWebmonitorTaskResult">
        <include refid="selectFfsafeWebmonitorTaskVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeWebmonitorTaskByIds" parameterType="Long" resultMap="FfsafeWebmonitorTaskResult">
        <include refid="selectFfsafeWebmonitorTaskVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeWebmonitorTask" parameterType="FfsafeWebmonitorTask" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_webmonitor_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="periodId != null">period_id,</if>
            <if test="url != null">url,</if>
            <if test="domain != null">domain,</if>
            <if test="systemName != null">system_name,</if>
            <if test="ip != null">ip,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="periodId != null">#{periodId},</if>
            <if test="url != null">#{url},</if>
            <if test="domain != null">#{domain},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="ip != null">#{ip},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateFfsafeWebmonitorTask" parameterType="FfsafeWebmonitorTask">
        update ffsafe_webmonitor_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="periodId != null">period_id = #{periodId},</if>
            <if test="url != null">url = #{url},</if>
            <if test="domain != null">domain = #{domain},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeWebmonitorTaskById" parameterType="Long">
        delete from ffsafe_webmonitor_task where id = #{id}
    </delete>

    <delete id="deleteFfsafeWebmonitorTaskByIds" parameterType="String">
        delete from ffsafe_webmonitor_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>