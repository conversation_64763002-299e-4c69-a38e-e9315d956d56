<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.anmte.aqsoc.threaten.origin.mapper.ThreatenWebpageTamperingMapper">

    <select id="queryPage" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenWebpageTamperingListVO">
        select a.* from threaten_webpage_tampering a left join threaten_alarm_origin b on a.id = b.origin_id and (b.threaten_type = 'threaten_webpage_tampering' or b.threaten_type = '网页篡改')
        <where>
            <if test="query.id!=null">
                and a.id = #{query.id}
            </if>
            <if test="query.baseId!=null">
                and a.base_id = #{query.baseId}
            </if>
            <if test="query.isEncry!=null">
                and a.is_encry = #{query.isEncry}
            </if>
            <if test="query.attackNum!=null">
                and a.attack_num = #{query.attackNum}
            </if>
            <if test="query.attackDirection!=null">
                and a.attack_direction = #{query.attackDirection}
            </if>
            <if test="query.attackStage!=null">
                and a.attack_stage = #{query.attackStage}
            </if>
            <if test="query.attackResult!=null">
                and a.attack_result = #{query.attackResult}
            </if>
            <if test="query.handleAction!=null">
                and a.handle_action = #{query.handleAction}
            </if>
            <if test="query.srcUrl!=null and query.srcUrl!=''">
                and a.src_url like concat('%', #{query.srcUrl}, '%')
            </if>
            <if test="query.tamperingUrl!=null and query.tamperingUrl!=''">
                and a.tampering_url like concat('%', #{query.tamperingUrl}, '%')
            </if>
            <if test="query.tamperingType!=null and query.tamperingType!=''">
                and a.tampering_type like concat('%', #{query.tamperingType}, '%')
            </if>
            <if test="query.keyword!=null and query.keyword!=''">
                and a.keyword like concat('%', #{query.keyword}, '%')
            </if>
            <if test="query.attackTime!=null">
                and a.attack_time = #{query.attackTime}
            </if>
            <if test="query.alarmId!=null">
                and b.alarm_id = #{query.alarmId}
            </if>
        </where>
    </select>
    <select id="queryById" resultType="cn.anmte.aqsoc.threaten.origin.model.ThreatenWebpageTamperingListVO">
        select a.* from threaten_webpage_tampering a 
        where a.id=#{id}
    </select>
</mapper>
