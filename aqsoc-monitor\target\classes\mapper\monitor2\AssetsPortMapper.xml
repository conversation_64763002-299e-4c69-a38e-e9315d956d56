<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.AssetsPortMapper">
    
    <resultMap type="AssetsPort" id="AssetsPortResult">
        <result property="id"    column="id"    />
        <result property="tid"    column="tid"    />
        <result property="ip"    column="IP"    />
        <result property="name"    column="name"    />
        <result property="port"    column="port"    />
        <result property="protocol"    column="protocol"    />
        <result property="memo"    column="memo"    />
        <result property="state"    column="state"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAssetsPortVo">
        select id, tid, IP, name, port, protocol, memo, state, update_time from monitor_assets_port
    </sql>

    <select id="selectAssetsPortList" parameterType="AssetsPort" resultMap="AssetsPortResult">
        <include refid="selectAssetsPortVo"/>
        <where>  
            <if test="tid != null  and tid != ''"> and tid = #{tid}</if>
            <if test="ip != null  and ip != ''"> and IP = #{ip}</if>
            <if test="name != null and name != ''"> and name = #{name}</if>
            <if test="port != null "> and port = #{port}</if>
            <if test="protocol != null "> and protocol = #{protocol}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
        </where>
    </select>
    
    <select id="selectAssetsPortById" parameterType="String" resultMap="AssetsPortResult">
        <include refid="selectAssetsPortVo"/>
        where id = #{id}
    </select>

    <select id="selectAssetsPortByTid" parameterType="String" resultMap="AssetsPortResult">
        <include refid="selectAssetsPortVo"/>
        where tid = #{id}
    </select>

        
    <insert id="insertAssetsPort" parameterType="AssetsPort">
        insert into monitor_assets_port
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="tid != null">tid,</if>
            <if test="ip != null">IP,</if>
            <if test="name != null">name,</if>
            <if test="port != null">port,</if>
            <if test="protocol != null">protocol,</if>
            <if test="memo != null">memo,</if>
            <if test="state != null">state,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="tid != null">#{tid},</if>
            <if test="ip != null">#{ip},</if>
            <if test="name != null">#{name},</if>
            <if test="port != null">#{port},</if>
            <if test="protocol != null">#{protocol},</if>
            <if test="memo != null">#{memo},</if>
            <if test="state != null">#{state},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAssetsPort" parameterType="AssetsPort">
        update monitor_assets_port
        <trim prefix="SET" suffixOverrides=",">
            <if test="tid != null">tid = #{tid},</if>
            <if test="ip != null">IP = #{ip},</if>
            <if test="name != null">name = #{name},</if>
            <if test="port != null">port = #{port},</if>
            <if test="protocol != null">protocol = #{protocol},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="state != null">state = #{state},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssetsPortById" parameterType="Long">
        delete from monitor_assets_port where id = #{id}
    </delete>

    <delete id="deleteAssetsPortByIds" parameterType="String">
        delete from monitor_assets_port where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>