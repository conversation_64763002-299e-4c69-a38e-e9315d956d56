<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.audit.mapper.TblAssetAuditPolicyMapper">
    
    <resultMap type="TblAssetAuditPolicy" id="TblAssetAuditPolicyResult">
        <result property="id"    column="id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="policyRequestIp"    column="policy_request_ip"    />
        <result property="policyResponseIp"    column="policy_response_ip"    />
        <result property="policyResponsePost"    column="policy_response_post"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <sql id="selectTblAssetAuditPolicyVo">
        select id, asset_id, policy_request_ip, policy_response_ip, policy_response_post, remark, create_time, create_by, update_time, update_by, user_id, dept_id from tbl_asset_audit_policy
    </sql>

    <select id="selectTblAssetAuditPolicyList" parameterType="TblAssetAuditPolicy" resultMap="TblAssetAuditPolicyResult">
        <include refid="selectTblAssetAuditPolicyVo"/>
        <where>  
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="policyRequestIp != null  and policyRequestIp != ''"> and policy_request_ip = #{policyRequestIp}</if>
            <if test="policyResponseIp != null  and policyResponseIp != ''"> and policy_response_ip = #{policyResponseIp}</if>
            <if test="policyResponsePost != null  and policyResponsePost != ''"> and policy_response_post = #{policyResponsePost}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
        </where>
    </select>
    
    <select id="selectTblAssetAuditPolicyById" parameterType="Long" resultMap="TblAssetAuditPolicyResult">
        <include refid="selectTblAssetAuditPolicyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblAssetAuditPolicy" parameterType="TblAssetAuditPolicy">
        insert into tbl_asset_audit_policy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="policyRequestIp != null">policy_request_ip,</if>
            <if test="policyResponseIp != null">policy_response_ip,</if>
            <if test="policyResponsePost != null">policy_response_post,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="policyRequestIp != null">#{policyRequestIp},</if>
            <if test="policyResponseIp != null">#{policyResponseIp},</if>
            <if test="policyResponsePost != null">#{policyResponsePost},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
         </trim>
    </insert>

    <update id="updateTblAssetAuditPolicy" parameterType="TblAssetAuditPolicy">
        update tbl_asset_audit_policy
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="policyRequestIp != null">policy_request_ip = #{policyRequestIp},</if>
            <if test="policyResponseIp != null">policy_response_ip = #{policyResponseIp},</if>
            <if test="policyResponsePost != null">policy_response_post = #{policyResponsePost},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblAssetAuditPolicyById" parameterType="Long">
        delete from tbl_asset_audit_policy where id = #{id}
    </delete>

    <delete id="deleteTblAssetAuditPolicyByIds" parameterType="String">
        delete from tbl_asset_audit_policy where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>