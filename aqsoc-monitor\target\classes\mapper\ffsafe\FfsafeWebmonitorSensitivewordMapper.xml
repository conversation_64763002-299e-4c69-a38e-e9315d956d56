<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeWebmonitorSensitivewordMapper">

    <resultMap type="FfsafeWebmonitorSensitiveword" id="FfsafeWebmonitorSensitivewordResult">
        <result property="id"    column="id"    />
        <result property="url"    column="url"    />
        <result property="sensitiveWord"    column="sensitive_word"    />
        <result property="systemName"    column="system_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="handleStatus"    column="handle_status"    />
        <result property="word"    column="word"    />
        <result property="recentlyTime"    column="recently_time"    />
    </resultMap>

    <sql id="selectFfsafeWebmonitorSensitivewordVo">
        select id, url, sensitive_word, system_name, file_url, handle_status, word, recently_time from ffsafe_webmonitor_sensitiveword
    </sql>

    <select id="selectFfsafeWebmonitorSensitivewordList" parameterType="FfsafeWebmonitorSensitiveword" resultMap="FfsafeWebmonitorSensitivewordResult">
        <include refid="selectFfsafeWebmonitorSensitivewordVo"/>
        <where>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="sensitiveWord != null  and sensitiveWord != ''"> and sensitive_word = #{sensitiveWord}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="handleStatus != null  and handleStatus != ''"> and handle_status = #{handleStatus}</if>
            <if test="word != null  and word != ''"> and word = #{word}</if>
            <if test="recentlyTime != null "> and recently_time = #{recentlyTime}</if>
        </where>
    </select>

    <select id="selectFfsafeWebmonitorSensitivewordById" parameterType="Long" resultMap="FfsafeWebmonitorSensitivewordResult">
        <include refid="selectFfsafeWebmonitorSensitivewordVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeWebmonitorSensitivewordByIds" parameterType="Long" resultMap="FfsafeWebmonitorSensitivewordResult">
        <include refid="selectFfsafeWebmonitorSensitivewordVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeWebmonitorSensitiveword" parameterType="FfsafeWebmonitorSensitiveword" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_webmonitor_sensitiveword
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="url != null">url,</if>
            <if test="sensitiveWord != null">sensitive_word,</if>
            <if test="systemName != null">system_name,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="handleStatus != null">handle_status,</if>
            <if test="word != null">word,</if>
            <if test="recentlyTime != null">recently_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="url != null">#{url},</if>
            <if test="sensitiveWord != null">#{sensitiveWord},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="handleStatus != null">#{handleStatus},</if>
            <if test="word != null">#{word},</if>
            <if test="recentlyTime != null">#{recentlyTime},</if>
        </trim>
    </insert>

    <update id="updateFfsafeWebmonitorSensitiveword" parameterType="FfsafeWebmonitorSensitiveword">
        update ffsafe_webmonitor_sensitiveword
        <trim prefix="SET" suffixOverrides=",">
            <if test="url != null">url = #{url},</if>
            <if test="sensitiveWord != null">sensitive_word = #{sensitiveWord},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
            <if test="word != null">word = #{word},</if>
            <if test="recentlyTime != null">recently_time = #{recentlyTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeWebmonitorSensitivewordById" parameterType="Long">
        delete from ffsafe_webmonitor_sensitiveword where id = #{id}
    </delete>

    <delete id="deleteFfsafeWebmonitorSensitivewordByIds" parameterType="String">
        delete from ffsafe_webmonitor_sensitiveword where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>