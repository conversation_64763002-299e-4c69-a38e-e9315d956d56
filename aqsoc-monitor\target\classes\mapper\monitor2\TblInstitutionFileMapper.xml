<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblInstitutionFileMapper">

    <resultMap type="TblInstitutionFile" id="TblInstitutionFileResult">
        <result property="id"    column="id"    />
        <result property="institutionId"    column="institution_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblInstitutionFileVo">
        select id, institution_id, file_name, file_type, file_url, create_time, update_time from tbl_institution_file
    </sql>

    <select id="selectTblInstitutionFileList" parameterType="TblInstitutionFile" resultMap="TblInstitutionFileResult">
        <include refid="selectTblInstitutionFileVo"/>
        <where>
            <if test="institutionId != null "> and institution_id = #{institutionId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
        </where>
    </select>

    <select id="selectTblInstitutionFileById" parameterType="Long" resultMap="TblInstitutionFileResult">
        <include refid="selectTblInstitutionFileVo"/>
        where id = #{id}
    </select>

    <select id="selectTblInstitutionFileByIds" parameterType="Long" resultMap="TblInstitutionFileResult">
        <include refid="selectTblInstitutionFileVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectTblInstitutionFileByInstitutionIds"  parameterType="Long" resultMap="TblInstitutionFileResult">
        <include refid="selectTblInstitutionFileVo"/>
        where institution_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectTblInstitutionFileByInstitutionId"
            resultType="com.ruoyi.monitor2.domain.TblInstitutionFile" resultMap="TblInstitutionFileResult">
        <include refid="selectTblInstitutionFileVo"/>
        where institution_id = #{institutionId}
    </select>

    <insert id="insertTblInstitutionFile" parameterType="TblInstitutionFile" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_institution_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="institutionId != null">institution_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="institutionId != null">#{institutionId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblInstitutionFile" parameterType="TblInstitutionFile">
        update tbl_institution_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="institutionId != null">institution_id = #{institutionId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateTblInstitutionFileByInstitutionId">
        update tbl_institution_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where institution_id = #{institutionId}
    </update>

    <delete id="deleteTblInstitutionFileById" parameterType="Long">
        delete from tbl_institution_file where id = #{id}
    </delete>

    <delete id="deleteTblInstitutionFileByIds" parameterType="String">
        delete from tbl_institution_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTblInstitutionFileByInstitutionIds">
        delete from tbl_institution_file where institution_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
