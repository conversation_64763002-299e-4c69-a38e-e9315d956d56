<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.TblFunctionApplicationMapper">

    <resultMap type="TblFunctionApplication" id="TblFunctionApplicationResult">
        <result property="functionId"    column="function_id"    />
        <result property="assetId"    column="asset_id"    />
        <result property="editFunctionState"    column="edit_function_state"    />
        <result property="editFunctionName"    column="edit_function_name"    />
    </resultMap>

    <sql id="selectTblFunctionApplicationVo">
        select function_id, asset_id, edit_function_state, edit_function_name from tbl_function_application
    </sql>

    <select id="selectTblFunctionApplicationList" parameterType="TblFunctionApplication" resultMap="TblFunctionApplicationResult">
        <include refid="selectTblFunctionApplicationVo"/>
        <where>
            <if test="assetId != null "> and asset_id = #{assetId}</if>
            <if test="editFunctionState != null  and editFunctionState != ''"> and edit_function_state = #{editFunctionState}</if>
            <if test="editFunctionName != null  and editFunctionName != ''"> and edit_function_name like concat('%', #{editFunctionName}, '%')</if>
        </where>
    </select>

    <select id="selectTblFunctionApplicationByFunctionId" parameterType="Integer" resultMap="TblFunctionApplicationResult">
        <include refid="selectTblFunctionApplicationVo"/>
        where function_id = #{functionId}
    </select>

    <select id="selectTblFunctionApplicationByFunctionIds" parameterType="Integer" resultMap="TblFunctionApplicationResult">
        <include refid="selectTblFunctionApplicationVo"/>
        where function_id in
        <foreach item="functionId" collection="array" open="(" separator="," close=")">
            #{functionId}
        </foreach>
    </select>

    <insert id="insertTblFunctionApplication" parameterType="TblFunctionApplication" useGeneratedKeys="true" keyProperty="functionId">
        insert into tbl_function_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">asset_id,</if>
            <if test="editFunctionState != null">edit_function_state,</if>
            <if test="editFunctionName != null">edit_function_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="editFunctionState != null">#{editFunctionState},</if>
            <if test="editFunctionName != null">#{editFunctionName},</if>
         </trim>
    </insert>

    <update id="updateTblFunctionApplication" parameterType="TblFunctionApplication">
        update tbl_function_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="editFunctionState != null">edit_function_state = #{editFunctionState},</if>
            <if test="editFunctionName != null">edit_function_name = #{editFunctionName},</if>
        </trim>
        where function_id = #{functionId}
    </update>

    <delete id="deleteTblFunctionApplicationByFunctionId" parameterType="Integer">
        delete from tbl_function_application where function_id = #{functionId}
    </delete>

    <delete id="deleteTblFunctionApplicationByFunctionIds" parameterType="String">
        delete from tbl_function_application where function_id in
        <foreach item="functionId" collection="array" open="(" separator="," close=")">
            #{functionId}
        </foreach>
    </delete>
</mapper>
