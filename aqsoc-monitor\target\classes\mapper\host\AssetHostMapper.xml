<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.host.mapper.AssetHostMapper">

    <select id="queryPage" resultType="com.ruoyi.monitor2.host.model.AssetHostListVO">
        select a.* from asset_host a
        <where>
                <if test="query.id!=null">
                    and a.id = #{query.id}
                </if>
                <if test="query.hostIds!=null">
                   a.id in
                   <foreach item="hostId" collection="query.hostIds" open="(" close=")" separator=",">
                       #{hostId}
                   </foreach>
                    and a.id not in (select cw_host_id from tbl_server)
                </if>
                <if test="query.hostIp!=null and query.hostIp!=''">
                    and a.host_ip like concat('%', #{query.hostIp}, '%')
                </if>
                <if test="query.hostName!=null and query.hostName!=''">
                    and a.host_name like concat('%', #{query.hostName}, '%')
                </if>
                <if test="query.osReleaseName!=null and query.osReleaseName!=''">
                    and a.os_release_name like concat('%', #{query.osReleaseName}, '%')
                </if>
                <if test="query.osReleaseVersion!=null and query.osReleaseVersion!=''">
                    and a.os_release_version like concat('%', #{query.osReleaseVersion}, '%')
                </if>
                <if test="query.cpuCore!=null">
                    and a.cpu_core = #{query.cpuCore}
                </if>
                <if test="query.memSize!=null and query.memSize!=''">
                    and a.mem_size like concat('%', #{query.memSize}, '%')
                </if>
                <if test="query.tags!=null and query.tags!=''">
                    and a.tags like concat('%', #{query.tags}, '%')
                </if>
                <if test="query.kernelVersion!=null and query.kernelVersion!=''">
                    and a.kernel_version like concat('%', #{query.kernelVersion}, '%')
                </if>
                <if test="query.cpu!=null">
                    and a.cpu = #{query.cpu}
                </if>
                <if test="query.memory!=null">
                    and a.memory = #{query.memory}
                </if>
                <if test="query.hostCpu!=null">
                    and a.host_cpu = #{query.hostCpu}
                </if>
                <if test="query.hostMemory!=null">
                    and a.host_memory = #{query.hostMemory}
                </if>
                <if test="query.upTime!=null">
                    and a.up_time = #{query.upTime}
                </if>
                <if test="query.lastSeenAt!=null">
                    and a.last_seen_at = #{query.lastSeenAt}
                </if>
                <if test="query.createdAt!=null">
                    and a.created_at = #{query.createdAt}
                </if>
                <if test="query.comment!=null and query.comment!=''">
                    and a.comment like concat('%', #{query.comment}, '%')
                </if>
                <if test="query.hostState!=null and query.hostState!=''">
                    and a.host_state like concat('%', #{query.hostState}, '%')
                </if>
                <if test="query.bashState!=null and query.bashState!=''">
                    and a.bash_state like concat('%', #{query.bashState}, '%')
                </if>
                <if test="query.version!=null and query.version!=''">
                    and a.version like concat('%', #{query.version}, '%')
                </if>
                <if test="query.upgrading!=null">
                    and a.upgrading = #{query.upgrading}
                </if>
                <if test="query.needToUpgrade!=null">
                    and a.need_to_upgrade = #{query.needToUpgrade}
                </if>
                <if test="query.latestVersion!=null and query.latestVersion!=''">
                    and a.latest_version like concat('%', #{query.latestVersion}, '%')
                </if>
                <if test="query.groupId!=null">
                    and a.group_id = #{query.groupId}
                </if>
                <if test="query.groupName!=null and query.groupName!=''">
                    and a.group_name like concat('%', #{query.groupName}, '%')
                </if>
                <if test="query.hostUptime!=null">
                    and a.host_uptime = #{query.hostUptime}
                </if>
        </where>
    </select>
    <select id="queryById" resultType="com.ruoyi.monitor2.host.model.AssetHostListVO">
        select a.* from asset_host a
        where a.id=#{id}
    </select>
</mapper>
