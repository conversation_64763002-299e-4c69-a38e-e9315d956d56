<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.TblKnowledgeFileMapper">

    <resultMap type="TblKnowledgeFile" id="TblKnowledgeFileResult">
        <result property="id" column="id"/>
        <result property="kid" column="kid"/>
        <result property="fileName" column="file_name"/>
        <result property="fileType" column="file_type"/>
        <result property="fileUrl" column="file_url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTblKnowledgeFileVo">
        select id, kid, file_name, file_type, file_url, create_time, update_time from tbl_knowledge_file
    </sql>

    <select id="selectTblKnowledgeFileList" parameterType="TblKnowledgeFile" resultMap="TblKnowledgeFileResult">
        <include refid="selectTblKnowledgeFileVo"/>
        <where>
            <if test="kid != null ">and kid = #{kid}</if>
            <if test="fileName != null  and fileName != ''">and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null  and fileType != ''">and file_type = #{fileType}</if>
            <if test="fileUrl != null  and fileUrl != ''">and file_url = #{fileUrl}</if>
        </where>
    </select>

    <select id="selectTblKnowledgeFileById" parameterType="Long" resultMap="TblKnowledgeFileResult">
        <include refid="selectTblKnowledgeFileVo"/>
        where id = #{id}
    </select>

    <insert id="insertTblKnowledgeFile" parameterType="TblKnowledgeFile">
        insert into tbl_knowledge_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="kid != null">kid,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="kid != null">#{kid},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTblKnowledgeFile" parameterType="TblKnowledgeFile">
        update tbl_knowledge_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="kid != null">kid = #{kid},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblKnowledgeFileById" parameterType="Long">
        delete from tbl_knowledge_file where id = #{id}
    </delete>

    <delete id="deleteTblKnowledgeFileByIds" parameterType="String">
        delete from tbl_knowledge_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTblKnowledgeFileByKid" parameterType="Long">
        delete from tbl_knowledge_file where kid = #{kid}
    </delete>

    <delete id="deleteTblKnowledgeFileByKids" parameterType="Long">
        delete from tbl_knowledge_file where kid in
        <foreach item="kid" collection="array" open="(" separator="," close=")">
            #{kid}
        </foreach>
    </delete>
</mapper>