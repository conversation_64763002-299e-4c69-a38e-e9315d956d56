{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\attackViewList.vue?vue&type=template&id=d5d9d844&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\attackViewList.vue", "mtime": 1756369455986}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}