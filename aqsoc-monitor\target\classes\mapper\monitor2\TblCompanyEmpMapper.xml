<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TblCompanyEmpMapper">

    <resultMap type="TblCompanyEmp" id="TblCompanyEmpResult">
        <result property="eid" column="eid"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="cardno" column="cardno"/>
        <result property="phone" column="phone"/>
        <result property="post" column="post"/>
        <result property="mail" column="mail"/>
        <result property="address" column="address"/>
        <result property="photo" column="photo"/>
        <result property="state" column="state"/>
        <result property="fileId" column="file_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="dwid" column="dwid"/>
        <result property="dwName" column="dw_name"/>
        <result property="enterDeptId" column="enter_dept_id"/>
        <result property="enterDeptIdName" column="enter_dept_id_name"/>
        <result property="assetIds" column="asset_ids"/>
        <result property="companyName" column="company_name"/>
        <result property="isSite" column="is_site"/>
        <result property="isBackTone" column="is_back_tone"/>
        <result property="leader" column="leader"/>
        <result property="legalPerson" column="legal_person"/>
        <result property="operationStartTime" column="operation_start_time"/>
        <result property="operationStopTime" column="operation_stop_time"/>
    </resultMap>

    <sql id="selectTblCompanyEmpVo">
        select a.eid, a.code,a.dwid,b.name as dw_name,a.sex,a.asset_ids, a.name, a.cardno, a.phone, a.post, a.mail, a.state, a.file_id, a.create_by, a.create_time, a.update_by, a.update_time, a.remark,
               a.address,a.photo,a.user_id, a.dept_id,a.enter_dept_id,d.dept_name as enter_dept_id_name, a.company_name, a.is_site, a.is_back_tone,a.leader,a.legal_person,a.operation_start_time,a.operation_stop_time
        from tbl_company_emp a
        left join tbl_company b on a.dwid = b.dwid
        left join sys_dept d on a.enter_dept_id = d.dept_id and d.status = 0 and d.del_flag = 0
    </sql>

    <select id="selectTblCompanyEmpList" parameterType="TblCompanyEmp" resultMap="TblCompanyEmpResult">
        <include refid="selectTblCompanyEmpVo"/>
        <where>
            <if test="code != null  and code != ''"> and (a.code like concat('%',#{code},'%') or a.name like concat('%', #{code}, '%'))</if>
            <if test="name != null and name != ''"> and a.name like concat('%', #{name},'%')</if>
            <if test="state != null  and state != ''"> and a.state = #{state}</if>
            <if test="phone != null "> and a.phone like concat('%', #{phone},'%')</if>
            <if test="dwid != null  and dwid != ''"> and a.dwid = #{dwid}</if>
            <if test="enterDeptId != null  and enterDeptId != ''"> and a.enter_dept_id = #{enterDeptId}</if>
            <if test="assetId != null  and assetId != ''"> and find_in_set(#{assetId},asset_ids)</if>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
            <if test="companyName != null and companyName != ''">and company_name like concat('%', #{companyName},'%')</if>
            <if test="isSite != null  and isSite != ''"> and is_site = #{isSite}</if>
            <if test="isBackTone != null  and isBackTone != ''"> and is_back_tone = #{isBackTone}</if>
            <if test="leader != null and leader != ''"> and a.leader like concat('%', #{leader},'%')</if>
            <if test="legalPerson != null and legalPerson != ''"> and a.name like concat('%', #{legalPerson},'%')</if>
        </where>
    </select>

    <select id="selectTblCompanyEmpByAssetId" parameterType="Long" resultMap="TblCompanyEmpResult">
        <include refid="selectTblCompanyEmpVo"/>
        where find_in_set(#{assetId}, asset_ids)
    </select>

    <select id="selectTblCompanyEmpByEid" parameterType="Long" resultMap="TblCompanyEmpResult">
        <include refid="selectTblCompanyEmpVo"/>
        where a.eid = #{eid}
    </select>

    <select id="selectTblCompanyEmpByEids" parameterType="Long" resultMap="TblCompanyEmpResult">
        <include refid="selectTblCompanyEmpVo"/>
        where a.eid in
        <foreach item="eid" collection="array" open="(" separator="," close=")">
            #{eid}
        </foreach>
    </select>

    <select id="getcompanyEmpNums" resultType="java.lang.Integer">
        select count(DISTINCT e.name) from tbl_company_emp e
        <where>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
    </select>

    <select id="getCompanyEmpInfo" resultType="com.ruoyi.system.domain.TblCompanyEmp" resultMap="TblCompanyEmpResult">
        select e.eid,e.name,e.address,e.operation_start_time,e.operation_stop_time  from tbl_company_emp e
        <where>
            <if test="params.dataScope != null and params.dataScope != ''">${params.dataScope}</if>
        </where>
    </select>

    <insert id="insertTblCompanyEmp" parameterType="TblCompanyEmp">
        insert into tbl_company_emp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eid != null">eid,</if>
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="sex != null">sex,</if>
            <if test="cardno != null">cardno,</if>
            <if test="address != null">address,</if>
            <if test="phone != null">phone,</if>
            <if test="post != null">post,</if>
            <if test="mail != null">mail,</if>
            <if test="photo != null">photo,</if>
            <if test="state != null">state,</if>
            <if test="fileId != null">file_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="dwid != null">dwid,</if>
            <if test="enterDeptId != null">enter_dept_id,</if>
            <if test="assetIds != null">asset_ids,</if>
            <if test="companyName != null">company_name,</if>
            <if test="isSite != null">is_site,</if>
            <if test="isBackTone != null">is_back_tone,</if>
            <if test="leader != null">leader,</if>
            <if test="legalPerson != null">legal_person,</if>
            <if test="operationStartTime != null">operation_start_time,</if>
            <if test="operationStopTime != null">operation_stop_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eid != null">#{eid},</if>
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="sex != null">#{sex},</if>
            <if test="cardno != null">#{cardno},</if>
            <if test="address != null">#{address},</if>
            <if test="phone != null">#{phone},</if>
            <if test="post != null">#{post},</if>
            <if test="mail != null">#{mail},</if>
            <if test="photo != null">#{photo},</if>
            <if test="state != null">#{state},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="dwid != null">#{dwid},</if>
            <if test="enterDeptId != null">#{enterDeptId},</if>
            <if test="assetIds != null">#{assetIds},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="isSite != null">#{isSite},</if>
            <if test="isBackTone != null">#{isBackTone},</if>
            <if test="leader != null">#{leader},</if>
            <if test="legalPerson != null">#{legalPerson},</if>
            <if test="operationStartTime != null">#{operationStartTime},</if>
            <if test="operationStopTime != null">#{operationStopTime},</if>
         </trim>
    </insert>

    <update id="updateTblCompanyEmp" parameterType="TblCompanyEmp">
        update tbl_company_emp
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="cardno != null">cardno = #{cardno},</if>
            <if test="address != null">address = #{address},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="post != null">post = #{post},</if>
            <if test="mail != null">mail = #{mail},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="state != null">state = #{state},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="dwid != null">dwid = #{dwid},</if>
            <if test="enterDeptId != null">enter_dept_id = #{enterDeptId},</if>
            <if test="assetIds != null">asset_ids = #{assetIds},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="isSite != null">is_site = #{isSite},</if>
            <if test="isBackTone != null">is_back_tone = #{isBackTone},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="legalPerson != null">legal_person = #{legalPerson},</if>
            <if test="operationStartTime != null">operation_start_time = #{operationStartTime},</if>
            <if test="operationStopTime != null"> operation_stop_time = #{operationStopTime},</if>
            <if test="seatId != null">  seat_id = #{seatId}</if>
        </trim>
        where eid = #{eid}
    </update>

    <delete id="deleteTblCompanyEmpByEid" parameterType="Long">
        delete from tbl_company_emp where eid = #{eid}
    </delete>

    <delete id="deleteTblCompanyEmpByEids" parameterType="String">
        delete from tbl_company_emp where eid in
        <foreach item="eid" collection="array" open="(" separator="," close=")">
            #{eid}
        </foreach>
    </delete>

    <update id="clearSeatIdsForEmpBatch" parameterType="java.util.List">
        update tbl_company_emp set seat_id = null where seat_id in
        <foreach collection="list" item="seatId" open="(" separator="," close=")">
            #{seatId}
        </foreach>
    </update>

</mapper>
