<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.AssetOverviewMapper">

    <resultMap id="CountByDomainMap" type="com.ruoyi.safe.vo.assetOverview.CountByDomainVO">
        <result property="domainId" column="domainId"/>
        <result property="count" column="count"/>
    </resultMap>

    <select id="getSafetyCountByDomains" parameterType="list" resultMap="CountByDomainMap">
        select  g.domain_id as domainId, COUNT(distinct a.asset_id) AS count
        from tbl_safety a
        left join tbl_network_ip_mac as g on g.asset_id = a.asset_id and g.main_ip='1'
        left join tbl_network_domain as n on g.domain_id is not null and g.domain_id = n.domain_id
        WHERE g.domain_id IN
        <foreach collection="domainIds" item="domainId" separator="," close=")" open="(">
            #{domainId}
        </foreach>
        GROUP BY g.domain_id
    </select>

    <select id="getAlarmCountByDomains" parameterType="list" resultMap="CountByDomainMap">
        <!--select nim.domain_id as domainId,
        count(DISTINCT ta.id) as count
        FROM tbl_threaten_alarm ta
        LEFT JOIN (select distinct ipv4,domain_id from tbl_network_ip_mac) nim ON nim.ipv4 = ta.src_ip OR nim.ipv4 = ta.dest_ip
        where ta.handle_state=0 and nim.domain_id in
        <foreach collection="domainIds" item="domainId" separator="," open="(" close=")">
            #{domainId}
        </foreach>
        GROUP BY nim.domain_id-->
        SELECT
        domainId,
        COUNT( DISTINCT id ) AS count
        FROM
        (
        SELECT
        nim.domain_id AS domainId,
        ta1.id AS id
        FROM
        ( SELECT DISTINCT ipv4, domain_id FROM tbl_network_ip_mac ) nim
        LEFT JOIN tbl_threaten_alarm ta1 ON nim.ipv4 = ta1.src_ip
        AND ta1.handle_state = 0
        WHERE
        nim.domain_id IN
        <foreach collection="domainIds" item="domainId" separator="," open="(" close=")">
            #{domainId}
        </foreach>
        UNION ALL
        SELECT
        nim.domain_id AS domainId,
        ta2.id AS id
        FROM
        ( SELECT DISTINCT ipv4, domain_id FROM tbl_network_ip_mac ) nim
        LEFT JOIN tbl_threaten_alarm ta2 ON nim.ipv4 = ta2.dest_ip
        AND ta2.handle_state = 0
        WHERE
        nim.domain_id IN
        <foreach collection="domainIds" item="domainId" separator="," open="(" close=")">
            #{domainId}
        </foreach>
        ) combined
        GROUP BY
        domainId;
    </select>


    <select id="getDomainStatistics" resultType="com.ruoyi.safe.vo.assetOverview.DomainStatisticsVO">
        SELECT
            t1.domain_id AS domainId,
            COUNT(DISTINCT t6.asset_id) AS applicationCount,
            COALESCE(COUNT(DISTINCT t7.id)+COUNT(DISTINCT t8.id), 0) AS vulnCount
        FROM
        tbl_network_domain t1
        LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
        LEFT JOIN tbl_network_ip_mac t2 ON t2.domain_id = t1.domain_id
        LEFT JOIN tbl_application_server t3 ON t3.server_id = t2.asset_id
        LEFT JOIN tbl_application_netware t4 ON t4.netware_id = t2.asset_id
        LEFT JOIN tbl_application_safe t5 ON t5.safe_id = t2.asset_id
        LEFT JOIN tbl_business_application t6 ON (t6.asset_id = t3.asset_id OR t6.asset_id = t4.asset_id OR t6.asset_id = t5.asset_id)
        LEFT JOIN monitor_bss_vuln_deal t7 ON t7.host_ip = t2.ipv4
        LEFT JOIN monitor_bss_webvuln_deal t8 ON t8.web_url = t6.url
        WHERE
        t1.domain_id IN
        <foreach item="domainId" collection="domainIds" open="(" separator="," close=")">
            #{domainId}
        </foreach>
        GROUP BY t1.domain_id
    </select>

    <select id="getOperatingSystemTop5" resultType="com.ruoyi.safe.vo.assetOverview.Top5VO">
        SELECT tp.prdid AS prdid,
                td.proc_name AS procName,
               COUNT(td.deploy_id)   AS count
        FROM tbl_deploy td
        LEFT JOIN tbl_product tp on tp.prdid = td.prdid
        LEFT JOIN tbl_server ts on ts.asset_id = td.asset_id
        LEFT JOIN sys_dept sd ON sd.dept_id = ts.dept_id
        <where>
            AND tp.proc_type = 'system'
            AND td.softlx = 1
            ${params.dataScope}
        </where>
        GROUP BY tp.prdid
        ORDER BY count DESC
        LIMIT 5
    </select>


    <select id="getMiddlewareTop5" resultType="com.ruoyi.safe.vo.assetOverview.Top5VO">
        SELECT
          t1.prdid,t1.proc_name AS procName,COUNT(t1.prdid) `count`,GROUP_CONCAT(tba.asset_id) applicationIds
        FROM
          tbl_product t1
          LEFT JOIN tbl_deploy t2 ON t2.prdid=t1.prdid
          LEFT JOIN tbl_server t3 ON t3.asset_id=t2.asset_id
          LEFT JOIN tbl_application_server t4 ON t4.server_id=t3.asset_id
          LEFT JOIN tbl_business_application tba ON tba.asset_id=t4.asset_id
          LEFT JOIN sys_dept sd ON sd.dept_id=tba.dept_id
        <where>
            AND t1.proc_type='middleware' AND tba.asset_id IS NOT NULL AND t2.moduel_id IS NULL
            AND t2.application_id IS NULL
            AND t2.data_resource_id IS NULL
            AND t2.softlx IS NULL
            ${params.dataScope}
        </where>
        GROUP BY
          t1.prdid
        ORDER BY
          COUNT(t1.prdid) DESC
        LIMIT
          5
    </select>

    <select id="getApplicationFirewallNat" resultType="com.ruoyi.safe.vo.assetOverview.ApplicationFirewallNatVO">
        SELECT
            tba.asset_id AS applicationId,
            t1.id AS natId,
            t1.public_expose_ip as publicIp,
            t1.public_expose_port as publicPort,
            tba.asset_name AS assetName
        FROM
            tbl_business_application tba
                inner JOIN
            tbl_firewall_nat t1 ON FIND_IN_SET(tba.asset_id, t1.business_application_id) > 0
        <where>
            ${params.dataScope}
        </where>
    </select>

    <select id="getAppCountBySystemType" resultType="com.ruoyi.safe.vo.assetOverview.AppCountBySystemTypeVO">
        SELECT tba.system_type as systemType,d.dict_label as systemTypeName, COUNT(tba.asset_id) AS count
        FROM tbl_business_application tba
        INNER JOIN sys_dict_data d ON tba.system_type = d.dict_value
        WHERE d.status = '0'
          AND d.dict_type = 'system_type'
          <!-- 数据范围过滤 -->
          ${params.dataScope}
        GROUP BY tba.system_type
        ORDER BY count DESC
    </select>

    <select id="assetTypeCount" resultType="com.alibaba.fastjson2.JSONObject">
        <!--SELECT
            CASE
                WHEN t1.asset_class = 4 THEN
                    'host'
                WHEN t1.asset_class = 3 THEN
                    'safetyEquipment'
                WHEN t1.asset_class = 2 THEN
                    'networkEquipment'
                WHEN t1.asset_class = 7 THEN
                    'businessSystems'
                WHEN t1.asset_class = 18 THEN
                    'regionalDevices'
                WHEN t1.asset_class = 5 THEN
                    'terminal'
                END NAME,
            count(DISTINCT t1.asset_id ) AS
	VALUE
        FROM
            tbl_asset_overview t1
            left join sys_dept sd on sd.dept_id = t1.dept_id
        <where>
            t1.asset_class IN ( 7, 2, 3, 4, 18,5 )
            &lt;!&ndash; 数据范围过滤 &ndash;&gt;
            ${params.dataScope}
        </where>
        GROUP BY
            t1.asset_class
        ORDER BY
            t1.asset_class DESC-->
        SELECT
        'businessSystems' AS NAME,
        COUNT( 1 ) AS VALUE
        FROM
        tbl_business_application t1
        LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
        <where>
            ${params.dataScope}
        </where>
                              UNION ALL
        SELECT
        'host' AS NAME,
        COUNT( 1 ) AS VALUE
        FROM
        tbl_server t1
        LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
        <where>
            ${params.dataScope}
        </where>
                              UNION ALL
        SELECT
        'networkEquipment' AS NAME,
        COUNT( 1 ) AS VALUE
        FROM
        tbl_network_devices t1
        LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
        <where>
            ${params.dataScope}
        </where>
                              UNION ALL
        SELECT
        'safetyEquipment' AS NAME,
        COUNT( 1 ) AS VALUE
        FROM
        tbl_safety t1
        LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
        <where>
            ${params.dataScope}
        </where>
                              UNION ALL
        SELECT
        'regionalDevices' AS NAME,
        COUNT( 1 ) AS VALUE
        FROM
        tbl_zone_boundary t1
        LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
        <where>
            ${params.dataScope}
        </where>
                              UNION ALL
        SELECT
        'terminal' AS NAME,
        COUNT( 1 ) AS VALUE
        FROM
        tbl_terminal t1
        LEFT JOIN sys_dept sd ON sd.dept_id = t1.dept_id
        <where>
            ${params.dataScope}
        </where>
    </select>

    <select id="selectAssetNewAndAbnormal" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            CASE
                WHEN exception_state = 1 THEN
                    'abnormalAsset'
                WHEN exception_state = 0 THEN
                    'newAsset'
                END NAME,
            COUNT(mh.id) AS VALUE
        FROM
            monitor_handle mh
            left join sys_dept sd on sd.dept_id = mh.dept_id
        WHERE
            exception_state IN (0, 1) and mh.is_del='0'
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        GROUP BY
            exception_state
    </select>

    <select id="getDeptAppOverview" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            sd.dept_id deptId,
            IF(sd.concatDeptName IS NOT NULL,CONCAT(sd.concatDeptName,'/',sd.dept_name),sd.dept_name) deptName,
            COUNT(DISTINCT tba.asset_id) assetNum,
            COUNT(DISTINCT tba2.asset_id) PG02,
            COUNT(DISTINCT tba3.asset_id) PG03,
            COUNT(DISTINCT tba4.asset_id) PG04,
            COUNT(DISTINCT tba5.asset_id) PG05,
            IF(SUM(t3.threat_sum) !='',SUM(DISTINCT t3.threat_sum),0) AS threatSum,
            (
                SELECT
                    SUM(IF(asset_id = tba.asset_id,1,0))
                FROM
                    tbl_application_link
            ) externalConnectionNum,
            IFNULL(COUNT(DISTINCT t5.id),0) ipVulnCount,
            IFNULL(COUNT(DISTINCT t6.id),0) webVulnCount
        FROM
        (
        SELECT
        d1.*,
        GROUP_CONCAT(d2.dept_name ORDER BY d2.parent_id SEPARATOR'/') concatDeptName
        FROM
        sys_dept d1
        LEFT JOIN sys_dept d2 ON LOCATE(d2.dept_id,d1.ancestors)
        GROUP BY
        d1.dept_id
        ) sd
                LEFT JOIN tbl_business_application tba ON tba.dept_id=sd.dept_id
                LEFT JOIN tbl_asset_overview t3 ON t3.asset_id=tba.asset_id
                LEFT JOIN tbl_application_server t4 ON t4.asset_id=tba.asset_id
                LEFT JOIN tbl_network_ip_mac nim ON nim.asset_id=t4.server_id
                LEFT JOIN monitor_bss_vuln_deal t5 ON t5.host_ip=nim.ipv4 and t5.handle_state=0
                LEFT JOIN monitor_bss_webvuln_deal t6 ON t6.web_url=tba.url and t6.handle_state=0
                LEFT JOIN tbl_business_application tba2 ON tba2.asset_id=tba.asset_id AND tba2.protect_grade='PG02'
                LEFT JOIN tbl_business_application tba3 ON tba3.asset_id=tba.asset_id AND tba3.protect_grade='PG03'
                LEFT JOIN tbl_business_application tba4 ON tba4.asset_id=tba.asset_id AND tba4.protect_grade='PG04'
                LEFT JOIN tbl_business_application tba5 ON tba5.asset_id=tba.asset_id AND (tba5.protect_grade='PG05' OR tba5.protect_grade IS NULL)
        WHERE
            1=1
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        GROUP BY
            sd.dept_id
        HAVING
            COUNT(DISTINCT tba.asset_id) > 0
    </select>

    <select id="getRiskAssetsTop5" resultType="com.ruoyi.safe.vo.assetOverview.RiskAssetsTop5VO">
        SELECT a.asset_id,
               a.asset_name AS asset_name,
               COALESCE(SUM(a.vuln_count), 0) AS count
        FROM (
               SELECT tba1.asset_id,tba1.asset_name, COUNT(DISTINCT vd.id) AS vuln_count
                 FROM monitor_bss_vuln_deal vd
                     left join tbl_network_ip_mac im on vd.host_ip = im.ipv4
                     left join tbl_asset_overview t2 on t2.asset_id=im.asset_id
                     left join sys_dept sd on sd.dept_id=t2.dept_id
                     LEFT JOIN tbl_application_server t3 ON t3.server_id=im.asset_id
                     LEFT JOIN tbl_application_netware t4 ON t4.netware_id=im.asset_id
                     LEFT JOIN tbl_application_safe t5 ON t5.safe_id=im.asset_id
                     LEFT JOIN tbl_business_application tba1 ON (tba1.asset_id=im.asset_id OR tba1.asset_id=t3.asset_id OR tba1.asset_id=t4.asset_id OR tba1.asset_id=t5.asset_id)
               WHERE  (tba1.dept_id = #{deptId} OR tba1.dept_id IN (SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET(#{deptId}, t.ancestors)))
                 GROUP BY tba1.asset_id
                 UNION ALL
                 SELECT tba.asset_id, tba.asset_name, COUNT(DISTINCT wvd.id) AS vuln_count
                 FROM monitor_bss_webvuln_deal wvd
                          LEFT JOIN tbl_business_application tba ON wvd.web_url = tba.url
                 WHERE (tba.dept_id = #{deptId} OR tba.dept_id IN (SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET(#{deptId}, t.ancestors)))
                 GROUP BY tba.asset_id
             ) a
        GROUP BY a.asset_id
        ORDER BY count DESC
        LIMIT 5
    </select>

    <select id="getThreatenAlarmCountTop5" resultType="com.ruoyi.safe.vo.assetOverview.RiskAssetsTop5VO">
        SELECT
            tba.asset_id,
            tba.asset_name,
            COUNT( DISTINCT vd.id ) AS count
        FROM
            tbl_threaten_alarm vd
            LEFT JOIN tbl_network_ip_mac im ON vd.src_ip = im.ipv4 OR vd.dest_ip=im.ipv4
            LEFT JOIN tbl_application_server t3 ON t3.server_id = im.asset_id
            LEFT JOIN tbl_business_application tba ON tba.asset_id = t3.asset_id
        WHERE
            (tba.dept_id = #{deptId} OR tba.dept_id IN (SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET(#{deptId}, t.ancestors)))
        GROUP BY
            tba.asset_id
        ORDER BY
            count DESC
        LIMIT 5
    </select>
    <select id="getVulnCountByDomain" resultType="com.ruoyi.safe.vo.assetOverview.CountByDomainVO">
        SELECT
          t1.domain_id domainId,COUNT(DISTINCT t3.id) `count`,GROUP_CONCAT(t3.id) bssIds
        FROM
          tbl_network_domain t1
          LEFT JOIN tbl_network_ip_mac nim ON nim.domain_id=t1.domain_id
          LEFT JOIN tbl_server t2 ON t2.asset_id=nim.asset_id
          LEFT JOIN monitor_bss_vuln_deal t3 ON t3.host_ip=nim.ipv4 and t3.handle_state=0
        WHERE
          t2.asset_id IS NOT NULL
          AND t1.domain_id IN
          <foreach collection="domainIds" item="domainId" open="(" close=")" separator=",">
              #{domainId}
          </foreach>
        GROUP BY
          t1.domain_id
    </select>
    <select id="getWebVulnCountByDomains" resultType="com.ruoyi.safe.vo.assetOverview.CountByDomainVO">
        SELECT
          t1.domain_id domainId,COUNT(DISTINCT t3.id) `count`
        FROM
          tbl_network_domain t1
          LEFT JOIN tbl_business_application t2 ON t2.domain_id=t1.domain_id
          LEFT JOIN monitor_bss_webvuln_deal t3 ON t3.web_url=t2.url and t3.handle_state=0
        WHERE
          t1.domain_id IN
          <foreach collection="domainIds" item="domainId" open="(" close=")" separator=",">
              #{domainId}
          </foreach>
        GROUP BY
          t1.domain_id
    </select>


</mapper>
