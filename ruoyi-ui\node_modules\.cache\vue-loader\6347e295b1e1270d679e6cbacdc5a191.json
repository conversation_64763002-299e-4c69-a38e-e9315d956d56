{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\editServer.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\editServer.vue", "mtime": 1756381475338}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7YWRkU2VydmVyMiwgdXBkYXRlU2VydmVyMiwgZ2V0U2VydmVyfSBmcm9tICJAL2FwaS9zYWZlL3NlcnZlciI7CmltcG9ydCB7Z2V0VmVuZG9yfSBmcm9tICJAL2FwaS9zYWZlL3ZlbmRvciI7CmltcG9ydCB7Z2V0QXNzZXRUeXBlQ2hpbGRyZW5CeWlkfSBmcm9tICJAL2FwaS9zYWZlL292ZXJ2aWV3IjsKaW1wb3J0IHZlbmRvclNlbGVjdCBmcm9tICJAL3ZpZXdzL2NvbXBvbmVudHMvc2VsZWN0L3ZlbmRvclNlbGVjdCI7CmltcG9ydCBwcm9kdWN0U2VsZWN0IGZyb20gIkAvdmlld3MvY29tcG9uZW50cy9zZWxlY3QvcHJvZHVjdFNlbGVjdCI7CmltcG9ydCBEZXB0U2VsZWN0IGZyb20gIkAvdmlld3MvY29tcG9uZW50cy9zZWxlY3QvZGVwdFNlbGVjdCI7CmltcG9ydCBOZXR3b3JrU2VsZWN0IGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9zZWxlY3QvbmV0d29ya1NlbGVjdCc7CmltcG9ydCBMb2NhdGlvblNlbGVjdCBmcm9tICJAL3ZpZXdzL2NvbXBvbmVudHMvc2VsZWN0L2xvY2F0aW9uU2VsZWN0IjsKaW1wb3J0IER5bmFtaWNUYWcgZnJvbSAiQC9jb21wb25lbnRzL0R5bmFtaWNUYWciOwppbXBvcnQgT3ZlclZpZXdTZWxlY3QgZnJvbSAiQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdC9vdmVyVmlld1NlbGVjdCI7CmltcG9ydCB7Z2V0SG9zdEluZm8sIGdldEhvc3RMaXN0fSBmcm9tICJAL2FwaS9hc3NldC9ob3N0IjsKaW1wb3J0IHtsaXN0UHJvZHVjdH0gZnJvbSAiQC9hcGkvbW9uaXRvcjIvcHJvZHVjdCI7CmltcG9ydCB7bGlzdERvbWFpbn0gZnJvbSAiQC9hcGkvZGljdC9kb21haW4iOwppbXBvcnQgYXNzZXRSZWdpc3RlciBmcm9tICJAL21peGlucy9hc3NldFJlZ2lzdGVyIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRWRpdFNlcnZlcicsCiAgbWl4aW5zOiBbYXNzZXRSZWdpc3Rlcl0sCiAgZGljdHM6IFsnaW1wdF9ncmFkZScsICdzeXNfeWVzX25vJywgJ3Byb2NfdHlwZScsJ2lzX3NwYXJpbmcnXSwKICBjb21wb25lbnRzOiB7CiAgICB2ZW5kb3JTZWxlY3QsCiAgICBwcm9kdWN0U2VsZWN0LAogICAgRGVwdFNlbGVjdCwKICAgIE5ldHdvcmtTZWxlY3QsCiAgICBMb2NhdGlvblNlbGVjdCwKICAgIER5bmFtaWNUYWcsCiAgICBPdmVyVmlld1NlbGVjdCwKICB9LAogIHByb3BzOiB7CiAgICBhc3NldElkOiB7CiAgICAgIHR5cGU6IFtOdW1iZXIsIFN0cmluZ10sCiAgICAgIGRlZmF1bHQ6IG51bGwsCiAgICAgIHJlcXVpcmVkOiBmYWxzZQogICAgfSwKICAgIGhhbmRsZURhdGE6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBudWxsLAogICAgICByZXF1aXJlZDogZmFsc2UKICAgIH0sCiAgICB0aXRsZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICfmlrDlop7mnI3liqHlmagnCiAgICB9LAogICAgZWRpdEZsYWdWaXNpYmxlOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaG9zdDogdW5kZWZpbmVkLAogICAgICBhc3NldEhvc3RzOiBbXSwKICAgICAgYXNzZXRIb3N0RGlhbG9nOiBmYWxzZSwKICAgICAgbXlUYWdzOiBbJ+WbveS6p+WMlicsICflronlj68nXSwKICAgICAgY2xhc3NJZDogNCwKICAgICAgY2hpbGRyZW46IFtdLAogICAgICB0eXBlVHJlZURhdGE6IFtdLAogICAgICB0eXBlbGlzdDogW10sCiAgICAgIHZlbmRvckRpYWxvZzogZmFsc2UsCiAgICAgIHNvZnREaWFsb2c6IGZhbHNlLAogICAgICBzb2Z0VHlwZTogJ3N5c3RlbScsCiAgICAgIHNvZnRGaWVsZDogJycsCiAgICAgIGVkaXRJdGVtOiAnZWRpdCcsCiAgICAgIGFkZEV2ZW50OiAnJywKICAgICAgbXVsdGlwbGU6IGZhbHNlLAogICAgICBmb3JtOiB7CiAgICAgICAgaXBNYWNBcnI6IFtdLAogICAgICAgIGFzc2V0TmFtZTogJycsCiAgICAgICAgZXhwb3NlZElwOiAnJywKICAgICAgICBoYW5kbGVJZDogdW5kZWZpbmVkLAogICAgICAgIGRic3lzdGVtQXJyOiBbXSwKICAgICAgICBtZHN5c3RlbUFycjogW10sCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgYXNzZXRDb2RlOiBbCiAgICAgICAgICAvKntyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui1hOS6p+e8lueggeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sKi8KICAgICAgICAgIHttaW46IDAsIG1heDogNjQsIG1lc3NhZ2U6ICfotYTkuqfnvJbnoIHkuI3og73otoXov4cgNjQg5Liq5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIGFzc2V0TmFtZTogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6LWE5Lqn5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICAgIHttaW46IDAsIG1heDogNjQsIG1lc3NhZ2U6ICfotYTkuqflkI3np7DkuI3og73otoXov4cgNjQg5Liq5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIGFzc2V0VHlwZTogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6LWE5Lqn57G75Z6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICBdLAogICAgICAgIGlwOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBwYXR0ZXJuOiAnXigyNVswLTVdfDJbMC00XVxcZHxbMC0xXT9cXGQ/XFxkKShcXC4oMjVbMC01XXwyWzAtNF1cXGR8WzAtMV0/XFxkP1xcZCkpezN9JCcsCiAgICAgICAgICAgIG1lc3NhZ2U6ICJJUOWcsOWdgOS4jeiDveS4uuepuuaIluagvOW8j+S4jeato+ehriIsCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICAgIG1hYzogWwogICAgICAgICAge3BhdHRlcm46ICdeKFswLTlhLWZBLUZdezJ9KDp8LSkpezV9WzAtOWEtZkEtRl17Mn0kJywgbWVzc2FnZTogIk1hY+WcsOWdgOagvOW8j+S4jeato+ehriIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgICBkb21haW5JZDogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi572R57uc5Yy65Z+f5LiN6IO95Li656m677yBIiwgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgIF0sCiAgICAgICAgbG9jYXRpb25EZXRhaWw6IFsKICAgICAgICAgIHttaW46IDAsIG1heDogMTI4LCBtZXNzYWdlOiAn6K+m57uG5Zyw5Z2A5LiN6IO96LaF6L+HIDEyOCDkuKrlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgIF0sCiAgICAgICAgZGVwdElkOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYDlsZ7pg6jpl6jkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgIF0KICAgICAgfSwKICAgICAgYmFzZUFzc2V0RGlhbG9nOiBmYWxzZSwKICAgICAgYXNzZXRBbGxvY2F0aW9uVHlwZTogJzInLAogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIHZpc2libGU6IHsKICAgICAgZ2V0KCkgewogICAgICAgIHJldHVybiB0aGlzLmVkaXRGbGFnVmlzaWJsZQogICAgICB9LAogICAgICBzZXQodmFsKSB7CiAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOmVkaXRGbGFnVmlzaWJsZScsIHZhbCkKICAgICAgfQogICAgfSwKICAgIC8vIOWKqOaAgeWtl+autQogICAgdmlzaWJsZUFzc2V0RmllbGRzKCkgewogICAgICByZXR1cm4gdGhpcy5hc3NldExpc3QubWFwKGdyb3VwID0+IHsKICAgICAgICBjb25zdCBuZXdHcm91cCA9IHsgLi4uZ3JvdXAgfTsKICAgICAgICBuZXdHcm91cC5maWVsZHNJdGVtcyA9IGdyb3VwLmZpZWxkc0l0ZW1zLmZpbHRlcihpdGVtID0+IGl0ZW0uaXNTaG93KTsKICAgICAgICByZXR1cm4gbmV3R3JvdXA7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWKqOaAgeinhOWImQogICAgZHluYW1pY1J1bGVzKCkgewogICAgICBjb25zdCBydWxlcyA9IHt9OwogICAgICB0aGlzLnZpc2libGVBc3NldEZpZWxkcy5mb3JFYWNoKGdyb3VwID0+IHsKICAgICAgICBncm91cC5maWVsZHNJdGVtcy5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgY29uc3QgZmllbGRLZXkgPSBpdGVtLmZpZWxkS2V5OwoKICAgICAgICAgIGlmICghcnVsZXNbZmllbGRLZXldKSB7CiAgICAgICAgICAgIHJ1bGVzW2ZpZWxkS2V5XSA9IFtdOwogICAgICAgICAgfQoKICAgICAgICAgIGlmICh0aGlzLnJ1bGVzW2ZpZWxkS2V5XSkgewogICAgICAgICAgICBjb25zdCBmaWx0ZXJlZFJ1bGVzID0gdGhpcy5ydWxlc1tmaWVsZEtleV0uZmlsdGVyKHJ1bGUgPT4gIXJ1bGUucmVxdWlyZWQpOwogICAgICAgICAgICBydWxlc1tmaWVsZEtleV0ucHVzaCguLi5maWx0ZXJlZFJ1bGVzKTsKICAgICAgICAgIH0KCiAgICAgICAgICBpZiAoaXRlbS5yZXF1aXJlZCkgewogICAgICAgICAgICBjb25zdCBoYXNSZXF1aXJlZFJ1bGUgPSBydWxlc1tmaWVsZEtleV0uc29tZShydWxlID0+IHJ1bGUucmVxdWlyZWQpOwogICAgICAgICAgICBpZiAoIWhhc1JlcXVpcmVkUnVsZSkgewogICAgICAgICAgICAgIHJ1bGVzW2ZpZWxkS2V5XS5wdXNoKHsKICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICAgICAgbWVzc2FnZTogYCR7aXRlbS5maWVsZE5hbWV95LiN6IO95Li656m6YCwKICAgICAgICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICByZXR1cm4gcnVsZXM7CiAgICB9LAoKICAgIC8vIOe9kee7nOS/oeaBr+WIl+ihqOWtl+autQogICAgbmV0aW5mb1RhYmxlKCkgewogICAgICBsZXQgYXNzZXRGaWVsZHMgPSB0aGlzLmFzc2V0TGlzdC5maWx0ZXIoZ3JvdXAgPT4gZ3JvdXAuZm9ybU5hbWUgPT09ICfnvZHnu5zkv6Hmga8nKQogICAgICByZXR1cm4gYXNzZXRGaWVsZHNbMF0uZmllbGRzSXRlbXMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5maWVsZEtleSAhPT0gJ2V4cG9zZWRJcCcgJiYgaXRlbS5pc1Nob3cpCiAgICB9CiAgfSwKICBtb3VudGVkKCkgewoKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldERpY3REYXRhSW5mbyhmaWVsZEtleSkgewogICAgICBzd2l0Y2ggKGZpZWxkS2V5KSB7CiAgICAgICAgY2FzZSAnaXNWaXJ0dWFsJzoKICAgICAgICAgIHJldHVybiB0aGlzLmRpY3QudHlwZS5zeXNfeWVzX25vOwogICAgICAgIGNhc2UgJ2lzU3BhcmluZyc6CiAgICAgICAgICByZXR1cm4gdGhpcy5kaWN0LnR5cGUuaXNfc3BhcmluZzsKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9LAoKICAgIG9wZW5EaWFsb2coKSB7CiAgICAgIHRoaXMuaW5pdCgpOwogICAgICB0aGlzLmluaXREYXRhKCk7CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICBpZiAoIXRoaXMuZm9ybS5hc3NldENvZGUpewogICAgICAgICAgdGhpcy5mb3JtLmFzc2V0Q29kZSA9ICdaSicgKyB0aGlzLmdldEZvcm1hdHRlZERhdGUoKQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBjaG9vc2VIb3N0KCkgewogICAgICBpZighdGhpcy5ob3N0KXsKICAgICAgICB0aGlzLiRtZXNzYWdlKCfor7fpgInmi6nkuIDlj7DkuLvmnLrvvIEnKQogICAgICB9ZWxzZXsKICAgICAgICB0aGlzLmdldGhvc3QodGhpcy5ob3N0KQogICAgICAgIHRoaXMuYXNzZXRIb3N0RGlhbG9nID0gZmFsc2UKICAgICAgfQogICAgfSwKICAgIGluaXQoKSB7CiAgICAgIGdldEFzc2V0VHlwZUNoaWxkcmVuQnlpZCh0aGlzLmNsYXNzSWQpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnR5cGVsaXN0ID0gcmVzLnJvd3M7CiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnR5cGVsaXN0Lmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICBpZiAodGhpcy50eXBlbGlzdFtpXS50eXBlR3JhZGVkUHJvdGVjdGlvbiA9PSAxKSB7CiAgICAgICAgICAgIHRoaXMudHlwZWxpc3Quc3BsaWNlKE51bWJlcihpKSwgMSk7CiAgICAgICAgICAgIGkgPSBpIC0gMTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgdGhpcy50eXBlVHJlZURhdGEgPSB0aGlzLmhhbmRsZVRyZWUodGhpcy50eXBlbGlzdCwgJ2lkJywgJ3BpZCcpOwogICAgICAgIHRoaXMuY2hpbGRyZW4gPSB0aGlzLnR5cGVUcmVlRGF0YVswXS5jaGlsZHJlbjsKICAgICAgICBpZiAodGhpcy5jaGlsZHJlbiAmJiB0aGlzLmNoaWxkcmVuLmxlbmd0aCkgdGhpcy5ydWxlcy5hc3NldFR5cGUgPSBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLotYTkuqfnsbvlnovkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgIF0KICAgICAgfSk7CiAgICB9LAogICAgYXN5bmMgaW5pdERhdGEoKSB7CiAgICAgIGlmICh0aGlzLmFzc2V0SWQpIHsKICAgICAgICBhd2FpdCBnZXRTZXJ2ZXIodGhpcy5hc3NldElkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmlwTWFjQXJyKSB7CiAgICAgICAgICAgIHJlc3BvbnNlLmRhdGEuaXBNYWNBcnIuZm9yRWFjaChlID0+IHsKICAgICAgICAgICAgICBpZiAoZS5tYWluSXAgPT09ICcxJykgewogICAgICAgICAgICAgICAgZS5pc01haW5JcCA9IHRydWUKICAgICAgICAgICAgICB9IGVsc2UgaWYgKGUubWFpbklwID09PSAnMCcpIHsKICAgICAgICAgICAgICAgIGUuaXNNYWluSXAgPSBmYWxzZQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLm9wdHN5c3RlbSAhPSBudWxsKSB0aGlzLmZvcm0ub3B0SWQgPSB0aGlzLmZvcm0ub3B0c3lzdGVtLnByb2NOYW1lOwogICAgICAgICAgaWYgKHRoaXMuZm9ybS5kYnN5c3RlbUFyciAhPT0gbnVsbCAmJiB0aGlzLmZvcm0uZGJzeXN0ZW1BcnIgIT09IHVuZGVmaW5lZCAmJiB0aGlzLmZvcm0uZGJzeXN0ZW1BcnIubGVuZ3RoKSB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5kYklkID0gJycKICAgICAgICAgICAgdGhpcy5mb3JtLmRic3lzdGVtQXJyLmZvckVhY2goZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5mb3JtLmRiSWQgKz0gJ+OAkCcgKyBlLnByb2NOYW1lICsgJ+OAkScKICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICAgIGlmICh0aGlzLmZvcm0ubWRzeXN0ZW1BcnIgIT09IG51bGwgJiYgdGhpcy5mb3JtLm1kc3lzdGVtQXJyICE9PSB1bmRlZmluZWQgJiYgdGhpcy5mb3JtLm1kc3lzdGVtQXJyLmxlbmd0aCkgewogICAgICAgICAgICB0aGlzLmZvcm0ubWRJZCA9ICcnCiAgICAgICAgICAgIHRoaXMuZm9ybS5tZHN5c3RlbUFyci5mb3JFYWNoKGUgPT4gewogICAgICAgICAgICAgIHRoaXMuZm9ybS5tZElkICs9ICfjgJAnICsgZS5wcm9jTmFtZSArICfjgJEnCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLm15VGFncyA9IHRoaXMuZm9ybS50YWdzID8gdGhpcy5mb3JtLnRhZ3Muc3BsaXQoJywnKSA6IFtdOwoKICAgICAgICAgIGlmICh0aGlzLmZvcm0uZmFjaWxpdHlNYW51ZmFjdHVyZXIpIHsKICAgICAgICAgICAgZ2V0VmVuZG9yKHRoaXMuZm9ybS5mYWNpbGl0eU1hbnVmYWN0dXJlcikudGhlbihyZXMgPT4gewogICAgICAgICAgICAgIHRoaXMuZm9ybS5mYWNpbGl0eU1hbnVmYWN0dXJlck5hbWUgPSByZXMuZGF0YS52ZW5kb3JOYW1lOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KCiAgICAgICAgICBpZiAodGhpcy5mb3JtLm1haW50YWluVW5pdCkgewogICAgICAgICAgICBnZXRWZW5kb3IodGhpcy5mb3JtLm1haW50YWluVW5pdCkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgIHRoaXMuZm9ybS5tYWludGFpblVuaXROYW1lID0gcmVzLmRhdGEudmVuZG9yTmFtZTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfQogICAgICBpZiAodGhpcy5oYW5kbGVEYXRhKSB7CiAgICAgICAgaWYodGhpcy5oYW5kbGVEYXRhLmRlcHRJZCl7CiAgICAgICAgICB0aGlzLmZvcm0uZGVwdElkID0gdGhpcy5oYW5kbGVEYXRhLmRlcHRJZDsKICAgICAgICB9CiAgICAgICAgdGhpcy5mb3JtLmhhbmRsZUlkID0gdGhpcy5oYW5kbGVEYXRhLmhhbmRsZUlkCiAgICAgICAgaWYodGhpcy5oYW5kbGVEYXRhLmlwKXsKICAgICAgICAgIGxldCBpcE1hY0l0ZW0gPSB7CiAgICAgICAgICAgIGlzTWFpbklwOiB0cnVlLAogICAgICAgICAgICBpcHY0OiB0aGlzLmhhbmRsZURhdGEuaXAKICAgICAgICAgIH0KICAgICAgICAgIGF3YWl0IGxpc3REb21haW4oe2lwYXJlYTogdGhpcy5oYW5kbGVEYXRhLmlwfSkudGhlbihkb21haW5SZXMgPT4gewogICAgICAgICAgICBpZihkb21haW5SZXMuZGF0YSAmJiBkb21haW5SZXMuZGF0YS5sZW5ndGg+MCl7CiAgICAgICAgICAgICAgaXBNYWNJdGVtLmRvbWFpbklkID0gZG9tYWluUmVzLmRhdGFbMF0uZG9tYWluSWQ7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgICB0aGlzLmZvcm0uaXBNYWNBcnIucHVzaChpcE1hY0l0ZW0pOwogICAgICAgIH0KICAgICAgICBpZih0aGlzLmhhbmRsZURhdGEubmFtZSl7CiAgICAgICAgICBhd2FpdCBsaXN0UHJvZHVjdCh7cHJvY05hbWU6IHRoaXMuaGFuZGxlRGF0YS5uYW1lfSkudGhlbihwcm9kdWN0UmVzID0+IHsKICAgICAgICAgICAgaWYocHJvZHVjdFJlcy5yb3dzKXsKICAgICAgICAgICAgICBsZXQgbWF0Y2hBcnIgPSBwcm9kdWN0UmVzLnJvd3MuZmlsdGVyKGl0ZW0gPT4gdGhpcy5oYW5kbGVEYXRhLm5hbWUgPT09IGl0ZW0ucHJvY05hbWUpOwogICAgICAgICAgICAgIGlmKG1hdGNoQXJyICYmIG1hdGNoQXJyLmxlbmd0aCA+IDApewogICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwnb3B0SWQnLG1hdGNoQXJyWzBdLnByb2NOYW1lKTsKICAgICAgICAgICAgICAgIGxldCBvcHRzeXN0ZW0gPSB0aGlzLmZvcm0ub3B0c3lzdGVtIHx8IHt9OwogICAgICAgICAgICAgICAgb3B0c3lzdGVtLnByZGlkID0gbWF0Y2hBcnJbMF0ucHJkaWQ7CiAgICAgICAgICAgICAgICBvcHRzeXN0ZW0ucHJvY05hbWUgPSBtYXRjaEFyclswXS5wcm9jTmFtZTsKICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sJ29wdHN5c3RlbScsb3B0c3lzdGVtKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICAgIGlmICh0aGlzLmhhbmRsZURhdGEuaG9zdElkKSB7CiAgICAgICAgICBpZiAodGhpcy5hc3NldElkKSB7CiAgICAgICAgICAgIGlmICh0aGlzLmhhbmRsZURhdGEuaG9zdElkLmluZGV4T2YoJywnKSA+IDApIHsKICAgICAgICAgICAgICB0aGlzLmFzc2V0SG9zdERpYWxvZyA9IHRydWUKCiAgICAgICAgICAgICAgYXdhaXQgZ2V0SG9zdExpc3Qoe3BhZ2VTaXplOiAtMSwgaG9zdElkczogdGhpcy5oYW5kbGVEYXRhLmhvc3RJZC5zcGxpdCgnLCcpfSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ3JlcycsIHJlcykKICAgICAgICAgICAgICAgIHRoaXMuYXNzZXRIb3N0cyA9IHJlcy5kYXRhLmxpc3QKICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuS9v+eUqOaOoua1i+aVsOaNruabv+aNouacrOWcsOaVsOaNrj8nKS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuZ2V0aG9zdCh0aGlzLmhhbmRsZURhdGEuaG9zdElkKQogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0KICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuZ2V0aG9zdCh0aGlzLmhhbmRsZURhdGEuaG9zdElkKQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGdldGhvc3QoaG9zdElkKSB7CiAgICAgIGdldEhvc3RJbmZvKGhvc3RJZCkudGhlbihyZXMgPT4gewogICAgICAgIGNvbnN0IGhvc3QgPSByZXMuZGF0YQogICAgICAgIHRoaXMuZm9ybS5hc3NldE5hbWUgPSBob3N0Lmhvc3ROYW1lCiAgICAgICAgdGhpcy5mb3JtLmNwdUZyYW1lID0gaG9zdC5ob3N0UGh5c2ljYWwuYXJjaAogICAgICAgIGxldCBpcE1hY0FyciA9IGhvc3QuaG9zdE5pY0xpc3QuZmlsdGVyKHJvdyA9PiByb3cuaXB2NC5pbmRleE9mKGhvc3QuaG9zdElwKSA+IC0xKQogICAgICAgIGNvbnN0IG9sZElwTWFjQXJyID0gdGhpcy5mb3JtLmlwTWFjQXJyLmZpbHRlcihyb3cgPT4gcm93LmlwdjQuaW5kZXhPZihob3N0Lmhvc3RJcCkgPiAtMSkKICAgICAgICBjb25zb2xlLmxvZygnb2xkSXBNYWNBcnInLCBvbGRJcE1hY0FycikKICAgICAgICBpcE1hY0Fyci5mb3JFYWNoKHJvdyA9PiB7CiAgICAgICAgICByb3cuaXB2NCA9IHJvdy5pcHY0LnJlcGxhY2UoL1tcW1xdXS9nLCAnJykuc3BsaXQoJy8nKVswXQogICAgICAgICAgcm93LmlwdjYgPSAnJwogICAgICAgICAgcm93LmlkID0gJycKICAgICAgICAgIGlmIChvbGRJcE1hY0Fyci5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIHJvdy5kb21haW5JZCA9IG9sZElwTWFjQXJyWzBdLmRvbWFpbklkCiAgICAgICAgICAgIHJvdy5pc01haW5JcCA9IG9sZElwTWFjQXJyWzBdLmlzTWFpbklwCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICB0aGlzLmZvcm0uZXhwb3NlZElwID0gaG9zdC5leHBvc2VkSXAKICAgICAgICB0aGlzLmZvcm0uaXBNYWNBcnIgPSBpcE1hY0FycgogICAgICAgIHRoaXMuZm9ybS5jd0hvc3RJZCA9IGhvc3QuaWQKICAgICAgICB0aGlzLmZvcm0ub3B0c3lzdGVtID0ge3ByZGlkOiBob3N0LnByZGlkLCBwcm9jTmFtZTogaG9zdC5vc1JlbGVhc2VOYW1lfQogICAgICAgIHRoaXMuZm9ybS5vcHRJZCA9IHRoaXMuZm9ybS5vcHRzeXN0ZW0ucHJvY05hbWU7CiAgICAgICAgdGhpcy5mb3JtLmRic3lzdGVtQXJyID0gW10KICAgICAgICB0aGlzLmZvcm0ubWRzeXN0ZW1BcnIgPSBbXQogICAgICAgIGZvciAobGV0IHJvdyBvZiBob3N0Lmhvc3RBcHBsaWNhdGlvbkxpc3QpIHsKICAgICAgICAgIGlmIChyb3cuY2F0ZWdvcnkgPT09ICdkYXRhYmFzZScpIHsKICAgICAgICAgICAgdGhpcy5mb3JtLmRic3lzdGVtQXJyLnB1c2goe3ByZGlkOiByb3cucHJkaWQsIHByb2NOYW1lOiByb3cuYXBwfSkKICAgICAgICAgIH0gZWxzZSBpZiAocm93LmNhdGVnb3J5ID09PSAnd2ViX3NlcnZlcicpIHsKICAgICAgICAgICAgdGhpcy5mb3JtLm1kc3lzdGVtQXJyLnB1c2goe3ByZGlkOiByb3cucHJkaWQsIHByb2NOYW1lOiByb3cuYXBwfSkKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgdGhpcy5mb3JtLmRiSWQgPSAnJwogICAgICAgIHRoaXMuZm9ybS5kYnN5c3RlbUFyci5mb3JFYWNoKGUgPT4gewogICAgICAgICAgdGhpcy5mb3JtLmRiSWQgKz0gJ+OAkCcgKyBlLnByb2NOYW1lICsgJ+OAkScKICAgICAgICB9KQogICAgICAgIHRoaXMuZm9ybS5tZElkID0gJycKICAgICAgICB0aGlzLmZvcm0ubWRzeXN0ZW1BcnIuZm9yRWFjaChlID0+IHsKICAgICAgICAgIHRoaXMuZm9ybS5tZElkICs9ICfjgJAnICsgZS5wcm9jTmFtZSArICfjgJEnCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICAvKioKICAgICAqIOmAieaLqeS+m+W6lOWVhgogICAgICovCiAgICBzaG93VmVuZG9yRGlhbG9nKHZlbmRvcikgewogICAgICBpZiAodmVuZG9yID09ICd2ZW5kb3InKSB7CiAgICAgICAgdGhpcy52ZW5kb3JUeXBlID0gJ3ZlbmRvcic7CiAgICAgIH0KICAgICAgaWYgKHZlbmRvciA9PSAnZmFjaWxpdHlNYW51ZmFjdHVyZXInKSB7CiAgICAgICAgdGhpcy52ZW5kb3JUeXBlID0gJ2ZhY2lsaXR5TWFudWZhY3R1cmVyJzsKICAgICAgfQogICAgICBpZiAodmVuZG9yID09ICdtYWludGFpblVuaXQnKSB7CiAgICAgICAgdGhpcy52ZW5kb3JUeXBlID0gJ21haW50YWluVW5pdCc7CiAgICAgIH0KICAgICAgdGhpcy52ZW5kb3JEaWFsb2cgPSB0cnVlCiAgICB9LAogICAgZ2V0U2VsZWN0ZWRWZW5kb3IoKSB7CiAgICAgIGlmICh0aGlzLnZlbmRvclR5cGUgPT0gJ3ZlbmRvcicgJiYgdGhpcy5mb3JtLnZlbmRvciAhPSB1bmRlZmluZWQpIHsKICAgICAgICByZXR1cm4gdGhpcy5mb3JtLnZlbmRvcjsKICAgICAgfSBlbHNlIGlmICh0aGlzLnZlbmRvclR5cGUgPT0gJ2ZhY2lsaXR5TWFudWZhY3R1cmVyJyAmJiB0aGlzLmZvcm0uZmFjaWxpdHlNYW51ZmFjdHVyZXIgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuZm9ybS5mYWNpbGl0eU1hbnVmYWN0dXJlcjsKICAgICAgfSBlbHNlIGlmICh0aGlzLnZlbmRvclR5cGUgPT0gJ21haW50YWluVW5pdCcgJiYgdGhpcy5mb3JtLm1haW50YWluVW5pdCAhPSB1bmRlZmluZWQpIHsKICAgICAgICByZXR1cm4gdGhpcy5mb3JtLm1haW50YWluVW5pdDsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICog6YCJ5oup5Lqn5ZWGCiAgICAgKi8KICAgIHNlbGVjdENvbmZpcm0yKHZlbmRvcikgewogICAgICBpZiAodGhpcy52ZW5kb3JUeXBlID09ICd2ZW5kb3InKSB7CiAgICAgICAgdGhpcy5mb3JtLnZlbmRvciA9IHZlbmRvci5pZDsKICAgICAgICB0aGlzLmZvcm0udmVuZG9yTmFtZSA9IHZlbmRvci52ZW5kb3JOYW1lOwogICAgICB9CiAgICAgIGlmICh0aGlzLnZlbmRvclR5cGUgPT0gJ2ZhY2lsaXR5TWFudWZhY3R1cmVyJykgewogICAgICAgIHRoaXMuZm9ybS5mYWNpbGl0eU1hbnVmYWN0dXJlciA9IHZlbmRvci5pZDsKICAgICAgICB0aGlzLmZvcm0uZmFjaWxpdHlNYW51ZmFjdHVyZXJOYW1lID0gdmVuZG9yLnZlbmRvck5hbWU7CiAgICAgIH0KICAgICAgaWYgKHRoaXMudmVuZG9yVHlwZSA9PSAnbWFpbnRhaW5Vbml0JykgewogICAgICAgIHRoaXMuZm9ybS5tYWludGFpblVuaXQgPSB2ZW5kb3IuaWQ7CiAgICAgICAgdGhpcy5mb3JtLm1haW50YWluVW5pdE5hbWUgPSB2ZW5kb3IudmVuZG9yTmFtZTsKICAgICAgfQogICAgICB0aGlzLnZlbmRvckRpYWxvZyA9IGZhbHNlOwogICAgfSwKICAgIHVzZXJDYW5jZWwyKCkgewogICAgICB0aGlzLnZlbmRvckRpYWxvZyA9IGZhbHNlOwogICAgfSwKICAgIC8v6YCJ5oup5pON5L2c57O757ufCiAgICBzZXR1cE9wdFN5c3RlbShmaWVsZCkgewogICAgICB0aGlzLnNvZnRGaWVsZCA9IGZpZWxkOwogICAgICBpZiAoZmllbGQgPT0gJ29wdElkJykgewogICAgICAgIHRoaXMubXVsdGlwbGUgPSBmYWxzZQogICAgICAgIHRoaXMuc29mdFR5cGUgPSAnc3lzdGVtJwogICAgICB9CiAgICAgIGlmIChmaWVsZCA9PSAnZGJJZCcpIHsKICAgICAgICB0aGlzLm11bHRpcGxlID0gdHJ1ZQogICAgICAgIHRoaXMuc29mdFR5cGUgPSAnZGF0YWJhc2UnOwogICAgICB9CiAgICAgIGlmIChmaWVsZCA9PSAnbWRJZCcpIHsKICAgICAgICB0aGlzLm11bHRpcGxlID0gdHJ1ZQogICAgICAgIHRoaXMuc29mdFR5cGUgPSAnbWlkZGxld2FyZSc7CiAgICAgIH0KICAgICAgdGhpcy5zb2Z0RGlhbG9nID0gdHJ1ZTsKICAgIH0sCiAgICBnZXRTZWxlY3RlZFByb2R1Y3QoKSB7CiAgICAgIGlmICh0aGlzLnNvZnRGaWVsZCA9PSAnb3B0SWQnICYmIHRoaXMuZm9ybS5vcHRzeXN0ZW0gIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuZm9ybS5vcHRzeXN0ZW0ucHJkaWQ7CiAgICAgIH0gZWxzZSBpZiAodGhpcy5zb2Z0RmllbGQgPT0gJ2RiSWQnICYmIHRoaXMuZm9ybS5kYnN5c3RlbUFyciAhPSBudWxsICYmIHRoaXMuZm9ybS5kYnN5c3RlbUFyciAhPSB1bmRlZmluZWQgJiYgdGhpcy5mb3JtLmRic3lzdGVtQXJyLmxlbmd0aCA+IDApIHsKICAgICAgICByZXR1cm4gdGhpcy5mb3JtLmRic3lzdGVtQXJyLm1hcChlID0+IGUucHJkaWQpCiAgICAgIH0gZWxzZSBpZiAodGhpcy5zb2Z0RmllbGQgPT0gJ21kSWQnICYmIHRoaXMuZm9ybS5tZHN5c3RlbUFyciAhPSBudWxsICYmIHRoaXMuZm9ybS5tZHN5c3RlbUFyciAhPSB1bmRlZmluZWQgJiYgdGhpcy5mb3JtLm1kc3lzdGVtQXJyLmxlbmd0aCA+IDApIHsKICAgICAgICByZXR1cm4gdGhpcy5mb3JtLm1kc3lzdGVtQXJyLm1hcChlID0+IGUucHJkaWQpCiAgICAgIH0KICAgIH0sCiAgICBzb2Z0Q29uZmlybShyb3cpIHsKICAgICAgbGV0IHZhbHVlID0gJycKICAgICAgaWYgKHRoaXMuc29mdEZpZWxkID09ICdvcHRJZCcpIHsKICAgICAgICBpZiAocm93ID09IG51bGwpIHsKICAgICAgICAgIHRoaXMuZm9ybS5vcHRzeXN0ZW0gPSBudWxsOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLm9wdHN5c3RlbSA9PSBudWxsKSB0aGlzLmZvcm0ub3B0c3lzdGVtID0ge307CiAgICAgICAgICB0aGlzLmZvcm0ub3B0c3lzdGVtLnByZGlkID0gcm93LnByZGlkOwogICAgICAgICAgdGhpcy5mb3JtLm9wdHN5c3RlbS5wcm9jTmFtZSA9IHJvdy5wcm9jTmFtZTsKICAgICAgICB9CiAgICAgICAgdmFsdWUgPSByb3cucHJvY05hbWUKICAgICAgfQogICAgICBpZiAodGhpcy5zb2Z0RmllbGQgPT0gJ2RiSWQnKSB7CiAgICAgICAgaWYgKHJvdyA9PSBudWxsIHx8IHJvdy5sZW5ndGggPT09IDApIHsKICAgICAgICAgIHRoaXMuZm9ybS5kYnN5c3RlbUFyciA9IFtdOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmZvcm0uZGJzeXN0ZW1BcnIgPSBbXTsKICAgICAgICAgIHJvdy5mb3JFYWNoKGUgPT4gewogICAgICAgICAgICB0aGlzLmZvcm0uZGJzeXN0ZW1BcnIucHVzaCh7cHJkaWQ6IGUucHJkaWQsIHByb2NOYW1lOiBlLnByb2NOYW1lfSkKICAgICAgICAgICAgdmFsdWUgKz0gJ+OAkCcgKyBlLnByb2NOYW1lICsgJ+OAkScKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9CiAgICAgIGlmICh0aGlzLnNvZnRGaWVsZCA9PSAnbWRJZCcpIHsKICAgICAgICBpZiAocm93ID09IG51bGwgfHwgcm93Lmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgdGhpcy5mb3JtLm1kc3lzdGVtQXJyID0gW107CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZm9ybS5tZHN5c3RlbUFyciA9IFtdOwogICAgICAgICAgcm93LmZvckVhY2goZSA9PiB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5tZHN5c3RlbUFyci5wdXNoKHtwcmRpZDogZS5wcmRpZCwgcHJvY05hbWU6IGUucHJvY05hbWV9KQogICAgICAgICAgICB2YWx1ZSArPSAn44CQJyArIGUucHJvY05hbWUgKyAn44CRJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy5mb3JtW3RoaXMuc29mdEZpZWxkXSA9IHZhbHVlCiAgICAgIHRoaXMuc29mdERpYWxvZyA9IGZhbHNlOwogICAgfSwKICAgIHNvZnRDYW5jZWwoKSB7CiAgICAgIHRoaXMuc29mdERpYWxvZyA9IGZhbHNlOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsuLi50aGlzLmZvcm19CiAgICAgIHRoaXMuZm9ybS5hc3NldENsYXNzID0gdGhpcy5jbGFzc0lkCiAgICAgIHRoaXMuZm9ybS5hc3NldENsYXNzRGVzYyA9IHRoaXMuY2xhc3NOYW1lCiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkICYmIHRoaXMuY2hlY2tJcE1hY0FycigpKSB7CiAgICAgICAgICBjb25zdCBmbGFnID0gKHRoaXMuY2hpbGRyZW4gJiYgdGhpcy5jaGlsZHJlbi5sZW5ndGggPiAwKQogICAgICAgICAgaWYgKGZsYWcgPT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5hc3NldFR5cGUgPSB0aGlzLmNsYXNzSWQKICAgICAgICAgICAgdGhpcy5mb3JtLmFzc2V0VHlwZURlc2MgPSB0aGlzLmNsYXNzTmFtZQogICAgICAgICAgfQogICAgICAgICAgdGhpcy5mb3JtLmlwTWFjQXJyLmZvckVhY2goZSA9PiB7CiAgICAgICAgICAgIGlmIChlLmlzTWFpbklwKSB7CiAgICAgICAgICAgICAgZS5tYWluSXAgPSAnMScKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICBlLm1haW5JcCA9ICcwJwogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5hc3NldElkICE9IG51bGwpIHsKICAgICAgICAgICAgdXBkYXRlU2VydmVyMih0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+S/ruaUueaIkOWKnycpCiAgICAgICAgICAgICAgdGhpcy5mb3JtLmlwTWFjQXJyID0gW107CiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5mb3JtLnJlc2V0RmllbGRzKCkKICAgICAgICAgICAgICB0aGlzLiRlbWl0KCdjb25maXJtJywgcmVzcG9uc2UuZGF0YSkKICAgICAgICAgICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICBpZih0aGlzLiRwYXJlbnQuJHBhcmVudCl7CiAgICAgICAgICAgICAgICAvLyB0aGlzLiRwYXJlbnQuJHBhcmVudC5lZGl0RmxhZyA9IGZhbHNlOwogICAgICAgICAgICAgICAgdGhpcy4kcGFyZW50LiRwYXJlbnQuZ2V0TGlzdCAmJiB0aGlzLiRwYXJlbnQuJHBhcmVudC5nZXRMaXN0KCk7CiAgICAgICAgICAgICAgICB0aGlzLiRwYXJlbnQuJHBhcmVudC5nZXRTZXJ2ZXJDb3VudEJ5RGljdERhdGEgJiYgdGhpcy4kcGFyZW50LiRwYXJlbnQuZ2V0U2VydmVyQ291bnRCeURpY3REYXRhKCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZFNlcnZlcjIodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfmlrDlop7miJDlip8nKQogICAgICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybS5yZXNldEZpZWxkcygpCiAgICAgICAgICAgICAgdGhpcy5mb3JtLmlwTWFjQXJyID0gW107CiAgICAgICAgICAgICAgdGhpcy4kZW1pdCgnY29uZmlybScsIHJlc3BvbnNlLmRhdGEpCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UKICAgICAgICAgICAgICBpZiAodGhpcy4kcm91dGUucGFyYW1zLmFkZCkgewogICAgICAgICAgICAgICAgdGhpcy4kcm91dGVyLmJhY2soKQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB0aGlzLmJhY2tBZGQodGhpcy5mb3JtKQogICAgICAgICAgICAgIHRoaXMudmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgIGlmKHRoaXMuJHBhcmVudC4kcGFyZW50KXsKICAgICAgICAgICAgICAgIC8vIHRoaXMuJHBhcmVudC4kcGFyZW50LmVkaXRGbGFnID0gZmFsc2U7CiAgICAgICAgICAgICAgICB0aGlzLiRwYXJlbnQuJHBhcmVudC5nZXRMaXN0ICYmIHRoaXMuJHBhcmVudC4kcGFyZW50LmdldExpc3QoKTsKICAgICAgICAgICAgICAgIHRoaXMuJHBhcmVudC4kcGFyZW50LmdldFNlcnZlckNvdW50QnlEaWN0RGF0YSAmJiB0aGlzLiRwYXJlbnQuJHBhcmVudC5nZXRTZXJ2ZXJDb3VudEJ5RGljdERhdGEoKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS5yZXNldEZpZWxkcygpCiAgICAgIHRoaXMuJGVtaXQoJ2NhbmNlbCcpCiAgICB9LAogICAgLyoqCiAgICAgKiDlhbbku5bpobXpnaLlop7liqDnmoTlm57osIMKICAgICAqLwogICAgYmFja0FkZChhZGRyb3cpIHsKICAgICAgdGhpcy4kcm9vdC4kZW1pdCh0aGlzLmFkZEV2ZW50LCBhZGRyb3cpCiAgICB9LAogICAgYWRkSXBNYWMoKSB7CiAgICAgIHRoaXMuZm9ybS5pcE1hY0Fyci5wdXNoKHtpc01haW5JcDogZmFsc2V9KQogICAgfSwKICAgIHJlbW92ZUlwTWFjKGluZGV4KSB7CiAgICAgIHRoaXMuZm9ybS5pcE1hY0Fyci5zcGxpY2UoaW5kZXgsIDEpCiAgICB9LAogICAgY2hlY2tJcE1hY0FycigpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5pcE1hY0FyciA9PT0gbnVsbCB8fCB0aGlzLmZvcm0uaXBNYWNBcnIubGVuZ3RoIDw9IDApIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign572R57uc5L+h5oGv5LiN5Y+v5Li656m6JykKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQoKICAgICAgbGV0IG1haW5JcENvdW50ID0gMAogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuZm9ybS5pcE1hY0Fyci5sZW5ndGg7IGkrKykgewogICAgICAgIGNvbnN0IGUgPSB0aGlzLmZvcm0uaXBNYWNBcnJbaV0KICAgICAgICBpZiAoZS5kb21haW5JZCA9PT0gbnVsbCB8fCBlLmRvbWFpbklkID09PSAnJyB8fCBlLmRvbWFpbklkID09PSB1bmRlZmluZWQpIHsKICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfmiYDlsZ7nvZHnu5zkuI3lj6/kuLrnqbonKQogICAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgICAgfQogICAgICAgIGlmIChlLmlwdjQgPT09IG51bGwgfHwgZS5pcHY0ID09PSAnJyB8fCBlLmlwdjQgPT09IHVuZGVmaW5lZCkgewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ0lQ5LiN5Y+v5Li656m6JykKICAgICAgICAgIHJldHVybiBmYWxzZQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBpZiAoIXRoaXMuaXNWYWxpZElQKGUuaXB2NCkpIHsKICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ivt+i+k+WFpeato+ehruagvOW8j+eahElQJykKICAgICAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIGlmIChlLmlzTWFpbklwKSB7CiAgICAgICAgICBtYWluSXBDb3VudCsrCiAgICAgICAgfQogICAgICB9CiAgICAgIGlmIChtYWluSXBDb3VudCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCflv4XpobvpgInmi6nkuIDkuKrkuLtJUCcpCiAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgIH0gZWxzZSBpZiAobWFpbklwQ291bnQgPiAxKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+WPquiDvemAieaLqeS4gOS4quS4u0lQJykKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQoKICAgICAgcmV0dXJuIHRydWUKICAgIH0sCiAgICBpbnB1dExvc3NPZkZvY3VzKHZhbCwgaW5kZXgpewogICAgICBpZiAodGhpcy5mb3JtLmFzc2V0Q29kZSA9PSAnJyB8fCAhdGhpcy5mb3JtLmFzc2V0Q29kZSl7CiAgICAgICAgdGhpcy5mb3JtLmFzc2V0Q29kZSA9ICdaSicgKyB0aGlzLmdldEZvcm1hdHRlZERhdGUoKQogICAgICB9CiAgICB9LAogICAgLyoqCiAgICAgKiDotYTkuqfnsbvlnovlj5jljJYKICAgICAqLwogICAgYXNzZXRUeXBlQ2hhbmdlKGRhdGEpIHsKICAgICAgbGV0IGl0ZW0gPSB0aGlzLmNoaWxkcmVuLmZpbmQoaXRlbSA9PiB7CiAgICAgICAgcmV0dXJuIGl0ZW0uaWQgPT0gZGF0YTsKICAgICAgfSkKICAgICAgaWYgKGl0ZW0pIHsKICAgICAgICB0aGlzLmZvcm0uYXNzZXRUeXBlRGVzYyA9IGl0ZW0udHlwZU5hbWU7CiAgICAgIH0KICAgIH0sCgogICAgZ2V0UnVsZXNGb3JGaWVsZChpdGVtKSB7CiAgICAgIHJldHVybiB0aGlzLmR5bmFtaWNSdWxlc1tpdGVtLmZpZWxkS2V5XSB8fCBbXTsKICAgIH0sCgogICAgLy8g6I635Y+W5a2X5q615omA5Y2g5YiX5pWwCiAgICBnZXRTcGFuKGZpZWxkS2V5KSB7CiAgICAgIHJldHVybiBmaWVsZEtleSA9PT0gJ3JlbWFyaycgPyAxNiA6IDg7CiAgICB9LAoKICAgIC8vIOWIpOaWreWtl+auteaYr+WQpuW6lOivpeaYvuekugogICAgc2hvdWxkU2hvd0ZpZWxkKGl0ZW0pIHsKICAgICAgLy8g5om/6L296K6+5aSH5Y+q5Zyo6Jma5ouf6K6+5aSH5Li6WeaXtuaYvuekugogICAgICBpZiAoaXRlbS5maWVsZEtleSA9PT0gJ2Jhc2VBc3NldCcpIHsKICAgICAgICByZXR1cm4gdGhpcy5mb3JtLmlzVmlydHVhbCA9PT0gJ1knOwogICAgICB9CiAgICAgIC8vIOi1hOS6p+exu+Wei+WPquWcqOacieWtkOexu+Wei+aXtuaYvuekugogICAgICBpZiAoaXRlbS5maWVsZEtleSA9PT0gJ2Fzc2V0VHlwZScpIHsKICAgICAgICByZXR1cm4gdGhpcy5jaGlsZHJlbiAmJiB0aGlzLmNoaWxkcmVuLmxlbmd0aDsKICAgICAgfQogICAgICByZXR1cm4gdHJ1ZTsKICAgIH0sCgogICAgZ2V0Rm9ybWF0dGVkRGF0ZSgpIHsKICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsKICAgICAgY29uc3QgeWVhciA9IG5vdy5nZXRGdWxsWWVhcigpOwogICAgICBjb25zdCBtb250aCA9IFN0cmluZyhub3cuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgIGNvbnN0IGRheSA9IFN0cmluZyhub3cuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpOwogICAgICBjb25zdCBob3VycyA9IFN0cmluZyhub3cuZ2V0SG91cnMoKSkucGFkU3RhcnQoMiwgJzAnKTsKICAgICAgY29uc3QgbWludXRlcyA9IFN0cmluZyhub3cuZ2V0TWludXRlcygpKS5wYWRTdGFydCgyLCAnMCcpOwogICAgICBjb25zdCBzZWNvbmRzID0gU3RyaW5nKG5vdy5nZXRTZWNvbmRzKCkpLnBhZFN0YXJ0KDIsICcwJyk7CgogICAgICByZXR1cm4gYCR7eWVhcn0ke21vbnRofSR7ZGF5fSR7aG91cnN9JHttaW51dGVzfSR7c2Vjb25kc31gOwogICAgfSwKICAgIGlzVmFsaWRJUChpcCkgewogICAgICBjb25zdCBpcFJlZ2V4ID0gL14oPzooPzoyNVswLTVdfDJbMC00XVswLTldfFswMV0/WzAtOV1bMC05XT8pXC4pezN9KD86MjVbMC01XXwyWzAtNF1bMC05XXxbMDFdP1swLTldWzAtOV0/KSQvCiAgICAgIGNvbnN0IGZsYWcgPSBpcFJlZ2V4LnRlc3QoaXApCiAgICAgIGNvbnNvbGUubG9nKGZsYWcpCiAgICAgIHJldHVybiBmbGFnCiAgICB9LAogICAgYmFzZUFzc2V0U2VsZWN0SGFuZGxlKCkgewogICAgICB0aGlzLmJhc2VBc3NldERpYWxvZyA9IHRydWU7CiAgICB9LAogICAgYmFzZUFzc2V0Q2FuY2VsKCkgewogICAgICB0aGlzLmJhc2VBc3NldERpYWxvZyA9IGZhbHNlOwogICAgfSwKICAgIGJhc2VBc3NldENvbmZpcm0ocm93KSB7CiAgICAgIHRoaXMuZm9ybS5iYXNlQXNzZXQgPSByb3cuYXNzZXRJZDsKICAgICAgdGhpcy5mb3JtLmJhc2VBc3NldE5hbWUgPSByb3cuYXNzZXROYW1lOwogICAgICB0aGlzLmJhc2VBc3NldENhbmNlbCgpOwogICAgfSwKICAgIHZhbHVlSW5pdCgpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5iYXNlQXNzZXQgIT0gbnVsbCAmJiB0aGlzLmZvcm0uYmFzZUFzc2V0ICE9IHVuZGVmaW5lZCkgewogICAgICAgIHJldHVybiBbe2Fzc2V0SWQ6IHRoaXMuZm9ybS5iYXNlQXNzZXR9XTsKICAgICAgfQogICAgfSwKICAgIGNoYW5nZUlzVmlydHVhbCh2YWx1ZSkgewogICAgICBpZiAodmFsdWUgPT09ICdOJykgewogICAgICAgIHRoaXMuZm9ybS5iYXNlQXNzZXQgPSAnJwogICAgICAgIHRoaXMuZm9ybS5iYXNlQXNzZXROYW1lID0gJycKICAgICAgfQogICAgfSwKICB9Cn0K"}, {"version": 3, "sources": ["editServer.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "editServer.vue", "sourceRoot": "src/views/safe/server", "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"title\"\n    :visible.sync=\"visible\"\n    width=\"1200px\"\n    @open=\"openDialog\"\n    class=\"customForm\"\n    append-to-body>\n    <div class=\"customForm-container\" style=\"height: 100%; padding: 0 20px;\">\n      <el-form\n        ref=\"form\"\n        :model=\"form\"\n        :rules=\"rules\"\n        label-position=\"top\">\n        <el-row>\n          <el-col :span=\"24\">\n            <template v-for=\"ele in visibleAssetFields\">\n              <el-row\n                :gutter=\"20\"\n                :key=\"ele.id\"\n                type=\"flex\"\n                v-if=\"ele.isShow\"\n                style=\"flex-wrap: wrap;margin-bottom: 10px;\">\n                <!-- 基本信息、硬件/软件概况信息、位置信息等模块 -->\n                <el-col\n                  :span=\"24\"\n                  style=\"display: flex; flex-wrap: wrap;\"\n                  v-if=\"ele.formName === '基本信息' || ele.formName === '硬件/软件概况信息' || ele.formName === '位置信息'\">\n                  <el-col class=\"my-title\">\n                    <img v-if=\"ele.formName === '基本信息' && ele.isShow\" src=\"@/assets/images/application/jbxx.png\" alt=\"\"/>\n                    <i v-else-if=\"ele.formName === '硬件/软件概况信息' && ele.isShow\" class=\"el-icon-cpu icon\" />\n                    <i v-else-if=\"ele.formName === '位置信息' && ele.isShow\" class=\"el-icon-location-information icon\" />\n                    {{ ele.formName }}\n                  </el-col>\n                  <template v-for=\"item in ele.fieldsItems\">\n                    <el-col :key=\"item.fieldKey\" :span=\"getSpan(item.fieldKey)\" v-if=\"shouldShowField(item)\">\n                      <el-form-item\n                        :label=\"item.fieldName\"\n                        :prop=\"item.fieldKey\"\n                        :rules=\"getRulesForField(item)\">\n                        <!-- 资产类型字段 -->\n                        <template v-if=\"item.fieldKey === 'assetType'\">\n                          <el-select v-model=\"form.assetType\" placeholder=\"请选择资产类型\" @change=\"assetTypeChange\">\n                            <el-option v-for=\"children in children\" :key=\"children.id\" :label=\"children.typeName\"\n                                       :value=\"children.id\"/>\n                          </el-select>\n                        </template>\n\n                        <!-- 重要程度字段 -->\n                        <template v-else-if=\"item.fieldKey === 'degreeImportance'\">\n                          <el-select v-model=\"form.degreeImportance\" :placeholder=\"'请选择' + item.fieldName\">\n                            <el-option\n                              v-for=\"dict in dict.type.impt_grade\"\n                              :key=\"dict.value\"\n                              :label=\"dict.label\"\n                              :value=\"dict.value\"\n                            ></el-option>\n                          </el-select>\n                        </template>\n\n                        <!-- 所属部门字段 -->\n                        <template v-else-if=\"item.fieldKey === 'deptId'\">\n                          <dept-select v-model=\"form.deptId\" is-current/>\n                        </template>\n\n                        <!-- 供应商字段 -->\n                        <template v-else-if=\"item.fieldKey === 'vendor'\">\n                          <el-input\n                            v-model=\"form.vendorName\"\n                            @focus=\"showVendorDialog('vendor')\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n\n                        <!-- 设备厂商字段 -->\n                        <template v-else-if=\"item.fieldKey === 'facilityManufacturer'\">\n                          <el-input\n                            v-model=\"form.facilityManufacturerName\"\n                            @focus=\"showVendorDialog('facilityManufacturer')\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n\n                        <!-- 维保单位字段 -->\n                        <template v-else-if=\"item.fieldKey === 'maintainUnit'\">\n                          <el-input\n                            v-model=\"form.maintainUnitName\"\n                            @focus=\"showVendorDialog('maintainUnit')\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n\n                        <!-- 虚拟设备字段 -->\n                        <template v-else-if=\"['isVirtual', 'isSparing'].includes(item.fieldKey)\">\n                          <el-radio-group\n                            v-model=\"form[item.fieldKey]\"\n                            @input=\"item.fieldKey === 'isVirtual' ? changeIsVirtual : undefined\"\n                          >\n                            <el-radio\n                              v-for=\"dict in getDictDataInfo(item.fieldKey)\"\n                              :key=\"dict.value\"\n                              :label=\"dict.value\"\n                            >\n                              {{ dict.label }}\n                            </el-radio>\n                          </el-radio-group>\n                        </template>\n\n                        <!-- 承载设备字段 -->\n                        <template v-else-if=\"item.fieldKey === 'baseAsset'\">\n                          <el-input\n                            v-model=\"form.baseAssetName\"\n                            @focus=\"baseAssetSelectHandle\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n\n                        <template v-else-if=\"item.fieldKey === 'optId'\">\n                          <el-input\n                            v-model=\"form.optId\"\n                            @focus=\"setupOptSystem('optId')\"\n                            placeholder=\"请输入操作系统\"/>\n                        </template>\n\n                        <template v-else-if=\"item.fieldKey === 'mdId'\">\n                          <el-input\n                          v-model=\"form.mdId\"\n                          @focus=\"setupOptSystem('mdId')\"\n                          placeholder=\"请输入中间件\"/>\n                        </template>\n\n                        <template v-else-if=\"item.fieldKey === 'dbId'\">\n                          <el-input\n                            v-model=\"form.dbId\"\n                            @focus=\"setupOptSystem('dbId')\"\n                            placeholder=\"请输入数据库\"/>\n                        </template>\n\n                        <!-- 资产标签字段 -->\n                        <template v-else-if=\"item.fieldKey === 'tags'\">\n                          <dynamic-tag v-model=\"form.tags\"/>\n                        </template>\n\n                        <!-- 所在机房 -->\n                        <template v-else-if=\"item.fieldKey === 'locationId'\">\n                          <location-select v-model=\"form.locationId\"/>\n                        </template>\n\n                        <!-- 默认文本输入框 -->\n                        <template v-else>\n                          <el-input\n                            v-model=\"form[item.fieldKey]\"\n                            :type=\"item.fieldKey === 'remark' ? 'textarea' : 'text'\"\n                            :placeholder=\"'请输入' + item.fieldName\"/>\n                        </template>\n                      </el-form-item>\n                    </el-col>\n                  </template>\n                </el-col>\n\n                <!-- 网络信息模块单独处理 -->\n                <el-col :span=\"24\" v-if=\"ele.formName === '网络信息' && ele.isShow\">\n                  <div class=\"my-title\"><img src=\"@/assets/images/application/netinfo.png\" alt=\"\"/>网络信息</div>\n                  <template v-for=\"item in ele.fieldsItems\">\n                    <el-form-item\n                      :key=\"item.fieldKey\"\n                      :label=\"item.fieldName\"\n                      :prop=\"item.fieldKey\"\n                      v-if=\"item.fieldKey === 'exposedIp'\"\n                      :rules=\"getRulesForField(item)\">\n                      <el-input v-model=\"form.exposedIp\" placeholder=\"请输入外网IP\"/>\n                    </el-form-item>\n                  </template>\n                  <el-table :data=\"form.ipMacArr\" v-if=\"netinfoTable.length\" style=\"width: 100%\">\n                    <el-table-column\n                      v-for=\"(colum, index) in netinfoTable\"\n                      :key=\"index\"\n                      :label=\"colum.fieldName\"\n                      :width=\"colum.fieldKey === 'isMainIp' ? '60px' : ''\">\n                      <template slot-scope=\"scope\">\n                        <!-- 主IP复选框 -->\n                        <el-checkbox\n                          v-if=\"colum.fieldKey === 'isMainIp'\"\n                          v-model=\"scope.row.isMainIp\"/>\n\n                        <!-- 网络区域选择器 -->\n                        <NetworkSelect\n                          v-else-if=\"colum.fieldKey === 'domainId'\"\n                          v-model=\"scope.row.domainId\"/>\n\n                        <!-- IP地址输入框 -->\n                        <el-input\n                          v-else-if=\"colum.fieldKey === 'ipv4'\"\n                          v-model=\"scope.row.ipv4\"\n                          placeholder=\"请输入IP\"/>\n\n                        <!-- MAC地址输入框 -->\n                        <el-input\n                          v-else-if=\"colum.fieldKey === 'mac'\"\n                          v-model=\"scope.row.mac\"\n                          placeholder=\"请输入MAC地址\"/>\n                      </template>\n                    </el-table-column>\n                    <el-table-column align=\"center\">\n                      <template slot=\"header\">\n                        <div style=\"display: inline;\">操作</div>\n                        <div style=\"display: inline;float: right;font-size: 18px;margin-left: 10px;\"><i\n                          class=\"el-icon-circle-plus-outline\" @click=\"addIpMac\"/></div>\n                      </template>\n                      <template slot-scope=\"scope\">\n                        <span style=\"font-size: 18px;\">\n                          <i class=\"el-icon-remove-outline\" @click=\"removeIpMac(scope.$index)\"/>\n                        </span>\n                      </template>\n                    </el-table-column>\n                  </el-table>\n                </el-col>\n              </el-row>\n            </template>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <el-dialog title=\"选择供应商\" :visible.sync=\"vendorDialog\" width=\"900px\" append-to-body>\n        <vendor-select v-if=\"vendorDialog\" :value=\"getSelectedVendor()\" @confirm=\"selectConfirm2\" @cancel=\"userCancel2\"/>\n      </el-dialog>\n      <el-dialog title=\"选择系统软件\" :visible.sync=\"softDialog\" width=\"800px\" append-to-body>\n        <product-select v-if=\"softDialog\" :type=\"softType\" :multipleMode=\"multiple\" :value=\"getSelectedProduct()\"\n                        @confirm=\"softConfirm\"\n                        @cancel=\"softCancel\" @change=\"softDialog= false\" ref=\"softForm\"/>\n      </el-dialog>\n      <el-dialog title=\"选择服务器\" :visible.sync=\"baseAssetDialog\" width=\"800px\" append-to-body>\n        <over-view-select v-if=\"baseAssetDialog\" :asset-class=\"4\" :type=\"softType\" :value=\"valueInit()\"\n                          @confirm=\"baseAssetConfirm\" @cancel=\"baseAssetCancel\"></over-view-select>\n      </el-dialog>\n      <el-dialog title=\"选择绑定主机\" :visible.sync=\"assetHostDialog\" width=\"800px\" append-to-body :show-close=\"false\">\n        <el-select v-model=\"host\">\n          <el-option v-for=\"host in assetHosts\"\n                     :key=\"host.id\"\n                     :label=\"host.hostName\"\n                     :value=\"host.id\"></el-option>\n        </el-select>\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"chooseHost\">确定</el-button>\n        </div>\n      </el-dialog>\n    </div>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button v-if=\"editItem === 'edit'\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\n      <el-button @click=\"cancel\">取 消</el-button>\n    </div>\n  </el-dialog>\n\n</template>\n\n<script>\nimport {addServer2, updateServer2, getServer} from \"@/api/safe/server\";\nimport {getVendor} from \"@/api/safe/vendor\";\nimport {getAssetTypeChildrenByid} from \"@/api/safe/overview\";\nimport vendorSelect from \"@/views/components/select/vendorSelect\";\nimport productSelect from \"@/views/components/select/productSelect\";\nimport DeptSelect from \"@/views/components/select/deptSelect\";\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport LocationSelect from \"@/views/components/select/locationSelect\";\nimport DynamicTag from \"@/components/DynamicTag\";\nimport OverViewSelect from \"@/views/components/select/overViewSelect\";\nimport {getHostInfo, getHostList} from \"@/api/asset/host\";\nimport {listProduct} from \"@/api/monitor2/product\";\nimport {listDomain} from \"@/api/dict/domain\";\nimport assetRegister from \"@/mixins/assetRegister\";\n\nexport default {\n  name: 'EditServer',\n  mixins: [assetRegister],\n  dicts: ['impt_grade', 'sys_yes_no', 'proc_type','is_sparing'],\n  components: {\n    vendorSelect,\n    productSelect,\n    DeptSelect,\n    NetworkSelect,\n    LocationSelect,\n    DynamicTag,\n    OverViewSelect,\n  },\n  props: {\n    assetId: {\n      type: [Number, String],\n      default: null,\n      required: false\n    },\n    handleData: {\n      type: Object,\n      default: null,\n      required: false\n    },\n    title: {\n      type: String,\n      default: '新增服务器'\n    },\n    editFlagVisible: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      host: undefined,\n      assetHosts: [],\n      assetHostDialog: false,\n      myTags: ['国产化', '安可'],\n      classId: 4,\n      children: [],\n      typeTreeData: [],\n      typelist: [],\n      vendorDialog: false,\n      softDialog: false,\n      softType: 'system',\n      softField: '',\n      editItem: 'edit',\n      addEvent: '',\n      multiple: false,\n      form: {\n        ipMacArr: [],\n        assetName: '',\n        exposedIp: '',\n        handleId: undefined,\n        dbsystemArr: [],\n        mdsystemArr: [],\n      },\n      rules: {\n        assetCode: [\n          /*{required: true, message: \"资产编码不能为空\", trigger: \"blur\"},*/\n          {min: 0, max: 64, message: '资产编码不能超过 64 个字符', trigger: 'blur'},\n        ],\n        assetName: [\n          {required: true, message: \"资产名称不能为空\", trigger: \"blur\"},\n          {min: 0, max: 64, message: '资产名称不能超过 64 个字符', trigger: 'blur'},\n        ],\n        assetType: [\n          {required: true, message: \"资产类型不能为空\", trigger: \"blur\"},\n        ],\n        ip: [\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: \"IP地址不能为空或格式不正确\",\n            trigger: \"blur\"\n          },\n        ],\n        mac: [\n          {pattern: '^([0-9a-fA-F]{2}(:|-)){5}[0-9a-fA-F]{2}$', message: \"Mac地址格式不正确\", trigger: \"blur\"},\n        ],\n        domainId: [\n          {required: true, message: \"网络区域不能为空！\", trigger: 'blur'}\n        ],\n        locationDetail: [\n          {min: 0, max: 128, message: '详细地址不能超过 128 个字符', trigger: 'blur'},\n        ],\n        deptId: [\n          {required: true, message: \"所属部门不能为空\", trigger: \"blur\"},\n        ]\n      },\n      baseAssetDialog: false,\n      assetAllocationType: '2',\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.editFlagVisible\n      },\n      set(val) {\n        this.$emit('update:editFlagVisible', val)\n      }\n    },\n    // 动态字段\n    visibleAssetFields() {\n      return this.assetList.map(group => {\n        const newGroup = { ...group };\n        newGroup.fieldsItems = group.fieldsItems.filter(item => item.isShow);\n        return newGroup;\n      });\n    },\n    // 动态规则\n    dynamicRules() {\n      const rules = {};\n      this.visibleAssetFields.forEach(group => {\n        group.fieldsItems.forEach(item => {\n          const fieldKey = item.fieldKey;\n\n          if (!rules[fieldKey]) {\n            rules[fieldKey] = [];\n          }\n\n          if (this.rules[fieldKey]) {\n            const filteredRules = this.rules[fieldKey].filter(rule => !rule.required);\n            rules[fieldKey].push(...filteredRules);\n          }\n\n          if (item.required) {\n            const hasRequiredRule = rules[fieldKey].some(rule => rule.required);\n            if (!hasRequiredRule) {\n              rules[fieldKey].push({\n                required: true,\n                message: `${item.fieldName}不能为空`,\n                trigger: 'blur'\n              });\n            }\n          }\n        });\n      });\n      return rules;\n    },\n\n    // 网络信息列表字段\n    netinfoTable() {\n      let assetFields = this.assetList.filter(group => group.formName === '网络信息')\n      return assetFields[0].fieldsItems.filter(item => item.fieldKey !== 'exposedIp' && item.isShow)\n    }\n  },\n  mounted() {\n\n  },\n  methods: {\n    getDictDataInfo(fieldKey) {\n      switch (fieldKey) {\n        case 'isVirtual':\n          return this.dict.type.sys_yes_no;\n        case 'isSparing':\n          return this.dict.type.is_sparing;\n        default:\n          return [];\n      }\n    },\n\n    openDialog() {\n      this.init();\n      this.initData();\n      this.$nextTick(() => {\n        if (!this.form.assetCode){\n          this.form.assetCode = 'ZJ' + this.getFormattedDate()\n        }\n      })\n    },\n    chooseHost() {\n      if(!this.host){\n        this.$message('请选择一台主机！')\n      }else{\n        this.gethost(this.host)\n        this.assetHostDialog = false\n      }\n    },\n    init() {\n      getAssetTypeChildrenByid(this.classId).then(res => {\n        this.typelist = res.rows;\n        for (let i = 0; i < this.typelist.length; i++) {\n          if (this.typelist[i].typeGradedProtection == 1) {\n            this.typelist.splice(Number(i), 1);\n            i = i - 1;\n          }\n        }\n        this.typeTreeData = this.handleTree(this.typelist, 'id', 'pid');\n        this.children = this.typeTreeData[0].children;\n        if (this.children && this.children.length) this.rules.assetType = [\n          {required: true, message: \"资产类型不能为空\", trigger: \"blur\"},\n        ]\n      });\n    },\n    async initData() {\n      if (this.assetId) {\n        await getServer(this.assetId).then(response => {\n          if (response.data.ipMacArr) {\n            response.data.ipMacArr.forEach(e => {\n              if (e.mainIp === '1') {\n                e.isMainIp = true\n              } else if (e.mainIp === '0') {\n                e.isMainIp = false\n              }\n            })\n          }\n          this.form = response.data;\n          if (this.form.optsystem != null) this.form.optId = this.form.optsystem.procName;\n          if (this.form.dbsystemArr !== null && this.form.dbsystemArr !== undefined && this.form.dbsystemArr.length) {\n            this.form.dbId = ''\n            this.form.dbsystemArr.forEach(e => {\n              this.form.dbId += '【' + e.procName + '】'\n            })\n          }\n          if (this.form.mdsystemArr !== null && this.form.mdsystemArr !== undefined && this.form.mdsystemArr.length) {\n            this.form.mdId = ''\n            this.form.mdsystemArr.forEach(e => {\n              this.form.mdId += '【' + e.procName + '】'\n            })\n          }\n          this.myTags = this.form.tags ? this.form.tags.split(',') : [];\n\n          if (this.form.facilityManufacturer) {\n            getVendor(this.form.facilityManufacturer).then(res => {\n              this.form.facilityManufacturerName = res.data.vendorName;\n            });\n          }\n\n          if (this.form.maintainUnit) {\n            getVendor(this.form.maintainUnit).then(res => {\n              this.form.maintainUnitName = res.data.vendorName;\n            });\n          }\n        })\n      }\n      if (this.handleData) {\n        if(this.handleData.deptId){\n          this.form.deptId = this.handleData.deptId;\n        }\n        this.form.handleId = this.handleData.handleId\n        if(this.handleData.ip){\n          let ipMacItem = {\n            isMainIp: true,\n            ipv4: this.handleData.ip\n          }\n          await listDomain({iparea: this.handleData.ip}).then(domainRes => {\n            if(domainRes.data && domainRes.data.length>0){\n              ipMacItem.domainId = domainRes.data[0].domainId;\n            }\n          })\n          this.form.ipMacArr.push(ipMacItem);\n        }\n        if(this.handleData.name){\n          await listProduct({procName: this.handleData.name}).then(productRes => {\n            if(productRes.rows){\n              let matchArr = productRes.rows.filter(item => this.handleData.name === item.procName);\n              if(matchArr && matchArr.length > 0){\n                this.$set(this.form,'optId',matchArr[0].procName);\n                let optsystem = this.form.optsystem || {};\n                optsystem.prdid = matchArr[0].prdid;\n                optsystem.procName = matchArr[0].procName;\n                this.$set(this.form,'optsystem',optsystem);\n              }\n            }\n          })\n        }\n        if (this.handleData.hostId) {\n          if (this.assetId) {\n            if (this.handleData.hostId.indexOf(',') > 0) {\n              this.assetHostDialog = true\n\n              await getHostList({pageSize: -1, hostIds: this.handleData.hostId.split(',')}).then(res => {\n                console.log('res', res)\n                this.assetHosts = res.data.list\n              })\n            } else {\n              this.$confirm('是否使用探测数据替换本地数据?').then(() => {\n                this.gethost(this.handleData.hostId)\n              })\n            }\n          } else {\n            this.gethost(this.handleData.hostId)\n          }\n        }\n      }\n    },\n    gethost(hostId) {\n      getHostInfo(hostId).then(res => {\n        const host = res.data\n        this.form.assetName = host.hostName\n        this.form.cpuFrame = host.hostPhysical.arch\n        let ipMacArr = host.hostNicList.filter(row => row.ipv4.indexOf(host.hostIp) > -1)\n        const oldIpMacArr = this.form.ipMacArr.filter(row => row.ipv4.indexOf(host.hostIp) > -1)\n        console.log('oldIpMacArr', oldIpMacArr)\n        ipMacArr.forEach(row => {\n          row.ipv4 = row.ipv4.replace(/[\\[\\]]/g, '').split('/')[0]\n          row.ipv6 = ''\n          row.id = ''\n          if (oldIpMacArr.length > 0) {\n            row.domainId = oldIpMacArr[0].domainId\n            row.isMainIp = oldIpMacArr[0].isMainIp\n          }\n        })\n        this.form.exposedIp = host.exposedIp\n        this.form.ipMacArr = ipMacArr\n        this.form.cwHostId = host.id\n        this.form.optsystem = {prdid: host.prdid, procName: host.osReleaseName}\n        this.form.optId = this.form.optsystem.procName;\n        this.form.dbsystemArr = []\n        this.form.mdsystemArr = []\n        for (let row of host.hostApplicationList) {\n          if (row.category === 'database') {\n            this.form.dbsystemArr.push({prdid: row.prdid, procName: row.app})\n          } else if (row.category === 'web_server') {\n            this.form.mdsystemArr.push({prdid: row.prdid, procName: row.app})\n          }\n        }\n        this.form.dbId = ''\n        this.form.dbsystemArr.forEach(e => {\n          this.form.dbId += '【' + e.procName + '】'\n        })\n        this.form.mdId = ''\n        this.form.mdsystemArr.forEach(e => {\n          this.form.mdId += '【' + e.procName + '】'\n        })\n      })\n    },\n    /**\n     * 选择供应商\n     */\n    showVendorDialog(vendor) {\n      if (vendor == 'vendor') {\n        this.vendorType = 'vendor';\n      }\n      if (vendor == 'facilityManufacturer') {\n        this.vendorType = 'facilityManufacturer';\n      }\n      if (vendor == 'maintainUnit') {\n        this.vendorType = 'maintainUnit';\n      }\n      this.vendorDialog = true\n    },\n    getSelectedVendor() {\n      if (this.vendorType == 'vendor' && this.form.vendor != undefined) {\n        return this.form.vendor;\n      } else if (this.vendorType == 'facilityManufacturer' && this.form.facilityManufacturer != undefined) {\n        return this.form.facilityManufacturer;\n      } else if (this.vendorType == 'maintainUnit' && this.form.maintainUnit != undefined) {\n        return this.form.maintainUnit;\n      }\n    },\n    /**\n     * 选择产商\n     */\n    selectConfirm2(vendor) {\n      if (this.vendorType == 'vendor') {\n        this.form.vendor = vendor.id;\n        this.form.vendorName = vendor.vendorName;\n      }\n      if (this.vendorType == 'facilityManufacturer') {\n        this.form.facilityManufacturer = vendor.id;\n        this.form.facilityManufacturerName = vendor.vendorName;\n      }\n      if (this.vendorType == 'maintainUnit') {\n        this.form.maintainUnit = vendor.id;\n        this.form.maintainUnitName = vendor.vendorName;\n      }\n      this.vendorDialog = false;\n    },\n    userCancel2() {\n      this.vendorDialog = false;\n    },\n    //选择操作系统\n    setupOptSystem(field) {\n      this.softField = field;\n      if (field == 'optId') {\n        this.multiple = false\n        this.softType = 'system'\n      }\n      if (field == 'dbId') {\n        this.multiple = true\n        this.softType = 'database';\n      }\n      if (field == 'mdId') {\n        this.multiple = true\n        this.softType = 'middleware';\n      }\n      this.softDialog = true;\n    },\n    getSelectedProduct() {\n      if (this.softField == 'optId' && this.form.optsystem != undefined) {\n        return this.form.optsystem.prdid;\n      } else if (this.softField == 'dbId' && this.form.dbsystemArr != null && this.form.dbsystemArr != undefined && this.form.dbsystemArr.length > 0) {\n        return this.form.dbsystemArr.map(e => e.prdid)\n      } else if (this.softField == 'mdId' && this.form.mdsystemArr != null && this.form.mdsystemArr != undefined && this.form.mdsystemArr.length > 0) {\n        return this.form.mdsystemArr.map(e => e.prdid)\n      }\n    },\n    softConfirm(row) {\n      let value = ''\n      if (this.softField == 'optId') {\n        if (row == null) {\n          this.form.optsystem = null;\n        } else {\n          if (this.form.optsystem == null) this.form.optsystem = {};\n          this.form.optsystem.prdid = row.prdid;\n          this.form.optsystem.procName = row.procName;\n        }\n        value = row.procName\n      }\n      if (this.softField == 'dbId') {\n        if (row == null || row.length === 0) {\n          this.form.dbsystemArr = [];\n        } else {\n          this.form.dbsystemArr = [];\n          row.forEach(e => {\n            this.form.dbsystemArr.push({prdid: e.prdid, procName: e.procName})\n            value += '【' + e.procName + '】'\n          })\n        }\n      }\n      if (this.softField == 'mdId') {\n        if (row == null || row.length === 0) {\n          this.form.mdsystemArr = [];\n        } else {\n          this.form.mdsystemArr = [];\n          row.forEach(e => {\n            this.form.mdsystemArr.push({prdid: e.prdid, procName: e.procName})\n            value += '【' + e.procName + '】'\n          })\n        }\n      }\n      this.form[this.softField] = value\n      this.softDialog = false;\n    },\n    softCancel() {\n      this.softDialog = false;\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.form = {...this.form}\n      this.form.assetClass = this.classId\n      this.form.assetClassDesc = this.className\n      this.$refs['form'].validate(valid => {\n        if (valid && this.checkIpMacArr()) {\n          const flag = (this.children && this.children.length > 0)\n          if (flag == undefined) {\n            this.form.assetType = this.classId\n            this.form.assetTypeDesc = this.className\n          }\n          this.form.ipMacArr.forEach(e => {\n            if (e.isMainIp) {\n              e.mainIp = '1'\n            } else {\n              e.mainIp = '0'\n            }\n          })\n          if (this.form.assetId != null) {\n            updateServer2(this.form).then(response => {\n              this.$modal.msgSuccess('修改成功')\n              this.form.ipMacArr = [];\n              this.$refs.form.resetFields()\n              this.$emit('confirm', response.data)\n              this.visible = false;\n              if(this.$parent.$parent){\n                // this.$parent.$parent.editFlag = false;\n                this.$parent.$parent.getList && this.$parent.$parent.getList();\n                this.$parent.$parent.getServerCountByDictData && this.$parent.$parent.getServerCountByDictData();\n              }\n            });\n          } else {\n            addServer2(this.form).then(response => {\n              this.$modal.msgSuccess('新增成功')\n              this.$refs.form.resetFields()\n              this.form.ipMacArr = [];\n              this.$emit('confirm', response.data)\n              this.open = false\n              if (this.$route.params.add) {\n                this.$router.back()\n              }\n              this.backAdd(this.form)\n              this.visible = false;\n              if(this.$parent.$parent){\n                // this.$parent.$parent.editFlag = false;\n                this.$parent.$parent.getList && this.$parent.$parent.getList();\n                this.$parent.$parent.getServerCountByDictData && this.$parent.$parent.getServerCountByDictData();\n              }\n            })\n          }\n        }\n      });\n    },\n    cancel() {\n      this.$refs['form'].resetFields()\n      this.$emit('cancel')\n    },\n    /**\n     * 其他页面增加的回调\n     */\n    backAdd(addrow) {\n      this.$root.$emit(this.addEvent, addrow)\n    },\n    addIpMac() {\n      this.form.ipMacArr.push({isMainIp: false})\n    },\n    removeIpMac(index) {\n      this.form.ipMacArr.splice(index, 1)\n    },\n    checkIpMacArr() {\n      if (this.form.ipMacArr === null || this.form.ipMacArr.length <= 0) {\n        this.$modal.msgError('网络信息不可为空')\n        return false\n      }\n\n      let mainIpCount = 0\n      for (let i = 0; i < this.form.ipMacArr.length; i++) {\n        const e = this.form.ipMacArr[i]\n        if (e.domainId === null || e.domainId === '' || e.domainId === undefined) {\n          this.$modal.msgError('所属网络不可为空')\n          return false\n        }\n        if (e.ipv4 === null || e.ipv4 === '' || e.ipv4 === undefined) {\n          this.$modal.msgError('IP不可为空')\n          return false\n        } else {\n          if (!this.isValidIP(e.ipv4)) {\n            this.$modal.msgError('请输入正确格式的IP')\n            return false\n          }\n        }\n        if (e.isMainIp) {\n          mainIpCount++\n        }\n      }\n      if (mainIpCount === 0) {\n        this.$modal.msgError('必须选择一个主IP')\n        return false\n      } else if (mainIpCount > 1) {\n        this.$modal.msgError('只能选择一个主IP')\n        return false\n      }\n\n      return true\n    },\n    inputLossOfFocus(val, index){\n      if (this.form.assetCode == '' || !this.form.assetCode){\n        this.form.assetCode = 'ZJ' + this.getFormattedDate()\n      }\n    },\n    /**\n     * 资产类型变化\n     */\n    assetTypeChange(data) {\n      let item = this.children.find(item => {\n        return item.id == data;\n      })\n      if (item) {\n        this.form.assetTypeDesc = item.typeName;\n      }\n    },\n\n    getRulesForField(item) {\n      return this.dynamicRules[item.fieldKey] || [];\n    },\n\n    // 获取字段所占列数\n    getSpan(fieldKey) {\n      return fieldKey === 'remark' ? 16 : 8;\n    },\n\n    // 判断字段是否应该显示\n    shouldShowField(item) {\n      // 承载设备只在虚拟设备为Y时显示\n      if (item.fieldKey === 'baseAsset') {\n        return this.form.isVirtual === 'Y';\n      }\n      // 资产类型只在有子类型时显示\n      if (item.fieldKey === 'assetType') {\n        return this.children && this.children.length;\n      }\n      return true;\n    },\n\n    getFormattedDate() {\n      const now = new Date();\n      const year = now.getFullYear();\n      const month = String(now.getMonth() + 1).padStart(2, '0');\n      const day = String(now.getDate()).padStart(2, '0');\n      const hours = String(now.getHours()).padStart(2, '0');\n      const minutes = String(now.getMinutes()).padStart(2, '0');\n      const seconds = String(now.getSeconds()).padStart(2, '0');\n\n      return `${year}${month}${day}${hours}${minutes}${seconds}`;\n    },\n    isValidIP(ip) {\n      const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/\n      const flag = ipRegex.test(ip)\n      console.log(flag)\n      return flag\n    },\n    baseAssetSelectHandle() {\n      this.baseAssetDialog = true;\n    },\n    baseAssetCancel() {\n      this.baseAssetDialog = false;\n    },\n    baseAssetConfirm(row) {\n      this.form.baseAsset = row.assetId;\n      this.form.baseAssetName = row.assetName;\n      this.baseAssetCancel();\n    },\n    valueInit() {\n      if (this.form.baseAsset != null && this.form.baseAsset != undefined) {\n        return [{assetId: this.form.baseAsset}];\n      }\n    },\n    changeIsVirtual(value) {\n      if (value === 'N') {\n        this.form.baseAsset = ''\n        this.form.baseAssetName = ''\n      }\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"@/assets/styles/customForm\";\n.customForm {\n  ::v-deep .el-dialog__body {\n    height: 75vh;\n    overflow-y: auto;\n    padding: 0 !important;\n  }\n}\n</style>\n"]}]}